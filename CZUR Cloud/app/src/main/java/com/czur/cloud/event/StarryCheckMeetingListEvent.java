package com.czur.cloud.event;

import com.czur.cloud.ui.starry.model.StarryDoingMeetingModel;

import java.util.List;

public class StarryCheckMeetingListEvent extends BaseEvent {
	private final List<StarryDoingMeetingModel> params;

	public StarryCheckMeetingListEvent(EventType eventType, List<StarryDoingMeetingModel> params) {
		super(eventType);
		this.params=params;
	}
	public List<StarryDoingMeetingModel> getParams() {
		return params;
	}

	@Override
	public boolean match(Object obj) {
		return true;
	}
}
