<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <include layout="@layout/layout_user_top_bar" />

        <RelativeLayout
            android:id="@+id/starry_menu_instructions_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:layout_marginTop="13.5dp"
            android:visibility="gone"
            android:background="@color/gary_ff">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/starryMargin20"
                android:text="@string/sitting_menu_instructions"
                android:textColor="@color/starry_text_title_color_black"
                android:textSize="@dimen/starryCompanySubTitle" />

            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:background="@mipmap/starry_right_arrow"
                android:padding="10dp" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="13.5dp"
            android:visibility="gone"/>

        <RelativeLayout
            android:id="@+id/starry_menu_advice_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:background="@color/gary_ff">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/starryMargin20"
                android:text="@string/advice"
                android:textColor="@color/starry_text_title_color_black"
                android:textSize="@dimen/starryCompanySubTitle" />


            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:background="@mipmap/starry_right_arrow"
                android:padding="10dp" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/starry_menu_question_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:background="@color/gary_ff"
            android:visibility="gone">


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="17.5dp"
                android:text="@string/question"
                android:textColor="@color/starry_text_title_color_black"
                android:textSize="@dimen/starryCompanySubTitle" />


            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="17dp"
                android:background="@mipmap/starry_right_arrow"
                android:padding="10dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/gray_e5" />
        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="13.5dp" />

        <RelativeLayout
            android:id="@+id/starry_menu_delete_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin50"
            android:background="@color/gary_ff">


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/starryMargin20"
                android:text="@string/delete_from_app"
                android:textColor="@color/red_e75252"
                android:textSize="15sp" />


        </RelativeLayout>
    </LinearLayout>


</RelativeLayout>

