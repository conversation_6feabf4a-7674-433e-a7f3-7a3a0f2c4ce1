package com.czur.cloud.ui.mirror;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.segmentview.SegmentViewSitting;

/**
 * Created by <PERSON> on 2020/12/29
 */
public class SittingReportActivity extends BaseActivity implements View.OnClickListener{
    private String equipmentId;

    private ImageView userBackBtn;
    private ImageView userShareBtn;

    private Fragment mFragment;
    private SegmentViewSitting mSegmentView;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_sitting_report);

        initComponent();
        registerEvent();

    }

    private void initComponent() {
        mSegmentView = findViewById(R.id.segmentview);

        userBackBtn = (ImageView) findViewById(R.id.user_back_btn);

        userShareBtn = (ImageView) findViewById(R.id.user_share_btn);
        TextView userTitle = (TextView) findViewById(R.id.user_title);
        userTitle.setText(R.string.aura_mate_report);

        //取得启动该Activity的Intent对象
        //取出Intent中附加的数据
        String deviceId = getIntent().getStringExtra("deviceId");
        equipmentId = getIntent().getStringExtra("equipmentId");

        /////// 分段选择器  ///////
        mSegmentView = findViewById(R.id.segmentview);
        setDefaultFragment();

        mSegmentView.setOnSegmentViewClickListener((view, position) -> {
            setFragment(position);
        });
    }

    private void setDefaultFragment() {
        setFragment(0);
    }

    //type：0为普通报告 1为日报 2为周报 3为月报
    //dateStr:日报-2020.11.26;周报-2020.11.23-11.29;月报-2020.11
    public void setFragment(int index) {
        Bundle bundle = new Bundle();
        bundle.putString("titleName", getString(R.string.smart_sitting));
        bundle.putString("type",(index+1) + "");//0为普通报告 1为日报 2为周报 3为月报
        bundle.putString("equipmentId",equipmentId);
        FragmentManager fm = getSupportFragmentManager();
        FragmentTransaction ft = fm.beginTransaction();
        userShareBtn.setVisibility(View.VISIBLE);
        if(mFragment != null){
            getSupportFragmentManager().beginTransaction().remove(mFragment).commit();
        }
        mFragment = new SittingReportDayFragment();
        mFragment.setArguments(bundle);
        ft.replace(R.id.layFragme, mFragment).commitAllowingStateLoss();
        mSegmentView.setSelect(index);
    }

    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.user_back_btn) {
            finish();
        }else if (v.getId() == R.id.user_share_btn) {
            //
        }
    }

    private void registerEvent() {
        userBackBtn.setOnClickListener(this);
        userShareBtn.setOnClickListener(this);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

}
