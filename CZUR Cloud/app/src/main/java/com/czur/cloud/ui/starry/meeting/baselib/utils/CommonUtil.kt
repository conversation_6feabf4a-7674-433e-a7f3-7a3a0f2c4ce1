package com.czur.cloud.ui.starry.meeting.baselib.utils

import android.os.Looper
import com.czur.czurutils.log.logE

/**
 * Created by 陈丰尧 on 3/9/21
 */
inline fun doWithoutCatch(
    tag: String = "doWithoutCatch",
    msg: String = "异常信息",
    ignoreError: Boolean = false,
    block: () -> Unit,
) {
    try {
        block()
    } catch (e: Exception) {
        if (!ignoreError) {
            logE("${msg}:${e.message}")
        }
    }
}

/**
 * 判断当前线程是否是主线程
 */
fun inMainThread(): Boolean = Looper.getMainLooper().isCurrentThread