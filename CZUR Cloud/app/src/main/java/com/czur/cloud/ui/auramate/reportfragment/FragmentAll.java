package com.czur.cloud.ui.auramate.reportfragment;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.ReportAdapter;
import com.czur.cloud.entity.realm.SPReportEntity;
import com.czur.cloud.model.AuraMateReportModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.AuraMateReportNewActivity;
import com.czur.cloud.ui.base.BaseFragment;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.reflect.TypeToken;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmResults;
import io.realm.Sort;

/**
 * Created by Jason on 2020/11/15.
 */
public class FragmentAll extends BaseFragment {
    protected String equipmentId, title, reportId;

    private RecyclerView reportRecyclerView;
    private SmartRefreshLayout swipeRefreshLayout;
    private UserPreferences userPreferences;
    private ReportAdapter reportAdapter;
    private Realm realm;
    private HttpManager httpManager;
    private RealmResults<SPReportEntity> datas;
    private RelativeLayout emptyRl;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_report_all, container, false);
        return view;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle != null) {
            equipmentId = bundle.getString("equipmentId");
            title = bundle.getString("title");
//            reportId = bundle.getString("reportId");
        }

        initComponent();

    }

    public static FragmentAll newInstance(String content) {
        Bundle args = new Bundle();
        FragmentAll fragment = new FragmentAll();
        args.putString("ARGS", content);
        fragment.setArguments(args);
        return fragment;
    }

    private void initComponent() {
        realm = Realm.getDefaultInstance();
        userPreferences = UserPreferences.getInstance();
        httpManager = HttpManager.getInstance();
        swipeRefreshLayout = (SmartRefreshLayout)getActivity().findViewById(R.id.refresh_layout);
        swipeRefreshLayout.setOnRefreshListener(onRefreshListener);
        reportRecyclerView = (RecyclerView)getActivity().findViewById(R.id.report_recyclerView);
        emptyRl = (RelativeLayout) getActivity().findViewById(R.id.empty_rl);

        initRecyclerView();
        showProgressDialog();
        getReportList();
    }

    private void showEmpty() {
        if (Validator.isEmpty(datas)){
            emptyRl.setVisibility(View.VISIBLE);
            return;
        }

        if (datas.size() > 0) {
            emptyRl.setVisibility(View.GONE);
        } else {
            emptyRl.setVisibility(View.VISIBLE);
        }
    }

    private void initRecyclerView() {
        reportAdapter = new ReportAdapter(getActivity(), new ArrayList<>());
        reportRecyclerView.setHasFixedSize(true);
        reportRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));

        reportAdapter.setOnItemClickListener(new ReportAdapter.onItemClickListener() {
            @Override
            public void onItemClick(View view, SPReportEntity data) {
                AuraMateReportNewActivity mainActivity= (AuraMateReportNewActivity) getActivity();

                int type = data.getType();
                int reportId = data.getId();
                if (type > 0) {
                    mainActivity.setFragment(type, reportId);
                }
            }
        });

        reportRecyclerView.setAdapter(reportAdapter);

    }

    /**
     * @des: 刷新接口
     * @params:
     * @return:
     */
    private OnRefreshListener onRefreshListener = refreshLayout -> getReportList();

    private List<AuraMateReportModel> getReportList(String equipmentId) {
        String  reportTime = userPreferences.getReportTime();
//        reportTime = "2020-12-1 10:40:17";
        try {
//            final MiaoHttpEntity<AuraMateReportModel> reportEntity = httpManager.request().getUserReportSync(userPreferences.getUserId(), equipmentId, userPreferences.getReportTime(), new TypeToken<List<AuraMateReportModel>>() {
            final MiaoHttpEntity<AuraMateReportModel> reportEntity = httpManager.request().
                    getAllUseReport(
                            userPreferences.getUserId(),
                            equipmentId,
                            reportTime,
                            new TypeToken<List<AuraMateReportModel>>() {}.getType());

            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {

                return reportEntity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private String getServerTimeSync() {
        try {
            MiaoHttpEntity<String> serverTimeEntity = httpManager.request().getServerTime(
                    userPreferences.getUserId(), String.class);
            if (serverTimeEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                String serverTime = serverTimeEntity.getBody();
                return serverTime;
            } else {
                return null;
            }

        } catch (Exception e) {
            logE(e.toString());
            e.printStackTrace();
        }

        return null;

    }

    /**
     * @des: 刷新列表
     * @params:
     * @return:
     */
    public void getReportList() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                try (Realm realm = Realm.getDefaultInstance()) {
                    String time = getServerTimeSync();
                    List<AuraMateReportModel> reportList = getReportList(equipmentId);
                    if (Validator.isNotEmpty(reportList)) {
                        realm.executeTransaction(new Realm.Transaction() {
                            @Override
                            public void execute(Realm realm) {
                                for (AuraMateReportModel auraMateReportModel : reportList) {

                                    int newID = auraMateReportModel.getId();
                                    int newType = auraMateReportModel.getType();
                                    if (newType == 0){
                                        newID = newID + 10000;
                                    }
                                    SPReportEntity sameEntity = realm.where(SPReportEntity.class).equalTo("id", newID).findFirst();
                                    if (sameEntity == null) {
                                        SPReportEntity object = realm.createObject(SPReportEntity.class, newID);
                                        object.setBeginTime(auraMateReportModel.getBeginTime());
                                        object.setCreateTime(auraMateReportModel.getCreateTime());
                                        object.setPushTime(auraMateReportModel.getPushTime());
                                        object.setEndTime(auraMateReportModel.getEndTime());
                                        object.setEquipmentUuid(auraMateReportModel.getEquipmentUuid());
                                        object.setErrorDuration(auraMateReportModel.getErrorDuration());
                                        object.setUsingDuration(auraMateReportModel.getUsingDuration());
                                        object.setProportion(auraMateReportModel.getProportion());
                                        object.setHaveRead(0);

                                        object.setRightDuration(auraMateReportModel.getRightDuration());
                                        object.setSeriousErrorDuration(auraMateReportModel.getSeriousErrorDuration());
                                        object.setMildErrorDuration(auraMateReportModel.getMildErrorDuration());
                                        object.setModerateErrorDuration(auraMateReportModel.getModerateErrorDuration());
                                        object.setRightProportion(auraMateReportModel.getRightProportion());
                                        object.setSeriousProportion(auraMateReportModel.getSeriousProportion());
                                        object.setMildProportion(auraMateReportModel.getMildProportion());
                                        object.setModerateProportion(auraMateReportModel.getModerateProportion());

                                        object.setType(auraMateReportModel.getType());
                                        object.setReportId(auraMateReportModel.getId()+"");
                                        object.setTitle(auraMateReportModel.getTitle());
                                    }
                                }
                            }
                        });
                    }
                    userPreferences.setReportTime(time);
                }

                return null;
            }

            @Override
            public void onSuccess(Void result) {
                swipeRefreshLayout.finishRefresh();
                refreshFiles();
                showEmpty();
                hideProgressDialog();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                swipeRefreshLayout.finishRefresh(false);
                hideProgressDialog();
            }
        });
    }

    private void refreshFiles() {
        datas = realm.where(SPReportEntity.class).equalTo("equipmentUuid", equipmentId).sort("createTime", Sort.DESCENDING).findAll();

        reportAdapter.refreshData(datas);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realm) {
                RealmResults<SPReportEntity> entities = realm.where(SPReportEntity.class).sort("createTime", Sort.DESCENDING).findAll();
                for (SPReportEntity spReportEntity : entities) {
                    spReportEntity.setHaveRead(1);
                }
            }
        });
        realm.close();
    }

    public interface MyOnItemClickListener {
        void OnItemClickListener(View itemView);
    }

}
