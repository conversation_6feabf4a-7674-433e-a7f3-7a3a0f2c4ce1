package com.czur.cloud.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;

import com.czur.cloud.R;

/**
 * Created by <PERSON>z on 2018/3/20
 * Email：<EMAIL>
 */

public class SaveHdViewFailedPopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.5f;


    public SaveHdViewFailedPopup(Context context) {
        super(context);
    }

    public SaveHdViewFailedPopup(Context context, int theme) {
        super(context, theme);
    }


    public static class Builder {
        private Context context;

        public Builder(Context context) {
            this.context = context;

        }


        public SaveHdViewFailedPopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            final SaveHdViewFailedPopup dialog = new SaveHdViewFailedPopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);

            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;

            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final SaveHdViewFailedPopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.5f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.save_failed_dialog, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);

            return layout;
        }
    }

    private void fullScreenImmersive(View view) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            int uiOptions = View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_FULLSCREEN;
            view.setSystemUiVisibility(uiOptions);
        }
    }

    @Override
    public void show() {
        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        super.show();
        fullScreenImmersive(getWindow().getDecorView());
        this.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
    }

}
