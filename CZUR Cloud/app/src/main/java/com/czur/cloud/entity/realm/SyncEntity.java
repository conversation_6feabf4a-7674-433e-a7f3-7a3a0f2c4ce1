package com.czur.cloud.entity.realm;

import java.util.List;

/**
 * Created by Yz on 2018/4/27.
 * Email：<EMAIL>
 */
public class SyncEntity {


    private List<CzurBooksNotesBean> czurBooksNotes;
    private List<CzurBooksPagesBean> czurBooksPages;
    private List<CzurBooksPageTagsBean> czurBooksPageTags;
    private List<CzurBooksPdfsBean> czurBooksPdfs;


    public List<CzurBooksNotesBean> getCzurBooksNotes() {
        return czurBooksNotes;
    }

    public void setCzurBooksNotes(List<CzurBooksNotesBean> czurBooksNotes) {
        this.czurBooksNotes = czurBooksNotes;
    }

    public List<CzurBooksPagesBean> getCzurBooksPages() {
        return czurBooksPages;
    }

    public void setCzurBooksPages(List<CzurBooksPagesBean> czurBooksPages) {
        this.czurBooksPages = czurBooksPages;
    }
    public List<CzurBooksPageTagsBean> getCzurBooksPageTags() {
        return czurBooksPageTags;
    }

    public void setCzurBooksPageTags(List<CzurBooksPageTagsBean> czurBooksPageTags) {
        this.czurBooksPageTags = czurBooksPageTags;
    }

    public List<CzurBooksPdfsBean> getCzurBooksPdfs() {
        return czurBooksPdfs;
    }

    public void setCzurBooksPdfs(List<CzurBooksPdfsBean> czurBooksPdfs) {
        this.czurBooksPdfs = czurBooksPdfs;
    }


    public static class CzurBooksNotesBean {
        /**
         * updateOn : 2018-04-27 10:00:10.659
         * userId : 2289
         * id : FCC73D46-2221-45F6-ACD9-315F7E99D549
         * isDelete : 0
         * synchronousOn : 2018-04-27 10:00:34.726
         * createOn : 2018-04-27 10:00:10.659
         * name : 测笔记本
         */

        private String updateOn;
        private String userId;
        private String id;
        private boolean isDelete;
        private String synchronousOn;
        private String createOn;
        private String name;

        public String getUpdateOn() {
            return updateOn;
        }

        public void setUpdateOn(String updateOn) {
            this.updateOn = updateOn;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public boolean getIsDelete() {
            return isDelete;
        }

        public void setIsDelete(boolean isDelete) {
            this.isDelete = isDelete;
        }

        public String getSynchronousOn() {
            return synchronousOn;
        }

        public void setSynchronousOn(String synchronousOn) {
            this.synchronousOn = synchronousOn;
        }

        public String getCreateOn() {
            return createOn;
        }

        public void setCreateOn(String createOn) {
            this.createOn = createOn;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class CzurBooksPagesBean {
        /**
         * noteId : FCC73D46-2221-45F6-ACD9-315F7E99D549
         * isStar : 0
         * userId : 2289
         * id : C2F93DF5-EAFD-4202-8D5C-342B6C512591
         * isDelete : 0
         * ossBucket: "changer-weilian"
         * ocrContent: "test-ocr",
         * updateOn : 2018-04-27 10:00:16.756
         * uuid : 22142808-C1A3-4C5E-90A4-2F05146351DB
         * synchronousOn : 2018-04-27 10:00:34.726
         * createOn : 2018-04-27 10:00:16.756
         * pageNum : 35
         */

        private String noteId;
        private boolean isStar;
        private String userId;
        private String tagId;
        private String tagName;
        private String noteName;
        private String id;
        private boolean isDelete;
        private String updateOn;
        private String ossBucket;
        private String uuid;
        private String ocrContent;
        private String synchronousOn;
        private String createOn;
        private long fileSize;
        private int pageNum;

        public String getNoteId() {
            return noteId;
        }

        public void setNoteId(String noteId) {
            this.noteId = noteId;
        }

        public boolean getIsStar() {
            return isStar;
        }

        public void setIsStar(boolean isStar) {
            this.isStar = isStar;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public boolean getIsDelete() {
            return isDelete;
        }

        public void setIsDelete(boolean isDelete) {
            this.isDelete = isDelete;
        }

        public String getUpdateOn() {
            return updateOn;
        }

        public void setUpdateOn(String updateOn) {
            this.updateOn = updateOn;
        }

        public String getUuid() {
            return uuid;
        }

        public void setUuid(String uuid) {
            this.uuid = uuid;
        }

        public String getSynchronousOn() {
            return synchronousOn;
        }

        public void setSynchronousOn(String synchronousOn) {
            this.synchronousOn = synchronousOn;
        }

        public String getOssBucket() {
            return ossBucket;
        }

        public void setOssBucket(String ossBucket) {
            this.ossBucket = ossBucket;
        }
        public String getOcrContent() {
            return ocrContent;
        }

        public void setOcrContent(String ocrContent) {
            this.ocrContent = ocrContent;
        }

        public String getCreateOn() {
            return createOn;
        }

        public void setCreateOn(String createOn) {
            this.createOn = createOn;
        }

        public int getPageNum() {
            return pageNum;
        }

        public void setPageNum(int pageNum) {
            this.pageNum = pageNum;
        }
        public long getFileSize() {
            return fileSize;
        }

        public void setFileSize(long fileSize) {
            this.fileSize = fileSize;
        }

        public String getTagId() {
            return tagId;
        }

        public void setTagId(String tagId) {
            this.tagId = tagId;
        }

        public String getTagName() {
            return tagName;
        }

        public void setTagName(String tagName) {
            this.tagName = tagName;
        }

        public String getNoteName() {
            return noteName;
        }

        public void setNoteName(String noteName) {
            this.noteName = noteName;
        }



    }


    public static class CzurBooksPageTagsBean {



        private String tagId;
        private String tagName;
        private String clientId;
        private String userId;
        private boolean isDelete;
        private String synchronousTime;
        private String createTime;
        private String updateTime;

        public String getTagId() {
            return tagId;
        }

        public void setTagId(String tagId) {
            this.tagId = tagId;
        }

        public String getTagName() {
            return tagName;
        }

        public void setTagName(String tagName) {
            this.tagName = tagName;
        }

        public String getClientId() {
            return clientId;
        }

        public void setClientId(String clientId) {
            this.clientId = clientId;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public boolean isDelete() {
            return isDelete;
        }

        public void setDelete(boolean delete) {
            isDelete = delete;
        }


        public String getSynchronousTime() {
            return synchronousTime;
        }

        public void setSynchronousTime(String synchronousTime) {
            this.synchronousTime = synchronousTime;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(String updateTime) {
            this.updateTime = updateTime;
        }
    }

    public static class CzurBooksPdfsBean {
        /**
         * ossKey : 2826/PDF/e(2).pdf
         * userId : 2826
         * updateOn : 2019-06-13 10:49:21.601
         * id : D938790C-EA94-424E-B26E-559FA2FC9B03
         * fileName : e(2)
         * isDelete : 0
         * ossBucket : changer-weilian
         * synchronousOn : 2019-06-13 10:49:37.821
         * createOn : 2019-06-13 10:49:21.601
         * fileSize : 142489
         */

        private String ossKey;
        private String userId;
        private String updateOn;
        private String id;
        private String fileName;
        private boolean isDelete;
        private String ossBucket;
        private String synchronousOn;
        private String createOn;
        private String fileSize;

        public String getOssKey() {
            return ossKey;
        }

        public void setOssKey(String ossKey) {
            this.ossKey = ossKey;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getUpdateOn() {
            return updateOn;
        }

        public void setUpdateOn(String updateOn) {
            this.updateOn = updateOn;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public boolean getIsDelete() {
            return isDelete;
        }

        public void setIsDelete(boolean isDelete) {
            this.isDelete = isDelete;
        }

        public String getOssBucket() {
            return ossBucket;
        }

        public void setOssBucket(String ossBucket) {
            this.ossBucket = ossBucket;
        }

        public String getSynchronousOn() {
            return synchronousOn;
        }

        public void setSynchronousOn(String synchronousOn) {
            this.synchronousOn = synchronousOn;
        }

        public String getCreateOn() {
            return createOn;
        }

        public void setCreateOn(String createOn) {
            this.createOn = createOn;
        }

        public String getFileSize() {
            return fileSize;
        }

        public void setFileSize(String fileSize) {
            this.fileSize = fileSize;
        }
    }
}
