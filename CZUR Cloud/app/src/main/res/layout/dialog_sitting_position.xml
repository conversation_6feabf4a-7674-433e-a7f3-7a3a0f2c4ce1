<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="130dp"
        android:layout_height="35dp"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rec_10_bg_with_code_white"
        android:gravity="center"
        android:orientation="vertical">


        <RelativeLayout

            android:layout_width="100dp"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/img"
                android:layout_width="15dp"
                android:layout_height="10dp"
                android:layout_alignParentLeft="true"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:layout_marginLeft="9dp"
                android:background="@mipmap/blue_right" />


            <TextView
                android:layout_marginLeft="9dp"
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@+id/img"
                android:text="@string/record_success"
                android:textColor="@color/blue_29b0d7"
                android:textSize="15sp"
                android:textStyle="bold" />


        </RelativeLayout>


    </LinearLayout>
</RelativeLayout>