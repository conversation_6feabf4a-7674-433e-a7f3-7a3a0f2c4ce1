package com.czur.cloud.ui.starry.livedatabus

import android.app.ActivityManager
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import com.blankj.utilcode.util.ActivityUtils
import com.czur.cloud.R
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.base.BaseActivity
import com.czur.cloud.ui.home.IndexActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.meeting.common.MeetingCMD
import com.czur.cloud.ui.starry.meeting.common.MsgProcessor.commonMeetCMD
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.czurutils.log.logI

const val pkgName = "com.czur.global.cloud"
const val clsName = "com.czur.cloud.ui.starry.meeting.MeetingMainActivity"

open class BlankActivityForNotification : BaseActivity() {
    val TAG = "BlankActivityForNotification:"

    companion object {
        const val INTENT_ACTION_TYPE = "intentActionType"

        const val AURA_MATE_VIDEO_IN_TYPE = "auraMateVideoIn"// auraMate视频呼入
        val instance: BlankActivityForNotification by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            BlankActivityForNotification()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.transparent)
        setContentView(R.layout.starry_activity_blank_notification)

        logI("$TAG.onCreate")
    }

    override fun onStart() {
        super.onStart()
        logI("$TAG.onStart")
        initData(intent)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        logI("${TAG}.data1 = ${intent?.getStringExtra("test")}")

    }

    fun initData(intent: Intent?) {
        val data = intent?.data
        // starry://com.czur.cloud:9007?uuid=049f0f4510394444a3ce5747d2b9f3f3
        logI("${TAG}.data = ${intent?.getStringExtra("test")}")
        logI("${TAG}.data = ${data}")
        val intentActionType = intent?.getStringExtra(INTENT_ACTION_TYPE)
        if (!intentActionType.isNullOrEmpty()) {
            when (intentActionType) {
                AURA_MATE_VIDEO_IN_TYPE -> {
                    val intent1 = Intent(this, IndexActivity::class.java)
                    intent1.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
                    ActivityUtils.startActivity(intent1)
                    finish()
                }
            }
        } else
            if (data != null) {
                logI(
                    "${TAG}.isAppAlive() = ${isAppAlive()}," +
                            "isActivityAlive() = ${isActivityAlive()}," +
                            "MeetingModel.isInMeeting = ${MeetingModel.isInMeeting.value}"
                )
                if (MeetingModel.isInMeeting.value == true) {

                    val meetingUUID = data.getQueryParameter("uuid") ?: ""
                    LiveDataBus.get().with(StarryConstants.MEETING_REJOIN_FROM_WEB).value =
                        meetingUUID
                    finish()
                } else {
                    val intent1 = Intent(this, IndexActivity::class.java)
                    intent1.data = intent.data
                    intent1.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
                    ActivityUtils.startActivity(intent1)
                    finish()
                }
            } else {
                // 清除制定的会议推送通知消息（华为放后台的会议通知提醒）
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && Build.MANUFACTURER == "HUAWEI") {
                    val notificationManager =
                        getSystemService(NOTIFICATION_SERVICE) as NotificationManager
                    notificationManager.cancel(StarryConstants.STARRY_MESSAGE_CALL_VIDEO_IN_BACKGROUND_CHANNEL_ID)

                    val cmd = MeetingCMD.RECENT_MEETING.cmd
                    val meetNo = StarryPreferences.getInstance().getAccountNo()
                    commonMeetCMD(cmd, meetNo)
                }
                finish()
            }

    }

    // 判断activity是否启动？
    private fun isActivityAlive(): Boolean {
        val intent = Intent()
        intent.setClassName(pkgName, clsName)

        // 说明系统中不存在这个activity
//        if (getPackageManager().resolveActivity(intent, 0) == null)
        return packageManager.resolveActivity(intent, 0) != null

    }

    /**
     * 判断应用是否已经启动
     * @return boolean
     */
    open fun isAppAlive(): Boolean {
        val activityManager: ActivityManager =
            getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val processInfos: List<ActivityManager.RunningAppProcessInfo> =
            activityManager.runningAppProcesses

        for (i in processInfos.indices) {
            if (processInfos[i].processName.equals(pkgName)) {
                return true
            }
        }
        return false
    }



}