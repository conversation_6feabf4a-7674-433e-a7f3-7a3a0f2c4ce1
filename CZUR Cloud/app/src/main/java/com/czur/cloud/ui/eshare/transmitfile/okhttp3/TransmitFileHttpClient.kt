package com.czur.cloud.ui.eshare.receiver.okhttp3

import android.content.Context
import android.util.Base64
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.StringUtils
import com.czur.cloud.R
import com.czur.cloud.preferences.ESharePreferences
import com.czur.cloud.ui.eshare.common.NetUrls
import com.czur.cloud.ui.eshare.common.TransmitFileEntity
import com.czur.cloud.ui.eshare.engine.Constants
import com.czur.cloud.ui.eshare.myentity.FileBrowserEntity
import com.czur.cloud.ui.eshare.myentity.ResultMessageEntity
import com.czur.cloud.ui.eshare.myenum.TransmitFileResultEnum
import com.czur.cloud.ui.eshare.widget.TransmitFileDialogManager
import com.czur.cloud.util.CzurFileUtils
import com.czur.czurutils.log.logD
import com.czur.czurutils.log.logTagD
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import okhttp3.ResponseBody
import okio.*
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.net.InetSocketAddress
import java.net.Socket
import java.net.SocketException
import java.net.SocketTimeoutException
import java.util.concurrent.TimeUnit
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 *  author : WangHao
 *  time   :2023/10/10
 */


object TransmitFileHttpClient {
    private const val KEY_FILE = "files"
    private const val TAG = "UpLoadFileHttpClient"
    private const val IP_PORT1 = 8652L
    private const val IP_PORT2 = 8687L// 备用
    private val IP_PORT_LIST = listOf(IP_PORT1, IP_PORT2)

//    const val CODE_200 = 200L //正常
//    const val CODE_202 = 202L //校验码
//    const val CODE_205 = 205L //md5校验失败
//    const val CODE_210 = 210L //空间不足
//    const val CODE_220 = 220L //上传开关关闭
//    const val CODE_300 = 300L //起始字节错误
//    const val CODE_400 = 400L //未知错误

    //    const val CODE_9527 = -9527L // 因为网络联不通或者服务器错误
//    const val CODE_9528 = -9528L // 接口连通了,返回的内容无法解析
    const val ERROR_PORT = -1L // 测试网络端口全部失效
    private const val CONNECT_FAILED = "Failed to connect to" // toast 网络连接失败
    private const val MANUAL_CLOSE_UPLOAD = "Socket closed" // 手动断开连接
    private const val SOCKET_ERROR = "Software caused connection abort" // 手动断开连接

    var successPort = IP_PORT1 // 当前可以连同的端口
    private var postFileCall: Call? = null
    private var downloadFileCall: Call? = null

    private var isUploading = false
    private var isDownloading = false

    private val client = OkHttpClient().newBuilder().connectTimeout(15, TimeUnit.SECONDS)
        .writeTimeout(15, TimeUnit.SECONDS).readTimeout(15, TimeUnit.SECONDS)
    private val postClient = OkHttpClient().newBuilder()
        .connectTimeout(15, TimeUnit.SECONDS)
        .writeTimeout(15, TimeUnit.SECONDS)
        .readTimeout(300, TimeUnit.SECONDS).build()


    /**
     * 检查服务器是否可用
     */
    suspend fun checkServer(ip: String, timeout: Int = 3000): Boolean {
        for (port in IP_PORT_LIST) {
            val networkAvailable = isNetworkAvailable(ip, port.toInt(), timeout)
            if (networkAvailable) {
                successPort = port
                return true
            }
        }
        return false

    }

    suspend fun isNetworkAvailable(ip: String, port: Int, timeout: Int): Boolean {
        return try {
            val time = System.currentTimeMillis()
            logD("socket_start_$time")
            val socket = Socket()
            val socketAddress = InetSocketAddress(ip, port)
            socket.connect(socketAddress, timeout)
            socket.close()
            logD("socket_End_$time  ${System.currentTimeMillis() - time}")
            true
        } catch (e: IOException) {
            e.printStackTrace()
            false
        }
    }

    //
    suspend fun getVerifyCode(
        ip: String, postCode: Long = IP_PORT1
    ): ResultMessageEntity {
        return suspendCoroutine { continuation ->

            val url = "http://${ip}:${postCode}${NetUrls.GET_VERIFY_CODE}"
            val request =
                Request.Builder().addHeader("Content-Type", "multipart/form-data").url(url).get()
                    .build()
            logTagD(TAG, "开始getVerifyCode url: ${url}")
            client.build().newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    logTagD(TAG, "Request Failure.==${e.message}")
                    continuation.resume(
                        ResultMessageEntity(
                            code = TransmitFileResultEnum.CODE_9527.code, content = e.message!!
                        )
                    )
                }

                override fun onResponse(call: Call, response: Response) {
                    val body = response.body?.string()
                    logTagD(TAG, "getVerifyCodeRequest--Request Success!==${body}")

                    val mapper = jacksonObjectMapper()
                    try {
                        val resultMessage =
                            mapper.readValue(body, ResultMessageEntity::class.java)
                        logTagD(
                            TAG, "getVerifyCodeRequest--Received message:==post= $resultMessage"
                        )
                        continuation.resume(resultMessage)
                    } catch (e: Exception) {
                        continuation.resume(ResultMessageEntity(code = TransmitFileResultEnum.CODE_9528.code))
                    }

                }
            })


        }


    }


    suspend fun getFileStartBytes(
        ip: String,
        entity: TransmitFileEntity,
        context: Context
    ): ResultMessageEntity {
        return suspendCoroutine { continuation ->

            if (successPort == 0L) {
                continuation.resume(ResultMessageEntity(TransmitFileResultEnum.CODE_9527.code))
            }
            val fileName = entity.fileName
            val fileSize = entity.fileSize
            val url = "http://${ip}:${successPort}${NetUrls.GET_GET_START_BYTES}"

            val md5 = entity.md5
            val encodedFileName = Base64.encodeToString(fileName.toByteArray(), Base64.DEFAULT)
            val cleanFileName = encodedFileName.replace("\n", "").replace("\r", "")
            val request = Request.Builder().addHeader("Content-Type", "multipart/form-data")

                .url(url).header("File-MD5", md5!!) // 添加文件名
                .header("File-Size", fileSize) // 添加文件大小的头部
                .header("File-Name", cleanFileName) // 添加文件名
                .header("Device-ID", ESharePreferences.getInstance().uuid) //
                .get().build()
            logTagD(TAG, "开始getFileStartBytes ${url}")
            client.build().newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    logTagD(TAG, "getFileStartBytes--Request Failure.==${e.message}")
                    continuation.resume(ResultMessageEntity(TransmitFileResultEnum.CODE_9527.code))
                }

                override fun onResponse(call: Call, response: Response) {
                    val body = response.body?.string()

                    try {
                        val mapper = jacksonObjectMapper()
                        val resultMessage = mapper.readValue(body, ResultMessageEntity::class.java)
                        logTagD(TAG, "getFileStartBytes--Request Success!==${body}")
                        continuation.resume(resultMessage)
                    } catch (e: Exception) {
                        continuation.resume(ResultMessageEntity(TransmitFileResultEnum.CODE_9528.code))
                    }
                }
            })

        }

    }

    fun stopCurrentPost() {
        isUploading = false
        postFileCall?.cancel()
    }

    suspend fun postFile(
        ipAddress: String,
        entity: TransmitFileEntity,
        uploadedBytes: Long,
        context: Context,
        progressListener: ProgressRequestBody.ProgressListener
    ): Long {
        return suspendCoroutine { continuation ->
            val url = "http://${ipAddress}:${successPort}${NetUrls.POST_UPLOAD_FILE}"
            val fileName = CzurFileUtils.getFileName(context, entity.uri)
            val fileSize = CzurFileUtils.getFileSize(context, entity.uri)

            val md5 = entity.md5

            logTagD(TAG, "======postFileUrl==${url}")
            logTagD(TAG, "======Realfilesize==${fileSize}")
            logTagD(TAG, "====client md5:${md5}")
            var inputStream: InputStream? = null
            try {
                inputStream = context.contentResolver.openInputStream(entity.uri)
            } catch (e: Exception) {
                continuation.resume(TransmitFileResultEnum.CODE_9528.code)
                inputStream?.close()
                return@suspendCoroutine
            }

            val skip = inputStream?.skip(uploadedBytes)
            logTagD(TAG, "====inputStream skip:${skip}")
//            File(entity.uri.path!!).asRequestBody("multipart/form-data".toMediaTypeOrNull())
            val multipartBody = ProgressRequestBody(inputStream!!,
                "application/octet-stream",
                fileSize?.toLong()!! - skip!!,
                uploadedBytes,
                object : ProgressRequestBody.ProgressListener {
                    override fun update(bytesRead: Long, contentLength: Long, done: Boolean) {
//                    println("bytesRead: $bytesRead")
//                    println("contentLength: $contentLength")
//                    println("done: $done")
//                    println("progress: " + (100 * bytesRead) / contentLength + "%")
                        progressListener.update(bytesRead, contentLength, done)
                    }
                })
            val encodedFileName = Base64.encodeToString(fileName.toByteArray(), Base64.DEFAULT)
            val cleanFileName = encodedFileName.replace("\n", "").replace("\r", "")
            val request =
                Request.Builder().addHeader("Content-Type", "multipart/form-data").url(url)
                    .header("File-MD5", md5!!) //
                    .header("File-Size", fileSize.toString()) //
                    .header("File-Name", cleanFileName) // 添加文件名
                    .header("Bytes-Start", "${uploadedBytes}") // 添加文件名
                    .header("Device-ID", ESharePreferences.getInstance().uuid) // 添加文件名
                    .post(multipartBody).build()
            logTagD(TAG, "开始上传")
            postFileCall = postClient.newCall(request)
            postFileCall?.enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    logTagD(TAG, "传输onFailure ${e.message}")
                    inputStream.close()
                    // 判断是手动取消还是被动断开
                    when {
                        e is SocketException && e.message == MANUAL_CLOSE_UPLOAD -> {
                            //手动断开
                            continuation.resume(TransmitFileResultEnum.CODE_9528.code)
                        }

                        e is FileNotFoundException -> {
                            // 文件在传输中被删除
                            continuation.resume(TransmitFileResultEnum.CODE_9528.code)
                        }
//                        e is ProtocolException -> {
//                            continuation.resume(UploadFileResultEnum.CODE_9528.code)
//                        }
                        else -> {
                            continuation.resume(TransmitFileResultEnum.CODE_9527.code)
                        }
                    }
                    logTagD(TAG, "postFile--Request Failure.==${e.message}")
                    // timeout 传输中,关闭设备
//                    java.net.ProtocolException: expected 3654 bytes but received 4096
                }

                override fun onResponse(call: Call, response: Response) {
                    inputStream.close()
                    val body = response.body?.string()
                    logTagD(TAG, "postFile--Request Success!post==${body}")
                    var resultMessage: ResultMessageEntity? = null

                    val mapper = jacksonObjectMapper()
                    try {
                        resultMessage = mapper.readValue(body, ResultMessageEntity::class.java)
                        logTagD(TAG, "postFile--Received message:==post= $resultMessage")
                        continuation.resume(resultMessage.code.toLong())
                    } catch (e: Exception) {
                        logTagD(TAG, "postFile--Received message:==post=catch  ${e.message}")
                        continuation.resume(TransmitFileResultEnum.CODE_9528.code)
                    }

                }
            })
        }
    }


    suspend fun deleteFile(context: Context, entity: TransmitFileEntity, ip: String): Long {
        return suspendCoroutine { continuation ->
            if (successPort == 0L) {
                continuation.resume(TransmitFileResultEnum.CODE_9527.code)
            }
            val fileName = CzurFileUtils.getFileName(context, entity.uri)

            val url = "http://${ip}:${successPort}${NetUrls.GET_DELETE_TEMP_FILE}"
            var md5: String? = ""
            if (entity.md5.isNullOrEmpty()) {
                md5 = CzurFileUtils.getFileMD5(entity.uri, context)
            } else {
                md5 = entity.md5
            }
            val encodedFileName = Base64.encodeToString(fileName.toByteArray(), Base64.DEFAULT)
            val cleanFileName = encodedFileName.replace("\n", "").replace("\r", "")
            val request =
                Request.Builder().addHeader("Content-Type", "multipart/form-data").url(url)
                    .header("File-MD5", md5!!) // 添加文件名
                    .header("File-Name", cleanFileName) // 添加文件名
                    .header("Device-ID", ESharePreferences.getInstance().uuid) //
                    .get().build()
            logTagD(TAG, "开始deleteTempFile ${url}")
            client.build().newCall(request).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    logTagD(TAG, "getFileStartBytes--Request Failure.==${e.message}")
                    continuation.resume(TransmitFileResultEnum.CODE_9527.code)
                }

                override fun onResponse(call: Call, response: Response) {
                    val body = response.body?.string()

                    try {
                        val mapper = jacksonObjectMapper()
                        val resultMessage = mapper.readValue(body, ResultMessageEntity::class.java)
                        logTagD(TAG, "deleteFile--Request Success!==${body}")
                        logTagD(TAG, "deleteFile--Received message: $resultMessage")
                        continuation.resume(resultMessage.code.toLong())
                    } catch (e: Exception) {
                        continuation.resume(TransmitFileResultEnum.CODE_9528.code)
                    }


                }
            })

        }
    }


    /**
     * Root-Local"（本地文件） ,
     * "Root-Download"（下载文件） ,
     * "Root-Share（成者妙传）",
     * "Root-Meeting"（会议录制）,
     * "Root-Picture（拍照/截图）
     */
    fun getFileList(path: String, ip: String): List<FileBrowserEntity.FileEntity> {
        val encodedPath =
            Base64.encodeToString(path.toByteArray(), Base64.DEFAULT).replace("\n", "")
                .replace("\r", "")

        val url = "http://${ip}:${successPort}${NetUrls.GET_FILE_LIST}"
        val request = Request.Builder().addHeader("Content-Type", "multipart/form-data").url(url)
            .header("File-Path", encodedPath) // 绝对路径
            .get().build()
        logTagD(TAG, "开始getFileList ${url}")

        return try {
            val response = client.build().newCall(request).execute()
            val body = response.body?.string()
            val mapper = jacksonObjectMapper()

            val gson = Gson()
            val fromJson = gson.fromJson(body, ResultMessageEntity::class.java)

            when (fromJson.code) {
                TransmitFileResultEnum.CODE_200.code -> {
                    val contentString: String = fromJson.content
                    // 这应该是你从 "content" 字段得到的字符串
                    val listType = object : TypeToken<List<FileBrowserEntity.FileEntity>>() {}.type
                    val files: List<FileBrowserEntity.FileEntity> =
                        Gson().fromJson(contentString, listType)

                    logTagD(TAG, "getFileList--Request Success000!==${files.get(0)?.absPath}")

                    files
                }
//                TransmitFileResultEnum.CODE_220.code -> {// 关闭传输文件
//                    val fileList = arrayListOf<FileBrowserEntity.FileEntity>()
//                    fileList.add(
//                        FileBrowserEntity.FileEntity(
//                            resultEnumCode = TransmitFileResultEnum.CODE_220.code
//                        )
//                    )
//                    fileList
//                }
//                TransmitFileResultEnum.CODE_404.code -> {// 文件不存在
//                    val fileList = arrayListOf<FileBrowserEntity.FileEntity>()
//                    fileList.add(
//                        FileBrowserEntity.FileEntity(
//                            resultEnumCode = TransmitFileResultEnum.CODE_404.code
//                        )
//                    )
//                    fileList
//                }
//
//                TransmitFileResultEnum.CODE_405.code -> {// 文件加锁
//                    val fileList = arrayListOf<FileBrowserEntity.FileEntity>()
//                    fileList.add(
//                        FileBrowserEntity.FileEntity(
//                            resultEnumCode = TransmitFileResultEnum.CODE_405.code
//                        )
//                    )
//                    fileList
//                }

                else -> {
                    val fileList = arrayListOf<FileBrowserEntity.FileEntity>()
                    fileList.add(
                        FileBrowserEntity.FileEntity(
                            resultEnumCode = fromJson.code
                        )
                    )
                    fileList
                }
            }


        } catch (e: Exception) {
            val fileList = arrayListOf<FileBrowserEntity.FileEntity>()
            if (e is SocketException
                || e is SocketTimeoutException
            ) {
                fileList.add(
                    FileBrowserEntity.FileEntity(
                        resultEnumCode = TransmitFileResultEnum.CODE_9527.code
                    )
                )
            } else {
                fileList.add(
                    FileBrowserEntity.FileEntity(
                        resultEnumCode = TransmitFileResultEnum.CODE_9528.code
                    )
                )
            }
            fileList
        }
    }


    fun stopCurrentDownload() {
        isDownloading = false
        downloadFileCall?.cancel()
    }

    suspend fun checkDownloadFile(
        context: Context,
        path: String,
        ip: String,
        checkChunkNumber: String,
    ): Long {
        return suspendCoroutine { continuation ->
            var code = TransmitFileResultEnum.CODE_9528.code
            val encodedPath =
                Base64.encodeToString(path.toByteArray(), Base64.DEFAULT).replace("\n", "")
                    .replace("\r", "")
            val downloadClient = client
                .build()
            val url = "http://${ip}:${successPort}${NetUrls.CHECK_FILE}"

            logTagD(TAG, "开始checkfile ${url}")

            val request = Request.Builder()
                .url(url)
                .header("File-Path", encodedPath) // 绝对路径
                .header("Chunk-Number", checkChunkNumber.toString()) // 分段
                .get()
                .build()

            try {
                downloadFileCall = downloadClient.newCall(request)
                val response = downloadFileCall!!
                    .execute()
                val body = response.body?.string()
                logTagD(TAG, "checkDownloadFile--Request Success000!==${body}")

                if (response.isSuccessful) {
                    try {
                        val mapper = jacksonObjectMapper()
                        val resultMessage = mapper.readValue(body, ResultMessageEntity::class.java)

                        continuation.resume(resultMessage.code)
                    } catch (e: Exception) {
                        continuation.resume(TransmitFileResultEnum.CODE_9528.code)
                    }
                } else {
                    // 处理请求失败
                    continuation.resume(TransmitFileResultEnum.CODE_9528.code)
                }

            } catch (e: IOException) {
                e.printStackTrace()
                continuation.resume(TransmitFileResultEnum.CODE_9528.code)
            }

        }
    }


    suspend fun downloadFile(
        context: Context,
        path: String,
        ip: String,
        currentChunkNumber: String,
        progressListener: DownloadProgressListener
    ): Long {
        return suspendCoroutine { continuation ->
            val encodedPath =
                Base64.encodeToString(path.toByteArray(), Base64.DEFAULT).replace("\n", "")
                    .replace("\r", "")
            val downloadClient = client
                .build()
            val url = "http://${ip}:${successPort}${NetUrls.DOWNLOAD_FILE}"

            logTagD(TAG, "开始downloadfile ${url}")
            val mapper = jacksonObjectMapper()

            val request = Request.Builder()
                .url(url)
                .header("File-Path", encodedPath) // 绝对路径
                .header("Chunk-Number", currentChunkNumber.toString()) // 分段
                .get()
                .build()

            var outputStream: OutputStream? = null
            var downloadedSize: Long = 0

            try {
                downloadFileCall = downloadClient.newCall(request)
                val response = downloadFileCall!!
                    .execute()
//                Total-Chunk-Number: 1
//                Current-Chunk-Size: 1480231
//                File-SIZE: 1480231
//                File-MD5: 2e68d208e19040a5bf181cbcf249cfcc
                val totalChunkNumber = response.header("Total-Chunk-Number", "1")
                val currentChunkSize = response.header("Current-Chunk-Size", "0")
                val allChunkFileSize = response.header("File-SIZE", "0")
                val currentChunkFileMD5 = response.header("File-MD5", "0")
                logTagD(
                    TAG, "downloadfile--Request Success000!==totalChunkNumber${totalChunkNumber}" +
                            " currentChunkSize${currentChunkSize}" +
                            " allChunkFileSize${allChunkFileSize}" +
                            "currentChunkFileMD5${currentChunkFileMD5}"
                )
                val currentChunkFileName = getCurrentChunkFileName(
                    path,
                    totalChunkNumber!!,
                    currentChunkNumber,
                    allChunkFileSize!!
                )

                CzurFileUtils.createDownloadCzurWMAFolder(context)

                if (response.isSuccessful) {
                    val file = File(
                        context.filesDir,
                        "${Constants.CZUR_SHARE_FOLDER}/${currentChunkFileName}"
                    )

                    var inputStream: InputStream? = null
                    try {
                        inputStream = response.body?.byteStream()

                        outputStream = FileOutputStream(file)

//                        currentChunkFileSize = response.body?.contentLength()!!

                        val buffer = ByteArray(1024)

                        var len: Int
                        while (inputStream?.read(buffer).also { len = it!! } != -1) {
                            outputStream.write(buffer, 0, len)
                            downloadedSize += len.toLong()

                            progressListener.update(
                                downloadedSize.toString(),
                                currentChunkSize!!,
                                currentChunkNumber,
                                allChunkFileSize,
                                totalChunkNumber,
                                currentChunkFileMD5!!,
                                file.path
                            )
//                             更新UI来显示下载进度
                        }

                        continuation.resume(TransmitFileResultEnum.CODE_200.code)
                    } catch (e: Exception) {
                        if (e is SocketException || e is SocketTimeoutException) {
                            continuation.resume(TransmitFileResultEnum.CODE_9527.code)
                        } else {
                            continuation.resume(TransmitFileResultEnum.CODE_9528.code)
                        }
                        e.printStackTrace()
                    } finally {
                        inputStream?.close()
                        outputStream?.close()
                    }
                } else {
                    // 处理请求失败
                    continuation.resume(TransmitFileResultEnum.CODE_200.code)
                }

            } catch (e: IOException) {
                e.printStackTrace()
                continuation.resume(TransmitFileResultEnum.CODE_9528.code)
            }

        }

    }


    fun getFunctions(ip: String): String {
        val url = "http://${ip}:${successPort}${NetUrls.CHECK_FUNCTIONS}"
        val request = Request.Builder().url(url)
            .get().build()
        logTagD(TAG, "开始check_funtions ${url}")

        return try {
            val response = client.build().newCall(request).execute()
            val body = response.body?.string()

            val gson = Gson()
            val fromJson = gson.fromJson(body, ResultMessageEntity::class.java)
            logTagD(TAG, "Exception ${fromJson.code}")
            when (fromJson.code) {
                TransmitFileResultEnum.CODE_200.code -> {
                    // 这样说明有这个接口, 根据里面判断是否有下载,上传功能
                    fromJson.content
                }

                TransmitFileResultEnum.CODE_220.code -> {// 文件加锁
                    // 这样就是没有这个接口, 说明没有下载功能
                    ""
                }

                else -> {
                    ""
                }
            }

        } catch (e: Exception) {
            logTagD(TAG, "Exception ${e.message}")
            ""
        }
    }


    private fun getCurrentChunkFileName(
        path: String,
        totalChunkNumber: String,
        currentChunkNumber: String,
        allChunkFileLength: String
    ): String {
        var fileName = FileUtils.getFileName(path)
//        if (totalChunkNumber != "1") { // 长度不为1时, 修改为临时文件名
        fileName = "${fileName}_${allChunkFileLength}_${currentChunkNumber}"
//        }

        return fileName
    }


    fun findTransmitResultEnum(code: Long): TransmitFileResultEnum {
        // 找到UploadFileEnum中code相对应的枚举
        return TransmitFileResultEnum.values().firstOrNull { it.code == code }
            ?: TransmitFileResultEnum.CODE_9527
    }

    //处理接口返回的状态
    fun handleResultMessage(
        resultCode: Long,
        needShowMessage: Boolean = true
    ): Boolean {
        return when (findTransmitResultEnum(resultCode)) {
            TransmitFileResultEnum.CODE_200 -> {
                true
            }

            TransmitFileResultEnum.CODE_202 -> {// 返回校验码
                logTagD(TAG, "TRANSFER_FILE_CODE_202")
                true
            }

            TransmitFileResultEnum.CODE_205 -> {
//                if (needShowMessage) {
//                    ToastUtils.showLong(R.string.eshare_transmit_file_toast_wrong_md5)
//                }
                // 不返回校验码 返回错误码
                logTagD(
                    TAG,
                    "TRANSFER_FILE_CODE_205${StringUtils.getString(R.string.eshare_transmit_file_toast_wrong_md5)}"
                )
                false
            }

            TransmitFileResultEnum.CODE_210 -> {
                if (needShowMessage) {
                    TransmitFileDialogManager.showSpaceNotEnoughDialog()
                }
                logTagD(
                    TAG,
                    "TRANSFER_FILE_CODE_210${StringUtils.getString(R.string.eshare_transmit_file_toast_store_limited)}}"
                )
                false
            }

            TransmitFileResultEnum.CODE_220 -> {
                if (needShowMessage) {
                    TransmitFileDialogManager.showErrorDevicesServerDialog()
                }
                false
            }

            TransmitFileResultEnum.CODE_230 -> {
                if (needShowMessage) {
                    TransmitFileDialogManager.showInMeetingDialog()
                }
                false
            }

            TransmitFileResultEnum.CODE_300 -> {
//                if (needShowMessage) {
//                    ToastUtils.showLong(R.string.eshare_transmit_file_toast_wrong_start_byte)
//                }
                logTagD(
                    TAG,
                    "TRANSFER_FILE_CODE_300${StringUtils.getString(R.string.eshare_transmit_file_toast_wrong_start_byte)}}"
                )
                false
            }

            TransmitFileResultEnum.CODE_400 -> {
//                if (needShowMessage) {
//                    ToastUtils.showLong(R.string.eshare_transmit_file_toast_unknow_error)
//                }
                logTagD(
                    TAG,
                    "TRANSFER_FILE_CODE_400${StringUtils.getString(R.string.eshare_transmit_file_toast_unknow_error)}}"
                )
                false
            }

            TransmitFileResultEnum.CODE_404 -> {
                logTagD(
                    TAG,
                    "TRANSFER_FILE_CODE_404-文件不存在"
                )
                false
            }

            TransmitFileResultEnum.CODE_405 -> {
                logTagD(
                    TAG,
                    "TRANSFER_FILE_CODE_405-文件加锁了"
                )
                false
            }

            TransmitFileResultEnum.CODE_9527 -> {
                false
            }

            TransmitFileResultEnum.CODE_9528 -> {
                false
            }

//            UploadFileEnum.CODE_9529 -> {
//                if (needShowMessage) {
//                    ToastUtils.showLong(R.string.transmit_file_not_find)
//                }
//                false
//            }

            else -> {
                false
            }
        }
    }
}


class ProgressRequestBody(
    private val inputStream: InputStream,
    private val mimeType: String,
    private val fileLength: Long,
    private var uploadedBytes: Long,
    private val progressListener: ProgressListener
) : RequestBody() {

    override fun contentType(): MediaType {
        return mimeType.toMediaTypeOrNull()!!
    }

    @Throws(IOException::class)
    override fun contentLength(): Long {
        return fileLength
    }

    val bufferSize = 1 * KB // can adjust as per the need

    @Throws(IOException::class)
    override fun writeTo(sink: BufferedSink) {
        val source = object : ForwardingSource(inputStream.source()) {
            var totalBytesRead = 0L

            @Throws(IOException::class)
            override fun read(sink: Buffer, byteCount: Long): Long {
                val bytesRead = super.read(sink, minOf(byteCount, bufferSize))
                totalBytesRead += if (bytesRead != -1L) bytesRead else 0
                progressListener.update(totalBytesRead, contentLength(), bytesRead == -1L)
                return bytesRead
            }
        }

        var read: Long
        while (source.read(sink.buffer, bufferSize).also { read = it } != -1L) {
            sink.emit()
        }
        sink.flush()

    }

    interface ProgressListener {
        fun update(bytesRead: Long, contentLength: Long, done: Boolean)
    }


    companion object {
        private const val KB = 1024L
        private const val MB = 1024 * KB
    }
}


class ProgressResponseBody(
    private val responseBody: ResponseBody,
    private val progressListener: ProgressListener
) : ResponseBody() {

    private var bufferedSource: BufferedSource? = null

    override fun contentLength(): Long {
        return responseBody.contentLength()
    }

    override fun contentType(): MediaType? {
        return responseBody.contentType()
    }

    override fun source(): BufferedSource {
        if (bufferedSource == null) {
            bufferedSource = source(responseBody.source()).buffer()
        }
        return bufferedSource!!
    }

    private fun source(source: Source): Source {
        return object : ForwardingSource(source) {
            var totalBytesRead = 0L

            override fun read(sink: Buffer, byteCount: Long): Long {
                val bytesRead = super.read(sink, byteCount)
                // read() returns the number of bytes read, or -1 if this source is exhausted.
                totalBytesRead += if (bytesRead != -1L) bytesRead else 0
                progressListener.update(
                    totalBytesRead,
                    responseBody.contentLength(),
                    bytesRead == -1L
                )
                return bytesRead
            }
        }
    }

    interface ProgressListener {
        fun update(bytesRead: Long, contentLength: Long, done: Boolean)
    }
}

interface DownloadProgressListener {
    fun update(
        currentChunkDownloadSize: String,
        currentChunkFileSize: String,
        currentChunkNumber: String,
        allChunkFileSize: String,
        totalChunkNumber: String,
        currentChunkFileMD5: String,
        localChunkPath: String
    )

}


