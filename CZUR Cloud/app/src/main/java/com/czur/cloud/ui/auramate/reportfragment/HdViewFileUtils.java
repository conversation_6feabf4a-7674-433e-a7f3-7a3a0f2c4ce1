package com.czur.cloud.ui.auramate.reportfragment;

import android.app.Activity;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.R;
import com.czur.cloud.util.PermissionUtil;

import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HdViewFileUtils {
    public static String[] imageFormatSet = {"jpg", "png"};
    public static String IMAGE_NAME = "imageName";
    public static String IMAGE_PATH = "imagePath";
    //记录高清查看的数据及osskey
    public static List<HdViewData> hdViewDataList= new ArrayList<>();
    public static boolean isAblumAcitivityOpen = false;
    public static Activity videoActivity = null;

    //从路径中提取文件名
    public static String getFileName(String pathandname){
        int start=pathandname.lastIndexOf("/");
        if(start!=-1){
            return pathandname.substring(start+1);
        }else{
            return null;
        }
    }

    /**
     * 判断是否是图片文件
     *
     * @param path
     * @return
     */
    public static boolean isImageFile(String path) {
        for (String format : imageFormatSet) {
            if (path.endsWith(format)) {
                return true;
            }
        }
        return false;
    }

    public static List<Map<String, String>> getFiles(String path) {

        List<Map<String, String>> pathList = new ArrayList<>();
        File file = new File(path);
        File[] files = file.listFiles();
        for (File f : files) {
            if (f.isDirectory()) {
                getFiles(f.getAbsolutePath());
            } else {
                if (isImageFile(f.getPath())) {
                    Map<String, String> map = new HashMap<>();
                    map.put(HdViewFileUtils.IMAGE_NAME, f.getName());
                    map.put(HdViewFileUtils.IMAGE_PATH, f.getPath());
                    pathList.add(map);
                }
            }
        }
        return pathList;
    }

    /**
     * 申请权限
     */
    public static void requestCopyToSdPermission(boolean isShare) {
        PermissionUtils.permission(PermissionUtil.getStoragePermission())
                .rationale((activity, shouldRequest) -> {
                    ToastUtils.showShort(isShare ? R.string.denied_share : R.string.denied_sdcard);
                    shouldRequest.again(true);
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NotNull List<String> permissionsGranted) {
                    }

                    @Override
                    public void onDenied(@NotNull List<String> permissionsDeniedForever,
                                         @NotNull List<String> permissionsDenied) {
                        ToastUtils.showShort(isShare ? R.string.denied_share : R.string.denied_sdcard);
                    }
                })
                .theme(ScreenUtils::setFullScreen)
                .request();
    }


}
