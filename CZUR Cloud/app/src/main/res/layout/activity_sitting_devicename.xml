<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include layout="@layout/layout_sitting_top_bar_withspace" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingStart="@dimen/jingMargin20"
        android:paddingTop="@dimen/jingMargin10"
        android:paddingEnd="@dimen/jingMargin20">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/jingMargin20"
            android:singleLine="true"
            app:autoSizeTextType="uniform"
            app:autoSizeMinTextSize="19sp"
            app:autoSizeMaxTextSize="22sp"
            app:autoSizeStepGranularity="1sp"
            android:text="@string/sitting_home_text"
            android:textColor="@color/black_22"
            android:textSize="22sp"
            android:visibility="gone"/>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <EditText
                android:id="@+id/sitting_device_name_edt"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@null"
                android:hint="@string/input_sitting_device_name"
                android:maxLength="14"
                android:maxLines="1"
                android:singleLine="true"
                android:textColor="@color/black_22"
                android:textColorHint="@color/gray_bb"
                android:textSize="15sp" />

            <ImageView
                android:id="@+id/iv_del"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:scaleType="fitXY"
                android:src="@mipmap/tag_delete_icon"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="10dp"
                android:layout_centerVertical="true"
                android:padding="@dimen/dp_5"
                android:visibility="visible"
                />

        </RelativeLayout>

    </LinearLayout>



</LinearLayout>