package com.czur.cloud.ui.mirror.model;

import com.czur.cloud.ui.mirror.comm.FastBleConstants;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class SittingDeviceOfflineModel implements Serializable {
    /*
bind：    ("u_id") String uid,("equipmentUuid") String equipmentUuid,("mac") String mac
unbind：("u_id") String uId,("equipmentUuid") String equipmentUuid,("mac") String mac
sitPic:     String u_id, String clientId, String pic_name
sitHappy/sitErrorPic:String u_id, String clientId, String pic_name, Map dict
dict.put("dateString", date_filename);
dict.put("localeTime", localeTime);
dict.put("type", type+"");
dict.put("uuid", image_dev_name);
    * */

    //bind/unbind
    private String u_id="";
    private String equipmentUuid="";
    private String mac="";

    // sitPic / sitHappy / sitErrorPic
    private String clientId="";
    private String pic_name="";
    private Map<String, String> dict= new HashMap<>();


    public void initDeviceOfflineModel() {
        this.u_id = "";
        this.equipmentUuid="";
        this.mac="";
        this.clientId="";
        this.pic_name="";
        this.dict.put(FastBleConstants.DICT_DATE_STRING, "");
        this.dict.put(FastBleConstants.DICT_LOCALE_TIME, "");
        this.dict.put(FastBleConstants.DICT_TYPE, "");
        this.dict.put(FastBleConstants.DICT_UUID, "");
    }

    public String getU_id() {        return u_id;    }
    public void setU_id(String u_id) {        this.u_id = u_id;    }

    public String getEquipmentUuid() {        return equipmentUuid;    }
    public void setEquipmentUuid(String equipmentUuid) {        this.equipmentUuid = equipmentUuid;    }

    public String getMac() {        return mac;    }
    public void setMac(String mac) {        this.mac = mac;    }

    public String getClientId() {        return clientId;    }
    public void setClientId(String clientId) {        this.clientId = clientId;    }

    public String getPic_name() {        return pic_name;    }
    public void setPic_name(String pic_name) {        this.pic_name = pic_name;    }

    public Map<String, String> getDict() {        return dict;    }
    public void setDict(Map<String, String> dict) {        this.dict = dict;    }

    public void setDateString(String date_filename){
        this.dict.put(FastBleConstants.DICT_DATE_STRING, date_filename);
    }
    public String getDateString(){
        return this.dict.get(FastBleConstants.DICT_DATE_STRING);
    }

    public void setLocaleTime(String localeTime){
        this.dict.put(FastBleConstants.DICT_LOCALE_TIME, localeTime);
    }
    public String getLocaleTime(){
        return this.dict.get(FastBleConstants.DICT_LOCALE_TIME);
    }

    public void setType(String type){
        this.dict.put(FastBleConstants.DICT_TYPE, type);
    }
    public String getType(){
        return this.dict.get(FastBleConstants.DICT_TYPE);
    }

    public void setUUID(String uuid){
        this.dict.put(FastBleConstants.DICT_UUID, uuid);
    }
    public String getUUID(){
        return this.dict.get(FastBleConstants.DICT_UUID);
    }



}
