package com.czur.cloud.ui.books;

import static android.view.View.VISIBLE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.drawable.AnimationDrawable;
import android.os.Bundle;
import android.view.View;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.CleanUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ServiceUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.entity.realm.BookEntity;
import com.czur.cloud.entity.realm.BookPdfEntity;
import com.czur.cloud.entity.realm.PageEntity;
import com.czur.cloud.entity.realm.TagEntity;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.HandWritingCountEvent;
import com.czur.cloud.event.RemoveBookEvent;
import com.czur.cloud.event.StopServiceEvent;
import com.czur.cloud.event.StopSyncTimeCountEvent;
import com.czur.cloud.model.HandwritingCountModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.FirstPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.books.sync.SyncService;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.home.IndexActivity;
import com.czur.cloud.ui.user.UserFeedbackActivity;
import com.suke.widget.SwitchButton;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.concurrent.atomic.AtomicBoolean;

import io.realm.Realm;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class BookMenuActivity extends BaseActivity implements View.OnClickListener {

    private ImageView backBtn;
    private TextView bookMenuTitle;
    private RelativeLayout syncNowRl;
    private TextView syncNowTv;
    private ImageView syncNowImg;
    private ImageView syncNowRight;
    private RelativeLayout bookMenuAddBookRl;
    private RelativeLayout bookMenuMyPdfRl;
    private RelativeLayout bookMenuDeleteRl;
    private RelativeLayout bookMenuHandwritingRl;
    private TextView bookMenuHandwritingNewTv;
    private TextView bookMenuHandwritingCountTv;

    private TextView bookMenuLastSyncTime;
    private RelativeLayout bookMenuAutoSyncRl;
    private SwitchButton bookMenuAutoSyncSwitchBtn;

    private UserPreferences userPreferences;
    private HttpManager httpManager;
    private FirstPreferences firstPreferences;
    private ObjectAnimator syncAnim;
    private ObjectAnimator hideSyncImgAnim;
    private CloudCommonPopup commonPopup;
    private SimpleDateFormat formatter;
    private boolean isAutoSync;
    private boolean isSyncOnlyWifi;
    private Realm realm;
    private WeakHandler handler;
    private boolean isFinish;
    private AtomicBoolean isRightAnim;
    private RelativeLayout bookMenuLanguageRl;
    private TextView bookMenuLanguageTv;
    private TextView bookMenuLanguageNameTv;
    private ImageView bookMenuLanguageRightArrow;
    private RelativeLayout bookMenuAdviceRl;
    private RelativeLayout bookMenuQuestionRl;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_book_menu);
        initComponent();
        registerEvent();
        EventBus.getDefault().register(this);
        getHandwritingCount();
    }

    private void initComponent() {
        handler = new WeakHandler();
        firstPreferences = FirstPreferences.getInstance(this);
        userPreferences = UserPreferences.getInstance(this);
        realm = Realm.getDefaultInstance();
        httpManager = HttpManager.getInstance();
        isRightAnim = new AtomicBoolean(false);
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        isAutoSync = userPreferences.getIsAutoSync();
        isSyncOnlyWifi = userPreferences.getIsSyncOnlyWifi();
        bookMenuLastSyncTime = (TextView) findViewById(R.id.book_menu_last_sync_time);
        syncNowRl = (RelativeLayout) findViewById(R.id.sync_now_rl);
        syncNowTv = (TextView) findViewById(R.id.sync_now_tv);
        syncNowImg = (ImageView) findViewById(R.id.sync_now_img);
        syncNowRight = (ImageView) findViewById(R.id.sync_now_right);
        syncNowRight.setImageResource(R.drawable.blue_right_animlist);

        backBtn = (ImageView) findViewById(R.id.user_back_btn);
        bookMenuTitle = (TextView) findViewById(R.id.user_title);
        bookMenuAddBookRl = (RelativeLayout) findViewById(R.id.book_menu_add_book_rl);
        bookMenuMyPdfRl = (RelativeLayout) findViewById(R.id.book_menu_my_pdf_rl);
        bookMenuDeleteRl = (RelativeLayout) findViewById(R.id.book_menu_delete_rl);

        bookMenuLanguageRl = (RelativeLayout) findViewById(R.id.book_menu_language_rl);
        bookMenuLanguageTv = (TextView) findViewById(R.id.book_menu_language_tv);
        bookMenuLanguageNameTv = (TextView) findViewById(R.id.book_menu_language_name_tv);
        bookMenuLanguageRightArrow = (ImageView) findViewById(R.id.book_menu_language_right_arrow);
        bookMenuAdviceRl = (RelativeLayout) findViewById(R.id.book_menu_advice_rl);
        bookMenuQuestionRl = (RelativeLayout) findViewById(R.id.book_menu_question_rl);

        bookMenuHandwritingRl = (RelativeLayout) findViewById(R.id.book_menu_handwriting_rl);
        bookMenuHandwritingNewTv = (TextView) findViewById(R.id.book_menu_handwriting_new_tv);
        bookMenuHandwritingCountTv = (TextView) findViewById(R.id.book_menu_handwriting_count_tv);
        bookMenuAutoSyncRl = (RelativeLayout) findViewById(R.id.book_menu_auto_sync_rl);
        bookMenuAutoSyncSwitchBtn = (SwitchButton) findViewById(R.id.book_menu_auto_sync_switch_btn);
        bookMenuAutoSyncSwitchBtn.setChecked(isAutoSync);
        setSyncText();


        if (firstPreferences.isFirstNoticeNew()) {
            bookMenuHandwritingNewTv.setVisibility(VISIBLE);
        } else {
            bookMenuHandwritingNewTv.setVisibility(View.GONE);
        }
        bookMenuLanguageNameTv.setText(CZURConstants.LANGUAGE[userPreferences.getOcrLanguageId()]);
        bookMenuLanguageRl.setVisibility(BuildConfig.IS_OVERSEAS? VISIBLE:View.GONE);
        bookMenuTitle.setText(R.string.more);
        initAnim();

    }


    private void registerEvent() {
        bookMenuAdviceRl.setOnClickListener(this);
        bookMenuQuestionRl.setOnClickListener(this);
        backBtn.setOnClickListener(this);
        bookMenuAddBookRl.setOnClickListener(this);
        bookMenuLanguageRl.setOnClickListener(this);
        bookMenuMyPdfRl.setOnClickListener(this);
        bookMenuDeleteRl.setOnClickListener(this);
        bookMenuHandwritingRl.setOnClickListener(this);
        bookMenuAutoSyncRl.setOnClickListener(this);
        syncNowRl.setOnClickListener(this);
        bookMenuAutoSyncSwitchBtn.setOnCheckedChangeListener(new SwitchButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(SwitchButton view, boolean isChecked) {
                userPreferences.setIsAutoSync(isChecked);
            }
        });

    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case SELECT_LANGUAGE:
                bookMenuLanguageNameTv.setText(CZURConstants.LANGUAGE[userPreferences.getOcrLanguageId()]);
                break;
            case HANDWRITING_COUNT_REDUCE:
            case HANDWRITING_COUNT_ADD:
                if (event instanceof HandWritingCountEvent) {
                    getHandwritingCount();
                }
                break;
            case IS_SYNCHRONIZING:
                if (ServiceUtils.isServiceRunning(SyncService.class)) {
                    syncAnim = ObjectAnimator.ofFloat(syncNowImg, "rotation", 0, 720);
                    syncAnim.setInterpolator(new LinearInterpolator());
                    syncAnim.setRepeatCount(ObjectAnimator.INFINITE);
                    syncAnim.setDuration(2000);
                    startLoadingAnim();
                }
                break;
            case SYNC_IS_FINISH:
                setSyncText();
                break;
            case SYNC_ANIM_FINISH:
                isFinish = true;
                stopSyncAnim();
                logI("receive service EventBUS 完成");
                break;
            case  SYNC_SPACE_IS_NOT_ENOUGH:
                logI("SYNC_SPACE_IS_NOT_ENOUGH");
            case SYNC_IS_STOP:
                isFinish = false;
                stopSyncAnim();
                logI("receive service EventBUS 中断");
                break;
            default:
                break;
        }
    }

    /**
     * @des: 设置同步时间文字
     * @params:
     * @return:
     */
    private void setSyncText() {
        if (userPreferences.getSyncTime() == 0) {
            bookMenuLastSyncTime.setText(getString(R.string.has_not_sync));
        } else {
            String curDate = formatter.format(userPreferences.getSyncTime());
            bookMenuLastSyncTime.setText(String.format(getString(R.string.last_sync_time), curDate));
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.book_menu_advice_rl:
                Intent intent=new Intent(BookMenuActivity.this, UserFeedbackActivity.class);
                intent.putExtra("isQuestion",false);
                intent.putExtra("type",0);
                ActivityUtils.startActivity(intent);
                break;
            case R.id.book_menu_question_rl:
                Intent intent1=new Intent(BookMenuActivity.this, UserFeedbackActivity.class);
                intent1.putExtra("isQuestion",true);
                intent1.putExtra("type",0);
                ActivityUtils.startActivity(intent1);
                break;
            case R.id.user_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.book_menu_add_book_rl:
                ActivityUtils.startActivity(AddBookActivity.class);
                break;
            case R.id.book_menu_my_pdf_rl:
                ActivityUtils.startActivity(BookPdfActivity.class);
                break;
            case R.id.book_menu_delete_rl:
                showConfirmDeleteDialog();
                break;
            case R.id.sync_now_rl:
                if (!ServiceUtils.isServiceRunning(SyncService.class) && !isRightAnim.get()) {
                    if (NetworkUtils.isConnected()) {
                        if (userPreferences.getIsSyncOnlyWifi()) {
                            if (!NetworkUtils.isWifiConnected()) {
                                showNotInWifiDialog();
                            } else {
                                startSyncAnim();
                            }
                        } else {
                            startSyncAnim();
                        }
                    } else {
                        showMessage(R.string.cant_sync_no_network);
                    }

                } else {
                    logI("同步服务正在运行！不需要启动");
                }
                break;
            case R.id.book_menu_handwriting_rl:
                bookMenuHandwritingNewTv.setVisibility(View.GONE);
                firstPreferences.setIsFirstNoticeNew(false);
                ActivityUtils.startActivity(HandwritingCountActivity.class);
                break;
            case R.id.book_menu_language_rl:
                ActivityUtils.startActivity(SelectLanguageActivity.class);
            default:
                break;
        }
    }


    /**
     * @des: 手动同步不在wifi下提示
     * @params:
     * @return:
     */

    private void showNotInWifiDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(BookMenuActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.sync_in_4g_prompt));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {

                if (commonPopup != null) {
                    commonPopup.dismiss();
                }
                logI("启动服务");
                startSyncAnim();

            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        commonPopup = builder.create();
        commonPopup.show();
    }

    /**
     * @des: 获取手写识别次数
     * @params:[]
     * @return:void
     */
    private void getHandwritingCount() {
        httpManager.request().getHandwritingCount(
                userPreferences.getUserId(), HandwritingCountModel.class, new MiaoHttpManager.CallbackNetwork<HandwritingCountModel>() {
                    @Override
                    public void onNoNetwork() {
                        hideProgressDialog();
                        bookMenuHandwritingCountTv.setText(String.format(BookMenuActivity.this.getString(R.string.handwriting_count), userPreferences.getHandwritingCount()));
                    }

                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<HandwritingCountModel> entity) {
                        hideProgressDialog();
                        userPreferences.setHandwritingCount(entity.getBody().getOcrNum());
                        bookMenuHandwritingCountTv.setText(String.format(BookMenuActivity.this.getString(R.string.handwriting_count), entity.getBody().getOcrNum()));


                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<HandwritingCountModel> entity) {
                        hideProgressDialog();
                        if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            showMessage(R.string.toast_internal_error);
                        } else {
                            showMessage(R.string.request_failed_alert);
                        }

                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
    }

    /**
     * @des: 移除设备
     * @params:[equipName]
     * @return:void
     */
    private void removeEquipment(String equipName) {
        httpManager.request().removeEquipment(
                userPreferences.getUserId(), equipName, String.class, new MiaoHttpManager.Callback<String>() {
                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        deleteBooksAndPagesFromRealm();
                        showMessage(R.string.remove_success);

                        Intent intent = new Intent(BookMenuActivity.this, IndexActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                        ActivityUtils.startActivity(intent);
//                        deleteServerBooks();

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            showMessage(R.string.toast_internal_error);
                        } else {
                            showMessage(R.string.request_failed_alert);
                        }

                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
    }

    /**
     * @des: 数据库中数据全部设为delete
     * @params:
     * @return:
     */

    private void deleteBooksAndPagesFromRealm() {

        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realm) {
                realm.where(PageEntity.class).findAll().deleteAllFromRealm();
                realm.where(BookEntity.class).findAll().deleteAllFromRealm();
                realm.where(TagEntity.class).findAll().deleteAllFromRealm();
                realm.where(BookPdfEntity.class).findAll().deleteAllFromRealm();
            }
        });
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
//                CleanUtils.cleanCustomDir(Environment.getExternalStorageDirectory() + CZURConstants.SD_PATH + CZURConstants.PDF_PATH);
                CleanUtils.cleanCustomDir(CZURConstants.SD_PATH + CZURConstants.PDF_PATH);
                CleanUtils.cleanCustomDir(getFilesDir() + File.separator + userPreferences.getUserId() + CZURConstants.PAGE_PATH);
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });

        // 删除笔记本同步时间
        UserPreferences.getInstance(this).setSyncTime(0);
    }


    /**
     * @des: 请求用户信息
     * @params:
     * @return:
     */

    public void deleteServerBooks() {
        httpManager.request().deleteServerBooks(userPreferences.getUserId(), String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                deleteBooksAndPagesFromRealm();
                showMessage(R.string.remove_success);

                Intent intent = new Intent(BookMenuActivity.this, IndexActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                ActivityUtils.startActivity(intent);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                showMessage(R.string.request_failed_alert);

            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showMessage(R.string.request_failed_alert);

            }
        });
    }

    /**
     * @des:确认是否删除Books
     * @params:
     * @return:
     */

    private void showConfirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(BookMenuActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.remove_book_confirm));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {

                if (commonPopup != null) {
                    commonPopup.dismiss();
                }
                EventBus.getDefault().post(new RemoveBookEvent(EventType.REMOVE_BOOK));
                EventBus.getDefault().post(new StopServiceEvent(EventType.STOP_SYNC));
                EventBus.getDefault().post(new StopSyncTimeCountEvent(EventType.STOP_SYNC_TIME_COUNT));
                removeEquipment("笔记本");
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        commonPopup = builder.create();
        commonPopup.show();
    }


    private void startSyncAnim() {
        startSyncNowByHand();

    }

    private void stopSyncAnim() {
        hideSyncImgAnim.start();
    }

    private void startLoadingAnim() {
        syncNowImg.setAlpha(1.0f);
        syncNowImg.setVisibility(VISIBLE);
        syncAnim.start();
    }

    private void stopLoadingAnim() {
        syncNowImg.setVisibility(View.GONE);
        if (syncAnim.isRunning()) {
            syncAnim.cancel();
        }
    }

    /**
     * @des: 初始化动画
     * @params:
     * @return:
     */

    private void initAnim() {
        //进度圆圈旋转动画
        syncAnim = ObjectAnimator.ofFloat(syncNowImg, "rotation", 0, 720);
        syncAnim.setInterpolator(new LinearInterpolator());
        syncAnim.setRepeatCount(ObjectAnimator.INFINITE);
        syncAnim.setDuration(2000);


        //圆圈进度消失渐变动画
        hideSyncImgAnim = ObjectAnimator.ofFloat(syncNowImg, "alpha", 1.0f, 0);
        hideSyncImgAnim.setDuration(500);
        hideSyncImgAnim.setInterpolator(new LinearInterpolator());
        hideSyncImgAnim.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (isFinish && ActivityUtils.getTopActivity().equals(BookMenuActivity.this)) {
                    isRightAnim.set(true);
                    syncNowRight.setVisibility(VISIBLE);
                    if (syncAnim.isRunning()) {
                        syncAnim.cancel();
                    }
                    AnimationDrawable animationDrawable = (AnimationDrawable) syncNowRight.getDrawable();
                    animationDrawable.start();
                    handler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            isRightAnim.set(false);
                            syncNowRight.setVisibility(View.GONE);
                        }

                    }, 450);
                } else {
                    stopLoadingAnim();
                }

            }

            @Override
            public void onAnimationCancel(Animator animation) {
            }

            @Override
            public void onAnimationRepeat(Animator animation) {
            }
        });
    }

    private void startSyncNowByHand() {
        if (UserPreferences.getInstance(this).isUserLogin()) {
            if (!ServiceUtils.isServiceRunning(SyncService.class)) {
                if (UserPreferences.getInstance(this).getIsAutoSync()) {
                    if (UserPreferences.getInstance(this).getIsSyncOnlyWifi()) {
                        if (NetworkUtils.isConnected()) {
                            startSyncService();
                        }
                    } else {
                        startSyncService();
                    }

                } else {
                    startSyncService();
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
//        removeStickyEvent();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        realm.close();
    }
}
