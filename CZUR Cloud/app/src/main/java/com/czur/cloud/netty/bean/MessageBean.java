package com.czur.cloud.netty.bean;

public class MessageBean {

    /**
     * appid : com.czur.aura.home
     * os : android
     * udid : 0OKM-9IJN-8UHB-7YGV
     * userid : 1234567890
     * device : Aura
     * type : BIZ
     * body : {"action":"COMMAND","udid_from":"","userid_from":"","udid_to":"1QAZ-2WSX-3EDC-4RFV","data":"1100001"}
     */

    private String appid;
    private String os;
    private String udid;
    private String userid;
    private String device;
    private String type;
    private BodyBean body;

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BodyBean getBody() {
        return body;
    }

    public void setBody(BodyBean body) {
        this.body = body;
    }

    public static class BodyBean {
        /**
         * action : COMMAND
         * udid_from :
         * userid_from :
         * udid_to : 1QAZ-2WSX-3EDC-4RFV
         * data : 1100001
         */

        private String action;
        private String udid_from;
        private String userid_from;
        private String udid_to;
        private String data;

        public String getAction() {
            return action;
        }

        public void setAction(String action) {
            this.action = action;
        }

        public String getUdid_from() {
            return udid_from;
        }

        public void setUdid_from(String udid_from) {
            this.udid_from = udid_from;
        }

        public String getUserid_from() {
            return userid_from;
        }

        public void setUserid_from(String userid_from) {
            this.userid_from = userid_from;
        }

        public String getUdid_to() {
            return udid_to;
        }

        public void setUdid_to(String udid_to) {
            this.udid_to = udid_to;
        }

        public String getData() {
            return data;
        }

        public void setData(String data) {
            this.data = data;
        }
    }
}
