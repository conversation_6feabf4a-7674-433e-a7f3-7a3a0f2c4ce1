package com.czur.cloud.netty.core;

import static com.czur.czurutils.log.CZURLogUtilsKt.logD;
import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;

import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ServiceUtils;
import com.blankj.utilcode.util.Utils;
import com.czur.cloud.entity.realm.MessageEntity;
import com.czur.cloud.netty.Config;
import com.czur.cloud.netty.bean.CZMessage;
import com.czur.cloud.netty.observer.CheckTimeOutService;
import com.czur.cloud.netty.observer.NettyService;
import com.czur.cloud.netty.observer.NettyUtils;
import com.czur.cloud.preferences.UserPreferences;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelOption;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.timeout.IdleStateHandler;
import io.realm.Realm;
import io.realm.RealmResults;

/**
 * Created by pangk on 2018/8/23.
 */
public class CZURTcpClient {
    private Channel channel;
    private List<CZMessage> unSendMessageList;

    public AtomicBoolean getIsConnected() {
        return isConnected;
    }

    private AtomicBoolean isConnected;

    public AtomicBoolean getIsConnecting() {
        return isConnecting;
    }

    private AtomicBoolean isConnecting;
    private NioEventLoopGroup group;

    public void closeChannel() {
        if (channel != null) {
            channel.pipeline().remove(ClientMessageHandler.class);
//            channel.pipeline().remove(HeartbeatHandler.class);
            channel.pipeline().remove(HeartbeatNewHandler.class);
            channel.pipeline().remove(IdleStateHandler.class);
            channel.close();
            channel.eventLoop().shutdownGracefully();
            logE("App service主动断开socket");
            channel = null;
            group.shutdownGracefully();
        }
        isConnected.set(false);
        isConnecting.set(false);
    }

    private CZURTcpClient() {
        unSendMessageList = new ArrayList<>();
        isConnected = new AtomicBoolean(false);
        isConnecting = new AtomicBoolean(false);
    }

    private static class CZURTcpClientInstance {
        private static final CZURTcpClient INSTANCE = new CZURTcpClient();
    }

    public static CZURTcpClient getInstance() {
        return CZURTcpClientInstance.INSTANCE;
    }

    public synchronized void connect() {
        if (isConnecting.get()) {
            return;
        }
        Thread clientThread = new Thread("client-Netty") {
            @Override
            public void run() {
                super.run();
                isConnecting.set(true);
                if (group != null) {
                    group.shutdownGracefully();
                }
                group = new NioEventLoopGroup(8);
                Bootstrap bootstrap = new Bootstrap()
                        .group(group)
                        .option(ChannelOption.TCP_NODELAY, true)//屏蔽Nagle算法
                        .option(ChannelOption.SO_KEEPALIVE, true)
                        .channel(NioSocketChannel.class)
                        .handler(CZURClientInitializer.getInstance());
                try {
                    SharedPreferences sharedPreferences = Utils.getApp().getApplicationContext().getSharedPreferences("ip_port_sp", Context.MODE_PRIVATE);
                    String ip = sharedPreferences.getString("ip", Config.ADDRESS);
                    int port = sharedPreferences.getInt("port", Config.PORT);
                    bootstrap.connect(ip, port).addListener(new ChannelFutureListener() {
                        @Override
                        public void operationComplete(ChannelFuture future) throws Exception {
                            if (future.isSuccess()) {
                                //连接成功
                                channel = future.channel();
                                logD("socket连接成功");
                                isConnected.set(true);
                                isConnecting.set(false);
                                //重发10秒之内的所有未发消息
                                long currentTime = System.currentTimeMillis();
                                Iterator<CZMessage> it = unSendMessageList.iterator();
                                while (it.hasNext()) {
                                    CZMessage message = it.next();
                                    if (currentTime - message.getMessageEntity().getCreateTime() <= 10000) {
                                        validateMsgAndFormat(message);
                                        sleep(1000);
                                    } else {
                                        it.remove();
                                    }
                                }
                            } else {
                                //连接失败
                                logD("socket连接失败");
                                isConnected.set(false);
                                sleep(5000);
                                logD("5秒后重连");
                                isConnecting.set(false);
                                startNettyService();
                            }
                        }
                    }).sync();
                } catch (Exception e) {
                    e.printStackTrace();
                    group.shutdownGracefully();
                    isConnected.set(false);
                    isConnecting.set(false);
                    channel = null;
                }
            }
        };
        clientThread.start();
    }

    public void commonMessage(CZMessage czMessage) {
        validateMsgAndFormat(czMessage);
    }

    /**
     * 设备注册上线消息
     */
    public void registerAppOnline(Context context) {
        commonMessage(MessageFactory.registerAppOnlineMessage(context));
    }

    /**
     * 设备检查消息
     */
    public void deviceCheckIsOnline(Context context, String deviceUdid) {
        commonMessage(MessageFactory.checkIsOnlineMessage(context, deviceUdid));
    }

    /**
     * 智能省电开关
     */
    public void smartPowerSwitch(Context context, String deviceUdid, String smartSwitch, String currentSwitch) {
        commonMessage(MessageFactory.smartPowerSwitch(context, deviceUdid, smartSwitch, currentSwitch));
    }

    /**
     * 久坐提醒开关
     */
    public void sedentaryReminderSwitch(Context context, String deviceUdid, String sedentarySwitch, String currentSwitch) {
        commonMessage(MessageFactory.sedentaryReminderSwitch(context, deviceUdid, sedentarySwitch, currentSwitch));
    }

    /**
     * 久坐提醒时间间隔设置
     */
    public void sedentaryReminderDuration(Context context, String deviceUdid, String sedentaryDuration, String currentDuration) {
        commonMessage(MessageFactory.sedentaryReminderDuration(context, deviceUdid, sedentaryDuration, currentDuration));
    }


    /**
     * 开灯
     */
    public void lightSwitch(Context context, String deviceUdid, String lightSwitch, String currentSwitch) {
        commonMessage(MessageFactory.lightSwitchMessage(context, deviceUdid, lightSwitch, currentSwitch));
    }


    /**
     * 改变亮度等级
     */
    public void lightLevel(Context context, String deviceUdid, String lightLevel, String currentLevel) {
        CZMessage czMessage = MessageFactory.lightLevelMessage(context, deviceUdid, lightLevel, currentLevel);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 改变灯模式
     */
    public void lightMode(Context context, String deviceUdid, String lightMode, String curLightMode) {
        CZMessage czMessage = MessageFactory.lightModeMessage(context, deviceUdid, lightMode, curLightMode);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 改变音量
     */
    public void volumeLevel(Context context, String deviceUdid, String volumeLevel, String curLevel) {
        CZMessage czMessage = MessageFactory.volumeLevelMessage(context, deviceUdid, volumeLevel, curLevel);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 设置提醒灵敏度
     */
    public void sittingPositionSensitivity(Context context, String deviceUdid, String sensitivityLevel, String curLevel) {
        CZMessage czMessage = MessageFactory.positionSensitivityMessage(context, deviceUdid, sensitivityLevel, curLevel);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 设置提醒灵敏度开关
     */
    public void sittingPositionSensitivitySwitch(Context context, String deviceUdid, String sensitivitySwitch, String curSwitch) {
        CZMessage czMessage = MessageFactory.positionSensitivitySwitchMessage(context, deviceUdid, sensitivitySwitch, curSwitch);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 设置错误坐姿开关
     */
    public void sittingWrongSitSwitch(Context context, String deviceUdid, String sensitivitySwitch, String curSwitch) {
        CZMessage czMessage = MessageFactory.wrongSitSwitchMessage(context, deviceUdid, sensitivitySwitch, curSwitch);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 视频请求
     */
    public void videoRequest(Context context, String deviceUdid) {
        CZMessage czMessage = MessageFactory.videoRequestMessage(context, deviceUdid);
        validateMsgAndFormat(czMessage);
    }

    /**
     * APP告诉设备是否进行视频
     */
    public void videoRequestSecond(Context context, String deviceUdid, int extra, String userIdTo, String call_id) {
        CZMessage czMessage = MessageFactory.videoRequestSecondMessage(context, deviceUdid, extra, userIdTo, call_id);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 手机准备好去视频
     */
    public void appReadyForVideo(Context context, String deviceUdid, String operate) {
        CZMessage czMessage = MessageFactory.appReadyForVideo(context, deviceUdid, operate);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 手机端检查设备端是否准备好
     */
    public void deviceReadyForVideo(Context context, String deviceUdid, String channel) {
        CZMessage czMessage = MessageFactory.deviceReadyForVideo(context, deviceUdid, channel);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 解绑AuraMate
     */
    public void unbindDevice(Context context, String deviceUdid) {
        CZMessage czMessage = MessageFactory.unbindDevice(context, deviceUdid);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 解绑AuraMate
     */
    public void transferDevice(Context context, String deviceUdid) {
        CZMessage czMessage = MessageFactory.transferDevice(context, deviceUdid);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 设备端和手机端关系发生改变
     */
    public void noticeRelationShipChange(Context context, String deviceUdid) {
        CZMessage czMessage = MessageFactory.noticeRelationShipChange(context, deviceUdid);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 进入坐姿校准(视频)
     */
    public void sittingPositionCalibrate(Context context, String deviceUdid, String channel) {
        CZMessage czMessage = MessageFactory.sittingPositionCalibrateMessage(context, deviceUdid, channel);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 坐姿校准拍照
     */
    public void sittingPositionPhoto(Context context, String deviceUdid) {
        CZMessage czMessage = MessageFactory.sittingPositionPhotoMessage(context, deviceUdid);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 切换摄像头
     */
    public void switchCamera(Context context, String deviceUdid) {
        CZMessage czMessage = MessageFactory.videoSwitchCameraMessage(context, deviceUdid);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 切换语言
     */
    public void changeLanguage(Context context, String deviceUdid, String language) {
        CZMessage czMessage = MessageFactory.changeDeviceLanguage(context, deviceUdid, language);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 升级
     */
    public void updateFw(Context context, String deviceUdid) {
        CZMessage czMessage = MessageFactory.updateFw(context, deviceUdid);
        validateMsgAndFormat(czMessage);
    }

    public void needCheckOTAUpdate(Context context, int messageApiVersion, String deviceUdid) {
        CZMessage czMessage = MessageFactory.needCheckOTAUpdate(context, messageApiVersion, deviceUdid);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 手机端检查设备端发起的视频是否有效
     */

    public void checkVideoRequestActive(Context context, String callId, String deviceUdid) {
        CZMessage czMessage = MessageFactory.checkVideoRequestActive(context, callId, deviceUdid);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 取消
     */
    public void videoRequestCancel(Context context, String deviceUdid) {
        CZMessage czMessage = MessageFactory.videoRequestCancel(context, deviceUdid);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 取消
     */
    public void videoCancel(Context context, String deviceUdid, String channel) {
        CZMessage czMessage = MessageFactory.videoCancel(context, deviceUdid, channel);
        validateMsgAndFormat(czMessage);
    }


    /**
     * 高清查看
     */
    public void hdView(Context context, String deviceUdid) {
        CZMessage czMessage = MessageFactory.hdImage(context, deviceUdid);
        validateMsgAndFormat(czMessage);
    }

    public void hdVideo(Context context, String deviceUdid, String isHD) {
        CZMessage czMessage = MessageFactory.hdVideo(context, deviceUdid, isHD);
        validateMsgAndFormat(czMessage);
    }

    public void stopRecordVideo(Context context, String deviceUdid, String channelID) {
        CZMessage czMessage = MessageFactory.stopRecordVideo(context, deviceUdid,channelID);
        validateMsgAndFormat(czMessage);
    }

    public void startRecordVideo(Context context, String deviceUdid, String channelID) {
        CZMessage czMessage = MessageFactory.startRecordVideo(context, deviceUdid,channelID);
        validateMsgAndFormat(czMessage);
    }


    /**
     * 高清查看保存
     */
    public void hdViewSave(Context context, String deviceUdid) {
        CZMessage czMessage = MessageFactory.hdViewSave(context, deviceUdid);
        validateMsgAndFormat(czMessage);
    }

    /**
     * 高清相册收藏
     */
    public void hdViewSaveV2(Context context, String deviceUdid, String osskey, String ossbucket) {
        CZMessage czMessage = MessageFactory.hdViewSaveV2(context, deviceUdid, osskey, ossbucket);

        validateMsgAndFormat(czMessage);
    }

    /**
     * 验证消息并且格式化
     *
     * @param
     * @return
     */
    private void validateMsgAndFormat(final CZMessage czMessage) {
        if (channel != null && channel.isActive() && isConnected.get()) {
            if (MessageFactory.validateMessageInfo(czMessage)) {
                channel.writeAndFlush(CZMessage.formatJsonResponse(czMessage)).addListener(new ChannelFutureListener() {
                    @Override
                    public void operationComplete(ChannelFuture future) throws Exception {
                        if (future.isSuccess()) {
                            unSendMessageList.remove(czMessage);
                            //发送成功才启动超时检验
                            if (!ServiceUtils.isServiceRunning(CheckTimeOutService.TAG)) {
                                Context context = Utils.getApp().getApplicationContext();
                                Intent intent = new Intent(context, CheckTimeOutService.class);
                                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                                    context.startForegroundService(intent);
                                } else {
                                    context.startService(intent);
                                }
                            }
                            insertMessageToDb(czMessage.getMessageEntity());
                        } else {
                            unSendMessageList.add(czMessage);
                        }
                    }
                });
            }
        } else {
            if (MessageFactory.validateMessageInfo(czMessage)) {
                unSendMessageList.add(czMessage);
            }
            if (!isConnecting.get() && !isConnected.get()) {
                startNettyService();
            }
            startNettyService();
        }
    }

    private void insertMessageToDb(MessageEntity messageEntity) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try (Realm realm = Realm.getDefaultInstance()) {
                    long msgTime = System.currentTimeMillis();
                    messageEntity.setCreateTime(msgTime);
                    realm.executeTransaction(new Realm.Transaction() {
                        @Override
                        public void execute(Realm realm) {
                            realm.copyToRealm(messageEntity);
                        }
                    });
                    RealmResults<MessageEntity> messageEntities = realm.where(MessageEntity.class)
                            .notEqualTo("status", 4)
                            .equalTo("type", 0)
                            .lessThan("createTime", msgTime)
                            .equalTo("deviceUDID", messageEntity.getDeviceUDID())
                            .equalTo("name", messageEntity.getName())
                            .findAll();

                    for (MessageEntity entity : messageEntities) {
                        realm.executeTransaction(new Realm.Transaction() {
                            @Override
                            public void execute(Realm realm) {
                                if (entity.isValid()) {
                                    entity.setStatus(4);
                                }
                            }
                        });
                    }
                }
            }
        }).start();

    }

    private void startNettyService() {
        if (ServiceUtils.isServiceRunning(NettyService.class)) {
            return;
        }
        Context context = Utils.getApp().getApplicationContext();
        UserPreferences instance = UserPreferences.getInstance(context);
        if (NetworkUtils.isConnected() && instance.isUserLogin() && instance.isHasAuraMate()) {
            if (ServiceUtils.isServiceRunning(NettyService.class)) {
                return;
            }
            logI("CZURTcpClient.startNettyService.NettyService.class");
            NettyUtils.getInstance().startNettyService();
        }
    }

    // Starry Message

    /**
     * starry CMD
     */
    public void starryMeetingCMD(CZMessage czMessage) {
        validateMsgAndFormat(czMessage);
    }

}
