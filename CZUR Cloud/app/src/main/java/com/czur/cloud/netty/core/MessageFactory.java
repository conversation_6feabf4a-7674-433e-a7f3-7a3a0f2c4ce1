package com.czur.cloud.netty.core;

import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.content.Context;
import android.util.Base64;
import android.util.Log;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.AppUtils;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.entity.realm.MessageEntity;
import com.czur.cloud.netty.CZURMessageConstants;
import com.czur.cloud.netty.Config;
import com.czur.cloud.netty.bean.CZMessage;
import com.czur.cloud.netty.bean.MessageType;
import com.czur.cloud.netty.messageData.CZURActionBody;
import com.czur.cloud.netty.messageData.DeviceMessageData;
import com.czur.cloud.netty.messageData.VideoMessageData;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.util.validator.Validator;

import java.util.Date;
import java.util.UUID;


public class MessageFactory {

    public static final String TAG = "nnnXXX";

    /**
     * 生成心跳包
     *
     * @return
     */
    public static CZMessage heartbeatMessage() {
        CZMessage message = new CZMessage();
        message.setType(MessageType.HEARTBEAT.getType());
        message.setBody(String.valueOf(new Date().getTime()));
        return message;
    }

    /**
     * 设备注册上线信息
     *
     * @return
     */
    public static CZMessage registerAppOnlineMessage(Context context) {
        CZMessage message = getBizCzMessage(context, "", MessageType.ONLINE.getType(), UUID.randomUUID().toString(), "online", "");
        Log.e(TAG, "register app online");
        return message;
    }

    /**
     * 检查设备端是否在线(发）
     *
     * @return
     */
    public static CZMessage checkIsOnlineMessage(Context context, String deviceUdid) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_CHECK_DEVICE_IS_ONLINE, ""), "");
    }


    /**
     * 智能省电开关
     *
     * @return
     */
    public static CZMessage smartPowerSwitch(Context context, String deviceUdid, String smartSwitch, String currentSwitch) {
        return sendNettyMessage(context, deviceUdid, -1, buildMessageData(CZURMessageConstants.MessageName.MESSAGE_SMART_POWER_SAVING, smartSwitch), currentSwitch);
    }

    /**
     * 久坐提醒开关
     *
     * @return
     */
    public static CZMessage sedentaryReminderSwitch(Context context, String deviceUdid, String sedentarySwitch, String currentSwitch) {
        return sendNettyMessage(context, deviceUdid, -1, buildMessageData(CZURMessageConstants.MessageName.MESSAGE_SEDENRARY_REMINDER_SWITCH, sedentarySwitch), currentSwitch);
    }

    /**
     * 久坐提醒时间间隔
     *
     * @return
     */
    public static CZMessage sedentaryReminderDuration(Context context, String deviceUdid, String sedentaryDuration, String currentDuration) {
        return sendNettyMessage(context, deviceUdid, -1, buildMessageData(CZURMessageConstants.MessageName.MESSAGE_SEDENRARY_REMINDER_DURATION, sedentaryDuration), currentDuration);
    }

    /**
     * 开灯(发）
     *
     * @return
     */
    public static CZMessage lightSwitchMessage(Context context, String deviceUdid, String lightSwitch, String currentSwitch) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_LIGHT_SWITCH, lightSwitch), currentSwitch);
    }


    /**
     * 设置灯光亮度等级
     *
     * @return
     */
    public static CZMessage lightLevelMessage(Context context, String deviceUdid, String lightLevel, String curLightLevel) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_LIGHT_LEVEL, lightLevel), curLightLevel);

    }

    /**
     * 设置灯光亮度模式
     *
     * @return
     */
    public static CZMessage lightModeMessage(Context context, String deviceUdid, String lightMode, String curLightMode) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_LIGHT_MODE, lightMode), curLightMode);
    }

    /**
     * 设置音量等级
     *
     * @return
     */
    public static CZMessage volumeLevelMessage(Context context, String deviceUdid, String volumeLevel, String curVolumeLevel) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_VOLUME_LEVEL, volumeLevel), curVolumeLevel);
    }

    /**
     * 设置提醒灵敏度
     *
     * @return
     */
    public static CZMessage positionSensitivityMessage(Context context, String deviceUdid, String sensitivityLevel, String curLevel) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_SITTING_POSITION_SENSITIVITY_LEVEL, sensitivityLevel), curLevel);

    }

    /**
     * 设置坐姿提醒开关
     *
     * @return
     */
    public static CZMessage positionSensitivitySwitchMessage(Context context, String deviceUdid, String positionSwitch, String curSwitch) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_SITTING_POSITION_SENSITIVITY_SWITCH, positionSwitch), curSwitch);
    }

    /**
     * 设置错误坐姿开关
     *
     * @return
     */
    public static CZMessage wrongSitSwitchMessage(Context context, String deviceUdid, String positionSwitch, String curSwitch) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_SITTING_WRONG_SIT_SWITCH, positionSwitch), curSwitch);
    }

    /**
     * 视频请求（发）
     *
     * @return
     */
    public static CZMessage videoRequestMessage(Context context, String deviceUdid) {
        return sendVideoNettyMessage(context, deviceUdid, CZURMessageConstants.Video.APPID_TO.getValue(), null);
    }

    /**
     * 手机端接到视频请求后状态回执
     *
     * @return
     */
    public static CZMessage videoRequestSecondMessage(Context context, String deviceUdid, int extra, String userIdTo, String call_id) {
        return sendVideoNettyMessage(context, deviceUdid, "",
                buildVideoMessageData(extra, userIdTo, call_id));
    }

    /**
     * APP准备好了
     *
     * @return
     */
    public static CZMessage appReadyForVideo(Context context, String deviceUdid, String operate) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_APP_READY_FOR_VIDEO, operate), "");
    }

    /**
     * 设备是否准备好
     *
     * @return
     */
    public static CZMessage deviceReadyForVideo(Context context, String deviceUdid, String channel) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_DEVICE_READY_FOR_VIDEO, channel), "");
    }

    /**
     * 解绑
     *
     * @return
     */

    public static CZMessage unbindDevice(Context context, String deviceUdid) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_UNBIND_DEVICE, ""), "");
    }

    /**
     * 转让
     *
     * @return
     */
    public static CZMessage transferDevice(Context context, String deviceUdid) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_TRANSFER_DEVICE, ""), "");
    }

    /**
     * 群发状态变更
     *
     * @return
     */
    public static CZMessage noticeRelationShipChange(Context context, String deviceUdid) {
        return sendNettyMessage(context, deviceUdid, 1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_DEVICE_RELATION_CHANGE, ""), "");
    }

    /**
     * 坐姿校准
     *
     * @return
     */
    public static CZMessage sittingPositionCalibrateMessage(Context context, String deviceUdid, String channel) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_SITTING_POSITION_CALIBRATE, channel), "");

    }


    /**
     * 坐姿校准
     *
     * @return
     */
    public static CZMessage sittingPositionPhotoMessage(Context context, String deviceUdid) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_SITTING_POSITION_PHOTO, ""), "");

    }


    /**
     * 切换摄像头
     *
     * @return
     */
    public static CZMessage videoSwitchCameraMessage(Context context, String deviceUdid) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_VIDEO_SWITCH_CAMERA, ""), "");
    }

    /**
     * 切换摄像头
     *
     * @return
     */
    public static CZMessage changeDeviceLanguage(Context context, String deviceUdid, String language) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_CHANGE_LANGUAGE, language), language);
    }


    /**
     * 切换摄像头
     *
     * @return
     */
    public static CZMessage updateFw(Context context, String deviceUdid) {
        return sendNettyMessage(context, deviceUdid, -1, buildMessageData(CZURMessageConstants.MessageName.MESSAGE_UPDATE_FW, ""), "");
    }


    public static CZMessage needCheckOTAUpdate(Context context, int messageApiVersion, String deviceUdid) {
        return sendNettyMessage(context, deviceUdid, -1, buildMessageData(CZURMessageConstants.MessageName.MESSAGE_NEED_CHECK_OTA_UPDATE, messageApiVersion + ""), "");
    }


    public static CZMessage checkVideoRequestActive(Context context, String callID, String deviceUdid) {
        return sendNettyMessage(context, deviceUdid, -1, buildMessageData(CZURMessageConstants.MessageName.MESSAGE_CHECK_VIDEO_REQUEST_ACTIVE, callID), "");
    }


    public static CZMessage videoRequestCancel(Context context, String deviceUdid) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_VIDEO_REQUEST_CANCEL, ""), "");
    }

    public static CZMessage videoCancel(Context context, String deviceUdid, String channel) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_VIDEO_CANCEL, channel), "");
    }

    public static CZMessage hdImage(Context context, String deviceUdid) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_HD_VIEW, ""), "");
    }

    public static CZMessage hdVideo(Context context, String deviceUdid, String isHD) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_HD_VIDEO, isHD), "");
    }

    public static CZMessage startRecordVideo(Context context, String deviceUdid, String channelID) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.START_RECORD, channelID), "");
    }

    public static CZMessage stopRecordVideo(Context context, String deviceUdid, String channelID) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.STOP_RECORD, channelID), "");
    }

    public static CZMessage hdViewSave(Context context, String deviceUdid) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageData(CZURMessageConstants.MessageName.MESSAGE_HD_VIEW_SAVE, ""), "");
    }

    public static CZMessage hdViewSaveV2(Context context, String deviceUdid, String osskey, String ossbucket) {
        return sendNettyMessage(context, deviceUdid, -1,
                buildMessageDataV2(CZURMessageConstants.MessageName.MESSAGE_HD_VIEW_SAVE_V2, osskey, ossbucket), "");
    }

    public static CZMessage sendNettyMessage(Context context, String deviceUdid, int groupPush, DeviceMessageData deviceMessageData, String dataBegin) {
        String uuid = UUID.randomUUID().toString();
        deviceMessageData.setUuid(uuid);
        CZMessage message = getBizCzMessage(context, deviceUdid, MessageType.BUSINESS.getType(), uuid, deviceMessageData.getMessage_name(), dataBegin);
        CZURActionBody czurActionBody = getCzurActionBody(deviceUdid, groupPush);
        czurActionBody.setData(deviceMessageData);
        message.setBody(czurActionBody);
        logI("MessageFactory.sendNettyMessage.deviceMessageData=" + deviceMessageData.toString());
        logI("MessageFactory.sendNettyMessage.message=" + message.toString());
        return message;
    }

    public static CZMessage sendVideoNettyMessage(Context context, String deviceUdid, String appidTo, VideoMessageData videoMessageData) {
        String uuid = UUID.randomUUID().toString();
        if (Validator.isNotEmpty(videoMessageData)) {
            videoMessageData.setUuid(uuid);
        }
        CZMessage message = getBizCzMessage(context, "", MessageType.BUSINESS.getType(), uuid, "video", "");
        if (Validator.isNotEmpty(videoMessageData)) {
            message.setBody(videoMessageData);
        } else {
            CZURActionBody czurActionBody = getVideoActionBody(deviceUdid, appidTo);
            message.setBody(czurActionBody);
        }
        logI("MessageFactory.sendVideoNettyMessage.message=" + message.toString());
        return message;
    }

    private static boolean checkNeedReceipt(String name) {
        boolean needReceipt = true;
        if (name.equals(CZURMessageConstants.MessageName.MESSAGE_TRANSFER_DEVICE.getMsg())) {
            needReceipt = false;
        } else if (name.equals(CZURMessageConstants.MessageName.MESSAGE_UNBIND_DEVICE.getMsg())) {
            needReceipt = false;
        }
        return needReceipt;
    }

    /**
     * 发送BIZ消息
     *
     * @param
     * @return
     */
    @NonNull
    private static CZMessage getBizCzMessage(Context context, String deviceUdid, String messageType, String uuid, String name, String dataBegin) {
        String requestId = UUID.randomUUID().toString();
        MessageEntity messageEntity = new MessageEntity();
        messageEntity.setRequestId(requestId);
        messageEntity.setUuid(uuid);
        messageEntity.setDeviceUDID(deviceUdid);
        messageEntity.setName(name);
        messageEntity.setNeedReceipt(checkNeedReceipt(name));
        messageEntity.setType(0);
        messageEntity.setStatus(0);
        if (Validator.isNotEmpty(dataBegin)) {
            messageEntity.setDataBegin(dataBegin);
        }
        CZMessage message = new CZMessage();
        message.setMessageEntity(messageEntity);
        message.setType(messageType);
        // message.setAppid("com.czur.cloud");
        message.setAppid(CZURConstants.PACKAGE_NAME);
        message.setOs(MessageType.OS.getType());
        message.setUdid(UserPreferences.getInstance(context).getUdid());
        message.setUserid(UserPreferences.getInstance(context).getUserId());
        message.setDevice(MessageType.DEVICE.getType());
        message.setRequestid(requestId);
        String text = Config.appid + ":" + Config.udid + ":" + requestId;
        String token = Base64.encodeToString(text.getBytes(), Base64.NO_WRAP);
        message.setToken(token);
        return message;
    }

    /**
     * actionBody 消息封装
     *
     * @param
     * @return
     */
    @NonNull
    private static CZURActionBody getCzurActionBody(String deviceUdid, int groupPush) {
        CZURActionBody czurActionBody = new CZURActionBody();
        czurActionBody.setAction(MessageType.COMMAND.getType());
        czurActionBody.setUdid_to(deviceUdid);
        if (groupPush != -1) {
            czurActionBody.setGroup_push(groupPush);
        }
        return czurActionBody;
    }

    /**
     * actionBody 消息封装
     *
     * @param
     * @return
     */
    @NonNull
    private static CZURActionBody getVideoActionBody(String deviceUdid, String appidTo) {
        CZURActionBody czurActionBody = new CZURActionBody();
        czurActionBody.setAction(MessageType.CALL_VIDEO.getType());
        czurActionBody.setUdid_to(deviceUdid);
        if (Validator.isNotEmpty(appidTo)) {
            czurActionBody.setAppid_to(appidTo);
        }
        return czurActionBody;
    }

    /**
     * 检查设备是否在线(发)
     *
     * @param
     * @return
     */
    @NonNull
    private static DeviceMessageData buildMessageDataV2(CZURMessageConstants.MessageName messageName, String operate1, String operate2) {
        DeviceMessageData deviceMessageData = new DeviceMessageData();
        deviceMessageData.setMessage_name(messageName.getMsg());
        deviceMessageData.setFunction(MessageType.AURA_MATE.getType());
        if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_HD_VIEW_SAVE_V2)) {
            deviceMessageData.setOssKey(operate1);
            deviceMessageData.setBucketName(operate2);
        }
        return deviceMessageData;
    }

    @NonNull
    private static DeviceMessageData buildMessageData(CZURMessageConstants.MessageName messageName, String operate) {
        DeviceMessageData deviceMessageData = new DeviceMessageData();
        deviceMessageData.setMessage_name(messageName.getMsg());
        deviceMessageData.setFunction(MessageType.AURA_MATE.getType());
        if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_LIGHT_LEVEL)) {
            deviceMessageData.setLight_Level(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_LIGHT_SWITCH)) {
            deviceMessageData.setLight_switch(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_LIGHT_MODE)) {
            deviceMessageData.setLight_mode(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_VOLUME_LEVEL)) {
            deviceMessageData.setSp_reminder_sensitivity_volume(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_APP_READY_FOR_VIDEO)) {
            deviceMessageData.setIs_ready_for_video(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_SITTING_POSITION_SENSITIVITY_LEVEL)) {
            deviceMessageData.setSp_reminder_sensitivity_level(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_SITTING_POSITION_SENSITIVITY_SWITCH)) {
            deviceMessageData.setSp_reminder_switch(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_SITTING_WRONG_SIT_SWITCH)) {
            deviceMessageData.setSp_wrong_sit_switch(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_VIDEO_CANCEL)) {
            deviceMessageData.setVideo_chat_room_no(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_DEVICE_READY_FOR_VIDEO)) {
            deviceMessageData.setVideo_chat_room_no(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_SITTING_POSITION_CALIBRATE)) {
            deviceMessageData.setVideo_chat_room_no(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_CHANGE_LANGUAGE)) {
            deviceMessageData.setSystem_language(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_SMART_POWER_SAVING)) {
            deviceMessageData.setSmart_power_saving_switch(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_SEDENRARY_REMINDER_SWITCH)) {
            deviceMessageData.setSedentary_reminder_switch(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_SEDENRARY_REMINDER_DURATION)) {
            deviceMessageData.setSedentary_reminder_duration(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_NEED_CHECK_OTA_UPDATE)) {
            deviceMessageData.setMessage_api_version(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_CHECK_VIDEO_REQUEST_ACTIVE)) {
            deviceMessageData.setCall_id(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_HD_VIEW_SAVE_V2)) {
            deviceMessageData.setCall_id(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.MESSAGE_HD_VIDEO)) {
            deviceMessageData.setIsHD(operate);
        } else if (messageName.equals(CZURMessageConstants.MessageName.START_RECORD)
                || messageName.equals(CZURMessageConstants.MessageName.STOP_RECORD)) {
            deviceMessageData.setVideo_chat_room_no(operate);
        }
        return deviceMessageData;
    }

    private static VideoMessageData buildVideoMessageData(int extra, String userIdTo, String call_id) {
        VideoMessageData videoMessageData = new VideoMessageData();
        videoMessageData.setAction(CZURMessageConstants.Video.ACTION.getValue());
        videoMessageData.setMethod(CZURMessageConstants.Video.METHOD.getValue());
        videoMessageData.setExtra(extra);
        if (Validator.isNotEmpty(userIdTo)) {
            videoMessageData.setUserid_to(userIdTo);
            videoMessageData.setAppid_to(AppUtils.getAppPackageName());
        }
        videoMessageData.setCall_id(call_id);
        return videoMessageData;
    }


    /**
     * 校验必要性信息
     *
     * @param message
     * @return
     */
    public static boolean validateMessageInfo(CZMessage message) {
        boolean valid = true;
        if (message == null) {
            valid = false;
        } else {
            if (message.getAppid() == null || "".equals(message.getAppid())) {
                valid = false;
                System.out.println("Message app id is required!");
            }
            if (message.getOs() == null || "".equals(message.getOs())) {
                valid = false;
                System.out.println("Message os is required!");
            }
            if (message.getUdid() == null || "".equals(message.getUdid())) {
                valid = false;
                System.out.println("Message udid is required!");
            }
        }
        if (valid == false) {
            System.out.println("Message is not valid");
        }
        return valid;
    }
}

