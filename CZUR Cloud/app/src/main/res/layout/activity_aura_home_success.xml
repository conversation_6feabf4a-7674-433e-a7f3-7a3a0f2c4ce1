<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/wifi_connect_success_top_bar"
        layout="@layout/layout_normal_white_top_bar" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="194.5dp"
        android:orientation="vertical">

        <ImageView
            android:layout_width="91dp"
            android:layout_height="60dp"
            android:layout_gravity="center_horizontal"
            android:background="@mipmap/aura_home_big_right" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/add_success_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="25dp"
            android:gravity="center"
            android:text="@string/aura_home_success_text"
            android:textColor="@color/identifying_code"
            android:textSize="14sp" />
    </LinearLayout>


    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/aura_home_success_btn"
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="30dp"
        android:background="@drawable/btn_rec_5_bg_with_code_blue"
        android:gravity="center"
        android:text="@string/finish"
        android:textColor="@color/white" />

    <RelativeLayout
        android:id="@+id/wifi_success_loading_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/wifi_connect_success_top_bar"
        android:background="@color/white">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/wifi_success_loading_img"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:src="@mipmap/aura_home_loading" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="25dp"
                android:text="@string/aura_home_connect_confirm1"
                android:textColor="@color/black_22"
                android:textSize="14sp" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:text="@string/aura_home_connect_confirm2"
                android:textColor="@color/black_22"
                android:textSize="14sp" />

        </LinearLayout>


    </RelativeLayout>
</RelativeLayout>