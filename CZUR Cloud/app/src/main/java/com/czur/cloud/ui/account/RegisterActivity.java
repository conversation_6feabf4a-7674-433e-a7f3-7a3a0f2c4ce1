package com.czur.cloud.ui.account;

import static com.czur.cloud.common.CZURConstants.PWD_MIN_LENGTH;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.Layout;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextPaint;
import android.text.TextWatcher;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.CleanUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.RegexUtils;
import com.blankj.utilcode.util.StringUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.Utils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.common.MD5Utils;
import com.czur.cloud.entity.realm.BookEntity;
import com.czur.cloud.entity.realm.BookPdfEntity;
import com.czur.cloud.entity.realm.DownloadEntity;
import com.czur.cloud.entity.realm.HomeCacheEntity;
import com.czur.cloud.entity.realm.OcrEntity;
import com.czur.cloud.entity.realm.PageEntity;
import com.czur.cloud.entity.realm.PdfDownloadEntity;
import com.czur.cloud.entity.realm.PdfEntity;
import com.czur.cloud.entity.realm.SPReportEntity;
import com.czur.cloud.entity.realm.SPReportEntitySub;
import com.czur.cloud.entity.realm.SPReportSittingEntity;
import com.czur.cloud.entity.realm.SyncBookEntity;
import com.czur.cloud.entity.realm.SyncPageEntity;
import com.czur.cloud.entity.realm.SyncPdfEntity;
import com.czur.cloud.entity.realm.SyncTagEntity;
import com.czur.cloud.entity.realm.TagEntity;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.LoginEvent;
import com.czur.cloud.model.ChannelModel;
import com.czur.cloud.model.CountryCode;
import com.czur.cloud.model.CountryList;
import com.czur.cloud.model.RegisterModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.FirstPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.NoHintEditText;
import com.czur.cloud.ui.component.ProgressButton;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.home.IndexActivity;
import com.czur.cloud.ui.market.WebViewActivity;
import com.czur.cloud.ui.starry.utils.Tools;
import com.czur.cloud.util.AppClearUtils;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.StringToolsUtils;
import com.czur.cloud.util.validator.StringUtilsKt;
import com.czur.cloud.util.validator.Validator;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Locale;

import io.realm.Realm;
import kotlin.Unit;
import kotlin.jvm.functions.Function2;
import kotlin.jvm.internal.Intrinsics;

/**
 * Created by Yz on 2018/3/7.
 * Email：<EMAIL>
 */

public class RegisterActivity extends BaseActivity implements View.OnClickListener {
    private static final String USER_PRIVACY_USER = "userPrivacyUser";
    private static final String USER_PRIVACY_PRIVACY = "userPrivacyPrivacy";
    private RelativeLayout choseCountryRl;
    private TextView chose_country_title;
    private TextView chosenCountryTv;
    private ImageView accountBackBtn;
    private TextView accountTitle;
    private NoHintEditText registerAccountEdt;
    private NoHintEditText registerPasswordEdt;
    private NoHintEditText registerIdentifyingCodeEdt;
    private TextView getCodeBtn;
    private ProgressButton registerBtn;
    private TimeCount timeCount;

    private ImageView user_privacy_img;
    private TextView user_privacy_tv2, user_terms_url_tv, user_privacy_tv1;
    private boolean isUserPrivacyChecked = false;
    private boolean isMobileRegister = false;
    private boolean isMaliRegister = false;
    private boolean codeHasContent = false;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private String channel;
    private FirstPreferences firstPreferences;
    private Realm realm;
    private String userId;
    private CloudCommonPopup commonPopup;
    private long currentTime;
    private boolean isMobile;
    private MiaoHttpEntity<RegisterModel> registerEntity;
    private String email;
    private String mailPwd;
    private String phone;
    private String mobilePwd;
    private ArrayList<CountryCode> countryList;

    private CountryCode selectCountryCode = new CountryCode();

//    private LinearLayout user_privacy_ll2;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_register);
        initComponent();
        registerEvent();

        String userPrivacyExplain = getString(R.string.user_privacy_register);
        String userPrivacyUser = getString(R.string.user_privacy_user);
        String userPrivacyPrivacy = getString(R.string.user_privacy_privacy);
        SpannableStringBuilder spannableBuilder = new SpannableStringBuilder((CharSequence) userPrivacyExplain);
        setTextViewColorAndClick(USER_PRIVACY_USER, spannableBuilder, userPrivacyExplain.indexOf(userPrivacyUser), userPrivacyExplain.indexOf(userPrivacyUser) + userPrivacyUser.length());
        setTextViewColorAndClick(USER_PRIVACY_PRIVACY, spannableBuilder, userPrivacyExplain.indexOf(userPrivacyPrivacy), userPrivacyExplain.indexOf(userPrivacyPrivacy) + userPrivacyPrivacy.length());
        user_privacy_tv1.setText((CharSequence) spannableBuilder);


        user_privacy_tv1.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                boolean ret = false;
                CharSequence text = ((TextView) v).getText();
                Spannable stext = Spannable.Factory.getInstance().newSpannable(text);
                TextView widget = (TextView) v;
                int action = event.getAction();

                if (action == MotionEvent.ACTION_UP ||
                        action == MotionEvent.ACTION_DOWN) {
                    int x = (int) event.getX();
                    int y = (int) event.getY();

                    x -= widget.getTotalPaddingLeft();
                    y -= widget.getTotalPaddingTop();

                    x += widget.getScrollX();
                    y += widget.getScrollY();

                    Layout layout = widget.getLayout();
                    int line = layout.getLineForVertical(y);
                    int off = layout.getOffsetForHorizontal(line, x);

                    ClickableSpan[] link = stext.getSpans(off, off, ClickableSpan.class);

                    if (link.length != 0) {
                        if (action == MotionEvent.ACTION_UP) {
                            link[0].onClick(widget);
                        }
                        ret = true;
                    }
                }
                return ret;
            }
        });
    }


    private void setTextViewColorAndClick(String type, SpannableStringBuilder spannableBuilder, int clickTextPositionStart, int clickTextPositionEnd) {

        ForegroundColorSpan colorSpan = new ForegroundColorSpan(getColor(R.color.identifying_code));
        spannableBuilder.setSpan(colorSpan, clickTextPositionStart, clickTextPositionEnd, 33);

        ClickableSpan clickableSpanTwo = (ClickableSpan) (new ClickableSpan() {
            public void onClick(@NotNull View view) {
                switch (type) {
                    case USER_PRIVACY_USER:
                        Intent intent = new Intent(RegisterActivity.this, WebViewActivity.class);
                        intent.putExtra("title", getString(R.string.user_privacy_user));
                        intent.putExtra("url", BuildConfig.TERMS_URL);
                        ActivityUtils.startActivity(intent);
                        break;
                    case USER_PRIVACY_PRIVACY:
                        Intent intent1 = new Intent(RegisterActivity.this, WebViewActivity.class);
                        intent1.putExtra("title", getString(R.string.user_privacy_privacy));
                        intent1.putExtra("url", BuildConfig.PRIVACY_AGREEMENT);
                        ActivityUtils.startActivity(intent1);
                        break;
                }
            }

            public void updateDrawState(@NotNull TextPaint paint) {
                Intrinsics.checkNotNullParameter(paint, "paint");
                paint.setColor(getColor(R.color.identifying_code));
                paint.setUnderlineText(false);
            }
        });
        spannableBuilder.setSpan(clickableSpanTwo, clickTextPositionStart, clickTextPositionEnd, 33);


    }

    private void initComponent() {
        realm = Realm.getDefaultInstance();
        firstPreferences = FirstPreferences.getInstance(this);
        userPreferences = UserPreferences.getInstance(this);
        userId = userPreferences.getUserId();
        channel = userPreferences.getChannel();
        httpManager = HttpManager.getInstance();
        choseCountryRl = (RelativeLayout) findViewById(R.id.chose_country_Rl);
        chose_country_title = findViewById(R.id.chose_country_title);
        chosenCountryTv = (TextView) findViewById(R.id.chosen_country_tv);
        accountBackBtn = (ImageView) findViewById(R.id.account_back_btn);
        accountTitle = (TextView) findViewById(R.id.account_title);
        registerAccountEdt = (NoHintEditText) findViewById(R.id.register_account_edt);
        registerAccountEdt.setSelection(0);
        registerPasswordEdt = (NoHintEditText) findViewById(R.id.register_password_edt);
        registerIdentifyingCodeEdt = (NoHintEditText) findViewById(R.id.register_identifying_code_edt);
        getCodeBtn = (TextView) findViewById(R.id.get_code_btn);
        registerBtn = (ProgressButton) findViewById(R.id.register_btn);
        //设置标题
        accountTitle.setText(R.string.register_text);

        user_privacy_img = (ImageView) findViewById(R.id.user_privacy_img);
        user_privacy_tv2 = (TextView) findViewById(R.id.user_privacy_tv2);
        user_terms_url_tv = (TextView) findViewById(R.id.user_terms_url_tv);
        user_privacy_tv1 = (TextView) findViewById(R.id.user_privacy_tv1);


        if (BuildConfig.IS_OVERSEAS) {
            Tools.setViewButtonEnable(chosenCountryTv, false);
            // 获取getCountryCode
            getCountryCode();
            chose_country_title.setVisibility(View.VISIBLE);
            choseCountryRl.setVisibility(View.VISIBLE);

        } else {
            chose_country_title.setVisibility(View.GONE);
            choseCountryRl.setVisibility(View.GONE);

        }
    }

    private void registerEvent() {
        chosenCountryTv.setOnClickListener(this);
        accountBackBtn.setOnClickListener(this);
        getCodeBtn.setOnClickListener(this);
        getCodeBtn.setSelected(true);
        registerBtn.setOnClickListener(this);
        registerBtn.setOnProgressFinishListener(onProgressFinish);
        registerBtn.setSelected(false);
        registerBtn.setClickable(false);
        registerPasswordEdt.addTextChangedListener(pswTextWatcher);
        registerAccountEdt.addTextChangedListener(accountTextWatcher);
        registerIdentifyingCodeEdt.addTextChangedListener(codeTextWatcher);

        user_privacy_tv2.setOnClickListener(this);
        user_privacy_img.setOnClickListener(this);
        user_terms_url_tv.setOnClickListener(this);

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.user_privacy_img:
                isUserPrivacyChecked = !isUserPrivacyChecked;
                if (isUserPrivacyChecked) {
                    user_privacy_img.setImageResource(R.mipmap.sitting_select);
                    user_privacy_img.setColorFilter(null);
                    user_privacy_img.setImageTintList(ColorStateList.valueOf(getResources().getColor(R.color.identifying_code)));
                } else {
//                    user_privacy_img.setImageResource(R.mipmap.sitting_no_select);
                    user_privacy_img.setImageResource(R.mipmap.sitting_no_select);
                    user_privacy_img.setColorFilter(getColor(R.color.gray_d8d8d8));
                    user_privacy_img.setImageTintList(ColorStateList.valueOf(getResources().getColor(R.color.gray_99)));
                }
                checkRegisterButtonToClick();
                break;

            case R.id.user_privacy_tv2:
                Intent intent1 = new Intent(this, WebViewActivity.class);
                intent1.putExtra("title", getString(R.string.user_privacy_privacy));
                intent1.putExtra("url", BuildConfig.PRIVACY_AGREEMENT);
                ActivityUtils.startActivity(intent1);
                break;
            case R.id.user_terms_url_tv:
                Intent intent = new Intent(this, WebViewActivity.class);
                intent.putExtra("title", getString(R.string.user_privacy_user));
                long cTime = Calendar.getInstance().getTimeInMillis();
                intent.putExtra("url", BuildConfig.TERMS_URL);
                ActivityUtils.startActivity(intent);
                break;
            case R.id.get_code_btn:
                validatorAccountAndPassword();

                break;
            case R.id.register_btn:
                register();
                break;
            case R.id.account_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.chosen_country_tv:
                LayoutInflater layoutInflater = (LayoutInflater) getSystemService(Context.LAYOUT_INFLATER_SERVICE);
                ChoseCountryListPopupWindow choseCountryPopupWindow = new ChoseCountryListPopupWindow(layoutInflater, choseCountryRl.getWidth()
                        , 800, countryList);


                choseCountryPopupWindow.setChoseListener(new Function2<CountryCode, Integer, Unit>() {
                    @Override
                    public Unit invoke(CountryCode countryCode, Integer integer) {
                        String packageName = AppUtils.getAppPackageName();
                        selectCountryCode = countryCode;
                        chosenCountryTv.setText(getCountryName(countryCode));
                        return null;
                    }
                });

                choseCountryPopupWindow.setSelectedCountry(selectCountryCode);
                choseCountryPopupWindow.showDropDown(choseCountryRl);
                break;
            default:
                break;
        }
    }

    /**
     * @des: 根据手机或者邮箱注册
     * @params:
     * @return:
     */

    private void register() {
        if (isMobileRegister) {
            mobileRegister();
        } else if (isMaliRegister) {
            mailRegister();
        } else {
            showMessage(R.string.account_format_error);
        }
    }

    /**
     * @des: 获取手机验证码
     * @params:[accountStr]
     * @return:void
     */
    private void getMobileCode(String accountStr) {

        httpManager.requestPassport().mobileCode(accountStr, String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {

                timeCountBegin();
                showMessage(R.string.toast_code_send);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {

                if (entity.getCode() == MiaoHttpManager.STATUS_CODE_1_MIN) {
                    showMessage(R.string.toast_code_1_min);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_5_MIN_4_TIME) {
                    showMessage(R.string.toast_5_min_4_time);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY) {
                    showMessage(R.string.toast_5_time_in_one_day);
                } else {
                    showMessage(R.string.request_failed_alert);
                }

            }

            @Override
            public void onError(Exception e) {

                showMessage((R.string.request_failed_alert));
            }
        });
    }


    /**
     * @des: 获取邮箱验证码
     * @params:[accountStr]
     * @return:void
     */
    private void getMailCode(String accountStr) {

        Locale locale = getResources().getConfiguration().locale;
        String language = locale.toString();
        HttpManager.getInstance().requestPassport().mailCode(accountStr,
                EtUtils.getLocale(language),
                String.class,
                new MiaoHttpManager.Callback<String>() {
                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {

                        timeCountBegin();
                        showMessage(R.string.toast_code_send);

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {

                        if (entity.getCode() == MiaoHttpManager.STATUS_CODE_1_MIN) {
                            showMessage(R.string.toast_code_1_min);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_5_MIN_4_TIME) {
                            showMessage(R.string.toast_5_min_4_time);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY) {
                            showMessage(R.string.toast_5_time_in_one_day);
                        } else {
                            showMessage(R.string.request_failed_alert);
                        }

                    }

                    @Override
                    public void onError(Exception e) {

                        showMessage(R.string.request_failed_alert);

                    }
                });
    }

    /**
     * @des: 手机注册前置判断
     * @params:
     * @return:
     */
    private void mobileRegister() {
        final String mobile = registerAccountEdt.getText().toString();
        final String pwd = registerPasswordEdt.getText().toString();
        final String mobileCode = registerIdentifyingCodeEdt.getText().toString();
        if (mobile.length() == 0) {

            showMessage(R.string.login_alert_phone_empty);
        } else if (!RegexUtils.isMobileExact(mobile)) {

            showMessage(R.string.toast_mobile_format_wrong);
        } else if (pwd.length() == 0) {

            showMessage(R.string.login_alert_password_empty);
        } else if (pwd.length() <= PWD_MIN_LENGTH ||
                !StringToolsUtils.isLetterDigit(pwd)) {

            showMessage(R.string.login_alert_pwd_length);
        } else if (mobileCode.length() == 0) {

            showMessage(R.string.login_alert_mail_code);
//        } else if (mobileCode.length() <= 5) {
//
//            showMessage(R.string.edit_text_code_length);
        } else {
            currentTime = System.currentTimeMillis();
            KeyboardUtils.hideSoftInput(this);
            //如果Channel没有值 获取Channel
            if (android.text.TextUtils.isEmpty(channel)) {
                httpManager.request().channel(ChannelModel.class, new MiaoHttpManager.Callback<ChannelModel>() {
                    @Override
                    public void onStart() {
                        registerBtn.startLoading();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<ChannelModel> entity) {
                        userPreferences.setChannel(entity.getBody().getChannel());
                        userPreferences.setEndpoint(entity.getBody().getEndPoint());
                        mobileRegisterRequest(mobile, pwd, mobileCode, false);
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<ChannelModel> entity) {
                        registerFailedDelay(R.string.request_failed_alert);
                    }

                    @Override
                    public void onError(Exception e) {
                        registerFailedDelay(R.string.request_failed_alert);
                    }
                });

            } else {
                mobileRegisterRequest(mobile, pwd, mobileCode, true);
            }
        }
    }

    /**
     * @des: 手机注册
     * @params:[mail, pwd, code]
     * @return:void
     */
    private void mobileRegisterRequest(final String mobile, final String pwd, String code, final boolean hasChannel) {
        final String password = MD5Utils.md5(pwd);
        String areaCode = "86";
        String countryCode = "CHN";
        if (BuildConfig.IS_OVERSEAS) {
            areaCode = "001";
            countryCode = "USA";
        }
        HttpManager.getInstance().requestPassport().mobileRegister(
                CZURConstants.CLOUD_ANDROID,
                userPreferences.getIMEI(),
                userPreferences.getChannel(),
                mobile,
                password,
                code,
                areaCode,
                countryCode,
                RegisterModel.class, new MiaoHttpManager.Callback<RegisterModel>() {
                    @Override
                    public void onStart() {
                        if (hasChannel) {
                            registerBtn.startLoading();
                        }
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<RegisterModel> entity) {
                        registerEntity = entity;
                        phone = mobile;
                        mobilePwd = pwd;
                        isMobile = true;
                        registerSuccessDelay(entity);

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<RegisterModel> entity) {

                        if (MiaoHttpManager.STATUS_USER_EXISTS == entity.getCode()) {
                            registerFailedDelay(R.string.toast_user_existing);
                        } else if (MiaoHttpManager.STATUS_INVALID_CODE == entity.getCode()) {
                            registerFailedDelay(R.string.toast_code_error);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_INVALID_MOBILE) {
                            registerFailedDelay(R.string.invalid_mobile);
                        } else {
                            registerFailedDelay(R.string.request_failed_alert);
                        }
                    }

                    @Override
                    public void onError(Exception e) {
                        registerFailedDelay(R.string.request_failed_alert);
                    }
                });
    }

    /**
     * @des: 邮箱注册前置判断
     * @params:
     * @return:
     */
    private void mailRegister() {

        final String mail = registerAccountEdt.getText().toString();
        final String pwd = registerPasswordEdt.getText().toString();
        final String mailCode = registerIdentifyingCodeEdt.getText().toString();
        logI("mailRegister.mail=" + mail + ", pwd=" + pwd + ", mailCode=" + mailCode);
        if (Validator.isEmpty(mail)) {
            showMessage(R.string.login_alert_mail_empty);
        } else if (isValidatorLoginName(mail)) {
            showMessage(R.string.login_alert_mail_error);
        } else if (!StringUtilsKt.isValidEmail(mail)) {
            showMessage(R.string.login_alert_mail_error);
        } else if (pwd.length() == 0) {

            showMessage(R.string.login_alert_password_empty);
        } else if (pwd.length() <= PWD_MIN_LENGTH ||
                !StringToolsUtils.isLetterDigit(pwd)) { // 1.密码规则由6-20位密码修改为：8-20位包含数字及字母的密码组合

            showMessage(R.string.login_alert_pwd_length);
        } else if (mailCode.length() == 0) {

            showMessage(R.string.login_alert_mail_code);
        } else {
            currentTime = System.currentTimeMillis();
            KeyboardUtils.hideSoftInput(this);
            if (android.text.TextUtils.isEmpty(channel)) {
                HttpManager.getInstance().request().channel(ChannelModel.class, new MiaoHttpManager.Callback<ChannelModel>() {
                    @Override
                    public void onStart() {
                        registerBtn.startLoading();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<ChannelModel> entity) {
                        hideProgressDialog();
                        userPreferences.setChannel(entity.getBody().getChannel());
                        userPreferences.setEndpoint(entity.getBody().getEndPoint());
                        mailRegisterRequest(mail, pwd, mailCode, false);

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<ChannelModel> entity) {

                        registerFailedDelay(R.string.request_failed_alert);
                    }

                    @Override
                    public void onError(Exception e) {

                        registerFailedDelay(R.string.request_failed_alert);
                    }
                });
                return;
            } else {
                mailRegisterRequest(mail, pwd, mailCode, true);
            }

        }

    }

    /**
     * @des: 邮箱注册
     * @params:[mail, pwd, code]
     * @return:void
     */
    private void mailRegisterRequest(final String mail, final String pwd, String code, final boolean hasChannel) {
        /**
         * @des: Null
         * @params:[mail, pwd, code]
         * @return:void
         */

        String countryCode = "CHN";
        if (BuildConfig.IS_OVERSEAS) {
            countryCode = selectCountryCode.getCountryCode();
        }
        final String password = MD5Utils.md5(pwd);
        HttpManager.getInstance().requestPassport().mailRegister(
                CZURConstants.CLOUD_ANDROID,
                userPreferences.getIMEI(),
                userPreferences.getChannel(),
                mail,
                password,
                code,
                countryCode,
                RegisterModel.class, new MiaoHttpManager.Callback<RegisterModel>() {
                    @Override
                    public void onStart() {
                        if (hasChannel) {
                            registerBtn.startLoading();
                        }
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<RegisterModel> entity) {
                        registerEntity = entity;
                        email = mail;
                        mailPwd = pwd;
                        isMobile = false;
                        registerSuccessDelay(entity);

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<RegisterModel> entity) {

                        if (MiaoHttpManager.STATUS_USER_EXISTS == entity.getCode()) {
                            registerFailedDelay(R.string.toast_user_existing);
                        } else if (MiaoHttpManager.STATUS_INVALID_CODE == entity.getCode()) {
                            registerFailedDelay(R.string.toast_code_error);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_INVALID_EMAIL) {
                            registerFailedDelay(R.string.invalid_email);
                        } else {
                            registerFailedDelay(R.string.request_failed_alert);
                        }
                    }

                    @Override
                    public void onError(Exception e) {
                        registerFailedDelay(R.string.request_failed_alert);
                    }
                });
    }

    private ProgressButton.OnProgressFinish onProgressFinish = new ProgressButton.OnProgressFinish() {
        @Override
        public void onFinish() {
            if (isMobile) {
                confirmToClearLastUserData(registerEntity, phone, mobilePwd);
            } else {
                confirmToClearLastUserData(registerEntity, email, mailPwd);
            }
        }
    };

    /**
     * @des: 确认删除用户信息
     * @params:
     * @return:
     */

    private void confirmToClearLastUserData(final MiaoHttpEntity<RegisterModel> entity, final String loginUsername, final String loginPassword) {
        String currentUserId = entity.getBody().getId();
        if ((!StringUtils.equals(userId, currentUserId)) && userPreferences.isValidUser()) {

            CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(RegisterActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON_YES_NO);
            builder.setTitle(getResources().getString(R.string.prompt));
            String title = String.format(getString(R.string.confirm_to_clear_account), userPreferences.getUserName());
            builder.setMessage(title);
            builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    if (commonPopup != null) {
                        commonPopup.dismiss();
                    }
                    clearLastUserDataAndSetCurrentData(entity, loginUsername, loginPassword);
                }
            });
            builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.dismiss();
                    ActivityUtils.finishActivity(RegisterActivity.this);
                }
            });
            commonPopup = builder.create();
            commonPopup.show();

        } else {
            setCurrentUserData(entity, loginUsername, loginPassword);
        }

    }

    private void registerFailedDelay(final int failedText) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showMessage(failedText);
                            registerBtn.stopLoading();
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }


    private void registerSuccessDelay(final MiaoHttpEntity<RegisterModel> entity) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            registerBtn.stopLoadingSuccess();
                            showLongMessage(R.string.register_success);

                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }

    /**
     * @des: 如果和上次userId不一样 就清除sp  data/file并且设置用户信息sp
     * @params:
     * @return:
     */

    private void clearLastUserDataAndSetCurrentData(final MiaoHttpEntity<RegisterModel> entity, final String loginUsername, final String loginPassword) {

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                logI("clean last user file and sp");
                String filePath = getFilesDir() + File.separator + userPreferences.getUserId();
                FileUtils.deleteAllInDir(new File(filePath));
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        //清空sp
                        userPreferences.resetUser();
                        firstPreferences.resetFirstPreference();
                        //清空数据库
                        realm.executeTransaction(new Realm.Transaction() {
                            @Override
                            public void execute(Realm realms) {
                                realm.where(SPReportEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SPReportEntitySub.class).findAll().deleteAllFromRealm();
                                realm.where(SPReportSittingEntity.class).findAll().deleteAllFromRealm();

                                realm.where(PageEntity.class).findAll().deleteAllFromRealm();
                                realm.where(BookEntity.class).findAll().deleteAllFromRealm();
                                realm.where(PdfEntity.class).findAll().deleteAllFromRealm();
                                realm.where(BookPdfEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SyncPdfEntity.class).findAll().deleteAllFromRealm();
                                realm.where(TagEntity.class).findAll().deleteAllFromRealm();
                                realm.where(OcrEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SyncTagEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SyncPageEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SyncBookEntity.class).findAll().deleteAllFromRealm();
                                realm.where(DownloadEntity.class).findAll().deleteAllFromRealm();
                                realm.where(PdfDownloadEntity.class).findAll().deleteAllFromRealm();
                                realm.where(HomeCacheEntity.class).findAll().deleteAllFromRealm();

                            }
                        });
                        Fresco.getImagePipeline().clearCaches();
                        CleanUtils.cleanCustomDir(Utils.getApp().getFilesDir() + File.separator + CZURConstants.PDF_PATH);
                        setCurrentUserData(entity, loginUsername, loginPassword);
                    }
                });

                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });

    }

    /**
     * @des: 显示登录成功并且跳转到主页
     * @params:
     * @return:
     */
    private void showLoginSuccessAndGoIndex() {

        syncStarryUserInfo();

        EventBus.getDefault().post(new LoginEvent(EventType.REGISTER_SUCCESS));
        Intent intent = new Intent(RegisterActivity.this, IndexActivity.class);
        intent.putExtra("needSync", true);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
        ActivityUtils.startActivity(intent);
    }

    private void syncStarryUserInfo() {
        // 同步一下starry的用户信息
        try {
            AppClearUtils.getStarryUserInfo();
            AppClearUtils.syncStarryUserInfo();
            Thread.sleep(1000);
        } catch (Exception e) {

        }
    }

    /**
     * @des: 存储当前用户信息到SP
     * @params:
     * @return:
     */
    private void setCurrentUserData(MiaoHttpEntity<RegisterModel> entity, final String loginUsername, String loginPassword) {
        Log.d("RegisterActivity", "setCurrentUserData: " + entity.getBody());
        userPreferences.setUser(entity.getBody());
        userPreferences.setIsUserLogin(true);
        userPreferences.setLoginUserName(loginUsername);
        userPreferences.setLoginPassword(loginPassword);
        userPreferences.setCountryCode(entity.getBody().getCountryCode());
        showLoginSuccessAndGoIndex();

    }


    /**
     * @des: 验证手机号或者邮箱 和密码
     * @params:[]
     * @return:void
     */
    private void validatorAccountAndPassword() {

        String accountStr = registerAccountEdt.getText().toString();

        if (Validator.isEmpty(accountStr)) {
            showMessage(R.string.account_empty);
        } else {

            if (RegexUtils.isMobileExact(accountStr)) {
                getMobileCode(accountStr);
//            } else if (com.czur.cloud.util.validator.StringUtils.isCZUREmail(accountStr)) {
            } else if (StringUtilsKt.isValidEmail(accountStr)) {
                getMailCode(accountStr);
            } else {
                showMessage(R.string.account_format_error);
            }
        }
    }


    /**
     * @des: 计时器
     * @params:
     * @return:
     */

    class TimeCount extends CountDownTimer {

        public TimeCount(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onFinish() {
            getCodeBtn.setText(R.string.resend_code);
            getCodeBtn.setClickable(true);
            getCodeBtn.setSelected(true);
            getCodeBtn.setTextColor(getColor(R.color.black_272f44));

        }

        @Override
        public void onTick(long millisUntilFinished) {
            getCodeBtn.setClickable(false);
            getCodeBtn.setText(millisUntilFinished / 1000 + " s");
            getCodeBtn.setSelected(false);
            getCodeBtn.setTextColor(getColor(R.color.grey_AEAEAE));
        }

    }


    /**
     * @des: 计时器开始倒计时60S
     * @params:
     * @return:void
     */
    private void timeCountBegin() {
        timeCount = new TimeCount(60000, 1000);
        timeCount.start();

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (Validator.isNotEmpty(timeCount)) {
            timeCount.cancel();
        }
        if (realm != null) {
            realm.close();
        }
    }

    private TextWatcher codeTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            if (s.length() > 0) {
                codeHasContent = true;
            } else {
                codeHasContent = false;
            }

            checkRegisterButtonToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            if (s.length() > 0) {
                codeHasContent = true;
            } else {
                codeHasContent = false;
            }
            checkRegisterButtonToClick();
        }
    };
    private TextWatcher pswTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听

            checkRegisterButtonToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            checkRegisterButtonToClick();
        }
    };

    private TextWatcher accountTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            judgingRegister(s);

        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            judgingRegister(s);
        }
    };

    private void judgingRegister(CharSequence s) {
        if (RegexUtils.isMobileExact(s)) {
            isMobileRegister = true;
            isMaliRegister = false;
//        } else if (com.czur.cloud.util.validator.StringUtils.isCZUREmail(s.toString())) {
        } else if (StringUtilsKt.isValidEmail(s.toString())) {
            isMobileRegister = false;
            isMaliRegister = true;
        } else {
            isMobileRegister = false;
            isMaliRegister = false;
        }
        checkRegisterButtonToClick();
    }

    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private void checkRegisterButtonToClick() {

        boolean accountIsNotEmpty = Validator.isNotEmpty(registerAccountEdt.getText().toString());
        boolean passwordIsNotEmpty = Validator.isNotEmpty(registerPasswordEdt.getText().toString());

        if (accountIsNotEmpty && passwordIsNotEmpty && codeHasContent && isUserPrivacyChecked) {
            registerBtn.setSelected(true);
            registerBtn.setClickable(true);
        } else {
            registerBtn.setSelected(false);
            registerBtn.setClickable(false);
        }
    }

    // 获取国家代码
    private void getCountryCode() {
        httpManager.requestPassport().countryCode(
                "en-US",
                CountryList.class,
                new MiaoHttpManager.Callback<CountryList>() {
                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<CountryList> entity) {
                        countryList = (ArrayList<CountryCode>) entity.getBody().getCountryList();
                        Log.i("RegisterActivity", "getCountryCode.onResponse.entity=" + new Gson().toJson(entity.getBody()));
                        if (BuildConfig.IS_OVERSEAS) {
                            boolean needResetDefaultCountry = false;
                            for (int i = 0; i < countryList.size(); i++) {//海外版删除列表中的中国
                                CountryCode countryCode = countryList.get(i);
                                if ("CHN".equals(countryCode.getCountryCode())) {
                                    if (countryCode.getDefaultCountry()) {
                                        needResetDefaultCountry = true;
                                    }
                                    countryList.remove(i);
                                    break;
                                }
                            }

                            if (needResetDefaultCountry) {
                                countryList.get(0).setDefaultCountry(true);
                            }
                        } else {

                        }

                        if (selectCountryCode == null || selectCountryCode.getCountryCode().isEmpty()) {
                            for (CountryCode countryCode : countryList) {
                                if (countryCode.getDefaultCountry()) {
                                    selectCountryCode = countryCode;
                                    break;
                                }
                            }
                            chosenCountryTv.setText(getCountryName(selectCountryCode));
                        }

                        Tools.setViewButtonEnable(chosenCountryTv, true);
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<CountryList> entity) {

                    }

                    @Override
                    public void onError(Exception e) {

                    }
                }
        );
    }

    private String getCountryName(CountryCode countryCode) {
        if (BuildConfig.IS_OVERSEAS) {//海外版
            String language = getString(R.string.countryCode);
            Log.i("RegisterActivity", "getCountryName.language=" + language);
            // 繁体中文
            if (language.equals("countryNameTw")) {
                return countryCode.getCountryNameTw();
            } else {
                return countryCode.getCountryNameUs();
            }
        } else { //中国版本
            return countryCode.getCountryName();
        }
    }
}
