package com.czur.cloud.ui.starry.meeting.adapter

import android.util.Log
import android.view.ViewGroup
import androidx.core.view.updatePadding
import androidx.recyclerview.widget.RecyclerView
import com.czur.cloud.R
import com.czur.cloud.ui.starry.meeting.baselib.adapter.BaseVH
import com.czur.cloud.ui.starry.meeting.baselib.utils.getString
import com.czur.cloud.ui.starry.meeting.bean.*
import com.czur.cloud.ui.starry.meeting.widget.HeadImageView
import com.czur.cloud.ui.starry.meeting.widget.setUrlOrText

/**
 * Created by 陈丰尧 on 2021/5/13
 */

private const val TYPE_TIME = 0 // 时间信息
private const val TYPE_REC = 1  // 接收信息
private const val TYPE_SEND = 2 // 发送信息
private const val TYPE_ACTION = 3 // 行为信息

private const val TAG = "ChatAdapter"

class ChatAdapter : RecyclerView.Adapter<BaseVH>() {
    var dataList: List<ChatMsg> = emptyList()
        set(value) {
            value.forEach {
                it.read = true
            }
            field = value
            notifyDataSetChanged()
        }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return when (viewType) {
            TYPE_TIME -> BaseVH(R.layout.starry_meeting_chat_item_chat_time, parent)
            TYPE_REC -> BaseVH(R.layout.starry_meeting_chat_item_chat_rec, parent)
            TYPE_SEND -> BaseVH(R.layout.starry_meeting_chat_item_chat_send, parent)
            TYPE_ACTION -> BaseVH(R.layout.starry_meeting_chat_item_chat_member_action, parent)
            else -> throw IllegalArgumentException("不支持消息类型:viewType:${viewType}")
        }
    }

    override fun onBindViewHolder(holder: BaseVH, position: Int) {
        when (val data = dataList[position]) {
            is ChatTimeMsg -> {
                // 时间消息
                holder.setText(data.timeStr, R.id.chatTimeTv)
                if (position != 0) {
                    // 如果不是第一条信息, 则时间信息与其他信息上方间隔30像素
                    holder.itemView.updatePadding(top = 30)
                } else {
                    holder.itemView.updatePadding(top = 0)
                }
            }
            // 文本消息
            is ChatTextMsg -> {
                val nickname = data.nickName

                if (data.isSelf) {
                    holder.setText(data.content, R.id.chatSendContentTv)
                    val headImage: HeadImageView = holder.getView(R.id.chatSendHeadIv)
                    headImage.setUrlOrText(data.headImg, data.nickName)
                    holder.visible(
                        data.status == ChatSendMsgStatus.FAIL,
                        R.id.sendMsgErrorTv
                    )
                } else {
                    holder.setText(data.content, R.id.chatRecContentTv)
                    holder.setText(data.nickName, R.id.chatRecNameTv)
                    val headImage: HeadImageView = holder.getView(R.id.chatRecHeadIv)
                    headImage.setUrlOrText(data.headImg, data.nickName)
                }
            }
            // 加入 退出消息
            is ChatActionMsg -> {
                val txt = when(data.action){
                    ChatMemberMsgAction.ONLINE -> {
                        getString(R.string.chat_join_meeting, data.nickName)
                    }
                    ChatMemberMsgAction.OFFLINE -> {
                        getString(R.string.chat_leave_meeting, data.nickName)
                    }
                    ChatMemberMsgAction.ADMIN -> {
                        getString(R.string.chat_admin_meeting, data.nickName)
                    }
                }
                holder.setHtmlText(txt, R.id.chatMemberActionTv)
            }
        }
    }

    override fun getItemCount(): Int = dataList.size

    override fun getItemViewType(position: Int): Int =
        when (val data = dataList[position]) {
            is ChatTimeMsg -> TYPE_TIME
            is ChatActionMsg -> TYPE_ACTION
            is ChatTextMsg -> if (data.isSelf) TYPE_SEND else TYPE_REC
            else -> {
                Log.w(TAG, "不支持的数据类型:${data.javaClass.simpleName}")
                super.getItemViewType(position)
            }
        }

}