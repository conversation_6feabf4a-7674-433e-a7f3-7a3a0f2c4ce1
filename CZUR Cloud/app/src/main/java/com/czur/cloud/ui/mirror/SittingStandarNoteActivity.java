package com.czur.cloud.ui.mirror;

import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.ui.base.BaseActivity;

public class SittingStandarNoteActivity extends BaseActivity implements View.OnClickListener {

    private TextView confirm;
    private ImageView back_iv;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sitting_standar_note);

        initView();
        initListener();
//        setPageTitle(R.string.sitting_standar_note);
        TextView title = (TextView) findViewById(R.id.top_bar_title);
        title.setText(R.string.sitting_standar_note);
    }

    protected void initView() {

        confirm = findViewById(R.id.confirm);
        back_iv = findViewById(R.id.top_bar_back_btn);

    }

    protected void initListener() {
        confirm.setOnClickListener(this);
        back_iv.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.confirm:
            case R.id.top_bar_back_btn:
                finish();
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

    }

}
