package com.czur.cloud.event;

public class DownloadMp3SuccessEvent extends BaseEvent {

	private String mp3Name;
    private String deviceId;
    private String createTime;

	public DownloadMp3SuccessEvent(EventType eventType,String mp3Name, String deviceId,String createTime) {
		super(eventType);
		this.mp3Name=mp3Name;
        this.deviceId=deviceId;
        this.createTime=createTime;
	}

	public String getMp3Name() {
		return mp3Name;
	}

    public String getDeviceId() {
        return deviceId;
    }

    public String getCreateTime() {
        return createTime;
    }

    @Override
	public boolean match(Object obj) {
		return true;
	}
}
