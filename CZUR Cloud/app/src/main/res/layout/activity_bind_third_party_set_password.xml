<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">

    <include
        android:id="@+id/bind_third_party_set_password_bar"
        layout="@layout/layout_account_top_bar" />


    <LinearLayout
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:layout_width="250dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/bind_third_party_set_password_bar"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="47dp"
        android:orientation="vertical">

        <com.czur.cloud.ui.component.NoHintEditText
            android:id="@+id/first_set_password_edt"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@drawable/btn_rec_5_bg_with_gray"
            android:digits="@string/password_digits"
            android:gravity="center"
            android:hint="@string/set_password"
            android:maxLength="20"
            android:maxLines="1"
            android:textColor="@color/login_btn_text"
            android:textColorHint="@color/login_edt_hint_color"
            android:textSize="15sp"
            android:textStyle="bold" />




        <com.czur.cloud.ui.component.ProgressButton
            android:id="@+id/confirm_btn"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="23dp"
            android:background="@drawable/selector_register_btn"
            app:progress_btn_tv="@string/confirm_text"
          />


    </LinearLayout>


</RelativeLayout>