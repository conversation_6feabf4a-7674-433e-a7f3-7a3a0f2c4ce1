package com.czur.cloud.event.starry;


import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;

/**
 * 上线通知
 */
public class ATBindSuccessEvent extends BaseEvent {

    private String deviceUdid;

    private String statusBean;

    public ATBindSuccessEvent(EventType eventType, String deviceUdid, String statusBean) {
        super(eventType);
        this.deviceUdid = deviceUdid;
        this.statusBean = statusBean;
    }

    public String getStatusBean() {
        return statusBean;
    }

    public String getDeviceUdid() {
        return deviceUdid;
    }
    @Override
    public boolean match(Object obj) {
        return true;
    }
}
