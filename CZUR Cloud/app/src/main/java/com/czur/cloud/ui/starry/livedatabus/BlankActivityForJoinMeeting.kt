package com.czur.cloud.ui.starry.livedatabus

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.TypedArray
import android.os.Build
import android.os.Bundle
import android.os.Looper
import android.util.Log
import android.view.KeyEvent
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ServiceUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.event.BaseEvent
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryCheckMeetingListEvent
import com.czur.cloud.event.StarryCommonEvent
import com.czur.cloud.netty.observer.NettyService
import com.czur.cloud.network.core.MiaoHttpManager
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.ui.base.BaseActivity
import com.czur.cloud.ui.home.IndexActivity
import com.czur.cloud.ui.starry.activity.StarryActivity
import com.czur.cloud.ui.starry.api.StarryRepository
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.meeting.MeetingMainActivity
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.bean.UserStatus
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup
import com.czur.cloud.ui.starry.meeting.dialog.StarryMeetJoinCodePopup
import com.czur.cloud.ui.starry.meeting.dialog.StarryMeetJoinPwdDialog
import com.czur.cloud.ui.starry.meeting.dialog.StarryMeetingInputDialog
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.model.JoinMeetData
import com.czur.cloud.ui.starry.model.JoinMeetModel
import com.czur.cloud.ui.starry.model.StarryCallInModel
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.viewmodel.RecentlyViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import com.czur.czurutils.log.logI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.lang.reflect.Field
import java.lang.reflect.Method

open class BlankActivityForJoinMeeting : BaseActivity() {

    private val recentlyViewModel by lazy {
        ViewModelProvider(this)[RecentlyViewModel::class.java]
    }

    var dlgMeetJoinCode: StarryMeetJoinCodePopup? = null
    var dlgMeetJoinPwd: StarryMeetJoinPwdDialog? = null

    // 带输入框的入会弹窗
    private var dlgMeetCode: StarryMeetingInputDialog? = null

    private val viewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this)[StarryViewModel::class.java]
    }

    companion object {
        private const val TAG = "BlankActivityForJoinMeeting"
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {

        when (event.eventType) {
            // 查询正在进行的会议
            EventType.STARRY_MEETING_CMD_CHECK_MEETING_LIST -> {
                val newMeetingListEvent = event as StarryCheckMeetingListEvent
                val dataBean = newMeetingListEvent.params
//                recentlyViewModel.listDoingMeeting.value = dataBean
//                recentlyViewModel.getCallRecordsRefresh()
                val isPCCount = dataBean.count { it1 ->
                    it1.isPCEnter && it1.status == UserStatus.STATUS_JOINED
                }
                MeetingModel.isPCEnter = isPCCount > 0
                if (MeetingModel.isPCEnter) {
                    MeetingModel.isPCEnterMeetingCode =
                        dataBean.first {
                            it.isPCEnter
                        }.meetingCode ?: ""
                }
            }
            EventType.LOG_OUT->{
                val type = intent?.getStringExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_TYPE) ?: ""
                if (type == StarryConstants.STARRY_BLANK_TYPE_SHORTCUT){
                    finish()
                }
            }
            else -> {
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        // Only fullscreen activities can request orientation 解决方法
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.O && isTranslucentOrFloating()) {
            val result: Boolean = fixOrientation()
            Log.i(
                "BlankActivityForJoinMeeting",
                "onCreate fixOrientation when Oreo, result = $result"
            )
        }
        super.onCreate(savedInstanceState)
        val window = window
        window.statusBarColor = resources.getColor(R.color.transparent)
        setContentView(R.layout.starry_activity_blank)
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }

        initData(intent)

    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        // 又一次启动了加入会议弹窗
        initData(intent)
    }

    fun initData(intent: Intent?) {
        ActivityUtils.getActivityList()
        val type = intent?.getStringExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_TYPE) ?: ""

        if (type != "") {
            launch {
                delay(500)
                recentlyViewModel.getDingMeetingAndCallRecords()
                delay(200)
//                logI("${TAG}.getDingMeetingAndCallRecords()")
                if (type == StarryConstants.STARRY_BLANK_TYPE_COPY) {
                    val ret =
                        intent?.getStringExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_CONTENT) ?: ""
                    if (ret != "") {
                        showMeetingJoinDlg(this@BlankActivityForJoinMeeting, ret)
                    }
                } else if (type == StarryConstants.STARRY_BLANK_TYPE_SHORTCUT || type == StarryConstants.STARRY_BLANK_TYPE_SHORTCUT_INMEETING) {
                    // 从快捷方式菜单跳转过来的
                    showJoinDlg()

                } else {
                    val uuid =
                        intent?.getStringExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_UUID) ?: ""
                    if (uuid != "") {
                        showMeetingJoinDlgWithUUID(this@BlankActivityForJoinMeeting, uuid)
                    }
                }
            }
        }
    }

    // 从web跳转进入
    private fun showMeetingJoinDlgWithUUID(context: Context, meetingUUID: String) {
        lifecycleScope.launch {
            val starryRepository = StarryRepository()
            // 先判断一下会议是否结束
            // 复制已结束的会议号口令后打开APP，直接toast提示“会议号不存在！”
            val retFlag = starryRepository.checkShareMeetingInfo(meetingUUID)
            logI("${TAG}.showMeetingJoinDlgWithUUID.retFlag=${retFlag}")
            if (retFlag == JoinMeetModel.ERROR_CODE_NOT_EXIST) {
                // 会议不存在
                ToastUtils.showLong(R.string.starry_join_error_not_exist_check)
                IndexActivity.isJumpToApp = false
                finish()
            } else if (retFlag == MiaoHttpManager.STATUS_SUCCESS) {
                val retData = starryRepository.shareMeetingInfo(meetingUUID)
                logI("${TAG}.showMeetingJoinDlgWithUUID.meetingUUID=${meetingUUID}")
                logI("${TAG}.showMeetingJoinDlgWithUUID.shareMeetingInfo.retData=${retData}")
                val roomName = retData.roomName
                val meetingCode = retData.meetingCode
                if (meetingCode.isNotBlank()) {
                    val retFlag1 = starryRepository.checkPassword(meetingCode, "")
                    logI("${TAG}.showMeetingJoinDlgWithUUID.retFlag1=${retFlag1}")

                    if (retFlag1) {
                        showMeetingJoinDlgWithCode(context, roomName, meetingCode, "")
                    } else {
                        showMeetingJoinPwdDlg(context, roomName, meetingCode)
                    }
                } else {
                    IndexActivity.isJumpToApp = false
                    finish()
                }
            } else {
                IndexActivity.isJumpToApp = false
                finish()
            }

            IndexActivity.isJumpToApp = false

        }
    }

    // 从剪切板copy进入，显示会议码dlg
    private fun showMeetingJoinDlg(context: Context, ret: String) {
        Log.i(TAG, "showMeetingJoinDlg.ret=${ret}")
        // 通过uuid获取meetcode和meetpwd,不从会议口令获取了
        val keyUrl = BuildConfig.SHARE_STARRY_URL
        val meetUUID = Tools.getMeetingNameFromClipboard(ret, keyUrl).trim()

        lifecycleScope.launch {
            val starryRepository = StarryRepository()
            // 先判断一下会议是否结束
            // 复制已结束的会议号口令后打开APP，直接toast提示“会议号不存在！”
            val retData = starryRepository.checkShareMeetingInfo(meetUUID)
            logI("${TAG}.showMeetingJoinDlg.retData=${retData}")
            if (retData == JoinMeetModel.ERROR_CODE_NOT_EXIST) {
                // 会议不存在
                ToastUtils.showLong(R.string.starry_join_error_not_exist_check)
                finish()
            } else if (retData == MiaoHttpManager.STATUS_SUCCESS) {
                //joinMeetingReturnData
                val joinMeetingData = JoinMeetModel.joinMeetingReturnData ?: JoinMeetData()
                logI("${TAG}.showMeetingJoinDlg.joinMeetingData=${joinMeetingData}")

                val meetName = joinMeetingData.roomName
                val meetCode1 = joinMeetingData.meetingCode
                val meetCode = Tools.resumeMeetCode(meetCode1, "-")
                val meetPwd = joinMeetingData.meetingPassword

                logI(
                    "${TAG}.showMeetingJoinDlg.meetName=${meetName}," +
                            "meetCode=${meetCode},meetPwd=${meetPwd},meetUUID=${meetUUID}"
                )

                val retFlag = starryRepository.checkPassword(meetCode, meetPwd)
                logI("${TAG}.showMeetingJoinDlg.retFlag=${retFlag}")
                if (retFlag) {
                    showMeetingJoinDlgWithCode(context, meetName, meetCode, meetPwd)
                } else {
                    showMeetingJoinPwdDlg(context, meetName, meetCode)
                }
            } else {
                finish()
            }
        }
    }

    // 从快捷菜单进入,直接弹出入会弹窗
    private fun showJoinDlg() {
        closeAllDlg()

        openDlgMeetCode()

    }

    // 先关闭所有的dlg
    private fun closeAllDlg() {
        // 消去上次的弹窗
        if (dlgMeetJoinCode != null) {
            dlgMeetJoinCode?.dismiss()
            dlgMeetJoinCode = null
        }
        if (dlgMeetJoinPwd != null) {
            dlgMeetJoinPwd?.dismiss()
            dlgMeetJoinPwd = null
        }
        if (dlgMeetCode != null) {
            dlgMeetCode?.dismiss()
            dlgMeetCode = null
        }
    }

    // 显示入会码dlg
    private fun openDlgMeetCode() {
        dlgMeetCode = StarryMeetingInputDialog(
            context = this,
            InputType = StarryMeetingInputDialog.DLG_TYPE_MEETING_CODE,
            title = getString(R.string.starry_join_meet_title),
            content = "",
            hint = getString(R.string.starry_join_meet_hint),
            OKTitle = getString(R.string.starry_join_meet_join),
            cancelTitle = getString(R.string.starry_join_meet_cancel),
            object : StarryMeetingInputDialog.clickCallBack {
                override fun yesClick(dialog: StarryMeetingInputDialog) {
                    launch {
                        dialog.yesButton?.let { Tools.setViewButtonEnable(it, false) }
                        var meet_no = dialog.editText?.text.toString() ?: ""
                        meet_no = meet_no.filterNot {
                            it.toString() == " "
                        }
                        val ret = viewModel.checkPassword(meet_no, "")

                        when (ret) {
                            // 开启会议
                            MiaoHttpManager.STATUS_SUCCESS -> {
                                onCheckPCMeeting(this@BlankActivityForJoinMeeting, meet_no, "")
                            }

                            // 需要密码
                            JoinMeetModel.ERROR_CODE_PWD_WRONG -> {
                                val meetName = getString(R.string.starry_join_meet_pwd)
                                showMeetingJoinPwdDlg(
                                    this@BlankActivityForJoinMeeting,
                                    meetName,
                                    meet_no
                                )
                                dialog.dismiss()
                            }

                            // 会议号不存在
                            JoinMeetModel.ERROR_CODE_NOT_EXIST -> {
                                ToastUtils.showLong(R.string.starry_join_error_not_exist)
                                dialog.yesButton?.let { Tools.setViewButtonEnable(it, true) }
                            }

                            // 会议已结束
                            JoinMeetModel.ERROR_CODE_OVER -> {
                                ToastUtils.showLong(R.string.starry_join_error_over)
                                dialog.yesButton?.let { Tools.setViewButtonEnable(it, true) }
                            }

                            else -> {
                                ToastUtils.showLong(R.string.starry_join_error_no_network)
                                dialog.dismiss()
                                finish()
                            }
                        }
                    }
                }

                override fun noClick(dialog: StarryMeetingInputDialog) {
                    dialog.dismiss()
                    finish()
                }
            }
        )

        dlgMeetCode?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dlgMeetCode?.show()
    }


    // 无密码的会议码弹窗
    private fun showMeetingJoinDlgWithCode(
        context: Context,
        meetName: String,
        meetCode: String,
        meetPwd: String
    ) {
        val showCode = Tools.formateMeetCode(meetCode)
        // 消去上次的弹窗
        if (dlgMeetJoinCode != null) {
            dlgMeetJoinCode?.dismiss()
            dlgMeetJoinCode = null
        }
        if (dlgMeetJoinPwd != null) {
            dlgMeetJoinPwd?.dismiss()
            dlgMeetJoinPwd = null
        }

        val builder = StarryMeetJoinCodePopup.Builder(context)
            .setTitle(meetName)
            .setMessage(showCode)
            .setPositiveTitle(getString(R.string.starry_join_meet_joinmeet))
            .setNegativeTitle(getString(R.string.starry_join_meet_cancel))
            .setOnPositiveListener { dialog, p1 ->

                lifecycleScope.launch {
                    recentlyViewModel.getDingMeetingAndCallRecords()
                    delay(200)
                    // 1先判断是否有正在进行的会议
//                    val model = JoinMeetModel.joinMeetingReturnData ?: JoinMeetData()
//                    val meetingCode = model.meetingCode
                    // 用来标识自己是否在其他会议中，可用此字段来判断是否需要进行弹窗
                    // 并且当前会议不是 同账号,其他端接听的情况, 才弹窗
                    if (MeetingModel.isPCEnter && (meetCode != MeetingModel.isPCEnterMeetingCode)) {
                        StarryCommonPopup.Builder(context)
                            .setTitle(getString(R.string.starry_popupwindow_title))
                            .setMessage(getString(R.string.starry_callin_dialog_msg))
                            .setPositiveTitle(getString(R.string.starry_callin_dialog_join))
                            .setNegativeTitle(getString(R.string.starry_callin_dialog_cancel))
                            .setOnPositiveListener { dialog, _ ->

                                lifecycleScope.launch {
                                    joinInMeeting(context, meetName, meetCode, meetPwd)
//                                    dialog.dismiss()
                                }

                            }
                            .setOnNegativeListener { dialog, _ ->
                                dialog.dismiss()
                                finish()
                            }
                            .setOnDismissListener {
                                it.dismiss()
                                finish()
                            }
                            .create()
                            .show()

                        dlgMeetJoinCode?.dismiss()
                        dlgMeetJoinCode = null
                    } else {
                        joinInMeeting(context, meetName, meetCode, meetPwd)
                    }


                }

            }
            .setOnNegativeListener { dialog, p1 ->
                dlgMeetJoinCode?.dismiss()
                IndexActivity.isJumpToApp = false
                dlgMeetJoinCode = null
                finish()
            }
            .setOnDismissListener {
                IndexActivity.isJumpToApp = false
                dlgMeetJoinCode = null
            }
            .setOnKeyBackListener { dialog, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    dlgMeetJoinCode?.dismiss()
                    dlgMeetJoinCode = null
                    IndexActivity.isJumpToApp = false
                    finish()
                }
                false
            }
        dlgMeetJoinCode = builder.create()
        dlgMeetJoinCode?.show()
    }

    private fun onCheckPCMeeting(context: Context, meetCode: String, meetPwd: String) {
        lifecycleScope.launch {
            recentlyViewModel.getDingMeetingAndCallRecords()
            delay(200)
            // 1先判断是否有正在进行的会议
            // 用来标识自己是否在其他会议中，可用此字段来判断是否需要进行弹窗
            // 并且当前会议不是 同账号,其他端接听的情况, 才弹窗
            val meetName = getString(R.string.starry_join_meet_pwd)
            if (MeetingModel.isPCEnter && (meetCode != MeetingModel.isPCEnterMeetingCode)) {
                StarryCommonPopup.Builder(context)
                    .setTitle(getString(R.string.starry_popupwindow_title))
                    .setMessage(getString(R.string.starry_callin_dialog_msg))
                    .setPositiveTitle(getString(R.string.starry_callin_dialog_join))
                    .setNegativeTitle(getString(R.string.starry_callin_dialog_cancel))
                    .setOnPositiveListener { dialog, _ ->

                        lifecycleScope.launch {
                            joinInMeeting(context, meetName, meetCode, meetPwd)
                        }

                    }
                    .setOnNegativeListener { dialog, _ ->
                        dialog.dismiss()
                        finish()
                    }
                    .setOnDismissListener {
                        it.dismiss()
                        finish()
                    }
                    .create()
                    .show()

                dlgMeetJoinCode?.dismiss()
                dlgMeetJoinCode = null
            } else {
                joinInMeeting(context, meetName, meetCode, meetPwd)
            }
        }
    }

    private suspend fun joinInMeeting(
        context: Context,
        meetName: String,
        meetCode: String,
        meetPwd: String
    ) {
        val starryRepository = StarryRepository()
        val ret = starryRepository.joinMeeting(meetCode, meetPwd) ?: 0
        logI("${TAG}.showMeetingJoinDlgWithCode.joinMeeting.ret=${ret}")
        when (ret) {
            // 开启会议
            MiaoHttpManager.STATUS_SUCCESS -> {
                recentlyViewModel.getDingMeetingAndCallRecords()
                delay(200)
                if (Looper.myLooper() == null)
                    Looper.prepare()
                dlgMeetJoinCode?.hide()
                onJoinMeeting(context)
                dlgMeetJoinCode?.dismiss()
                dlgMeetJoinCode = null
                Looper.loop()
//                            ActivityUtils.finishActivity(this@BlankActivityForJoinMeeting)
            }

            // 需要密码
            JoinMeetModel.ERROR_CODE_PWD_WRONG -> {
                if (Looper.myLooper() == null)
                    Looper.prepare()
                dlgMeetJoinCode?.hide()
                showMeetingJoinPwdDlg(
                    this@BlankActivityForJoinMeeting,
                    meetName,
                    meetCode
                )
                dlgMeetJoinCode?.dismiss()
                dlgMeetJoinCode = null
//                            ActivityUtils.finishActivity(this@BlankActivityForJoinMeeting)
                Looper.loop()
            }

            // 会议号不存在
            JoinMeetModel.ERROR_CODE_NOT_EXIST -> {
                ToastUtils.showLong(R.string.starry_join_error_not_exist_check)
            }

            // 人数满
            JoinMeetModel.ERROR_MEMBER_MUCH -> {
                ToastUtils.showLong(R.string.starry_join_error_member_over)
            }

            // 会议已锁定
            JoinMeetModel.ERROR_CODE_CLOCK -> {
                ToastUtils.showLong(R.string.starry_join_error_clock)
            }

            // 会议已结束
            JoinMeetModel.ERROR_CODE_OVER -> {
                ToastUtils.showLong(R.string.starry_join_error_over)
            }

            else -> {
                ToastUtils.showLong(R.string.starry_join_error_no_network)
            }
        }
        IndexActivity.isJumpToApp = false
    }

    // 有密码的会议码弹窗 显示入会密码dlg
    private fun showMeetingJoinPwdDlg(context: Context, meetName: String, meetCode: String) {
        val showCode = Tools.formateMeetCode(meetCode)
//        if (dlgMeetJoinCode != null) {
//            dlgMeetJoinCode?.dismiss()
//            dlgMeetJoinCode = null
//        }
//        if (dlgMeetJoinPwd != null) {
//            dlgMeetJoinPwd?.dismiss()
//            dlgMeetJoinPwd = null
//        }
        closeAllDlg()

        dlgMeetJoinPwd = StarryMeetJoinPwdDialog(
            context = context,
            title = meetName,
            content = showCode,
            hint = getString(R.string.starry_join_meet_pwd_hint),
            OKTitle = getString(R.string.starry_join_meet_joinmeet),
            cancelTitle = getString(R.string.starry_join_meet_cancel),
            object : StarryMeetJoinPwdDialog.clickCallBack {
                override fun yesClick(dialog: StarryMeetJoinPwdDialog) {
                    dlgMeetJoinPwd?.yesButton?.let { Tools.setViewButtonEnable(it, false) }
                    val meetPwd = dialog.editText?.text.toString() ?: ""
                    lifecycleScope.launch {
                        recentlyViewModel.getDingMeetingAndCallRecords()
                        delay(200)
                        //先判断密码是否正确，在弹其它端在会中。
                        lifecycleScope.launch {
                            joinInMeetingWithPassword(context, meetPwd, meetCode)
                        }
                    }
                }

                override fun noClick(dialog: StarryMeetJoinPwdDialog) {
                    dlgMeetJoinPwd?.dismiss()
                    dlgMeetJoinPwd = null
                    finish()
                }
            }
        )
        dlgMeetJoinPwd?.window?.setBackgroundDrawableResource(android.R.color.transparent)
        dlgMeetJoinPwd?.setOnDismissListener {
            dlgMeetJoinPwd = null
        }
        dlgMeetJoinPwd?.setOnKeyListener { p0, p1, p2 ->
            if (p2?.keyCode == KeyEvent.KEYCODE_BACK) {
                dlgMeetJoinPwd?.dismiss()
                dlgMeetJoinPwd = null
                finish()
            }
            false
        }
        dlgMeetJoinPwd?.show()
    }


    private suspend fun joinInMeetingWithPassword(
        context: Context,
        meetPwd: String,
        meetCode: String
    ) {
        val starryRepository = StarryRepository()

//        val ret = starryRepository.joinMeeting(meetCode, meetPwd) ?: 0
        val ret = starryRepository.checkPasswordNew(meetCode, meetPwd) ?: 0
        logI("${TAG}.showMeetingJoinPwdDlg.joinMeeting.ret=${ret}")

        when (ret) {
            // 开启会议
            MiaoHttpManager.STATUS_SUCCESS -> {
                recentlyViewModel.getDingMeetingAndCallRecords()
                delay(200)
                // 用来标识自己是否在其他会议中，可用此字段来判断是否需要进行弹窗
                // 并且当前会议不是 同账号,其他端接听的情况, 才弹窗

                recentlyViewModel.getDingMeetingAndCallRecords()
                delay(200)

                // 判断PC是否在同一会议中
                dlgMeetJoinPwd?.dismiss()
                dlgMeetJoinPwd = null

//                isPCEnterMeeting(context)
                isPCEnterMeetingNew(context, meetCode, meetPwd)

            }
            // 会议号不存在
            JoinMeetModel.ERROR_CODE_NOT_EXIST -> {
                ToastUtils.showLong(R.string.starry_join_error_not_exist_check)
                dlgMeetJoinPwd?.yesButton?.let { Tools.setViewButtonEnable(it, true) }
            }

            // 人数满
            JoinMeetModel.ERROR_MEMBER_MUCH -> {
                ToastUtils.showLong(R.string.starry_join_error_member_over)
                dlgMeetJoinPwd?.yesButton?.let { Tools.setViewButtonEnable(it, true) }
            }

            // 会议已锁定
            JoinMeetModel.ERROR_CODE_CLOCK -> {
                ToastUtils.showLong(R.string.starry_join_error_clock)
                dlgMeetJoinPwd?.yesButton?.let { Tools.setViewButtonEnable(it, true) }
            }

            // 会议已结束
            JoinMeetModel.ERROR_CODE_OVER -> {
                ToastUtils.showLong(R.string.starry_join_error_over)
                dlgMeetJoinPwd?.yesButton?.let { Tools.setViewButtonEnable(it, true) }
            }

            // 密码错误
            JoinMeetModel.ERROR_CODE_PWD_WRONG -> {
                ToastUtils.showLong(R.string.starry_join_error_pwd_wrong)
                dlgMeetJoinPwd?.yesButton?.let { Tools.setViewButtonEnable(it, true) }
            }

            // 密码次数过多
            JoinMeetModel.ERROR_CODE_PWD_MUCH -> {
                ToastUtils.showLong(
                    String.format(
                        getString(R.string.starry_join_error_much),
                        JoinMeetModel.returnLeftTimes
                    )
                )
                dlgMeetJoinPwd?.yesButton?.let { Tools.setViewButtonEnable(it, true) }
            }

            else -> {
                ToastUtils.showLong(R.string.starry_join_error_no_network)
                dlgMeetJoinPwd?.dismiss()
                dlgMeetJoinPwd = null
                finish()
            }
        }
    }

    // 判断pc是否在会中，弹窗提示
    private fun isPCEnterMeeting(context: Context) {
        // 1先判断是否有正在进行的会议
        val model = JoinMeetModel.joinMeetingReturnData ?: JoinMeetData()
        val meetingCode = model.meetingCode

        // 用来标识自己是否在其他会议中，可用此字段来判断是否需要进行弹窗
        // 并且当前会议不是 同账号,其他端接听的情况, 才弹窗
        if (MeetingModel.isPCEnter && (meetingCode != MeetingModel.isPCEnterMeetingCode)) {
            showInMeetingDialog(context) {
                if (it) {
                    pcIsInMeetingPopup?.dismiss()
                    onJoinMeeting(context)
                } else {
                    pcIsInMeetingPopup?.dismiss()
                    finish()
                }
            }
        } else {
            onJoinMeeting(context)
        }
    }

    private fun isPCEnterMeetingNew(context: Context, meetCode: String, meetPwd: String) {
        // 1先判断是否有正在进行的会议
        // 用来标识自己是否在其他会议中，可用此字段来判断是否需要进行弹窗
        // 并且当前会议不是 同账号,其他端接听的情况, 才弹窗
        if (MeetingModel.isPCEnter && (meetCode != MeetingModel.isPCEnterMeetingCode)) {
            showInMeetingDialog(context) {
                if (it) {
                    lifecycleScope.launch {
//                        StarryRepository().joinMeeting(meetCode, meetPwd)
//                        delay(5)
//                        onJoinMeeting(context)
                        checkJoinMeetingRetCode(context, meetCode, meetPwd)
                        pcIsInMeetingPopup?.dismiss()
                    }
                } else {
                    pcIsInMeetingPopup?.dismiss()
                    finish()
                }
            }
        } else {
            lifecycleScope.launch {
//                StarryRepository().joinMeeting(meetCode, meetPwd)
//                delay(5)
//                onJoinMeeting(context)
                checkJoinMeetingRetCode(context, meetCode, meetPwd)
            }
        }
    }

    private suspend fun checkJoinMeetingRetCode(
        context: Context,
        meetCode: String,
        meetPwd: String
    ) {
        val ret = StarryRepository().joinMeeting(meetCode, meetPwd)
        when (ret) {
            // 开启会议
            MiaoHttpManager.STATUS_SUCCESS -> {
                onJoinMeeting(context)
            }

            // 会议号不存在
            JoinMeetModel.ERROR_CODE_NOT_EXIST -> {
                ToastUtils.showLong(R.string.starry_join_error_not_exist)
                finish()
            }

            // 人数满
            JoinMeetModel.ERROR_MEMBER_MUCH -> {
                ToastUtils.showLong(R.string.starry_join_error_member_over)
                finish()
            }

            // 会议已锁定
            JoinMeetModel.ERROR_CODE_CLOCK -> {
                ToastUtils.showLong(R.string.starry_join_error_clock)
                finish()
            }

            // 会议已结束
            JoinMeetModel.ERROR_CODE_OVER -> {
                ToastUtils.showLong(R.string.starry_join_error_over)
                finish()
            }

            else -> {
                ToastUtils.showLong(R.string.starry_join_error_no_network)
                finish()
            }
        }

    }

    // 加入会议
    private fun onJoinMeeting(context: Context) {

        val model = JoinMeetModel.joinMeetingReturnData ?: JoinMeetData()
        logI("${TAG}.onJoinMeeting.model=${model}")
        logI("${TAG}.onJoinMeeting.MeetingModel.isPCEnter=${MeetingModel.isPCEnter}")
        logI("${TAG}.onJoinMeeting.MeetingModel.isPCEnterMeetingCode=${MeetingModel.isPCEnterMeetingCode}")

        val name = UserPreferences.getInstance().user.name
        val subject = com.czur.cloud.ui.starry.meeting.baselib.utils.getString(
            R.string.starry_main_meeting_start_title,
            name
        )

        val meetingName = model.roomName ?: subject
        val roomNo = model.room ?: "0"

        val isCam = StarryPreferences.getInstance()?.lastCam ?: false
        val isMic = StarryPreferences.getInstance()?.lastMic ?: true

        val callInModel = StarryCallInModel()
        callInModel.apply {
            room_name = meetingName
            room = roomNo
            udid_from = ""
            callId = ""
            userid_from = ""
            nickname_from = ""
            headImage = ""
            isCamOpen = isCam
            isMicOpen = isMic
            userid = StarryPreferences.getInstance().userId
            czurId = StarryPreferences.getInstance().czurId
            accountNo = StarryPreferences.getInstance().accountNo
            agoraId = StarryPreferences.getInstance().starryUserinfoModel.id.toString()
        }

        // 会议中，需要先停止当前会议
        if (MeetingModel.isInMeeting.value == true) {
            LiveDataBus.get().with(StarryConstants.MEETING_REJOIN_STOP_FROM_WEB).value = true
            finish()
            return
        }

        // 用来标识自己是否在其他会议中，可用此字段来判断是否需要进行弹窗
        // 并且当前会议不是 同账号,其他端接听的情况, 才弹窗
//        val meetingCode = model.meetingCode
//        if (MeetingModel.isPCEnter && (meetingCode != MeetingModel.isPCEnterMeetingCode)) {
//            StarryCommonPopup.Builder(context)
//                .setTitle(getString(R.string.starry_popupwindow_title))
//                .setMessage(getString(R.string.starry_callin_dialog_msg))
//                .setPositiveTitle(getString(R.string.starry_callin_dialog_join))
//                .setNegativeTitle(getString(R.string.starry_callin_dialog_cancel))
//                .setOnPositiveListener { dialog, _ ->
//                    val i = Intent(context, MeetingMainActivity::class.java)
//                    i.putExtra(MeetingMainActivity.KEY_BOOT_MODEL, callInModel)
//                    i.putExtra(MeetingMainActivity.KEY_ROOM, roomNo)
//                    i.putExtra(MeetingMainActivity.KEY_BOOT_TYPE, MeetingMainActivity.BOOT_TPE_JOIN)
//                    ActivityUtils.startActivity(i)
//                    dialog?.dismiss()
//
//                    EventBus.getDefault()
//                        .post(StarryCommonEvent(EventType.STARRY_EQUIPMENTLIST_CHECK_ADD, ""))
//                    finish()
//                }
//                .setOnNegativeListener { dialog, _ ->
//                    dialog.dismiss()
//                    finish()
//                }
//                .setOnDismissListener {
//                    it.dismiss()
//                    finish()
//                }
//                .create()
//                .show()
//        } else {
        // 应该等待长连接链接成功后再加入会议
        var flag = ServiceUtils.isServiceRunning(NettyService::class.java)
        if (flag) {//长连接已连接
            joinMeeting(context, callInModel, roomNo)
        } else {// 长连接未连接
            launch(Dispatchers.IO) {
                while (!flag) {
                    delay(150)
                    flag = ServiceUtils.isServiceRunning(NettyService::class.java)
                }
                delay(300)
                joinMeeting(context, callInModel, roomNo)
            }

        }

//        }
    }

    private fun joinMeeting(context: Context, callInModel: StarryCallInModel, roomNo: String) {
        val i = Intent(context, MeetingMainActivity::class.java)
        i.putExtra(MeetingMainActivity.KEY_BOOT_MODEL, callInModel)
        i.putExtra(MeetingMainActivity.KEY_ROOM, roomNo)
        i.putExtra(MeetingMainActivity.KEY_BOOT_TYPE, MeetingMainActivity.BOOT_TPE_JOIN)
        ActivityUtils.startActivity(i)

        EventBus.getDefault()
            .post(StarryCommonEvent(EventType.STARRY_EQUIPMENTLIST_CHECK_ADD, ""))
        finish()
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.i(TAG, "onDestroy")
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }

        IndexActivity.isJumpToApp = false
        if (dlgMeetJoinCode != null) {
            dlgMeetJoinCode?.dismiss()
            dlgMeetJoinCode = null
        }
        if (dlgMeetJoinPwd != null) {
            dlgMeetJoinPwd?.dismiss()
            dlgMeetJoinPwd = null
        }

    }

    var pcIsInMeetingPopup: StarryCommonPopup? = null
    private fun showInMeetingDialog(context: Context, objects: (Boolean) -> Unit) {

        pcIsInMeetingPopup = StarryCommonPopup.Builder(context)
            .setTitle(getString(R.string.starry_popupwindow_title))
            .setMessage(getString(R.string.starry_callin_dialog_msg))
            .setPositiveTitle(getString(R.string.starry_callin_dialog_join))
            .setNegativeTitle(getString(R.string.starry_callin_dialog_cancel))
            .setOnPositiveListener { dialog, _ ->
                objects.invoke(true)
//                dialog.dismiss()
            }
            .setOnNegativeListener { dialog, _ ->
                objects.invoke(false)
                dialog.dismiss()
                finish()
            }
            .setOnDismissListener {
                it.dismiss()
                finish()
            }
            .create()
        pcIsInMeetingPopup?.show()



        dlgMeetJoinCode?.dismiss()
        dlgMeetJoinCode = null
    }

    override fun setRequestedOrientation(requestedOrientation: Int) {
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.O && isTranslucentOrFloating()) {
            Log.i("BlankActivityForJoinMeeting", "avoid calling setRequestedOrientation when Oreo.")
            return
        }
        super.setRequestedOrientation(requestedOrientation)
    }

    private fun isTranslucentOrFloating(): Boolean {
        var isTranslucentOrFloating = false
        try {
            val styleableRes = Class.forName("com.android.internal.R\$styleable")
                .getField("Window").get(null) as IntArray
            val ta: TypedArray = obtainStyledAttributes(styleableRes)
            val m: Method = ActivityInfo::class.java.getMethod(
                "isTranslucentOrFloating",
                TypedArray::class.java
            )
            m.isAccessible = true
            isTranslucentOrFloating = m.invoke(null, ta) as Boolean
            m.isAccessible = false
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return isTranslucentOrFloating
    }

    private fun fixOrientation(): Boolean {
        try {
            val field: Field = Activity::class.java.getDeclaredField("mActivityInfo")
            field.isAccessible = true
            val o: ActivityInfo = field.get(this) as ActivityInfo
            o.screenOrientation = -1
            field.isAccessible = false
            return true
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

}