package com.czur.cloud.ui.account;

import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Rect;
import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextWatcher;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.inputmethod.InputMethodManager;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentContainerView;
import androidx.lifecycle.Observer;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ServiceUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.blankj.utilcode.util.StringUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.clj.fastble.BleManager;
import com.clj.fastble.data.BleDevice;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.common.MD5Utils;
import com.czur.cloud.common.ShareSDKPlatforms;
import com.czur.cloud.common.ShareSDKUtils;
import com.czur.cloud.common.ThirdPartyCallback;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.LoginEvent;
import com.czur.cloud.event.TokenTimeOutEvent;
import com.czur.cloud.event.UpdateEvent;
import com.czur.cloud.model.ChannelModel;
import com.czur.cloud.model.CountryCode;
import com.czur.cloud.model.RegisterModel;
import com.czur.cloud.netty.observer.NettyService;
import com.czur.cloud.netty.observer.NettyUtils;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.FirstPreferences;
import com.czur.cloud.preferences.StarryPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.album.ImageDataSource;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.base.CzurCloudApplication;
import com.czur.cloud.ui.component.NoHintEditText;
import com.czur.cloud.ui.component.ProgressButton;
import com.czur.cloud.ui.component.dialog.SocialAccountDialog;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.home.IndexActivity;
import com.czur.cloud.ui.market.WebViewActivity;
import com.czur.cloud.ui.mirror.comm.FastBleOperationUtils;
import com.czur.cloud.ui.starry.common.StarryConstants;
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus;
import com.czur.cloud.ui.starry.meeting.dialog.RemindChoseCountryPopup;
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup;
import com.czur.cloud.ui.starry.utils.RomUtils;
import com.czur.cloud.util.AppClearUtils;
import com.czur.cloud.util.PermissionUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import io.realm.Realm;

/**
 * Created by Yz on 2018/3/5.
 * Email：<EMAIL>
 */
public class LoginActivity extends BaseActivity implements View.OnClickListener {

    public static final String FROM_FORGET_PASSWORD = "intentFromForgetPassword";
    public static final String KEY_FROM = "from";

    private TextView socialLoginBtn;
    private ProgressButton loginBtn;
    private SocialAccountDialog socialAccountDialog;
    private NoHintEditText loginAccountEdt;
    private NoHintEditText loginUserPasswordEdt;
    private TextView loginForgetPasswordBtn;
    private TextView loginNewUserRegisterBtn;
    private LinearLayout loginInputLl;

    private WeakHandler handler;
    private boolean accountHasContent = false;
    private boolean passwordHasContent = false;
    private UserPreferences userPreferences;
    private FirstPreferences firstPreferences;
    private String channel;
    private long exitTime = 0;
    private LinearLayout changeKeyboardLl;
    private ImageView backgroundImg;
    private Realm realm;
    private String userId;
    private CloudCommonPopup commonPopup;
    //    private long currentTimeMillis = 0;
    private long currentLoginTime;
    private MiaoHttpEntity<RegisterModel> loginEntity;
    private boolean checkPrivacy = true;
    private ImageDataSource imageDataSource;
    private StarryCommonPopup requestPermissionDialog;
    private boolean hasIntentToRequestPermission = false;
    long requestPermissionClickTime = 0;
    private ArrayList<CountryCode> countryList = new ArrayList<>();
    private CountryCode selectCountryCode;
    private RemindChoseCountryPopup choseCountryPopup;
    private String accountCountryCode;
    private ImageView loginBackBtn;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_login);

        CzurCloudApplication.isOtherLogin = true;

        if (BuildConfig.VERSION_CODE >= CZURConstants.REALM_UPDATE_VERSION){ // 408版本升级了数据库,需要清除用户所有信息
            if (UserPreferences.getInstance().isFirstInVersion408()){
                AppClearUtils.clearAllUserData(getApplication());
                UserPreferences.getInstance().setFirstInVersion408(false);
            }
        }

        initComponent();
        checkEventBusStickyMsg();
        registerEvent();
        createBottomSheetDialog();
        isThirdPartyTokenTimeOut();

        // 坐姿仪断开检测
        checkMirrorConnect();

        // Starry检查并断开长连接，登录后重新连接
        if (ServiceUtils.isServiceRunning(NettyService.class)) {
//            ServiceUtils.stopService(NettyService.class);
            NettyUtils.getInstance().stopNettyService();
//        EventBus.getDefault().post(new LogoutEvent(EventType.LOG_OUT));
        }

        showHideUI(true);
        setStatusBarBg();

        loginBackBtn = (ImageView) findViewById(R.id.account_back_btn);
        Animation animEnter = AnimationUtils.loadAnimation(this, R.anim.anim_enter);
        loginBackBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                backBtnClick();
            }
        });

        Animation anim_exit = AnimationUtils.loadAnimation(this, R.anim.anim_exit);
        LiveDataBus.get()
                .with(StarryConstants.LOGIN_SHOW_LOGIN_BTN, Boolean.class)
                .observe(this, new Observer<Boolean>() {
                    @Override
                    public void onChanged(Boolean aBoolean) {
                        showHideUI(true);
                    }
                });


    }

    private void checkEventBusStickyMsg() {//检查是否是通过快捷方式进入的,并且没有登陆,需要弹出toast
        UpdateEvent stickyEvent = EventBus.getDefault().getStickyEvent(UpdateEvent.class);
        if (stickyEvent != null) {
            if (stickyEvent.getEventType() == EventType.SHORTCUT_JOIN_MEET_NOLOGIN) {
                ToastUtils.showLong(R.string.starry_no_login);
                EventBus.getDefault().removeStickyEvent(stickyEvent);
            }
        }
    }

    private void backBtnClick() {
        showHideUI(true);

        // 收起软键盘
        InputMethodManager manager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        manager.hideSoftInputFromWindow(
                loginBackBtn.getWindowToken(),
                InputMethodManager.HIDE_NOT_ALWAYS);

        setStatusBarBg();
    }

    public void setStatusBarBg() {
        // 状态栏背景色设置
        Window window = getWindow();
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        // 设置状态栏的文字颜色 isLightMode：true，黑色； false，白色；
        BarUtils.setStatusBarLightMode(window, true);
        window.setStatusBarColor(Color.TRANSPARENT);
    }

    private void showHideUI(Boolean hideFlag) {
        int flag = hideFlag ? View.VISIBLE : View.GONE;
        findViewById(R.id.login_top_bar).setVisibility(View.GONE);

        socialLoginBtn.setVisibility(flag);
        socialLoginBtn.setEnabled(hideFlag);
        socialLoginBtn.setClickable(hideFlag);
    }

    private void isThirdPartyTokenTimeOut() {
//        if (isThirdPartyToken) {
//            Platform platform = ShareSDK.getPlatform(platName);
//            authorize(platform);
//        }
    }

    private void initComponent() {

        realm = Realm.getDefaultInstance();
        EventBus.getDefault().register(this);
        handler = new WeakHandler();
        userPreferences = UserPreferences.getInstance(this);
        firstPreferences = FirstPreferences.getInstance(this);
        channel = userPreferences.getChannel();
//        HttpManager httpManager = HttpManager.getInstance();
        userId = userPreferences.getUserId();
//        String platName = getIntent().getStringExtra("platName");
//        boolean isThirdPartyToken = getIntent().getBooleanExtra("isThirdPartyToken", false);

        loginInputLl = (LinearLayout) findViewById(R.id.login_input_ll);
        changeKeyboardLl = (LinearLayout) findViewById(R.id.change_keyboard_ll);
        backgroundImg = (ImageView) findViewById(R.id.background_img);
        socialLoginBtn = (TextView) findViewById(R.id.social_account_login_btn);
//        RelativeLayout socialAccountLoginDialog = (RelativeLayout) findViewById(R.id.social_account_login_dialog);
//        ImageView accountBackBtn = (ImageView) findViewById(R.id.account_back_btn);
//        ImageView loginUserHeadImg = (ImageView) findViewById(R.id.login_user_head_img);
        loginAccountEdt = (NoHintEditText) findViewById(R.id.login_user_name_edt);
        loginUserPasswordEdt = (NoHintEditText) findViewById(R.id.login_user_password_edt);
        loginForgetPasswordBtn = (TextView) findViewById(R.id.login_forget_password_btn);
        loginNewUserRegisterBtn = (TextView) findViewById(R.id.login_new_user_register_btn);
        loginBtn = (ProgressButton) findViewById(R.id.login_btn);

        socialLoginBtn.setVisibility(BuildConfig.IS_OVERSEAS ? View.GONE : View.VISIBLE);

        CheckBox checkBtn = findViewById(R.id.chk_privacy_policy);
        checkBtn.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {

            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                checkPrivacy = isChecked;
                checkLoginButtonToClick();
            }
        });
        TextView tv = findViewById(R.id.privacy_policy_text);
        setTextViewClick(tv);


    }


    private void registerEvent() {
        socialLoginBtn.setOnClickListener(this);
        loginBtn.setOnClickListener(this);
        loginBtn.setOnProgressFinishListener(onProgressFinish);
        loginBtn.setClickable(false);
        loginBtn.setSelected(false);
        loginForgetPasswordBtn.setOnClickListener(this);
        loginNewUserRegisterBtn.setOnClickListener(this);
        loginAccountEdt.addTextChangedListener(accountTextWatcher);
        loginUserPasswordEdt.addTextChangedListener(passwordTextWatcher);
        //设软键盘弹出监听
        setSoftInputVisibleListener();

    }

    /**
     * 确认删除用户信息
     */
    private void confirmToClearLastUserData(final MiaoHttpEntity<RegisterModel> entity, final boolean isThirdParty,
                                            final String openId, final String token,
                                            final String platformName, final String finalPlatName, final String refreshToken) {
        String currentUserId = entity.getBody().getId();
        if ((!StringUtils.equals(userId, currentUserId)) && userPreferences.isValidUser()) {
            CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(LoginActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON_YES_NO);
            builder.setTitle(getResources().getString(R.string.prompt));
            String title = String.format(getString(R.string.confirm_to_clear_account), userPreferences.getUserName());
            builder.setMessage(title);
            builder.setOnPositiveListener((dialog, which) -> {
                if (commonPopup != null) {
                    commonPopup.dismiss();
                }
                clearLastUserDataAndSetCurrentData(entity, isThirdParty, openId, token, platformName, finalPlatName, refreshToken);
                commonPopup = null;
            });
            builder.setOnNegativeListener((dialog, which) -> {
                // 清除上一账户信息，点击取消，恢复登录按钮
                loginBtn.reset();
                dialog.dismiss();
                commonPopup = null;
            });
            commonPopup = builder.create();
            commonPopup.setCancelable(false);
            commonPopup.show();


        } else {
            setCurrentUserData(entity, isThirdParty, openId, token, platformName, finalPlatName, refreshToken);
        }

    }

    /**
     * 如果和上次userId不一样 就清除sp  data/file并且设置用户信息sp
     */
    private void clearLastUserDataAndSetCurrentData(final MiaoHttpEntity<RegisterModel> entity, final boolean isThirdParty, final String openId, final String token, final String platformName, final String finalPlatName, final String refreshToken) {

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                logI("clean last user file and sp");
                String filePath = getFilesDir() + File.separator + userPreferences.getUserId();
                FileUtils.deleteAllInDir(new File(filePath));
                runOnUiThread(() -> {
                    AppClearUtils.clearAllUserData(getApplication());

                    setCurrentUserData(entity, isThirdParty, openId, token, platformName, finalPlatName, refreshToken);
                    FirstPreferences.getInstance(LoginActivity.this).setIsFirstAuraPrompt(true);
                });
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });
    }

    /**
     * 显示登录成功并且跳转到主页
     */
    private void showLoginSuccessAndGoIndex(boolean isThirdParty) {
        if (isThirdParty) {
            EventBus.getDefault().post(new LoginEvent(EventType.THIRD_PARTY_LOGIN_IN));
        } else {
            EventBus.getDefault().post(new LoginEvent(EventType.LOG_IN));
        }

        Intent intent = new Intent(LoginActivity.this, IndexActivity.class);
        intent.putExtra("needSync", true);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
        ActivityUtils.startActivity(intent);
        ActivityUtils.finishActivity(this);
    }

    /**
     * 存储当前用户信息到SP
     */
    private void setCurrentUserData(MiaoHttpEntity<RegisterModel> entity, boolean isThirdParty, String openId, String token, String platformName, String finalPlatName, String refreshToken) {
        userPreferences.setUser(entity.getBody());

        if (isThirdParty) {
            userPreferences.setIsThirdParty(true);
            userPreferences.setThirdPartyOpenid(openId);
            userPreferences.setThirdPartyToken(token);
            userPreferences.setThirdPartyPlatName(platformName);
            userPreferences.setServicePlatName(finalPlatName);
            userPreferences.setThirdPartyRefreshToken(refreshToken);
        } else {
            userPreferences.setLoginUserName(Objects.requireNonNull(loginAccountEdt.getText()).toString());
            userPreferences.setLoginPassword(Objects.requireNonNull(loginUserPasswordEdt.getText()).toString());
        }

        userPreferences.setCountryCode(entity.getBody().getCountryCode());

        String mobile = userPreferences.getUserMobile();
        String userid = userPreferences.getUserId();
        if (mobile == null) {
            mobile = "";
        }
        // 获取starry用户信息
        AppClearUtils.getStarryUserInfo();
        AppClearUtils.syncStarryUserInfo();


        StarryPreferences.getInstance().setHasShowedPermissionsDialog(false);
        StarryPreferences.getInstance().setIsSpecialXiaomi(false);
        StarryPreferences.getInstance().setAccountNo(mobile);
        StarryPreferences.getInstance().setCzurId(userid);

        userPreferences.setIsUserLogin(true);
        showLoginSuccessAndGoIndex(isThirdParty);

    }

    private void useToolsRequestPermission() {
        requestPermissionClickTime = System.currentTimeMillis();
        final boolean[] isRefuseSecondPermission = {false};//当时点击了永久拒绝

        PermissionUtils.permission(PermissionUtil.getStoragePermission())
                .rationale(new PermissionUtils.OnRationaleListener() {
                    @Override
                    public void rationale(@NonNull UtilsTransActivity activity, @NonNull ShouldRequest shouldRequest) {
                        //解释后是否需要继续弹出请求权限弹窗
                        shouldRequest.again(true);
                        isRefuseSecondPermission[0] = true;
                    }
                })

                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> granted) {
                        hasIntentToRequestPermission = false;
                        //权限通过
                        if (requestPermissionDialog != null && requestPermissionDialog.isShowing()) {
                            requestPermissionDialog.dismiss();
                        }

                    }

                    @Override
                    public void onDenied(@NonNull List<String> deniedForever, @NonNull List<String> denied) {
                        //权限拒绝
                        if (deniedForever.size() > 0) {//永久拒绝
                            if (isRefuseSecondPermission[0]) {
                                //当时点击永久拒绝的时候不做处理
                            } else {
                                if ((System.currentTimeMillis() - requestPermissionClickTime) < 800) {//500ms之内 主观认为是系统返回,而非用户点击
                                    if (RomUtils.INSTANCE.isVivo()) {
                                        RomUtils.PermissionPageManagement.INSTANCE.goIntentSetting(LoginActivity.this);
                                    } else {
                                        RomUtils.PermissionPageManagement.INSTANCE.goToSetting(LoginActivity.this);
                                    }
                                    hasIntentToRequestPermission = true;
                                } else {
                                    showExplainForPermission();
                                }
                            }

                        } else if (denied.size() > 0) {
                            //第一次拒绝

                        }
                    }
                })
                .explain(new PermissionUtils.OnExplainListener() {
                    @Override
                    public void explain(@NonNull UtilsTransActivity activity, @NonNull List<String> denied, @NonNull ShouldRequest shouldRequest) {
                        //第一次拒绝过了, 现在进行第二次权限弹窗,提示你需要弹出解释窗了
                        shouldRequest.start(true);
                    }
                }).request();
    }

    private void showExplainForPermission() {
        if (PermissionUtils.isGranted(PermissionUtil.getStoragePermission())) {
            return;
        }

        if (requestPermissionDialog != null && requestPermissionDialog.isShowing()) {
            requestPermissionDialog.dismiss();
        }//Manifest.permission.READ_EXTERNAL_STORAGE,
        //没权限时候
        String str = "";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            str = getString(R.string.starry_photo_permission_explain);
        } else {
            str = getString(R.string.starry_storage_permission_explain);
        }

        requestPermissionDialog = new StarryCommonPopup.Builder(this, CloudCommonPopupConstants.COMMON_TWO_BUTTON)
                .setTitle(getString(R.string.starry_popupwindow_title))
                .setMessage(str)
                .setPositiveTitle(getString(R.string.starry_drawoverlays_msg_open))
                .setNegativeTitle(getString(R.string.starry_drawoverlays_msg_cancel))
                .setOnPositiveListener(new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        useToolsRequestPermission();
                        dialog.dismiss();
                    }
                })
                .setOnNegativeListener(new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                    }
                })
                .create();
        requestPermissionDialog.show();


    }

    private void createBottomSheetDialog() {
        socialAccountDialog = new SocialAccountDialog(this, socialAccountDialogOnClickListener);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.social_account_login_btn:
                socialAccountDialog.show();
                break;
            case R.id.login_new_user_register_btn:
                ActivityUtils.startActivity(RegisterActivity.class);
                break;
            case R.id.login_forget_password_btn:
                ActivityUtils.startActivity(ForgetPasswordActivity.class);
                break;
            case R.id.login_btn:
                KeyboardUtils.hideSoftInput(this);
                userLogin();

                break;
            default:
                break;
        }
    }

    private void loginFailedDelay(final int failedText) {
        new Thread(() -> {
            try {
                long sleepTime;
                if (System.currentTimeMillis() - currentLoginTime < 1000) {
                    sleepTime = 1000 - (System.currentTimeMillis() - currentLoginTime);
                } else {
                    sleepTime = 1;
                }
                Thread.sleep(sleepTime);
                runOnUiThread(() -> {
                    showMessage(failedText);
                    loginBtn.stopLoading();
                });
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }).start();
    }

    private void loginSuccessDelay() {
        new Thread(() -> {
            try {
                long sleepTime;
                if (System.currentTimeMillis() - currentLoginTime < 1000) {
                    sleepTime = 1000 - (System.currentTimeMillis() - currentLoginTime);
                } else {
                    sleepTime = 1;
                }
                Thread.sleep(sleepTime);
                runOnUiThread(() -> loginBtn.stopLoadingSuccess());
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }).start();
    }

    private ProgressButton.OnProgressFinish onProgressFinish = new ProgressButton.OnProgressFinish() {
        @Override
        public void onFinish() {
            confirmToClearLastUserData(loginEntity, false, null, null, null, null, null);
        }
    };

    /**
     * 登录前置校验
     */
    private void userLogin() {

        currentLoginTime = System.currentTimeMillis();
        final String mobileMail = Objects.requireNonNull(loginAccountEdt.getText()).toString();
        final String pwd = Objects.requireNonNull(loginUserPasswordEdt.getText()).toString();
        if (mobileMail.length() == 0) {
            showLongMessage(R.string.toast_mobile_mail_entity);
        } else if (pwd.length() == 0) {
            showLongMessage(R.string.login_alert_password_empty);
        } else if (pwd.length() <= 5) {     // 登录密码不做限制
            showLongMessage(R.string.login_alert_pwd_length);
        } else if (isValidatorLoginName(mobileMail)) {
            showLongMessage(R.string.toast_format_wrong);
        } else if (!checkPrivacy) {
            showLongMessage("请阅读并同意《隐私政策》");
        } else {
            if (android.text.TextUtils.isEmpty(channel)) {
                getChannel(null, null, null, false, mobileMail, pwd, null);
            } else {
                loginRequest(mobileMail, pwd, true);
            }
        }


    }

    /**
     * 登录
     */
    private void loginRequest(final String mobileMail, String pwd, final boolean hasChannel) {
        if (mobileMail.length() == 0) {
            loginFailedDelay(R.string.request_failed_alert);
            return;
        }

        final String password = MD5Utils.md5(pwd);
        HttpManager.getInstance().requestPassport().
                login(CZURConstants.CLOUD_ANDROID,
                        userPreferences.getIMEI(),
                        userPreferences.getChannel(),
                        mobileMail,
                        password,
                        RegisterModel.class,
                        new MiaoHttpManager.Callback<RegisterModel>() {
                            @Override
                            public void onStart() {
                                if (hasChannel) {
                                    loginBtn.startLoading();
                                }
                            }

                            @Override
                            public void onResponse(MiaoHttpEntity<RegisterModel> entity) {
                                loginEntity = entity;
                                accountCountryCode = loginEntity.getBody().getCountryCode();
                                loginSuccessDelay();
                            }

                            @Override
                            public void onFailure(MiaoHttpEntity<RegisterModel> entity) {
                                if (entity.getCode() == MiaoHttpManager.STATUS_INVALID_USER_OR_PASSWORD) {
                                    loginFailedDelay(R.string.toast_error);
//                    showMessage(R.string.toast_error);
                                } else {
                                    loginFailedDelay(R.string.request_failed_alert);
//                    showMessage(R.string.request_failed_alert);
                                }
                            }

                            @Override
                            public void onError(Exception e) {
                                loginFailedDelay(R.string.request_failed_alert);
//                loginBtn.stopLoading();
//                showMessage(R.string.request_failed_alert);
                            }
                        });
    }

    /**
     * 社交登录dialog 点击事件监听
     */
    private SocialAccountDialog.SocialAccountDialogOnClickListener socialAccountDialogOnClickListener = viewId -> {
        switch (viewId) {
            case R.id.weixin_account:
                authorize(ShareSDKPlatforms.WECHAT);
                break;
            case R.id.qq_account:
                authorize(ShareSDKPlatforms.QQ);
                break;
            case R.id.weibo_account:
                authorize(ShareSDKPlatforms.WEIBO);
                break;
            default:
                break;
        }
    };

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case THIRD_TOKEN_TIME_OUT_TO_LOGIN:
                if (event instanceof TokenTimeOutEvent) {
                    TokenTimeOutEvent tokenTimeOutEvent = (TokenTimeOutEvent) event;
                    authorize(ShareSDKUtils.INSTANCE.getPlatform(tokenTimeOutEvent.getPlatName()));
                }
                break;

        }
    }

    /**
     * 授权
     */
    private void authorize(ShareSDKPlatforms plat) {
        dismissBottomSheet();
        ShareSDKUtils.INSTANCE.thirdPartyLogin(plat, channel, new ThirdPartyCallback() {
            @Override
            public void loginByThirdParty(@NotNull String platformName, @NotNull String openId, @NotNull String token, @NotNull String refreshToken) {
                LoginActivity.this.loginByThirdParty(platformName, openId, token, refreshToken);
            }

            @Override
            public void getChannel(@NotNull String userId, @NotNull String token, @NotNull String platformNname, boolean isThirdParty,
                                   @org.jetbrains.annotations.Nullable String mobileMail, @org.jetbrains.annotations.Nullable String pwd, @NotNull String refreshToken) {
                LoginActivity.this.getChannel(userId, token, platformNname, isThirdParty, mobileMail, pwd, refreshToken);
            }

            @Override
            public void showLongMessage(@NotNull String message) {
                LoginActivity.this.showLongMessage(message);
            }

            @Override
            public void showMessage(int messageRes) {
                LoginActivity.this.showMessage(messageRes);
            }
        });
    }

    /**
     * 获取channel
     */
    private void getChannel(final String userId, final String token, final String platformNname, final boolean isThirdParty, final String mobileMail, final String pwd, final String refreshToken) {

        HttpManager.getInstance().request().channel(ChannelModel.class, new MiaoHttpManager.Callback<ChannelModel>() {
            @Override
            public void onStart() {
                if (!isThirdParty) {
                    loginBtn.startLoading();
                }
            }

            @Override
            public void onResponse(MiaoHttpEntity<ChannelModel> entity) {
                userPreferences.setChannel(entity.getBody().getChannel());
                userPreferences.setEndpoint(entity.getBody().getEndPoint());
                if (isThirdParty) {
                    loginByThirdParty(platformNname, userId, token, refreshToken);
                } else {
                    loginRequest(mobileMail, pwd, false);
                }
            }

            @Override
            public void onFailure(MiaoHttpEntity<ChannelModel> entity) {
                loginFailedDelay(R.string.request_failed_alert);
//                loginBtn.stopLoading();
//                showMessage(R.string.request_failed_alert);
            }

            @Override
            public void onError(Exception e) {
                loginFailedDelay(R.string.request_failed_alert);
            }
        });
    }

    /**
     * 隐藏底部sheet
     */
    private void dismissBottomSheet() {
        if (socialAccountDialog != null) {
            socialAccountDialog.dismiss();
        }
    }

    /**
     * 第三方登录
     */
    private void loginByThirdParty(final String platformName, final String openId, final String token, final String refreshToken) {
        String platName = "";

        if (platformName.equals("SinaWeibo")) {
            platName = CZURConstants.WEIBO;
            //微博授权
        } else if (platformName.equals("QQ")) {
            platName = CZURConstants.QQ;

        } else if (platformName.equals("Wechat")) {
            //微信授权
            platName = CZURConstants.WECHAT;
        }
        final String finalPlatName = platName;
        HttpManager.getInstance().requestPassport().thirdPartyLogin(userPreferences.getChannel(), userPreferences.getIMEI()
                , CZURConstants.CLOUD_ANDROID
                , platName, token, openId, RegisterModel.class, new MiaoHttpManager.Callback<RegisterModel>() {
                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<RegisterModel> entity) {

                        hideProgressDialog();
                        RegisterModel registerModel = entity.getBody();
                        if (!registerModel.isActive()) {
                            Intent intent = new Intent(LoginActivity.this, ThirdPartyBindActivity.class);
                            intent.putExtra("thirdPartyToken", token);
                            intent.putExtra("thirdPartyOpenId", openId);
                            intent.putExtra("thirdPartyPlatName", platformName);
                            intent.putExtra("thirdPartyRefreshToken", refreshToken);
                            intent.putExtra("platName", finalPlatName);
                            intent.putExtra("userId", entity.getBody().getId());
                            ActivityUtils.startActivity(intent);
                        } else {
                            confirmToClearLastUserData(entity, true, openId, token, platformName, finalPlatName, refreshToken);
                        }
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<RegisterModel> entity) {
                        hideProgressDialog();
                        if (entity.getCode() == MiaoHttpManager.STATUS_INVALID_USER_OR_PASSWORD) {
                            showMessage(R.string.toast_error);
                        } else {
                            showMessage(R.string.request_failed_alert);
                        }
                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
    }

    /**
     * 设置键盘弹出监听
     */
//    private boolean mKeyboardUp = false;
    private void setSoftInputVisibleListener() {
        changeKeyboardLl.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                Rect r = new Rect();
                //r will be populated with the coordinates of your view that area still visible.
                changeKeyboardLl.getWindowVisibleDisplayFrame(r);

                int screenHeight = changeKeyboardLl.getRootView().getHeight();

                // r.bottom is the position above soft keypad or device button.
                // if keypad is shown, the r.bottom is smaller than that before.
                int keypadHeight = screenHeight - r.bottom;

                ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) changeKeyboardLl.getLayoutParams();

                if (keypadHeight > screenHeight * 0.15) { // 0.15 ratio is perhaps enough to determine keypad height.
                    // keyboard is opened
                    params.setMargins(0, 0, 0, keypadHeight);
                    backgroundImg.setTranslationY(-keypadHeight);
                }
                else {
                    // keyboard is closed
                    params.setMargins(0, 0, 0, 0);
                    backgroundImg.setTranslationY(0);
                }
                changeKeyboardLl.setLayoutParams(params);
            }
        });

    }

    private class KeyBoardActionRunnable implements Runnable {

        private int visibility;
        private int remainHeight;

        public KeyBoardActionRunnable(int visibility, int remainHeight) {
            this.visibility = visibility;
            this.remainHeight = remainHeight;
        }

        @Override
        public void run() {

            if (loginInputLl.getVisibility() == View.VISIBLE) {
                socialLoginBtn.setVisibility(BuildConfig.IS_OVERSEAS ? View.GONE : visibility);
            }

            if (visibility == View.GONE) {

                backgroundImg.setTranslationY(-remainHeight);
                changeKeyboardLl.setTranslationY(-remainHeight);
//                layoutParams.setMargins(0, 0, 0, 2000);
            } else {
                backgroundImg.setTranslationY(0);
                changeKeyboardLl.setTranslationY(0);
//                layoutParams.setMargins(0, 0, 0, 0);
            }
        }

    }

    private TextWatcher accountTextWatcher = new TextWatcher() {

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            accountHasContent = s.length() > 0;
            checkLoginButtonToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            accountHasContent = s.length() > 0;
            checkLoginButtonToClick();
        }
    };
    private TextWatcher passwordTextWatcher = new TextWatcher() {

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            passwordHasContent = s.length() > 0;
            checkLoginButtonToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            passwordHasContent = s.length() > 0;
            checkLoginButtonToClick();

        }
    };

    /**
     * 检查登录按钮是否可以点击
     */
    private void checkLoginButtonToClick() {
//        if (accountHasContent && passwordHasContent) {
        if (accountHasContent && passwordHasContent && checkPrivacy) {
            loginBtn.setBackgroundResource(R.drawable.btn_rec_5_bg_with_blue);
            loginBtn.setClickable(true);
        } else {
            loginBtn.setBackgroundResource(R.drawable.btn_rec_5_bg_with_gray_eb);
            loginBtn.setClickable(false);
        }
    }

    @Override
    public void onBackPressed() {
            exitApp();
    }

    /**
     * 退出App
     */
    @Override
    protected void onDestroy() {
        super.onDestroy();
//        KeyboardUtils.unregisterSoftInputChangedListener(this);
        realm.close();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }

        AppClearUtils.stopScreenNotify();
    }

    private void exitApp() {

        if ((System.currentTimeMillis() - exitTime) > 2000) {
            showMessage(R.string.confirm_exit);
            exitTime = System.currentTimeMillis();
        } else {
            super.onBackPressed();
        }
    }

    private void setTextViewClick(TextView mTextView) {

        String str = new String("请您在使用成者CZUR前仔细阅读并同意《隐私政策》");
        // SpannableStringBuilder 用法
        SpannableStringBuilder spannableBuilder = new SpannableStringBuilder(str);

        // 单独设置字体颜色
        ForegroundColorSpan colorSpan = new ForegroundColorSpan(Color.parseColor("#3072F6"));
        spannableBuilder.setSpan(colorSpan, 19, str.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        // 在设置点击事件、同时设置字体颜色
        ClickableSpan clickableSpanTwo = new ClickableSpan() {
            @Override
            public void onClick(View view) {
//                Toast.makeText(LoginActivity.this, "隐私政策", Toast.LENGTH_SHORT).show();
                Intent intent = new Intent(LoginActivity.this, WebViewActivity.class);
                intent.putExtra("title", getString(R.string.user_privacy));
                intent.putExtra("url", getString(R.string.privacy_policy_url));
                ActivityUtils.startActivity(intent);
            }

            @Override
            public void updateDrawState(TextPaint paint) {
                paint.setColor(Color.parseColor("#3072F6"));
                // 设置下划线 true显示、false不显示
                paint.setUnderlineText(false);
            }
        };
        spannableBuilder.setSpan(clickableSpanTwo, 19, str.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        // 不设置点击不生效
        mTextView.setMovementMethod(LinkMovementMethod.getInstance());
        mTextView.setText(spannableBuilder);
        // 去掉点击后文字的背景色
        mTextView.setHighlightColor(Color.parseColor("#00000000"));

    }

    private void checkMirrorConnect() {
        BleDevice bleDevice = FastBleOperationUtils.getBleDevice();
        if (bleDevice != null) {
            logI("LoginActivity.checkMirrorConnect======账号登出！");
            if (BleManager.getInstance().isConnected(bleDevice)) {
                BleManager.getInstance().disconnect(bleDevice);
            }
        }
        BleManager.getInstance().disconnectAllDevice();

    }

    @Override
    protected void onResume() {
        super.onResume();
        if (hasIntentToRequestPermission) {
            showExplainForPermission();
        }
    }

    //    setCurrentUserData
//在此方法之前进行弹窗,设置成功后,在写入本地
    private void showChoseCountryPopup(View.OnClickListener onClickListener) {
        choseCountryPopup = new RemindChoseCountryPopup.Builder(this)
                .setTitle(getString(R.string.starry_popupwindow_title))
                .setMessage(getString(R.string.user_location_country_explain))
                .setPositiveTitle(getString(R.string.confirm))
                .setCountryList(countryList)
                .setOnPositiveListener(new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {

                        String packageName = AppUtils.getAppPackageName();
                        if (which != -1) {//-1情况没点击选择, 使用默认
                            selectCountryCode = countryList.get(which);
                        }

                        updateCountryCode(selectCountryCode.getCountryCode(), new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {

                                if (choseCountryPopup != null && choseCountryPopup.isShowing()) {
                                    choseCountryPopup.dismiss();
                                }
                                onClickListener.onClick(v);
//                                setCurrentUserData();
                            }
                        });
                    }
                })
                .create();

        choseCountryPopup.show();
    }


    // 更新国家代码
    private void updateCountryCode(String countryCode, View.OnClickListener onClickListener) {
        HttpManager.getInstance().requestPassport().updateCountry(
                userPreferences.getIMEI(),
                CZURConstants.CLOUD_ANDROID,
                userPreferences.getChannel(),
                loginEntity.getBody().getId(),
                loginEntity.getBody().getToken(),
                loginEntity.getBody().getId(),
                "",
                countryCode,
                String.class,
                new MiaoHttpManager.Callback<String>() {
                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        onClickListener.onClick(new View(LoginActivity.this));
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        ToastUtils.showShort(R.string.eshare_connect_fail);
                    }

                    @Override
                    public void onError(Exception e) {
                        ToastUtils.showShort(R.string.eshare_connect_fail);
                    }
                }
        );
    }

}
