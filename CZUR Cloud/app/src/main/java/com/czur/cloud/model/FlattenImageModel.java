package com.czur.cloud.model;

public class FlattenImageModel {


    private String id;
    private String url;
    private String ossKey;
    private Long originalSize;
    private Long flattenSize;
    private String ossBigKey;
    private String ossBigKeyUrl;
    private String ossSmallKey;
    private String ossSmallKeyUrl;

    private Long seqNum;


    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getOssKey() {
        return ossKey;
    }

    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }

    public Long getOriginalSize() {
        return originalSize;
    }

    public void setOriginalSize(Long originalSize) {
        this.originalSize = originalSize;
    }

    public Long getFlattenSize() {
        return flattenSize;
    }

    public void setFlattenSize(Long flattenSize) {
        this.flattenSize = flattenSize;
    }

    public String getOssSmallKey() {
        return ossSmallKey;
    }

    public void setOssSmallKey(String ossSmallKey) {
        this.ossSmallKey = ossSmallKey;
    }

    public String getOssSmallKeyUrl() {
        return ossSmallKeyUrl;
    }

    public void setOssSmallKeyUrl(String ossSmallKeyUrl) {
        this.ossSmallKeyUrl = ossSmallKeyUrl;
    }

    public Long getSeqNum() {
        return seqNum;
    }

    public void setSeqNum(Long seqNum) {
        this.seqNum = seqNum;
    }

    public String getOssBigKey() {
        return ossBigKey;
    }

    public void setOssBigKey(String ossBigKey) {
        this.ossBigKey = ossBigKey;
    }

    public String getOssBigKeyUrl() {
        return ossBigKeyUrl;
    }

    public void setOssBigKeyUrl(String ossBigKeyUrl) {
        this.ossBigKeyUrl = ossBigKeyUrl;
    }
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

}
