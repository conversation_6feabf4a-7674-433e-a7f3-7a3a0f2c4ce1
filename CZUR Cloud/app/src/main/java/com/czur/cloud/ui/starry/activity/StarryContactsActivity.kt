package com.czur.cloud.ui.starry.activity

import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import android.util.Log
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ActivityUtils
import com.czur.cloud.R
import com.czur.cloud.event.BaseEvent
import com.czur.cloud.event.EventType
import com.czur.cloud.ui.starry.adapter.StarryContactAdapter
import com.czur.cloud.ui.starry.base.StarryNewBaseActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.component.LetterViewNew
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.model.StarryAddressBookModel
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.ui.starry.viewmodel.StarryContactViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import com.czur.czurutils.log.logI
import kotlinx.android.synthetic.main.starry_activity_contacts.contacts_no_contact
import kotlinx.android.synthetic.main.starry_activity_contacts.contacts_search_rl
import kotlinx.android.synthetic.main.starry_activity_contacts.letter_view
import kotlinx.android.synthetic.main.starry_activity_contacts.recently_bottom_msg_coount
import kotlinx.android.synthetic.main.starry_activity_contacts.recycler_view_contacts
import kotlinx.android.synthetic.main.starry_activity_contacts.tv_sticky_header_view
import kotlinx.android.synthetic.main.starry_layout_top_bar.user_add_btn
import kotlinx.android.synthetic.main.starry_layout_top_bar.user_back_btn
import kotlinx.android.synthetic.main.starry_layout_top_bar.user_detail_btn
import kotlinx.android.synthetic.main.starry_layout_top_bar.user_title
import kotlinx.android.synthetic.main.starry_layout_top_bar.user_title_icon
import kotlinx.android.synthetic.main.starry_layout_top_bar.user_top_btn_detail
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 */
class StarryContactsActivity : StarryNewBaseActivity() {

    override fun getLayout(): Int = R.layout.starry_activity_contacts

    private var mLastClickTime: Long = 0

    //    private val viewModel by lazy { StarryViewModel() }
    private val viewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryViewModel::class.java)
    }

    private val contactViewModel by lazy {
        ViewModelProvider(
            StarryActivity.mainActivity ?: this
        ).get(StarryContactViewModel::class.java)
    }

    private val linearLayoutManager by lazy { LinearLayoutManager(this) }
    private val mAdapter by lazy { StarryContactAdapter(this) }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {

        when (event.eventType) {
            EventType.STARRY_CONTACT_ADD,
            EventType.STARRY_CONTACT_DEL,
            EventType.STARRY_CONTACT_EDIT,
            EventType.STARRY_CONTACT_ADDTO -> {
                viewModel.getEnterpriseMembers()
            }
            EventType.STARRY_COMPANY_EXIT -> {
                finish()
            }
            else -> {
            }
        }
    }

    override fun initViews() {
        super.initViews()
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }

        contactViewModel.currentFromType = intent.getIntExtra(StarryConstants.INTENT_FROM_TYPE,0);
//        contactViewModel.currentFromType = StarryConstants.INTENT_FROM_TYPE_MEETING
//        contactViewModel.currentFromType = StarryConstants.INTENT_FROM_TYPE_COMPANY

        // top bar
        user_back_btn?.singleClick {
            ActivityUtils.finishActivity(this)
        }

        user_add_btn?.visibility = View.GONE
        user_detail_btn?.visibility = View.GONE
        user_title_icon?.visibility = View.GONE
        user_top_btn_detail?.visibility = View.GONE

        contacts_search_rl?.visibility = View.VISIBLE   // 有无联系人都显示试搜索框
        recycler_view_contacts?.visibility = View.GONE
        contacts_no_contact?.visibility = View.GONE
//        letter_view?.visibility = View.GONE
        recently_bottom_msg_coount?.visibility = View.GONE

        // Title
        val title = intent.getStringExtra(StarryConstants.STARRY_USER_TITLE)
        user_title.text = title

        val userType = viewModel.getCurrentUserType()
        if (userType == StarryConstants.STARRY_USER_TYPE_CONTACT) {
            user_add_btn?.visibility = View.VISIBLE
        } else {
            user_top_btn_detail?.visibility = View.VISIBLE
            user_title_icon?.visibility = View.VISIBLE
        }

        var list = arrayListOf<StarryAddressBookModel>()
        if (!viewModel.currentContactsList.value.isNullOrEmpty()) {
            list = viewModel.currentContactsList.value?.toMutableList() as ArrayList
        }
        mAdapter.setListContacts(list)
        list = mAdapter.getListContacts()
        viewModel.currentContactsSortList.postValue(list)
//        logI("StarryContactsActivity.viewModel.currentContactsSortList=${list}")

        // list
        recycler_view_contacts?.apply {
            setHasFixedSize(true)
            layoutManager = linearLayoutManager
            adapter = mAdapter
        }
        recycler_view_contacts?.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val stickview = recyclerView.findChildViewUnder(0f, 0f)
//                logI("StarryContactsActivity.onScrolled,stickview=${stickview?.contentDescription},tv_sticky_header_view=${tv_sticky_header_view?.text}")
                if (stickview != null && stickview.contentDescription != null) {
                    if (tv_sticky_header_view?.text != stickview.contentDescription) {
                        tv_sticky_header_view?.text = stickview.contentDescription
                        viewModel.currentIndexDescription.value =
                            stickview.contentDescription as String?
                    }
                }
            }
        })

        mAdapter.setOnItemPickListener(object : StarryContactAdapter.OnItemPickListener {
            override fun onItemPick(position: Int, model: StarryAddressBookModel) {
                if (viewModel.clickNoNetwork()) {
                    return
                }

                // 双击太快过滤
                if (SystemClock.elapsedRealtime() - mLastClickTime < 800) {
                    return
                }
                mLastClickTime = SystemClock.elapsedRealtime()

//                // 判断是否为被删除人员
//                contactViewModel.getContactDetailV2(model.id){
//                    if (contactViewModel.contactDetail.value?.meetingNo?.isBlank() == true) {
//                        viewModel.getEnterpriseMembers()
//                        ToastUtils.showLong(R.string.starry_contact_deleted)
//                    }else{
//                        intent = Intent(applicationContext, StarryContactDetailActivity::class.java)
//                        intent.putExtra(StarryConstants.STARRY_USER_MODEL, model)
//                        intent.putExtra(StarryConstants.STARRY_USER_TYPE, userType)
//                        ActivityUtils.startActivity(intent)
//                    }
//                }
                intent = Intent(applicationContext, StarryContactDetailActivity::class.java)
                intent.putExtra(StarryConstants.STARRY_USER_MODEL, model)
                intent.putExtra(StarryConstants.STARRY_USER_TYPE, userType)
                ActivityUtils.startActivity(intent)
            }
        })

        // 详情
        user_top_btn_detail?.singleClick {
            logI("StarryContactsActivity.user_top_btn_detail")
            if (viewModel.clickNoNetwork()) {
                return@singleClick
            }
            val intent = Intent(this, StarryCompanyDetailActivity::class.java)
            intent.putExtra(StarryConstants.STARRY_USER_MODEL, viewModel.currentCompanyModel.value)
            intent.putExtra(
                StarryConstants.STARRY_USER_TYPE,
                StarryConstants.STARRY_USER_TYPE_COMPANY
            )
            ActivityUtils.startActivity(intent)
        }

        // 添加
        user_add_btn?.singleClick {
            logI("StarryContactsActivity.user_add_btn")
            if (viewModel.clickNoNetwork()) {
                return@singleClick
            }
            val intent = Intent(applicationContext, StarryContactAddActivity::class.java)
            ActivityUtils.startActivity(intent)
        }

        // 字母索引定位
        if (mAdapter.getCharacterList().isNotEmpty()) {
            letter_view?.initViewNew(mAdapter.getCharacterList())
            val description = mAdapter.getCharacterList()[0]
            viewModel.currentIndexDescription.value = description
//            letter_view?.setSelectedChar(description)
        }
        letter_view?.setCharacterListener(object : LetterViewNew.CharacterClickListener {
            override fun clickCharacter(character: String?) {
//                logI("StarryContactsActivity.clickCharacter.character=${character}")
                character?.let {
//                    logI("StarryContactsActivity.clickCharacter.character=${it}")
                    mAdapter.getScrollPosition(it)
                }?.let {
//                    logI("StarryContactsActivity.clickCharacter.postion=${it}")
                    val posLast = viewModel.currentIndexDescription.value?.let { it1 ->
                        mAdapter.getScrollPosition(
                            it1
                        )
                    } ?: 0

                    if (it > posLast){
                        val chr = viewModel.currentIndexDescription.value ?: character
                        letter_view?.setSelectedChar(chr)
                    }

                    linearLayoutManager.scrollToPositionWithOffset(it, 0)
                }
            }

            override fun clickArrow() {
                linearLayoutManager.scrollToPositionWithOffset(0, 0)
            }
        })

        // 搜索
        contacts_search_rl?.singleClick {
            val list = viewModel.currentContactsSortList.value?.toMutableList() as ArrayList
            val intent = Intent(this, StarrySearchActivity::class.java)
            intent.putExtra(StarryConstants.STARRY_CONTACT_LIST, list)
            intent.putExtra(StarryConstants.STARRY_USER_TYPE, userType)
            ActivityUtils.startActivity(intent)
        }

        // 当退出企业时，需要退出列表，返回首页
        LiveDataBus.get()
            .with(StarryConstants.STARRY_COMPANY_EXIT, String::class.java)
            .observe(this) {
                logI("StarryContactsActivity.StarryConstants.STARRY_COMPANY_EXIT")
                finish()
            }

    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        viewModel.getEnterpriseMembers()

        viewModel.currentContactsList.observe(this) {
//            logI("Jason","viewModel.currentContactsList="+
//                Gson().toJson(it))
            var list = it?.toMutableList() as ArrayList

            mAdapter.setListContacts(list)
            list = mAdapter.getListContacts()
            viewModel.currentContactsSortList.postValue(list)
//            logI("StarryContactsActivity.currentContactsList======${viewModel.currentContactsSortList.value}")

            mAdapter.getCharacterList().let {
//                Log.i("Jason", "mAdapter.getCharacterList()=$it")
                letter_view?.initViewNew(it)
            }

            if (it.isNotEmpty()) {
//                contacts_search_rl?.visibility = View.VISIBLE
                recycler_view_contacts?.visibility = View.VISIBLE
                letter_view?.visibility = View.VISIBLE
                contacts_no_contact?.visibility = View.GONE
                recently_bottom_msg_coount?.visibility = View.GONE
                tv_sticky_header_view?.visibility = View.VISIBLE
            } else {
//                contacts_search_rl?.visibility = View.GONE
                recycler_view_contacts?.visibility = View.GONE
                letter_view?.visibility = View.GONE
                contacts_no_contact?.visibility = View.VISIBLE
                recently_bottom_msg_coount?.visibility = View.GONE
                tv_sticky_header_view?.visibility = View.GONE
            }
            setFooterText(it.size ?: 0)
            setFooterView(recycler_view_contacts)

//            recently_bottom_msg_coount?.text =
//                getString(R.string.starry_contact_bottom_msg_count, it.size ?: 0)

            recycler_view_contacts?.post {
                val stickPos = linearLayoutManager.findFirstVisibleItemPosition()
                if (!mAdapter.resultList.isNullOrEmpty()
                    && stickPos in 0 until mAdapter.resultList.size){
                    viewModel.currentIndexDescription.value = mAdapter.resultList[stickPos].getFirstLetter()
                    tv_sticky_header_view?.text = viewModel.currentIndexDescription.value
                }
            }

        }

        viewModel.currentIndexDescription.observe(this) {
            Log.i("Jason", "currentIndexDescription=${it}")
            letter_view?.setSelectedChar(it)
        }
    }

    private fun setFooterView(view: RecyclerView) {
        val footer = layoutInflater.inflate(R.layout.contact_footer, view, false)
        mAdapter.setFooterFivew(footer)
    }

    private fun setFooterText(count: Int) {
        mAdapter.setFootertext(count)
    }


    override fun onDestroy() {
        super.onDestroy()
        viewModel.setCurrentUserTypeCompany(0)//在视频会议-企业通讯录页面进入后,退出时,当前选择公司改为默认第一个公司
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

}