package com.czur.cloud.ui.starry.activity

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.view.WindowManager
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.*
import com.czur.cloud.R
import com.czur.cloud.event.BaseEvent
import com.czur.cloud.event.EventType
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.starry.activity.StarryContactDetailActivity.Companion.DEL_ACCOUNT_PRE
import com.czur.cloud.ui.starry.adapter.StarryCompanyListContactAdapter
import com.czur.cloud.ui.starry.adapter.StarryContactAdapter
import com.czur.cloud.ui.starry.base.StarryNewBaseActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.meeting.baselib.utils.isPinyinContains
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.model.Contact
import com.czur.cloud.ui.starry.model.StarryAddressBookModel
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.ui.starry.viewmodel.StarryContactViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import com.czur.czurutils.log.logI
import kotlinx.android.synthetic.main.starry_activity_company_list_contacts.*
import kotlinx.android.synthetic.main.starry_activity_search_company_contact.*
import kotlinx.android.synthetic.main.starry_activity_search_company_contact.starry_no_search_result_tv
import kotlinx.android.synthetic.main.starry_activity_search_company_contact.starry_search_del_iv
import kotlinx.android.synthetic.main.starry_activity_search_company_contact.starry_search_et
import kotlinx.android.synthetic.main.starry_activity_search_company_contact.starry_search_result_tv
import kotlinx.android.synthetic.main.starry_activity_search_company_contact.starry_search_tv
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*
import kotlin.collections.ArrayList

/**
 * 选择企业联系人的搜索
 */
class StarrySearchCompanyContactActivity : StarryNewBaseActivity() {
    private var resultListContacts = ArrayList<StarryAddressBookModel>()

    private val linearLayoutManager by lazy { LinearLayoutManager(this) }
    private val mAdapter by lazy { StarryCompanyListContactAdapter(this) }
    private var searchKey: String = ""
    private var isMaxSelectedCount = false  //是否为当前选中的最大值？
    private val selectMaxCountNumber = MeetingModel.memberLimitCount ?: UserHandler.portLimit

    private val contactViewModel by lazy {
        ViewModelProvider(
            StarryActivity.mainActivity ?: this
        ).get(StarryContactViewModel::class.java)
    }
    private val viewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryViewModel::class.java)
    }

    private val selectType by lazy {
        viewModel.getSelectType() ?: StarryConstants.STARRY_SELECT_TYPE_START
    }

    override fun getLayout(): Int = R.layout.starry_activity_search_company_contact
    var initList = arrayListOf<String>()//记录进入时候的数据

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
//        logI("StarrySearchCompanyContactActivity.onEvent=" + event.eventType)
        when (event.eventType) {
            // 接收到的会议结束指令-会议结束
            // 接收到的离开会议室指令--被移除
            EventType.STARRY_MEETING_CMD_STOP,
            EventType.STARRY_MEETING_CMD_REMOVE -> {
                finish()
            }

            else -> {
            }
        }
    }

    override fun initViews() {
        super.initViews()

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }

        starry_search_result_tv?.visibility = View.INVISIBLE
        starry_no_search_result_tv?.visibility = View.GONE

        recycler_view_search_result_company?.apply {
            setHasFixedSize(true)
            layoutManager = linearLayoutManager
            adapter = mAdapter
        }

//        mAdapter.setListContacts(resultListContacts, ,true)
//        resultListContacts = mAdapter.getListContacts()

        mAdapter.setOnItemClickListener(object : StarryCompanyListContactAdapter.OnClickListener {
            override fun onclickSel(position: Int, checkMap: LinkedHashMap<String, String>) {
                logI(
                    "mAdapter.setOnItemClickListener.onclickSel.position=${position},checkMap=${checkMap.size},${checkMap}",
                    "isMaxSelectedCount=${isMaxSelectedCount},selectMaxCountNumber=${selectMaxCountNumber}"
                )
                if (isMaxSelectedCount && (checkMap.size >= selectMaxCountNumber)) {
                    ToastUtils.showLong(
                        String.format(
                            getString(R.string.starry_company_list_contacts_selected_more),
                            (selectMaxCountNumber - 1).toString()
                        )
                    )
                }else{
                    val meetingNoList = arrayListOf<String>()
                    for (keyAndValue in checkMap){
                        meetingNoList.add(keyAndValue.key)
                    }
                    meetingNoList.sort()
                    meetingNoList.containsAll(initList)

                    if (CollectionUtils.isEqualCollection(initList,meetingNoList)){
                        starry_search_tv?.text = getString(R.string.cancel)
                    }else{
                        starry_search_tv?.text = getString(R.string.ok)
                    }
                }
                contactViewModel.tempCheckedMap.value = checkMap

                isMaxSelectedCount = checkMap.size >= selectMaxCountNumber

            }

        })

        starry_search_del_iv?.singleClick {
            searchKey = ""
            starry_search_et?.setText(searchKey)

            searchFromList()
        }

        if (searchKey.isNotEmpty()) {
            starry_search_del_iv?.visibility = View.VISIBLE
        } else {
            starry_search_del_iv?.visibility = View.GONE
        }

        starry_search_et?.apply {
            isFocusable = true
            isFocusableInTouchMode = true
            requestFocus()
            findFocus()

        }
        this.window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
        starry_search_et?.addTextChangedListener(object : TextWatcher {
            private var temp: CharSequence? = null
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                temp = s
            }

            override fun afterTextChanged(s: Editable) {
                val str = s.toString()
                if (!TextUtils.isEmpty(str)) {
                    searchKey = str
                    starry_search_del_iv?.visibility = View.VISIBLE
                } else {
                    starry_search_del_iv?.visibility = View.INVISIBLE
                    searchKey = ""
                }

                searchFromList()
            }
        })

        starry_search_tv?.singleClick {
            // 保存选中的内容，带回到上页面
            val intent = Intent(this, StarryCompanyListContactsActivity::class.java)
            setResult(RESULT_OK, intent)
            ActivityUtils.finishActivity(this)
        }

        starry_search_tv?.text = getString(R.string.cancel)

        val accountNo = StarryPreferences.getInstance().accountNo
        // 设置选中的项
        if (selectType == StarryConstants.STARRY_SELECT_TYPE_ADD) {
            // 设置选中的成员
            val checkedList = ArrayList<String>()
            contactViewModel.isDisableCheckedMap.value?.forEach { (t, u) ->
                checkedList.add(t)
            }
            mAdapter.setDisableChecked(checkedList)

            contact_new_call_btn?.visibility = View.GONE
            contact_add_btn?.visibility = View.VISIBLE

        } else {
            // 设置选中的自己
            val checkedList = ArrayList<String>()
            checkedList.add(accountNo)
            mAdapter.setDisableChecked(checkedList)

            contact_new_call_btn?.visibility = View.VISIBLE
            contact_add_btn?.visibility = View.GONE
        }

        // 初始化时，把原来的isCheckMap内容带入
        if (contactViewModel.tempCheckedMap.value != null) {
            // 设置原有的选择项
            val checkedListAll = ArrayList<String>()
            contactViewModel.tempCheckedMap.value?.forEach { (t, u) ->
                checkedListAll.add(t)
            }
            mAdapter.setAllreadyChecked(checkedListAll)
        }
        contactViewModel.tempCheckedMap.value = mAdapter.getCheckMap()

    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        contactViewModel.tempCheckedMap.observe(this) {
            if (initList.size == 0){
                for (str in it){
                    initList.add(str.key)
                }
                initList.sort()
            }


            val checkCount = it.size ?: 1
            if (checkCount >= selectMaxCountNumber) {
                mAdapter.setSelectedMore(false)
            } else {
                mAdapter.setSelectedMore(true)
            }

            // 初始化时，把原来的isCheckMap内容带入
            if (contactViewModel.tempCheckedMap.value != null && contactViewModel.tempCheckedMap.value?.size ?: 0 > 0) {
                // 设置原有的选择项
                val checkedListAll = ArrayList<String>()
                contactViewModel.tempCheckedMap.value?.forEach { (t, u) ->
                    checkedListAll.add(t)
                }
                mAdapter.setAllreadyChecked(checkedListAll)
                isMaxSelectedCount = contactViewModel.tempCheckedMap.value?.size ?:0  >= selectMaxCountNumber
            }


        }

        formatNamesPinyin()
    }

    // 根据keyword，从列表过滤，得到新的符合的列表
    private fun searchFromList() {
         val resultTotalList : ArrayList<ArrayList<Contact>>
                = arrayListOf(arrayListOf(),arrayListOf(),arrayListOf(),arrayListOf())
        val resultTotalViewList : java.util.ArrayList<java.util.ArrayList<StarryAddressBookModel>>
                = arrayListOf(arrayListOf(),arrayListOf(),arrayListOf(),arrayListOf())
//        val listContacts = viewModel.currentContactsSortList.value?.toMutableList() as ArrayList
        val listContacts = viewModel.currentContactsSortList.value?.toMutableList() as ArrayList
//        logI("searchFromList.searchKey=${searchKey}")
//        logI("searchFromList.listContacts=${listContacts.size}")

        resultListContacts.clear()

        if (searchKey == "") {
            starry_search_result_tv?.visibility = View.INVISIBLE
            starry_no_search_result_tv?.visibility = View.GONE
            recycler_view_search_result_company?.visibility = View.GONE
            mAdapter.setListContacts(resultListContacts, arrayListOf(),true)
            return
        }
        val resultList: ArrayList<Contact> = arrayListOf()

        for (model in listContacts) {
            if ((model.id.toString() + "") == "0") {
                continue
            }
            //备注 remark
            //昵称 nickname
            //企业名称 enterpriseName
            //账号 meetingNo
            val nickname = model.nickname ?: ""
            val nicknamePinyin = model.nicknamePinyin ?: ""
//            val name = (model.name ?: "").lowercase()
//            val pinyin = (model.pinyin.lowercase(Locale.ROOT) ?: "").lowercase()
            val mobile = model.meetingAccout ?: ""
            val remark = model.remark ?: ""
            val remarkNamePersonalAdress = model.remarkNamePersonalAdress ?: ""
            val remarkNamePersonalAdressPinYin =
                model.remarkNamePersonalAdressPinYin ?: ""

            var addOrderKey = 0
            var firstContent = ""
            var secondContent = ""
            if (searchEnterpriseName(model, true)) {//查询结果匹配到企业姓名，此时仅显示一行信息（企业姓名）
                firstContent = nameForCompany
                secondContent = ""
                addOrderKey = 0
            } else if (remarkNamePersonalAdress.isPinyinContains(searchKey)
                || remarkNamePersonalAdressPinYin.isPinyinContains(searchKey)
            ) {//查询结果匹配到备注，此时显示两行信息（企业姓名+备注：xx）
                firstContent = getEnterpriseName(czurId = model.czurId)
                secondContent = "${getString(R.string.starry_company_note)}：${model.remarkNamePersonalAdress ?: ""}"
                addOrderKey = 1
            }else if (nickname.isPinyinContains(searchKey)
                || nicknamePinyin.isPinyinContains(searchKey)
            ) {//查询结果匹配到昵称，此时显示两行信息（企业姓名+昵称：xx）
                firstContent = getEnterpriseName(czurId = model.czurId)
                secondContent = "${getString(R.string.starry_company_nickname)}：${model.nickname ?: ""}"
                addOrderKey = 2
            } else if (mobile.isPinyinContains(searchKey)) {//查询结果匹配到账号，此时显示两行信息（企业姓名+账号：xx）
                firstContent = getEnterpriseName(czurId = model.czurId)
                secondContent = "${getString(R.string.starry_company_mobile)}：${model.meetingAccout ?: ""}"
                addOrderKey = 3

                // 已注销账号不显示
                if (model.meetingAccout.startsWith(StarryContactDetailActivity.DEL_ACCOUNT_PRE) ||
                    model.meetingAccout.startsWith(StarryContactDetailActivity.DEL_ACCOUNT_000)){
                    continue
                }
            } else {
                continue
            }

            resultTotalList[addOrderKey].add(Contact(
                mType = StarryContactAdapter.ITEM_TYPE.ITEM_TYPE_CONTACT.ordinal,
                firstLineContent = firstContent,
                secondLineContent = secondContent,
            ))

//            resultListContacts.add(model)
            resultTotalViewList[addOrderKey].add(model)
        }

        for (list in resultTotalList){
            resultList.addAll(list)
        }
        for (list in resultTotalViewList){
            resultListContacts.addAll(list)
        }

        mAdapter.setNoIndex(true)
        mAdapter.setListContacts(resultListContacts,resultList,true)
        resultListContacts = mAdapter.getListContacts()

        if (resultListContacts.size > 0) {
            starry_search_result_tv?.visibility = View.VISIBLE
            starry_no_search_result_tv?.visibility = View.GONE
            recycler_view_search_result_company?.visibility = View.VISIBLE
        } else {
            starry_search_result_tv?.visibility = View.INVISIBLE
            starry_no_search_result_tv?.visibility = View.VISIBLE
            recycler_view_search_result_company?.visibility = View.GONE
        }

//        logI("searchFromList.resultListContacts3=${resultListContacts.size},", Gson().toJson(resultListContacts))
    }


    //获取当前企业中的企业姓名
    fun getEnterpriseName(czurId: String): String {
        viewModel.currentCompanyModel.value?.membersList.let {
            if (it != null) {
                for (member in it) {
                    if (member.czurId == czurId) {
                        return member.name
                    }
                }
            }
        }
        return ""
    }


    var nameForCompany = ""

    //搜索是否有匹配的企业姓名
    fun searchEnterpriseName(mode: StarryAddressBookModel, isCompanySearchMode: Boolean = false): Boolean {
        viewModel.currentCompanyModel.value?.membersList.let {
            if (it != null) {
                for (member in it) {
                    if (member.czurId == mode.czurId
                        && member.meetingAccout == mode.meetingAccout
                        && (member.name.isPinyinContains(searchKey)
                                || member.namePinyin.isPinyinContains(searchKey))
                    ) {
                        nameForCompany = member.name
                        return true
                    }
                }
            }
        }

        return false
    }

    fun formatNamesPinyin() {
        val value = viewModel.enterpriseMembers.value
        viewModel.currentCompanyModel.value?.membersList.let {
            if (it != null) {
                for (member in it) {
                    member.namePinyin = member.pinyin ?: ""
                    member.nicknamePinyin = member.nicknamePinyin ?: ""
                    if (value != null) {
                        for (personalAddressMember in value.addressBook) {
                            if (member.czurId == personalAddressMember.czurId && member.meetingAccout == personalAddressMember.meetingNo) {
                                member.remarkNamePersonalAdress = personalAddressMember.remark
                                member.remarkNamePersonalAdressPinYin = personalAddressMember.pinyin ?: ""
                            }
                        }
                    }
                }
            }
        }

        resultListContacts.forEach {
            it.nicknamePinyin = it.nicknamePinyin ?: ""
        }

    }

//    fun toSemiangle(src: String): String {
//        /*全角空格为12288，半角空格为32
//         * 其他字符半角(33-126)与全角(65281-65374)的对应关系是：均相差65248
//         * 将字符串中的全角字符转为半角
//         * @param src 要转换的包含全角的任意字符串
//         * @return  转换之后的字符串
//         */
//        val org = src.lowercase().replace(Regex("[\\u0020-\\u007E]")) {
//            (it.value[0] + 0xFEE0).toString()
//        }
//        PinyinUtils.getSurnamePinyin()
//        return org
//    }

//    fun String.formatPinyin(): String = PinyinUtils.toDbc(this as String)
}