<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_bottom"
    android:layout_width="match_parent"
    android:layout_height="@dimen/starry_meeting_bottombar_bg_height"
    android:visibility="visible"
    app:layout_constraintBottom_toBottomOf="parent">

    <com.czur.cloud.ui.starry.meeting.widget.BulletLayout
        android:id="@+id/bulletLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/starry_meeting_bullet_height"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="60dp"
        />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/controlActionBar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">
        <View
            android:id="@+id/controlBarBg"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starry_meeting_bottom_bar_height"
            android:background="@color/meeting_main_navbar_bg_black"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="visible"/>

        <LinearLayout
            android:id="@+id/tabMicLl"
            style="@style/meeting_main_navbar_ll_style"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline12"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/guidelineNav"
            >

            <RelativeLayout
                style="@style/meeting_main_navbar_image_style"
                android:gravity="center">

                <com.czur.cloud.ui.starry.meeting.widget.VolumeView
                    android:id="@+id/micIv"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/starry_meeting_sel_tab_mic" />
            </RelativeLayout>

            <TextView
                android:id="@+id/micTv"
                style="@style/meeting_main_navbar_title_style"
                android:text="@string/starry_main_audio_on" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tabCamLl"
            style="@style/meeting_main_navbar_ll_style"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline13"
            app:layout_constraintStart_toStartOf="@+id/guideline12"
            app:layout_constraintTop_toBottomOf="@+id/guidelineNav"
            >

            <ImageView
                android:id="@+id/cameraIv"
                style="@style/meeting_main_navbar_image_style"
                android:src="@drawable/starry_meeting_sel_tab_video" />

            <TextView
                android:id="@+id/cameraTv"
                style="@style/meeting_main_navbar_title_style"
                android:lineSpacingMultiplier="0.9"
                android:text="@string/starry_main_video_on" />

        </LinearLayout>

        <RelativeLayout
            android:id="@+id/tabCamRl"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline13"
            app:layout_constraintStart_toStartOf="@+id/guideline12"
            app:layout_constraintTop_toBottomOf="@+id/guidelineNav"
            android:visibility="gone"
            >

            <ImageView
                android:id="@+id/cameraIvRL"
                style="@style/meeting_main_navbar_image_style"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:src="@drawable/starry_meeting_sel_tab_video" />

            <TextView
                android:id="@+id/cameraTvRL"
                style="@style/meeting_main_navbar_title_style"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:text="@string/starry_main_video_on" />
        </RelativeLayout>

        <LinearLayout
                    android:id="@+id/tabShareLl"
                    style="@style/meeting_main_navbar_ll_style"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/guideline14"
                    app:layout_constraintStart_toStartOf="@+id/guideline13"
                    app:layout_constraintTop_toBottomOf="@+id/guidelineNav">

                    <ImageView
                        android:id="@+id/shareScreenIv"
                        style="@style/meeting_main_navbar_image_style"
                        android:src="@drawable/starry_meeting_sel_tab_share" />

                    <TextView
                        android:id="@+id/shareScreenTv"
                        style="@style/meeting_main_navbar_title_style"
                        android:text="@string/starry_main_share" />

                </LinearLayout>

        <LinearLayout
            android:id="@+id/tabMemberLl"
            style="@style/meeting_main_navbar_ll_style"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline15"
            app:layout_constraintStart_toStartOf="@+id/guideline14"
            app:layout_constraintTop_toBottomOf="@+id/guidelineNav">

            <ImageView
                android:id="@+id/showMembersIv"
                style="@style/meeting_main_navbar_image_style"
                android:padding="5dp"
                android:src="@mipmap/starry_meeting_ic_tab_member_def" />

            <TextView
                android:id="@+id/showMembersTv"
                style="@style/meeting_main_navbar_title_style"
                android:text="@string/starry_main_member" />

        </LinearLayout>

        <LinearLayout
                android:id="@+id/tabChatLl"
                style="@style/meeting_main_navbar_ll_style"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/guideline15"
                app:layout_constraintTop_toBottomOf="@+id/guidelineNav">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    app:layout_constraintEnd_toStartOf="@+id/showMsgIv"
                    app:layout_constraintStart_toEndOf="@+id/shareScreenIv"
                    app:layout_constraintTop_toTopOf="@+id/micIv">

                    <ImageView
                        android:id="@+id/showMsgIv"
                        style="@style/meeting_main_navbar_image_style"
                        android:src="@mipmap/starry_meeting_ic_tab_chat_def" />

                    <TextView
                        android:id="@+id/unreadCountTv"
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:layout_marginStart="-18dp"
                        android:layout_marginTop="2dp"
                        android:layout_toEndOf="@+id/showMsgIv"
                        android:background="@drawable/starry_unread_bg_with_red"
                        android:padding="2dp"
                        android:textColor="#ffffff"
                        android:gravity="center"
                        android:textSize="8sp"
                        tools:text="99+"
                        android:visibility="visible" />
                </RelativeLayout>

                <TextView
                    android:id="@+id/showMsgTv"
                    style="@style/meeting_main_navbar_title_style"
                    android:text="@string/starry_main_chat" />

            </LinearLayout>

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guidelineNav"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_begin="70dp" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline12"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.2" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline13"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.4" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline14"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.6" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline15"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.8" />

        <ImageView
            android:id="@+id/mainBottomRotateBtn"
            android:layout_width="@dimen/starryMargin50"
            android:layout_height="@dimen/starryMargin50"
            android:padding="12dp"
            android:background="@drawable/starry_circly_with_gray"
            android:src="@mipmap/starry_main_btn_rotate"
            android:alpha="0.8"
            android:visibility="visible"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:layout_marginBottom="@dimen/starryMargin10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/guidelineNav"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>