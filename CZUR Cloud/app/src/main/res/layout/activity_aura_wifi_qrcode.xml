<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9"
    android:keepScreenOn="true"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include
        android:id="@+id/wifi_retry_top_bar"
        layout="@layout/layout_normal_top_bar" />

    <ImageView
        android:layout_marginTop="20dp"
        android:id="@+id/aura_home_gif1"
        android:layout_marginRight="20dp"
        android:layout_marginLeft="20dp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@mipmap/aura_home_guide"
        app:layout_constraintDimensionRatio="w,300:560"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/wifi_retry_top_bar" />

    <ImageView
        app:layout_constraintVertical_chainStyle="packed"
        android:id="@+id/qrcode_img"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/retry_text"
        app:layout_constraintTop_toBottomOf="@+id/aura_home_gif1"
        />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:layout_marginTop="40dp"
        android:id="@+id/loading_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/aura_home_connect_qrcode_loading_text"
        android:textColor="@color/black_22"
        android:textSize="15sp"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/aura_home_gif1"
        app:layout_constraintBottom_toTopOf="@+id/retry_text"/>

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:layout_marginTop="20dp"
        android:id="@+id/retry_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/aura_home_connect_qrcode_confirm1"
        android:textColor="@color/black_22"
        android:textSize="15sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginBottom="5dp"
        app:layout_constraintTop_toBottomOf="@+id/qrcode_img"
        app:layout_constraintBottom_toTopOf="@+id/retry_text1"/>
    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/retry_text1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/aura_home_connect_qrcode_confirm2"
        android:textColor="@color/black_22"
        android:textSize="15sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/retry_text"
        app:layout_constraintBottom_toTopOf="@+id/next_step_btn"/>

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/next_step_btn"
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/btn_rec_5_bg_with_code_blue"
        android:gravity="center"
        android:text="@string/aura_home_connect_qrcode_already"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        android:visibility="invisible"/>


</androidx.constraintlayout.widget.ConstraintLayout>

