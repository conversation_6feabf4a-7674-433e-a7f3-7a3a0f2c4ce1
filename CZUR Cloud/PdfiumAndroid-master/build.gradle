apply plugin: 'com.android.library'

buildscript {
    repositories {
        google()
        mavenCentral ()
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.0'
//        classpath 'com.jfrog.bintray.gradle:gradle-bintray-plugin:1.8.5'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
    }
}

android {
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 26
        targetSdkVersion 34
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        releaseTest {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

        }

        releaseOversea {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        releaseTestOversea {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

        }
    }


    sourceSets{
        main {
            jniLibs.srcDir 'src/main/libs'
        }
    }
    namespace 'com.shockwave.pdfium'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
//    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    def lifecycle_version = "2.6.1"
    // ViewModel
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-viewmodel:$lifecycle_version"
    // LiveData
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
    // Lifecycles only (without ViewModel or LiveData)
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"
    // helpers for implementing LifecycleOwner in a Service
    implementation "androidx.lifecycle:lifecycle-service:$lifecycle_version"

    implementation "androidx.activity:activity-ktx:1.7.0"
    implementation "androidx.fragment:fragment-ktx:1.5.5"


    implementation "com.google.android.material:material:1.0.0"

    implementation 'androidx.core:core-ktx:1.13.1'

    implementation "androidx.appcompat:appcompat:1.7.0"

    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    //OkHttp3
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'
    //Gson
    implementation 'com.google.code.gson:gson:2.10'
    //gif
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'androidx.preference:preference-ktx:1.2.1'
    // endregion

    //ViewPager
//    implementation 'com.github.lsjwzh.RecyclerViewPager:lib:v1.1.1@aar'

    // Kotlin协程
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1'

    // push 解析
    implementation 'org.apache.commons:commons-lang3:3.7'
    // 解除android p反射限制
    implementation 'me.weishu:free_reflection:2.2.0'
    implementation 'org.lsposed.hiddenapibypass:hiddenapibypass:2.0'

    // pinyin4j
    implementation 'com.github.open-android:pinyin4j:2.5.0'

    // region jetpack lib
    implementation 'androidx.navigation:navigation-fragment-ktx:2.5.3'
    implementation 'androidx.navigation:navigation-ui-ktx:2.5.3'
//    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.0'
    implementation 'androidx.annotation:annotation:1.6.0'

}
