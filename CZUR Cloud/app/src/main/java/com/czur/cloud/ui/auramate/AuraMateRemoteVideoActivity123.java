package com.czur.cloud.ui.auramate;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.KeyguardManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.PowerManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.SurfaceView;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.callback.OSSProgressCallback;
import com.alibaba.sdk.android.oss.common.OSSLog;
import com.alibaba.sdk.android.oss.model.GetObjectRequest;
import com.alibaba.sdk.android.oss.model.GetObjectResult;
import com.alibaba.sdk.android.oss.model.ObjectMetadata;
import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.OSSInstance;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.GetRoomChannelEvent;
import com.czur.cloud.event.HdViewEvent;
import com.czur.cloud.event.HdViewSaveEvent;
import com.czur.cloud.event.VideoCameraEvent;
import com.czur.cloud.event.aurahome.VideoEvent;
import com.czur.cloud.model.VideoTokenModel;
import com.czur.cloud.netty.CZURMessageConstants;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.reportfragment.AuraMateHDViewAlbumActivity;
import com.czur.cloud.ui.auramate.reportfragment.HdViewData;
import com.czur.cloud.ui.auramate.reportfragment.HdViewFileUtils;
import com.czur.cloud.ui.component.CustomClickListener;
import com.czur.cloud.ui.component.popup.SaveHdViewPopupDialog;
import com.czur.cloud.util.validator.Validator;
import com.davemorrissey.labs.subscaleview.ImageSource;
import com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.util.concurrent.atomic.AtomicBoolean;

import io.agora.rtc2.IRtcEngineEventHandler;
import io.agora.rtc2.RtcEngine;
import io.agora.rtc2.video.BeautyOptions;
import io.agora.rtc2.video.VideoCanvas;
import io.agora.rtc2.video.VideoEncoderConfiguration;

public class AuraMateRemoteVideoActivity123 extends AuramateBaseActivity implements View.OnClickListener {

    public static final int WARN_ADM_PLAYOUT_ABNORMAL_FREQUENCY = 1020;
    public static final int STREAM_FALLBACK_OPTION_AUDIO_ONLY = 2;

    private static final String LOG_TAG = AuraMateRemoteVideoActivity123.class.getSimpleName();
    private static final int PERMISSION_REQ_ID_RECORD_AUDIO = 22;
    private static final int PERMISSION_REQ_ID_CAMERA = PERMISSION_REQ_ID_RECORD_AUDIO + 1;
    private ImageView changeCameraBtn;
    private TextView changeCameraTv;
    private TextView hdViewTv;
    private ImageView dialogOutBtn;
    private ImageView hdViewBtn;
    private ImageView remoteVideoBackBtn;
    private String channel;

    private AtomicBoolean isInVideo;
    private RelativeLayout videoLoadingRl;
    private ImageView loadingImg;
    private RelativeLayout hdViewLoadingRl;
    private ImageView hdViewLoadingImg;
    private TextView hdViewLoadingTv;
    private RelativeLayout saveLoadingRl;
    private ImageView saveLoadingImg;
    private boolean isCallIn;
    private String callId;
    private String userIdTo;
    private String token;
    private Animation imgAnim;
    private ImageView saveCloseImg;
    private ImageView saveImg,saveImgAblum;

    private RelativeLayout failedToast;
    private TextView tvNetToast;
    private SubsamplingScaleImageView frescoImageView;
    private WeakHandler handler;
    private SaveHdViewPopupDialog successPopup;
    private RelativeLayout hdViewLoadingBg;
    private LinearLayout hdViewLl;
    private LinearLayout changeCameraLl;
    private FrameLayout bigContainer, smallContainer;

    private TextView loadingTv;
    private CountDownTimer timer;
    private int meCount, otherCount, unKnownCount;
    private TextView tvMeTxQuality, tvMeRxQuality;
    private TextView tvOtherTxQuality, tvOtherRxQuality;
    private SurfaceView localSv, remoteSv;
    private AtomicBoolean isCameraSwitching;
    private AtomicBoolean isCamareVideo;
    private AtomicBoolean isJoinChannel;

    private ImageView btnVideoZan;  //赞按钮
    private ImageView btnVideoMic;  //mic按钮
    private ImageView btnVideoSub;  //小窗按钮
    private ImageView btnVideoAlbum;  //相册
    private AtomicBoolean isVideoMic;   //是否静音
    private AtomicBoolean isVideoSub;   //是否开启小窗
    private AtomicBoolean isVideoZan;   //是否已经赞了
    private AtomicBoolean isVideoAlbum;   //相册是否有图

    private int albumCount=0;//相册中的照片数量
    private TextView btn_video_album_count;
    private String saveHdViewPath;
    private HdViewData locHdViewData;  //当前保存的图片数据

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        PowerManager pm = ((PowerManager) getSystemService(POWER_SERVICE));
        @SuppressLint("InvalidWakeLockTag")
        PowerManager.WakeLock screenLock = pm.newWakeLock(PowerManager.SCREEN_DIM_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP, "TAG");
        screenLock.acquire(100 * 60 * 1000L /*100 minutes*/);
        KeyguardManager km = (KeyguardManager) getSystemService(Context.KEYGUARD_SERVICE);
        final KeyguardManager.KeyguardLock kl = km.newKeyguardLock("unLock");
        kl.disableKeyguard();
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
                | WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
                | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
                | WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD);
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.systemUiVisibility = View.SYSTEM_UI_FLAG_LOW_PROFILE;
        setStatusBarColor(R.color.transparent);
        BarUtils.setNavBarVisibility(this, false);
        ScreenUtils.setFullScreen(this);
        setContentView(R.layout.activity_remote_video);

        saveHdViewPath = this.getExternalFilesDir("hdview")+ File.separator + "/";

        initComponent();
        registerEvent();

        initDataImageCount();

        hideHDViewAblum(true);
        HdViewFileUtils.videoActivity = this;
    }

    private void hideHDViewAblum(boolean flag){
        int hidden = View.VISIBLE;
        if (flag) {
            hidden = View.INVISIBLE;
        }
        findViewById(R.id.save_close_img).setVisibility(hidden);
        findViewById(R.id.save_img).setVisibility(hidden);
        findViewById(R.id.save_tv).setVisibility(hidden);
        findViewById(R.id.save_img_ablum).setVisibility(hidden);
        findViewById(R.id.save_tv_ablum).setVisibility(hidden);

    }

    private void initDataImageCount() {
        //退出时，清除缓存图片
        deleteFolderFile(saveHdViewPath);
        HdViewFileUtils.hdViewDataList.clear();

        File folder = new File(saveHdViewPath);
        //将文件夹下所有文件名存入数组
        String[] allFiles = folder.list();
        albumCount = allFiles.length;
    }

    private void initComponent() {
        isInVideo = new AtomicBoolean(false);
        isJoinChannel = new AtomicBoolean(false);
        isCameraSwitching = new AtomicBoolean(false);
        isCamareVideo = new AtomicBoolean(true);
        handler = new WeakHandler();
        isCallIn = getIntent().getBooleanExtra("isCallIn", false);
        callId = getIntent().getStringExtra("callId");
        userIdTo = getIntent().getStringExtra("userIdTo");

        if (isCallIn) {
            CZURTcpClient.getInstance().videoRequestSecond(this, equipmentId, 1, userIdTo, callId);
        } else {
            CZURTcpClient.getInstance().videoRequest(this, equipmentId);
        }
        remoteVideoBackBtn = (ImageView) findViewById(R.id.remote_video_back_btn);
        changeCameraBtn = (ImageView) findViewById(R.id.change_camera_btn);
        changeCameraTv = (TextView) findViewById(R.id.change_camera_tv);
        loadingTv = (TextView) findViewById(R.id.loading_tv);
        failedToast = (RelativeLayout) findViewById(R.id.failed_toast);
        tvNetToast = findViewById(R.id.tv_net_toast);
        hdViewLoadingBg = (RelativeLayout) findViewById(R.id.hd_view_loading_bg);
        hdViewBtn = (ImageView) findViewById(R.id.hd_imageview_btn);
        hdViewTv = (TextView) findViewById(R.id.hd_imageview_tv);
        dialogOutBtn = (ImageView) findViewById(R.id.dialog_out_btn);
        saveCloseImg = (ImageView) findViewById(R.id.save_close_img);
        saveImg = (ImageView) findViewById(R.id.save_img);
        saveImgAblum = (ImageView) findViewById(R.id.save_img_ablum);
        frescoImageView = findViewById(R.id.save_show_img);
        videoLoadingRl = (RelativeLayout) findViewById(R.id.video_loading_rl);
        loadingImg = (ImageView) findViewById(R.id.loading_img);
        saveLoadingRl = (RelativeLayout) findViewById(R.id.save_loading_rl);
        saveLoadingImg = (ImageView) findViewById(R.id.save_loading_img);
        hdViewLl = (LinearLayout) findViewById(R.id.hd_imageview_ll);
        changeCameraLl = (LinearLayout) findViewById(R.id.change_camera_ll);
        hdViewLoadingRl = (RelativeLayout) findViewById(R.id.hd_view_loading_rl);
        hdViewLoadingImg = (ImageView) findViewById(R.id.hd_view_loading_img);
        hdViewLoadingTv = (TextView) findViewById(R.id.hd_view_loading_tv);
        imgAnim = AnimationUtils.loadAnimation(this, R.anim.dialog_anim);
        loadingImg.startAnimation(imgAnim);
        hdViewLoadingImg.startAnimation(imgAnim);
        saveLoadingImg.startAnimation(imgAnim);
        bigContainer = (FrameLayout) findViewById(R.id.remote_video_view_container);
        smallContainer = (FrameLayout) findViewById(R.id.local_video_view_container);
        SaveHdViewPopupDialog.Builder builder = new SaveHdViewPopupDialog.Builder(AuraMateRemoteVideoActivity123.this);
        successPopup = builder.create();
        tvMeTxQuality = findViewById(R.id.tv_me_tx_quality);
        tvMeRxQuality = findViewById(R.id.tv_me_rx_quality);
        tvOtherTxQuality = findViewById(R.id.tv_other_tx_quality);
        tvOtherRxQuality = findViewById(R.id.tv_other_rx_quality);
        setCameraDisable();

        //Jason 20201107
        isVideoZan = new AtomicBoolean(false);
        isVideoMic = new AtomicBoolean(false);
        isVideoSub = new AtomicBoolean(true);
        isVideoAlbum = new AtomicBoolean(true);
        btnVideoZan = (ImageView) findViewById(R.id.btn_video_zan);
        btnVideoMic = (ImageView) findViewById(R.id.btn_video_mic);
        btnVideoSub = (ImageView) findViewById(R.id.btn_video_sub);
        btnVideoAlbum = (ImageView) findViewById(R.id.btn_video_album);

        btn_video_album_count = findViewById(R.id.btn_video_album_count);
        btn_video_album_count.setVisibility(View.GONE);
        btnVideoAlbum.setImageResource(R.mipmap.btn_video_album);
        if (albumCount > 0){
            btn_video_album_count.setVisibility(View.VISIBLE);
            btn_video_album_count.setText(albumCount+"");
        }

        btnVideoZan.setVisibility(View.GONE);
        setLocalAudioMuteDisable();
    }

    private void setSaveGroupUI(boolean visible) {
        if (visible) {
            findViewById(R.id.save_tv).setVisibility(View.VISIBLE);
            findViewById(R.id.save_img).setVisibility(View.VISIBLE);
            findViewById(R.id.save_tv_ablum).setVisibility(View.VISIBLE);
            findViewById(R.id.save_img_ablum).setVisibility(View.VISIBLE);
        } else {
            findViewById(R.id.save_tv).setVisibility(View.INVISIBLE);
            findViewById(R.id.save_img).setVisibility(View.INVISIBLE);
        }
    }

    private void setShowGroupUI(boolean visible) {
        if (visible) {
            findViewById(R.id.save_bg).setVisibility(View.VISIBLE);
            findViewById(R.id.save_show_img).setVisibility(View.VISIBLE);
            findViewById(R.id.save_close_img).setVisibility(View.VISIBLE);
        } else {
            findViewById(R.id.save_bg).setVisibility(View.INVISIBLE);
            findViewById(R.id.save_show_img).setVisibility(View.INVISIBLE);
            findViewById(R.id.save_close_img).setVisibility(View.INVISIBLE);
        }
    }

    private void setCameraDisable() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                changeCameraBtn.setAlpha(0.5F);
                changeCameraTv.setAlpha(0.5F);
                changeCameraBtn.setEnabled(false);
                changeCameraBtn.setClickable(false);
            }
        });

    }

    private int count=5;
    private void countDown() {
        timer = new CountDownTimer(9000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                if (isCallIn ) {
                    loadingTv.setText(String.format(getString(R.string.calling)));
                } else {
                    if (count > 0) {
                        loadingTv.setText(String.format(getString(R.string.calling_second), ((count)) + ""));
                    } else {
                        loadingTv.setText(String.format(getString(R.string.calling_second), (1 + "")));
                    }
                }
                if (count < 0 && firstView) {
                    changeCameraLl.setVisibility(View.VISIBLE);
                    videoLoadingRl.setVisibility(View.INVISIBLE);
                    setCameraEnable();
                    setLocalAudioMuteEnable();
                    firstView = false;
                }
                count--;
            }

            @Override
            public void onFinish() {
                changeCameraLl.setVisibility(View.VISIBLE);
                videoLoadingRl.setVisibility(View.INVISIBLE);
                setCameraEnable();
                setLocalAudioMuteEnable();
            }
        }.start();

    }

    private void setCameraEnable() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                changeCameraBtn.setAlpha(1F);
                changeCameraTv.setAlpha(1F);
                changeCameraBtn.setEnabled(true);
                changeCameraBtn.setClickable(true);

                if (isCamareVideo.get()) {
                    changeCameraTv.setText(R.string.scan_camera);
                    hdViewLl.setVisibility(View.INVISIBLE);
                } else {
                    changeCameraTv.setText(R.string.video_camera);
                    hdViewLl.setVisibility(View.VISIBLE);
                }

            }
        });

    }

    private void setHDDisable() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                hdViewBtn.setAlpha(0.5F);
                hdViewTv.setAlpha(0.5F);
                hdViewBtn.setEnabled(false);
                hdViewBtn.setClickable(false);
            }
        });
    }

    private void setHDEnable() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                hdViewBtn.setAlpha(1F);
                hdViewTv.setAlpha(1F);
                hdViewBtn.setEnabled(true);
                hdViewBtn.setClickable(true);
            }
        });
    }

    private void showImage(String bucket, String ossKey) {
        long start = System.currentTimeMillis();

        GetObjectRequest get = new GetObjectRequest(bucket, ossKey);
        //设置下载进度回调
        get.setProgressListener(new OSSProgressCallback<GetObjectRequest>() {
            @Override
            public void onProgress(GetObjectRequest request, long currentSize, long totalSize) {
                OSSLog.logDebug("getobj_progress: " + currentSize + "  total_size: " + totalSize, false);
            }
        });
        try {
            // 同步执行下载请求，返回结果
            GetObjectResult getResult = OSSInstance.Companion.getInstance().oss().getObject(get);
            Log.d("Content-Length", "" + getResult.getContentLength());
            // 获取文件输入流
            InputStream inputStream = getResult.getObjectContent();
            ByteArrayOutputStream byteArrayOutputStream = ConvertUtils.input2OutputStream(inputStream);
            if (byteArrayOutputStream != null && byteArrayOutputStream.toByteArray() != null) {
                Bitmap bitmap = ConvertUtils.bytes2Bitmap(byteArrayOutputStream.toByteArray());
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        setHDEnable();
                        hdViewLoadingRl.setVisibility(View.INVISIBLE);
                        hdViewLoadingBg.setVisibility(View.INVISIBLE);
                        dialogOutBtn.setVisibility(View.INVISIBLE);
                        changeCameraLl.setVisibility(View.INVISIBLE);
                        hdViewLl.setVisibility(View.INVISIBLE);
                        setShowGroupUI(true);
                        setSaveGroupUI(true);
                        //压到2048以下就可以
                        frescoImageView.setImage(ImageSource.bitmap(ImageUtils.compressByScale(bitmap, 2000, bitmap.getHeight() * 2000 / bitmap.getWidth())));

                    }
                });

                //Jason 2021-01-08
                // 同步存储图片到高清查看的临时图库
                saveHDViewSave(bitmap, bucket, ossKey);

                // 下载后可以查看文件元信息
                ObjectMetadata metadata = getResult.getMetadata();
                Log.d("ContentType", metadata.getContentType());
            }

        } catch (ClientException e) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    setHDEnable();
                    hdViewLoadingRl.setVisibility(View.INVISIBLE);
                    hdViewLoadingBg.setVisibility(View.INVISIBLE);
                    dialogOutBtn.setVisibility(View.INVISIBLE);
                    changeCameraLl.setVisibility(View.INVISIBLE);
                    hdViewLl.setVisibility(View.INVISIBLE);
                    setShowGroupUI(true);
                    setSaveGroupUI(true);
                }
            });
            e.printStackTrace();
        } catch (ServiceException e) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    setHDEnable();
                    hdViewLoadingRl.setVisibility(View.INVISIBLE);
                    hdViewLoadingBg.setVisibility(View.INVISIBLE);
                    dialogOutBtn.setVisibility(View.INVISIBLE);
                    changeCameraLl.setVisibility(View.INVISIBLE);
                    hdViewLl.setVisibility(View.INVISIBLE);
                    setShowGroupUI(true);
                    setSaveGroupUI(true);
                }
            });
        }

        long end = System.currentTimeMillis();
    }


    private void registerEvent() {
        changeCameraBtn.setOnClickListener(this);
        remoteVideoBackBtn.setOnClickListener(this);
        dialogOutBtn.postDelayed(new Runnable() {
            @Override
            public void run() {
                dialogOutBtn.setOnClickListener(new CustomClickListener() {
                    @Override
                    protected void onSingleClick() {
                        ActivityUtils.finishActivity(AuraMateRemoteVideoActivity123.this);
                    }

                    @Override
                    protected void onFastClick() {

                    }
                });
            }
        }, 2000);
        hdViewBtn.setOnClickListener(this);
        saveCloseImg.setOnClickListener(this);
        saveImg.setOnClickListener(this);
        saveImgAblum.setOnClickListener(this);
        changeCameraBtn.setEnabled(false);
        changeCameraBtn.setClickable(false);

        //Jason
        btnVideoSub.setOnClickListener(this);
        btnVideoZan.setOnClickListener(this);
        btnVideoMic.setOnClickListener(this);
        btnVideoAlbum.setOnClickListener(this);

    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.save_close_img:
                setCameraEnable();
                dialogOutBtn.setVisibility(View.VISIBLE);
                changeCameraLl.setVisibility(View.VISIBLE);
                if (changeCameraTv.getText().equals(getResources().getString(R.string.video_camera))) {
                    hdViewLl.setVisibility(View.VISIBLE);
                } else {
                    hdViewLl.setVisibility(View.INVISIBLE);
                }
                setShowGroupUI(false);
                setSaveGroupUI(false);
                hideHDViewAblum(true);
                break;
            case R.id.save_img:
                saveLoadingRl.setVisibility(View.VISIBLE);
                CZURTcpClient.getInstance().hdViewSave(AuraMateRemoteVideoActivity123.this, equipmentId);
                break;

            case R.id.save_img_ablum:

                openLocalAlbum();//相册按钮
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        setCameraEnable();
                        dialogOutBtn.setVisibility(View.VISIBLE);
                        changeCameraLl.setVisibility(View.VISIBLE);
                        if (changeCameraTv.getText().equals(getResources().getString(R.string.video_camera))) {
                            hdViewLl.setVisibility(View.VISIBLE);
                        } else {
                            hdViewLl.setVisibility(View.INVISIBLE);
                        }
                        setShowGroupUI(false);
                        setSaveGroupUI(false);
                        hideHDViewAblum(true);
                    }
                }, 500);

                break;

            case R.id.hd_imageview_btn:
                setCameraDisable();
                setHDDisable();
                CZURTcpClient.getInstance().hdView(this, equipmentId);
                break;
            case R.id.change_camera_btn:
            case R.id.change_camera_tv:
                isCamareVideo.set(!isCamareVideo.get());
                isCameraSwitching.set(true);
                setCameraDisable();
                setHDDisable();
                CZURTcpClient.getInstance().switchCamera(this, equipmentId);
                if (isCamareVideo.get()) {
//                    changeCameraTv.setText(R.string.scan_camera);
                    hdViewLl.setVisibility(View.INVISIBLE);
                } else {
//                    changeCameraTv.setText(R.string.video_camera);
                    hdViewLl.setVisibility(View.VISIBLE);
                }
                break;
            case R.id.remote_video_back_btn:
            case R.id.dialog_out_btn:
                ActivityUtils.finishActivity(this);
                break;

            //赞按钮
            case R.id.btn_video_zan:
                break;

            //mic按钮
            case R.id.btn_video_mic:
                onLocalAudioMuteClicked();
                break;

            //小窗按钮
            case R.id.btn_video_sub:
                switchLocalVideoSub();
                break;

            //相册按钮
            case R.id.btn_video_album:
                openLocalAlbum();
                break;

            default:
                break;
        }
    }

    private void initShowLoading() {
        hdViewLoadingRl.setVisibility(View.VISIBLE);
        hdViewLoadingBg.setVisibility(View.VISIBLE);
        hdViewLoadingImg.startAnimation(imgAnim);
        hdViewLoadingImg.setBackgroundResource(R.mipmap.video_loading);
    }

    private void showFailed(int id) {
        timer = new CountDownTimer(5000, 1000) {
            @Override
            public void onTick(long l) {
                if (l == 4000) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            hdViewLoadingTv.setText(id);
                        }
                    });
                }
            }

            @Override
            public void onFinish() {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        setCameraEnable();
                        setHDEnable();
                        hdViewLoadingBg.setVisibility(View.INVISIBLE);
                        hdViewLoadingTv.setVisibility(View.INVISIBLE);
                    }
                });
            }
        }.start();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case HD_SAVE_VIEW:
                HdViewSaveEvent hdViewSaveEvent = (HdViewSaveEvent) event;

                saveLoadingRl.setVisibility(View.INVISIBLE);
                if (hdViewSaveEvent.getStatus() == 0) {
                    setSaveGroupUI(true);
                    successPopup.show(getString(R.string.hdview_save_failed1),getString(R.string.hdview_save_failed2), 0);
                    handler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            successPopup.dismiss();
                        }
                    }, 2000);
                } else {
                    setSaveGroupUI(false);
                    successPopup.show(getString(R.string.hdview_save_success1),getString(R.string.hdview_save_success2), 1);

                    if (!HdViewFileUtils.isAblumAcitivityOpen) {
                        locHdViewData.setFav(true);
                    }

                    handler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            successPopup.dismiss();
                        }
                    }, 2000);
                }
                break;
            case HD_VIEW:
                HdViewEvent hdViewEvent = (HdViewEvent) event;
                String hdViewType = hdViewEvent.getHdViewType();
                if (hdViewType.equals(CZURMessageConstants.HdView.UPLOADING.getStatus())) {
                    cancelTimer();
                    initShowLoading();
                    hdViewLoadingTv.setText(R.string.uploading);
                    hdViewLoadingTv.setVisibility(View.VISIBLE);
//                    showFailed(R.string.uploading);
                    setHDDisable();
                } else if (hdViewType.equals(CZURMessageConstants.HdView.TAKING_PICTURES.getStatus())) {
                    cancelTimer();
                    initShowLoading();
                    hdViewLoadingTv.setText(R.string.taking_photo);
                    hdViewLoadingTv.setVisibility(View.VISIBLE);
//                    showFailed(R.string.taking_photo);
                    setHDDisable();
                } else if (hdViewType.equals(CZURMessageConstants.HdView.TAKING_PICTURES_FAILURE.getStatus())) {
                    cancelTimer();
                    initShowLoading();
                    hdViewLoadingRl.setVisibility(View.VISIBLE);
                    hdViewLoadingBg.setVisibility(View.VISIBLE);
                    hdViewLoadingTv.setVisibility(View.VISIBLE);
                    hdViewLoadingTv.setText(R.string.taking_photo_failed);
                    showFailed(R.string.taking_photo_failed);
                    setHDDisable();
                    hdViewLoadingImg.clearAnimation();
                    hdViewLoadingImg.setBackgroundResource(R.mipmap.hd_failed_icon);
                } else if (hdViewType.equals(CZURMessageConstants.HdView.UPLOADING_FAILURE.getStatus())) {
                    cancelTimer();
                    initShowLoading();
                    hdViewLoadingRl.setVisibility(View.VISIBLE);
                    hdViewLoadingBg.setVisibility(View.VISIBLE);
                    hdViewLoadingTv.setVisibility(View.VISIBLE);
                    hdViewLoadingTv.setText(R.string.upload_failed);
                    showFailed(R.string.upload_failed);
                    setHDDisable();
                    hdViewLoadingImg.clearAnimation();
                    hdViewLoadingImg.setBackgroundResource(R.mipmap.hd_failed_icon);
                } else if (hdViewType.equals(CZURMessageConstants.HdView.UPLOAD_COMPLETED.getStatus())) {
                    cancelTimer();
                    initShowLoading();
                    hdViewLoadingTv.setVisibility(View.VISIBLE);
                    hdViewLoadingTv.setText(R.string.getting);
//                    showFailed(R.string.getting);
                    setHDDisable();
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            showImage(hdViewEvent.getOss_bucket(), hdViewEvent.getOss_key());
                        }
                    }).start();
                }
                break;
            case GET_VIDEO_ROOM_CHANNEL:
                GetRoomChannelEvent roomChannelEvent = (GetRoomChannelEvent) event;
                channel = roomChannelEvent.getChannel();
                String udid_from = roomChannelEvent.getUdid_from();
                CZURTcpClient.getInstance().deviceReadyForVideo(this, udid_from, channel);
                HttpManager.getInstance().request().getVideoToken(UserPreferences.getInstance().getUserId(), channel, VideoTokenModel.class, new MiaoHttpManager.CallbackNetwork<VideoTokenModel>() {
                    @Override
                    public void onNoNetwork() {

                    }

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<VideoTokenModel> entity) {
                        if (!isFinishing()) {
                            token = entity.getBody().getRtc_token();
                            videoLoadingRl.setVisibility(View.VISIBLE);
                            initAgoraEngineAndJoinChannel();
                            if (!isCallIn) {
                                countDown();
                            }
                        }
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<VideoTokenModel> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                    }
                });
                break;
            case LOG_OUT:
            case DEVICE_CANCEL_VIDEO:
                ActivityUtils.finishActivity(this);
                break;
            case APP_IS_READY_FOR_VIDEO:
                VideoEvent videoEvent = (VideoEvent) event;
                channel = videoEvent.getVideo_chat_room_no();
                CZURTcpClient.getInstance().appReadyForVideo(AuraMateRemoteVideoActivity123.this, equipmentId, CZURMessageConstants.CallIn.YES.getValue());
                HttpManager.getInstance().request().getVideoToken(UserPreferences.getInstance().getUserId(), channel, VideoTokenModel.class, new MiaoHttpManager.CallbackNetwork<VideoTokenModel>() {
                    @Override
                    public void onNoNetwork() {
                    }

                    @Override
                    public void onStart() {
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<VideoTokenModel> entity) {
                        if (!isFinishing()) {
                            token = entity.getBody().getRtc_token();
                            videoLoadingRl.setVisibility(View.VISIBLE);
                            initAgoraEngineAndJoinChannel();
                            if (!isCallIn) {
                                countDown();
                            }else if(isCallIn){
                                countDown();
                            }
                        }
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<VideoTokenModel> entity) {
                    }

                    @Override
                    public void onError(Exception e) {
                    }
                });
                break;
            case DEVICE_IS_READY_FOR_VIDEO:
                VideoEvent event1 = (VideoEvent) event;
                if (event1.getIsReadyForCalibrate().equals(CZURMessageConstants.CallIn.NO.getValue())) {
                    showMessage(R.string.msg_busy);
                    ActivityUtils.finishActivity(this);
                }
                break;
            case VIDEO_CANCEL:
            case DEVICE_IS_READY_FOR_VIDEO_TIME_OUT:
                if (hasShowPcPopup) {
                    return;
                }
                VideoEvent videoEvent1 = (VideoEvent) event;
                if (videoEvent1.getDeviceUdid().equals(equipmentId)) {
                    ActivityUtils.finishActivity(this);
                }
                break;
            case VIDEO_CAMERA_SWITCH:
                VideoCameraEvent cameraEvent = (VideoCameraEvent) event;
                if (cameraEvent.getCamera() == null) {
                    //超时重置
                    if (isCameraSwitching.get()) {
                        setCameraEnable();
                        setHDEnable();
                        isCameraSwitching.set(false);
                    }
                } else {
                    if (cameraEvent.getCamera().equals(CZURMessageConstants.CameraSwitch.CAMERA_ABOVE.getCamera())) {
//                        changeCameraTv.setText(R.string.video_camera);
                        hdViewLl.setVisibility(View.VISIBLE);
                    } else {
//                        changeCameraTv.setText(R.string.scan_camera);
                        hdViewLl.setVisibility(View.INVISIBLE);
                    }
                }
                break;
            default:
                break;
        }
    }

    private void cancelTimer() {
        if (timer != null) {
            timer.cancel();
        }
    }

    private void initAgoraEngineAndJoinChannel() {
        initializeAgoraEngine();     // Tutorial Step 1
        setupVideoProfile();         // Tutorial Step 2
        setupLocalVideo();           // Tutorial Step 3
        joinChannel();               // Tutorial Step 4
    }

    private boolean firstView = false;
    private RtcEngine mRtcEngine;// Tutorial Step 1
    private final IRtcEngineEventHandler mRtcEventHandler = new IRtcEngineEventHandler() { // Tutorial Step 1
        @Override
        public void onFirstRemoteVideoDecoded(final int uid, int width, int height, int elapsed) { // Tutorial Step 5
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    setupRemoteVideo(uid);
                }
            });
        }

        @Override
        public void onRemoteVideoStateChanged(int uid, int state, int reason, int elapsed) {
            super.onRemoteVideoStateChanged(uid, state, reason, elapsed);
            if (state == 2) {
                firstView = true;
            }
            runOnUiThread(new Runnable() {
                @Override
                public void run() {

                    if (isCameraSwitching.get()) {
                        if (state == 2 && reason == 2) {
                            setCameraEnable();
                            setHDEnable();
                            isCameraSwitching.set(false);
                        }
                    }
                }
            });

        }


        @Override
        public void onUserOffline(int uid, int reason) { // Tutorial Step 7
            if (hasShowPcPopup) {
                return;
            }
            ActivityUtils.finishActivity(AuraMateRemoteVideoActivity123.this);
        }

        @Override
        public void onUserMuteVideo(final int uid, final boolean muted) { // Tutorial Step 10
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    onRemoteUserVideoMuted(uid, muted);
                }
            });
        }

        @Override
        public void onJoinChannelSuccess(String channel, int uid, int elapsed) {
            super.onJoinChannelSuccess(channel, uid, elapsed);
            isJoinChannel.set(true);
        }

        @Override
        public void onUserJoined(int uid, int elapsed) {
            super.onUserJoined(uid, elapsed);

            isInVideo.set(true);
         /*   runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (isCallIn) {
                        changeCameraLl.setVisibility(View.VISIBLE);
                        videoLoadingRl.setVisibility(View.INVISIBLE);
                        setCameraEnable();
                    }
                }
            });*/

        }

        @Override
        public void onNetworkQuality(int uid, int txQuality, int rxQuality) {
            //tx 是本地用户 上行； rx 下行
            super.onNetworkQuality(uid, txQuality, rxQuality);
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    showNetToast(uid, txQuality, rxQuality);
                }
            });

        }


        @Override
        public void onConnectionStateChanged(int state, int reason) {
            super.onConnectionStateChanged(state, reason);
        }
    };


    private void showNetToast(int uid, int txQuality, int rxQuality) {
        if (uid == 0) {
            tvMeTxQuality.setText("本机上行：" + txQuality);
            tvMeRxQuality.setText("本机下行：" + rxQuality);
        } else {
            tvOtherRxQuality.setText("对方上行：" + txQuality);
            tvOtherTxQuality.setText("对方下行：" + rxQuality);
        }

        if (uid == 0) {
            if (txQuality >= 4 && txQuality <= 6) {
                meCount++;
            } else {
                meCount--;
                if (meCount < 0) {
                    meCount = 0;
                }
            }
        } else {
            if (txQuality >= 4 && txQuality <= 6) {
                otherCount++;
            } else {
                otherCount--;
                if (otherCount < 0) {
                    otherCount = 0;
                }
            }
        }

        //双方网络同时都差
        if (meCount == 2 && otherCount == 2) {
            meCount = 0;
            otherCount = 0;
            tvNetToast.setText(getResources().getString(R.string.network_bad));
            failedToast.setVisibility(View.VISIBLE);
            failedToast.postDelayed(new Runnable() {
                @Override
                public void run() {
                    failedToast.setVisibility(View.INVISIBLE);
                }
            }, 3000);
        } else if (meCount == 2) {
            //我的网络差
            meCount = 0;
            tvNetToast.setText(getResources().getString(R.string.me_network_bad));
            failedToast.setVisibility(View.VISIBLE);
            failedToast.postDelayed(new Runnable() {
                @Override
                public void run() {
                    failedToast.setVisibility(View.INVISIBLE);
                }
            }, 3000);
        } else if (otherCount == 2) {
            //对方网络差
            otherCount = 0;
            tvNetToast.setText(getResources().getString(R.string.other_network_bad));
            failedToast.setVisibility(View.VISIBLE);
            failedToast.postDelayed(new Runnable() {
                @Override
                public void run() {
                    failedToast.setVisibility(View.INVISIBLE);
                }
            }, 3000);
        }
        if (uid == 0) {
            if (txQuality == 0 || txQuality == 6) {
                unKnownCount++;
            } else {
                unKnownCount--;
                if (unKnownCount < 0) {
                    unKnownCount = 0;
                }
            }

            if (unKnownCount == 5) {
                unKnownCount = 0;
                ActivityUtils.finishActivity(this);
            }
        }
    }

    private void closeAgora() {
        if (Validator.isNotEmpty(channel)) {
            CZURTcpClient.getInstance().videoCancel(AuraMateRemoteVideoActivity123.this, equipmentId, channel);
            leaveChannel();
            if (mRtcEngine != null) {
                RtcEngine.destroy();
            }
        }
    }

    // Tutorial Step 1
    private void initializeAgoraEngine() {
        try {
            mRtcEngine = RtcEngine.create(getApplicationContext(), BuildConfig.AGORA_APP_ID, mRtcEventHandler);
        } catch (Exception e) {
            Log.e(LOG_TAG, Log.getStackTraceString(e));
            throw new RuntimeException("NEED TO check rtc sdk init fatal error\n" + Log.getStackTraceString(e));
        }
    }

    // Tutorial Step 2
    private void setupVideoProfile() {
        if (mRtcEngine != null) {
            mRtcEngine.enableVideo();
            mRtcEngine.setBeautyEffectOptions(true, new BeautyOptions(1, 1f, 1f, 1f, 0.3f));
            mRtcEngine.setVideoEncoderConfiguration(
                    new VideoEncoderConfiguration(
                            VideoEncoderConfiguration.VD_320x240,
                            VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_15,
                            VideoEncoderConfiguration.STANDARD_BITRATE,
                            VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_FIXED_LANDSCAPE
                    )
            );
            mRtcEngine.enableDualStreamMode(true);
            mRtcEngine.setLocalPublishFallbackOption(STREAM_FALLBACK_OPTION_AUDIO_ONLY);
            mRtcEngine.setRemoteSubscribeFallbackOption(STREAM_FALLBACK_OPTION_AUDIO_ONLY);
        }

    }

    // Tutorial Step 3
    private void setupLocalVideo() {
        if (mRtcEngine != null) {
            localSv = RtcEngine.CreateRendererView(this);
            localSv.setZOrderMediaOverlay(true);
            smallContainer.addView(localSv);
            mRtcEngine.setupLocalVideo(new VideoCanvas(localSv, VideoCanvas.RENDER_MODE_HIDDEN, 0));
        }
    }

    // Tutorial Step 4
    private void joinChannel() {
        if (mRtcEngine != null) {
            mRtcEngine.joinChannel(token, channel, "AuraMate", Integer.parseInt(UserPreferences.getInstance().getUserId()));// if you do not specify the uid, we will generate the uid for you
            mRtcEngine.startPreview();
        }
    }

    // Tutorial Step 5
    private void setupRemoteVideo(int uid) {
        //因为远端只有一个设备 点对点视频
        if (bigContainer.getChildCount() > 0) {
            bigContainer.removeAllViews();
        }
        remoteSv = RtcEngine.CreateRendererView(this);
        bigContainer.addView(remoteSv);
        if (mRtcEngine != null) {
            mRtcEngine.setupRemoteVideo(new VideoCanvas(remoteSv, VideoCanvas.RENDER_MODE_HIDDEN, uid));
        }
        remoteSv.setTag(uid); // for mark purpose

    }

    // Tutorial Step 6
    private void leaveChannel() {
        if (mRtcEngine != null && isJoinChannel.get()) {
            mRtcEngine.leaveChannel();
        }
    }

    // Tutorial Step 7
    private void onRemoteUserLeft() {
        bigContainer.removeAllViews();
    }

    // Tutorial Step 10
    private void onRemoteUserVideoMuted(int uid, boolean muted) {
        SurfaceView surfaceView = (SurfaceView) bigContainer.getChildAt(0);
        if(surfaceView != null) {
            Object tag = surfaceView.getTag();
            if (tag != null && (Integer) tag == uid) {
                surfaceView.setVisibility(muted ? View.INVISIBLE : View.VISIBLE);
            }
        }
    }

    public boolean checkSelfPermission(String permission, int requestCode) {
        Log.i(LOG_TAG, "checkSelfPermission " + permission + " " + requestCode);
        if (ContextCompat.checkSelfPermission(this,
                permission)
                != PackageManager.PERMISSION_GRANTED) {

            ActivityCompat.requestPermissions(this,
                    new String[]{permission},
                    requestCode);
            return false;
        }
        return true;
    }


    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String permissions[], @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        Log.i(LOG_TAG, "onRequestPermissionsResult " + grantResults[0] + " " + requestCode);

        switch (requestCode) {
            case PERMISSION_REQ_ID_RECORD_AUDIO: {
                if (grantResults.length > 0
                        && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    checkSelfPermission(Manifest.permission.CAMERA, PERMISSION_REQ_ID_CAMERA);
                } else {
                    showMessage("No permission for " + Manifest.permission.RECORD_AUDIO);
                    ActivityUtils.finishActivity(this);
                }
                break;
            }
            case PERMISSION_REQ_ID_CAMERA: {
                if (grantResults.length > 0
                        && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    initAgoraEngineAndJoinChannel();
                } else {
                    showMessage("No permission for " + Manifest.permission.CAMERA);
                    ActivityUtils.finishActivity(this);
                }
                break;
            }
        }
    }

    //Jason
    private void switchLocalVideoSub() {
        boolean type = isVideoSub.get();

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (localSv != null) {
                    if (type) {
                        smallContainer.removeView(localSv);
                        btnVideoSub.setImageResource(R.mipmap.btn_video_sub);
                    } else {
                        smallContainer.addView(localSv);
                        btnVideoSub.setImageResource(R.mipmap.btn_video_sub_gray);
                    }
                    isVideoSub.set(!isVideoSub.get());
                }
            }
        });
    }

    /**
     * Tutorial Step 8
     * 将自己静音
     */
    public void onLocalAudioMuteClicked() {
        if (mRtcEngine != null) {
            int res = isVideoMic.get() ? R.mipmap.btn_video_mic_gray : R.mipmap.btn_video_mic;
            btnVideoMic.setImageResource(res);

            isVideoMic.set(!isVideoMic.get());
            mRtcEngine.muteLocalAudioStream(isVideoMic.get());
        }
    }

    private void setLocalAudioMuteDisable() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                btnVideoMic.setAlpha(0.5F);
                btnVideoSub.setAlpha(0.5F);
                btnVideoAlbum.setAlpha(0.5F);
//                btnVideoZan.setAlpha(0.5F);
                btnVideoMic.setEnabled(false);
                btnVideoMic.setClickable(false);
                btnVideoSub.setEnabled(false);
                btnVideoSub.setClickable(false);
                btnVideoAlbum.setEnabled(false);
                btnVideoAlbum.setClickable(false);
            }
        });
    }

    private void setLocalAudioMuteEnable() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                btnVideoMic.setAlpha(1F);
                btnVideoSub.setAlpha(1F);
                btnVideoAlbum.setAlpha(1F);
//                btnVideoZan.setAlpha(1F);
                btnVideoMic.setEnabled(true);
                btnVideoMic.setClickable(true);
                btnVideoSub.setEnabled(true);
                btnVideoSub.setClickable(true);
                btnVideoAlbum.setEnabled(true);
                btnVideoAlbum.setClickable(true);
            }
        });
    }

    public void openLocalAlbum() {
        if (albumCount < 1){
            ToastUtils.showLong(getResources().getString(R.string.hdview_no_picture));
            return;
        }

        HdViewFileUtils.isAblumAcitivityOpen = true;

        Intent intent = new Intent(this, AuraMateHDViewAlbumActivity.class);
        intent.putExtra("saveHdViewPath", saveHdViewPath);
        intent.putExtra("ownerId", getIntent().getStringExtra("ownerId"));
        intent.putExtra("equipmentId", equipmentId);
        ActivityUtils.startActivity(intent);
        overridePendingTransition(0, 0);
    }

    private void saveHDViewSave(Bitmap bmp, String bucket, String ossKey){

        long start = System.currentTimeMillis();

        try {

            long name_time = System.currentTimeMillis();
            String img_filename = name_time+".png";
            String img_name = saveHdViewPath + img_filename;

            boolean flag = ImageUtils.save(bmp, img_name, Bitmap.CompressFormat.JPEG, false);

            albumCount+=1;

            String fullname = "file://"+saveHdViewPath + "/" + img_filename;
            locHdViewData = new HdViewData(img_filename, fullname, ossKey, bucket);
            HdViewFileUtils.hdViewDataList.add(0, locHdViewData);

            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    //需要同步更新相册中的照片数量
                    if (albumCount > 0){
                        btn_video_album_count.setVisibility(View.VISIBLE);
                        btn_video_album_count.setText(albumCount+"");
                    }
                }
            });
        } catch (Exception e) {
        }
    }

    //删除制定目录的图片文件
    public void deleteFolderFile(String filePath){
        try {
            File file = new File(filePath);//获取SD卡指定路径
            File[] files = file.listFiles();//获取SD卡指定路径下的文件或者文件夹
            for (int i = 0; i < files.length; i++) {
                if (files[i].isFile()){//如果是文件直接删除
                    File photoFile = new File(files[i].getPath());
                    photoFile.delete();
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }


    @Override
    public boolean PCNeedFinish() {
        return !TextUtils.isEmpty(equipmentId);
    }

    @Override
    protected void onPause() {
        super.onPause();

        //判断是否需要进入后台就挂断视频
        if (HdViewFileUtils.isAblumAcitivityOpen) {
            //
        }else{
            ActivityUtils.finishActivity(this);
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
//        //进入后台就挂断视频
//        ActivityUtils.finishActivity(this);
    }

    @Override
    protected void onResume(){
        super.onResume();
        HdViewFileUtils.isAblumAcitivityOpen = false;
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        ActivityUtils.finishActivity(this);
    }

    @Override
    public void finish() {
        super.finish();
        closeAgora();
        mRtcEngine = null;
    }

    @Override
    protected void onDestroy() {
        if (remoteSv != null) {
            bigContainer.removeView(remoteSv);
        }
        remoteSv = null;
        if (localSv != null) {
            smallContainer.removeView(localSv);
        }
        localSv = null;

        //退出时，清除缓存图片
        deleteFolderFile(saveHdViewPath);
        HdViewFileUtils.hdViewDataList.clear();

        HdViewFileUtils.videoActivity = null;

        super.onDestroy();
    }
}
