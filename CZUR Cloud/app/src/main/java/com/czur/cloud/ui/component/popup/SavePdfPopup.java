package com.czur.cloud.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.TextView;

import com.czur.cloud.R;
import com.czur.cloud.util.validator.StringUtils;

/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class SavePdfPopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;


    public SavePdfPopup(Context context) {
        super(context);
    }

    public SavePdfPopup(Context context, int theme) {
        super(context, theme);
    }


    public static class Builder {
        private Context context;

        private String title;
        private View contentsView;

        private OnClickListener positiveListener;
        private OnClickListener onNegativeListener;
        private OnDismissListener onDismissListener;

        public Builder(Context context) {
            this.context = context;

        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }




        public SavePdfPopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            final SavePdfPopup dialog = new SavePdfPopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);

            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;

            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final SavePdfPopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.save_pdf_dialog, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);

            ImageView img = (ImageView)layout. findViewById(R.id.img);
            TextView title = (TextView) layout.findViewById(R.id.title);
            Animation hyperspaceJumpAnimation = AnimationUtils.loadAnimation(
                    context, R.anim.dialog_anim);
            // 使用ImageView显示动画
            img.startAnimation(hyperspaceJumpAnimation);
            if (contentsView == null) {

                if (StringUtils.isNotEmpty(this.title)) {
                    title.setText(this.title + StringUtils.EMPTY);
                }
            }

            return layout;
        }
    }
}
