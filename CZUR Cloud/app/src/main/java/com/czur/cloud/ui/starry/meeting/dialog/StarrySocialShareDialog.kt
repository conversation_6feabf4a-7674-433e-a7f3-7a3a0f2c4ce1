package com.czur.cloud.ui.starry.meeting.dialog

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.TextView
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.ui.starry.meeting.baselib.utils.getDrawable

class StarrySocialShareDialog(context: Context?, themeResId: Int) : Dialog(
    context!!, themeResId
), View.OnClickListener {
    private var mContext: Context? = null
    private var weixinShare: LinearLayout? = null
    private var copyShare: LinearLayout? = null
    private var shareDialogCancelBtn: TextView? = null

    constructor(context: Context?, shareDialogOnClickListener: ShareDialogOnClickListener?) : this(
        context,
        R.style.SocialAccountDialogStyle
    ) {
        //重点实现R.style.DialogStyle 动画效果
        this.shareDialogOnClickListener = shareDialogOnClickListener
        mContext = context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.starry_share_bottom_sheet)
        val params = window?.attributes
        weixinShare = window?.findViewById<View>(R.id.weixin_share) as LinearLayout
        copyShare = window?.findViewById<View>(R.id.copy_share) as LinearLayout
        shareDialogCancelBtn = window?.findViewById<View>(R.id.share_dialog_cancel_btn) as TextView

        //设置显示的位置
        params?.gravity = Gravity.BOTTOM
        //设置dialog的宽度
        params?.width = WindowManager.LayoutParams.MATCH_PARENT
        params?.height = WindowManager.LayoutParams.WRAP_CONTENT
        window?.setBackgroundDrawable(getDrawable(R.color.transparent))

        window?.attributes = params
        weixinShare?.setOnClickListener(this)
        copyShare?.setOnClickListener(this)
        shareDialogCancelBtn?.setOnClickListener(this)

        if (BuildConfig.IS_OVERSEAS) {
            weixinShare?.visibility = View.GONE
        }
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.weixin_share -> if (shareDialogOnClickListener != null) {
                shareDialogOnClickListener?.onShareItemClick(R.id.weixin_share)
            }
            R.id.copy_share -> if (shareDialogOnClickListener != null) {
                shareDialogOnClickListener?.onShareItemClick(R.id.copy_share)
            }
            R.id.share_dialog_cancel_btn -> if (shareDialogOnClickListener != null) {
                shareDialogOnClickListener?.onShareItemClick(R.id.share_dialog_cancel_btn)
            }
            else -> {}
        }
    }

    /**
     *
     * 点击事件接口
     *
     */
    interface ShareDialogOnClickListener {
        /**
         *
         * @param viewId
         */
        fun onShareItemClick(viewId: Int)
    }

    private var shareDialogOnClickListener: ShareDialogOnClickListener? = null
    private fun setShareDialogOnClickListener(shareDialogOnClickListener: ShareDialogOnClickListener) {
        this.shareDialogOnClickListener = shareDialogOnClickListener
    }
}