package com.czur.cloud.netty.core

import java.util.*

/**
 * Created by 陈丰尧 on 4/6/21
 * 参考网址:https://shimo.im/docs/IXMDMD6hrd4bqjMB/read
 * CZMessage
 * |- PingMsg(心跳包)
 * |- CZMInfoMsg(带各种信息的)
 *    |- CZBizMsg (业务信息的Msg)
 */

/**
 * Msg的Type属性
 */
enum class CZMsgType {
    HEARTBEAT,  //心跳包
}

open class CZMessageNew(
    val type: CZMsgType, //消息类型
) {
    var timestamp: Long = Date().time

    override fun toString(): String {
        return "CZMessageNew(type=$type)"
    }
}

/**
 * 心跳包, Body是String
 */
class PingMsg : CZMessageNew(CZMsgType.HEARTBEAT) {
    // 心跳包的body属性是时间戳, 但是时String类型的
    val body = timestamp.toString()
}
