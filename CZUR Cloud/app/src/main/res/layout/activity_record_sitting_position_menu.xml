<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <include layout="@layout/layout_user_top_bar" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="17.5dp"
                android:text="@string/aura_home_sitting_remind"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />

            <com.github.iielse.switchbutton.SwitchView
                android:id="@+id/sitting_position_menu_remind_switch_btn"
                android:layout_width="50dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="17.5dp"
                app:isOpened="false"
                app:primaryColor="@color/blue_33c5e4"
                app:primaryColorDark="@color/blue_33c5e4"
                app:ratioAspect="0.6"
                android:layout_height="30dp"/>


        </RelativeLayout>

        <LinearLayout
            android:id="@+id/sitting_position_menu_sensitivity_rl"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/gray_e5" />
            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:background="@color/gary_f9"
                android:paddingStart="18.5dp"
                android:gravity="center_vertical"
                android:text="@string/sensitivity_remind_text"
                android:textColor="@color/gary_c4"
                android:textSize="12sp" />
            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/gray_e5" />
            <RelativeLayout
                android:id="@+id/sensitivity_high_rl"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/gary_ff">

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="17.5dp"
                    android:text="@string/high"
                    android:textColor="@color/normal_blue"
                    android:textSize="15sp" />
                <ImageView
                    android:visibility="gone"
                    android:id="@+id/sensitivity_high_right"
                    android:layout_width="17dp"
                    android:layout_height="12dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="17dp"
                    android:background="@mipmap/right_aura_home_icon"
                    android:padding="10dp" />

                <View
                    android:layout_marginStart="17.5dp"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_alignParentBottom="true"
                    android:background="@color/gray_e5" />
            </RelativeLayout>
            <RelativeLayout
                android:id="@+id/sensitivity_middel_rl"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/gary_ff">

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="17.5dp"
                    android:text="@string/middle"
                    android:textColor="@color/normal_blue"
                    android:textSize="15sp" />
                <ImageView
                    android:visibility="gone"
                    android:id="@+id/sensitivity_middle_right"
                    android:layout_width="17dp"
                    android:layout_height="12dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="17dp"
                    android:background="@mipmap/right_aura_home_icon"
                    android:padding="10dp" />

                <View
                    android:layout_marginStart="17.5dp"
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_alignParentBottom="true"
                    android:background="@color/gray_e5" />
            </RelativeLayout>
            <RelativeLayout
                android:id="@+id/sensitivity_low_rl"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/gary_ff">

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="17.5dp"
                    android:text="@string/low"
                    android:textColor="@color/normal_blue"
                    android:textSize="15sp" />
                <ImageView
                    android:visibility="gone"
                    android:id="@+id/sensitivity_low_right"
                    android:layout_width="17dp"
                    android:layout_height="12dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="17dp"
                    android:background="@mipmap/right_aura_home_icon"
                    android:padding="10dp" />

            </RelativeLayout>

        </LinearLayout>


        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"
            android:background="@color/gray_e5" />
        <View
            android:layout_marginTop="13.5dp"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_alignParentBottom="true"

            android:background="@color/gray_e5" />
        <RelativeLayout
            android:id="@+id/sitting_position_menu_record_rl"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:background="@color/gary_ff">


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginLeft="17.5dp"
                android:text="@string/aura_home_sitting_record"
                android:textColor="@color/normal_blue"
                android:textSize="15sp" />
            <ImageView
                android:layout_width="6dp"
                android:layout_height="10.5dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="17dp"
                android:background="@mipmap/user_right_gray_arrow"
                android:padding="10dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_alignParentBottom="true"
                android:background="@color/gray_e5" />
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/volume_rl"
            android:layout_marginTop="14dp"
            android:background="@color/white"
            android:layout_width="match_parent"
            android:layout_height="110dp">
            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_marginTop="13dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="15sp"
                android:textColor="@color/black_22"
                android:layout_marginLeft="17.5dp"
                android:text="@string/vol_text"/>
            
            <RelativeLayout
                android:layout_above="@+id/vol_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                
                <ImageView
                    android:layout_marginLeft="15dp"
                    android:id="@+id/vol_minus"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:src="@mipmap/vol_minus_icon"/>
                <TextView
                    android:id="@+id/vol_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/blue_29b0d7"
                    android:layout_centerInParent="true"
                    android:textSize="14sp"/>

                <ImageView
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="15dp"
                    android:id="@+id/vol_plus"
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:src="@mipmap/vol_plus_icon"/>
            </RelativeLayout>
            <LinearLayout
                android:id="@+id/vol_ll"
                android:layout_alignParentBottom="true"
                android:layout_width="match_parent"
                android:layout_height="35dp">

                <com.czur.cloud.ui.component.progressbar.AuraMateProgressBar
                    android:layout_marginTop="7.5dp"
                    android:id="@+id/vol_progress"
                    android:layout_gravity="center"
                    android:layout_marginLeft="15dp"
                    android:layout_marginRight="15dp"
                    android:layout_width="match_parent"
                    android:layout_height="35dp"
                    app:backColors="@color/gary_f1"
                    app:barColors="@color/blue_29b0d7"
                    app:lineWidth="5dp" />
            </LinearLayout>

        </RelativeLayout>


    </LinearLayout>



</RelativeLayout>

