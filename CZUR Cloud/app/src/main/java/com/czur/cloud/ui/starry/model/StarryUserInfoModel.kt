package com.czur.cloud.ui.starry.model

import com.czur.cloud.ui.starry.common.StarryConstants.STARRY_USERTYPE_COMP
import com.czur.cloud.ui.starry.common.StarryConstants.STARRY_USERTYPE_PERSION

data class StarryUserInfoModel(
//    "data": {
//        "id": 2552,
//        "accountNo": ***********,
//        "czurId": 3747,
//        "mobile": "***********",
//        "name": "Jason315",
//        "kind": "2",
//        "type": 1,
//        "createTime": "2021-08-24 11:51:22",
//        "updateTime": "2021-08-24 11:51:22",
//        "headImage": "https://changer-passport.oss-cn-beijing.aliyuncs.com/fa4moh0cdrudrdk.png",
//        "num": 0,
//        "inEnterprise": true,
//        "portLimit": 10,
//        "remain": 0,
//        "admin": false
//    }
        val id: String = "0",
        var accountNo: String = "0",
        var czurId: String = "",
        var mobile: String = "",
        val name: String = "",
        val kind: String = "",
        val type: Int = 1,
        val createTime: String = "",
        val updateTime: String = "",
        val headImage: String = "",
        val num: Int = 0,
        val inEnterprise: Boolean = false,
        val portLimit: Int = 0,
        val remain: String = "0",
        val admin: Boolean = false,
        // 用户类型 普通用户，企业联系人用户
        var userType: String = STARRY_USERTYPE_COMP,
        // 记忆用户上次的cam，mic开关状态
        var lastCam: Boolean = false,
        var lastMic: Boolean = true,
        var targetMicStatus: String = "-1",//重新设置目标改变状态都为-1(不处理) 0(关) 1(开)
        var targetCamStatus: String = "-1",//重新设置目标改变状态都为-1(不处理) 0(关) 1(开)
        var callInTargetMicStatus: String = "-1",//重新设置目标改变状态都为-1(不处理) 0(关) 1(开)
        var callInTargetCamStatus: String = "-1",//重新设置目标改变状态都为-1(不处理) 0(关) 1(开)
        // 记忆用户上次的pwd开关状态
        var lastShowPwd: Boolean = false,
        // 记忆用户上次的pwd显示状态，眼睛状态
        var lastPwdEye: Boolean = false,
        // 记忆用户上次的pwd
        var lastPwd: String = "",
        // 是否已经提示过弹窗,提示过以后不再提示,改为界面显示
        var hasShowedPermissionsDialog: Boolean = false,
        // 部分小米机型获取不到真实的设置结果 例如小米12,所以特殊小米机型弹窗后,不再进行提示
        var isSpecialXiaomi: Boolean = false,
        // 记录最新的一条通话记录
        var firstMeetingRecordId: String = "",
        // 记录上次关闭app的的时间
        var lastCloseAppTime: String = "",
)