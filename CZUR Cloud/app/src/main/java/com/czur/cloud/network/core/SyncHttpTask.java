package com.czur.cloud.network.core;

import static com.blankj.utilcode.util.StringUtils.getString;
import static com.czur.czurutils.log.CZURLogUtilsKt.logD;
import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;
import static com.czur.czurutils.log.CZURLogUtilsKt.logTagD;

import android.app.Activity;
import android.app.Application;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.LogUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.common.ChangerTimestampTypeAdapter;
import com.czur.cloud.common.MD5Utils;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.LogoutEvent;
import com.czur.cloud.event.StopServiceEvent;
import com.czur.cloud.event.StopSyncTimeCountEvent;
import com.czur.cloud.model.RegisterModel;
import com.czur.cloud.netty.observer.NettyUtils;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.account.LoginActivity;
import com.czur.cloud.ui.base.CzurCloudApplication;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.component.popup.SingleLoginPopup;
import com.czur.cloud.ui.eshare.EShareActivity;
import com.czur.cloud.ui.eshare.engine.HostHearBeat;
import com.czur.cloud.vendorPush.VendorPushTask;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONObject;

import java.lang.reflect.Type;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlin.jvm.functions.Function2;
import kotlinx.coroutines.flow.Flow;
import kotlinx.coroutines.flow.FlowKt;
import okhttp3.FormBody;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

public class SyncHttpTask {
    private String TAG = "com.czur.cloud.network.core.SyncHttpTask";
    private static final String FORMATTER = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final String DATA_CODE = "code";
    private static final String DATA_MSG = "msg";
    public static final String DATA_BODY = "body";
    public static final String DATA_DATA = "data";

    private static final int LOGIN_AGAIN_SUCCESS = 0;
    private static final int LOGIN_AGAIN_NOT_CONFORM = 1;

    private Gson gson = new GsonBuilder().setDateFormat(FORMATTER).registerTypeAdapter(Timestamp.class, new ChangerTimestampTypeAdapter()).create();
    private Handler handler = new Handler(Looper.getMainLooper());
    private long lastTime = 0;
    private long singleLoginTime = 0;
    private SingleLoginPopup commonPopup;

    private SyncHttpTask() {
    }

    public static SyncHttpTask getInstance() {
        return SingletonHolder.instance;
    }

    private static class SingletonHolder {
        static final SyncHttpTask instance = new SyncHttpTask();
    }

    <T> MiaoHttpEntity<T> syncGet(Application application, String url, Type type, HashMap<String, String> headers, ArrayList<String> logs, boolean isRetry) throws Exception {
        MiaoHttpEntity<T> entity = null;
        Request.Builder builder = new Request.Builder().url(url);
        if (headers.size() != 0) {
            for (HashMap.Entry<String, String> entry : headers.entrySet()) {
                builder.addHeader(entry.getKey(), entry.getValue());
            }
        }

        Response response;
        try {
            response = isRetry ? MiaoHttpManager.getInstance().getHttpClient().newCall(builder.build()).execute()
                    : MiaoHttpManager.getInstance().getHttpClientNoRetry().newCall(builder.build()).execute();
            if (response.isSuccessful()) {
                String json = response.body().string();
                responseLog(json, response, logs);
                entity = makeEntity(json, type);
                if (entity.getCode() == MiaoHttpManager.STATUS_TIMEOUT && LOGIN_AGAIN_SUCCESS == tokenTimeout(application)) {
                    String token = UserPreferences.getInstance(application).getToken();
                    builder.removeHeader("T-ID").addHeader("T-ID", token);
                    Response responseAgain = isRetry ? MiaoHttpManager.getInstance().getHttpClient().newCall(builder.build()).execute()
                            : MiaoHttpManager.getInstance().getHttpClientNoRetry().newCall(builder.build()).execute();
                    if (responseAgain.isSuccessful()) {
                        String jsonAgain = responseAgain.body().string();
                        responseLog(jsonAgain, responseAgain, logs);
                        entity = makeEntity(jsonAgain, type);
                    }
                    responseAgain.body().close();
                } else if (entity.getCode() == MiaoHttpManager.STATUS_SINGLE_LOGIN) {
                    boolean showSingleLoginDialog = false;// 只有是登录状态下的第一次检测才弹出提示框
                    singleLoginTime = System.currentTimeMillis();
//                    if (!(ActivityUtils.getTopActivity() instanceof LoginActivity)) {
                    if (!CzurCloudApplication.isOtherLogin) {
                        CzurCloudApplication.isOtherLogin = true;
                        VendorPushTask.INSTANCE.off();
                        EventBus.getDefault().post(new StopServiceEvent(EventType.STOP_SYNC));
                        EventBus.getDefault().post(new StopSyncTimeCountEvent(EventType.STOP_SYNC_TIME_COUNT));
                        if (UserPreferences.getInstance(application).isUserLogin()){
                            showSingleLoginDialog = true;
                        }
                        UserPreferences.getInstance(application).setIsUserLogin(false);
                        NettyUtils.getInstance().stopNettyService();
//                        ServiceUtils.stopService(NettyService.class);
                        EventBus.getDefault().post(new LogoutEvent(EventType.LOG_OUT));
                        if (!EShareActivity.Companion.getEshareIsRunning()) {
                            Intent intent = new Intent(ActivityUtils.getTopActivity(), LoginActivity.class);
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                            ActivityUtils.startActivity(intent);
                        }
//
                        boolean finalShowSingleLoginDialog = showSingleLoginDialog;
                        handler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (EShareActivity.Companion.getEshareIsRunning()) {
                                    // 如果是正在投屏的状态, 不关闭投屏页面
                                    List<Activity> activities = ActivityUtils.getActivityList();
                                    for (int i = 0; i < activities.size(); i++) {
                                        if (activities.get(i).getClass().getSimpleName().contains("EShareActivity")
                                                || (activities.get(i).getClass().getSimpleName().contains("FindDeviceActivity"))
                                        ) {
                                            continue;
                                        } else {
                                            activities.get(i).finish();
                                        }
                                    }
                                } else {
                                    ActivityUtils.finishAllActivitiesExceptNewest();
                                }

                                if (finalShowSingleLoginDialog){
                                    if (commonPopup == null) {
                                        SingleLoginPopup.Builder builder1 = new SingleLoginPopup.Builder(ActivityUtils.getTopActivity(), CloudCommonPopupConstants.COMMON_ONE_BUTTON);
                                        builder1.setTitle(getString(R.string.prompt));
                                        builder1.setOnPositiveListener(new DialogInterface.OnClickListener() {
                                            @Override
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        });
                                        commonPopup = builder1.create();
                                        commonPopup.show();
                                    } else {
                                        if (commonPopup.isShowing()) {
                                            return;
                                        }
                                        //防止多次弹窗
                                        commonPopup.dismiss();
                                        SingleLoginPopup.Builder builder1 = new SingleLoginPopup.Builder(ActivityUtils.getTopActivity(), CloudCommonPopupConstants.COMMON_ONE_BUTTON);
                                        builder1.setTitle(getString(R.string.prompt));
                                        builder1.setOnPositiveListener(new DialogInterface.OnClickListener() {
                                            @Override
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        });
                                        commonPopup = builder1.create();
                                        commonPopup.show();
                                    }
                                }

                            }
                        }, 1000);
                    }
                }
            } else {
                responseLog("", response, logs);
            }
            response.body().close();
            return entity;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    <T> MiaoHttpEntity<T> syncPost(Application application, String url, Type type, HashMap<String, String> postParam, HashMap<String, String> headers, ArrayList<String> logs, boolean isRetry) throws Exception {
        MiaoHttpEntity<T> entity = null;
        FormBody.Builder builder = new FormBody.Builder();
        if (postParam != null && postParam.size() > 0) {
            for (Map.Entry<String, String> entry : postParam.entrySet()) {
                builder.add(entry.getKey(), entry.getValue());
            }
        }
        Request.Builder requestBuilder = new Request.Builder().url(url).post(builder.build());
        if (headers.size() != 0) {
            for (HashMap.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }
        Response response;
        try {
            response = isRetry ? MiaoHttpManager.getInstance().getHttpClient().newCall(requestBuilder.build()).execute()
                    : MiaoHttpManager.getInstance().getHttpClientNoRetry().newCall(requestBuilder.build()).execute();
            if (response.isSuccessful()) {
                String json = response.body().string();
                responseLog(json, response, logs);
                entity = makeEntity(json, type);
                Log.d("xxx", entity.getCode() + "重新登录的code");
                if (entity.getCode() == MiaoHttpManager.STATUS_TIMEOUT && LOGIN_AGAIN_SUCCESS == tokenTimeout(application)) {
                    String token = UserPreferences.getInstance(application).getToken();
                    requestBuilder.removeHeader("T-ID").addHeader("T-ID", token);
                    Response responseAgain = isRetry ? MiaoHttpManager.getInstance().getHttpClient().newCall(requestBuilder.build()).execute()
                            : MiaoHttpManager.getInstance().getHttpClientNoRetry().newCall(requestBuilder.build()).execute();
                    if (responseAgain.isSuccessful()) {
                        try {
                            String jsonAgain = responseAgain.body().string();
                            responseLog(jsonAgain, responseAgain, logs);
                            entity = makeEntity(jsonAgain, type);
                        } catch (Exception e) {
                            logE("syncPost.e=" + e.toString());
                        }
                    }
                    responseAgain.body().close();
                } else if (entity.getCode() == MiaoHttpManager.STATUS_SINGLE_LOGIN) {
                    boolean showSingleLoginDialog = false;// 只有是登录状态下的第一次检测才弹出提示框
                    singleLoginTime = System.currentTimeMillis();
//                    if (!(ActivityUtils.getTopActivity() instanceof LoginActivity)) {
                    if (!CzurCloudApplication.isOtherLogin) {
                        CzurCloudApplication.isOtherLogin = true;
                        VendorPushTask.INSTANCE.off();
                        EventBus.getDefault().post(new StopServiceEvent(EventType.STOP_SYNC));
                        EventBus.getDefault().post(new StopSyncTimeCountEvent(EventType.STOP_SYNC_TIME_COUNT));
                        if (UserPreferences.getInstance(application).isUserLogin()) {
                            showSingleLoginDialog = true;
                        }
                        UserPreferences.getInstance(application).setIsUserLogin(false);
//                        ServiceUtils.stopService(NettyService.class);
                        NettyUtils.getInstance().stopNettyService();
                        EventBus.getDefault().post(new LogoutEvent(EventType.LOG_OUT));
                        if (!EShareActivity.Companion.getEshareIsRunning()) {
                            Intent intent = new Intent(ActivityUtils.getTopActivity(), LoginActivity.class);
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                            ActivityUtils.startActivity(intent);
                        }
                        boolean finalShowSingleLoginDialog = showSingleLoginDialog;
                        handler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (EShareActivity.Companion.getEshareIsRunning()) {
                                    // 如果是正在投屏的状态, 不关闭投屏页面
                                    List<Activity> activities = ActivityUtils.getActivityList();
                                    for (int i = 0; i < activities.size(); i++) {
                                        if (activities.get(i).getClass().getSimpleName().contains("EShareActivity")
                                                || (activities.get(i).getClass().getSimpleName().contains("FindDeviceActivity"))
                                        ) {
                                            continue;
                                        } else {
                                            activities.get(i).finish();
                                        }
                                    }
                                } else {
                                    List<Activity> activities = ActivityUtils.getActivityList();
                                    for (int i = 0; i < activities.size(); i++) {
                                    }

                                    ActivityUtils.finishAllActivitiesExceptNewest();
                                }


                                if (finalShowSingleLoginDialog) {
                                    if (commonPopup == null) {
                                        SingleLoginPopup.Builder builder1 = new SingleLoginPopup.Builder(ActivityUtils.getTopActivity(), CloudCommonPopupConstants.COMMON_ONE_BUTTON);
                                        builder1.setTitle(getString(R.string.prompt));
                                        builder1.setOnPositiveListener(new DialogInterface.OnClickListener() {
                                            @Override
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        });
                                        commonPopup = builder1.create();
                                        commonPopup.show();
                                    } else {
                                        if (commonPopup.isShowing()) {
                                            return;
                                        }
                                        //防止多次弹窗
                                        commonPopup.dismiss();
                                        SingleLoginPopup.Builder builder1 = new SingleLoginPopup.Builder(ActivityUtils.getTopActivity(), CloudCommonPopupConstants.COMMON_ONE_BUTTON);
                                        builder1.setTitle(getString(R.string.prompt));
                                        builder1.setOnPositiveListener(new DialogInterface.OnClickListener() {
                                            @Override
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        });
                                        commonPopup = builder1.create();
                                        commonPopup.show();
                                    }
                                }

                            }

                        }, 1000);
                    }

                }
            } else {
                responseLog("", response, logs);
            }
            response.body().close();
            return entity;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private int tokenTimeout(final Application application) throws Exception {
        UserPreferences userPreferences = UserPreferences.getInstance(application);
        String loginUsername = userPreferences.getLoginUserName();
        String loginPassword = MD5Utils.md5(userPreferences.getLoginPassword());
        String channel = userPreferences.getChannel();
        String thirdPartyOpenid = userPreferences.getThirdPartyOpenid();
        final String thirdPartyPlatName = userPreferences.getThirdPartyPlatName();
        String servicePlatName = userPreferences.getServicePlatName();
        String thirdPartyToken = userPreferences.getThirdPartyToken();
        String thirdPartyRefreshToken = userPreferences.getThirdPartyRefreshToken();
        boolean thirdParty = userPreferences.isThirdParty();
        if (thirdParty) {
            // 微信微博先尝试续第三方AccessToken
            if (thirdPartyPlatName.equals("Wechat")) {
                String urlBuilder = "https://api.weixin.qq.com/sns/oauth2/refresh_token?appid=wxfdaf39a42bd1152c&grant_type=refresh_token&refresh_token=" + thirdPartyRefreshToken;
                Request request = new Request.Builder().url(urlBuilder).get().build();
                Response response = MiaoHttpManager.getInstance().getHttpClientWeixin().newCall(request).execute();
                if (response.isSuccessful()) {
                    String resultStr = response.body().string();
                    JSONObject jsonObject = new JSONObject(resultStr);
                    String newAccessToken = null;
                    String newRefreshToken = null;
                    try {
                        newAccessToken = jsonObject.getString("access_token");
                        newRefreshToken = jsonObject.getString("refresh_token");
                    } catch (org.json.JSONException e) {
                        logE("tokenTimeout.e=" + e.toString());
                    }
                    if (newAccessToken == null) {
                        if (System.currentTimeMillis() - lastTime <= 2000) {
                            return LOGIN_AGAIN_NOT_CONFORM;
                        }
                        thirdPartGoLogin(application, thirdPartyPlatName);
                        return LOGIN_AGAIN_NOT_CONFORM;
                    } else {
                        thirdPartyToken = newAccessToken;
                        userPreferences.setThirdPartyToken(newAccessToken);
                        userPreferences.setThirdPartyRefreshToken(newRefreshToken);
                        logD("微信刷新AccessToken成功");
                    }
                    Log.d("weixin", resultStr);
                } else {
                    throw new Exception("重新登录失败或其他code");
                }
            } else if (thirdPartyPlatName.equals("SinaWeibo")) {
                RequestBody formBody = new FormBody.Builder()
                        .add("client_id", "3027241250")
                        .add("client_secret", "2d19a8e82a3bb17ac8e5985ba4373d4f")
                        .add("grant_type", "refresh_token")
                        .add("redirect_uri", "https://www.sina.com")
                        .add("refresh_token", thirdPartyRefreshToken).build();
                Request request = new Request.Builder().url("https://api.weibo.com/oauth2/access_token").post(formBody).build();
                Response response = MiaoHttpManager.getInstance().getHttpClientWeibo().newCall(request).execute();
                if (response.isSuccessful()) {
                    String resultStr = response.body().string();
                    JSONObject jsonObject = new JSONObject(resultStr);
                    String newAccessToken = null;
                    String refreshToken = null;
                    try {
                        newAccessToken = jsonObject.getString("access_token");
                        refreshToken = jsonObject.getString("refresh_token");
                    } catch (org.json.JSONException e) {
                        logE("tokenTimeout.e=" + e.toString());
                    }
                    if (newAccessToken == null) {
                        if (System.currentTimeMillis() - lastTime <= 2000) {
                            return LOGIN_AGAIN_NOT_CONFORM;
                        }
                        thirdPartGoLogin(application, thirdPartyPlatName);
                        return LOGIN_AGAIN_NOT_CONFORM;
                    } else {
                        thirdPartyToken = newAccessToken;
                        userPreferences.setThirdPartyToken(thirdPartyToken);
                        userPreferences.setThirdPartyRefreshToken(refreshToken);
                        logD("微博刷新AccessToken成功");
                    }
                    Log.d("weixin", resultStr);
                } else {
                    ResponseBody responseBody = response.body();
                    if (responseBody != null) {
                        String result = responseBody.string();
                        JSONObject resultJson = new JSONObject(result);
                        if (resultJson.getString("error_code") != null) {
                            if (System.currentTimeMillis() - lastTime <= 2000) {
                                return LOGIN_AGAIN_NOT_CONFORM;
                            }
                            thirdPartGoLogin(application, thirdPartyPlatName);
                            return LOGIN_AGAIN_NOT_CONFORM;
                        } else {
                            throw new Exception("重新登录失败或其他code");
                        }
                    } else {
                        throw new Exception("重新登录失败或其他code");
                    }

                }
            }

            MiaoHttpEntity<RegisterModel> thirdPartyLoginModel = HttpManager.getInstance().requestPassport().thirdPartyLoginSync(channel, userPreferences.getIMEI(), CZURConstants.CLOUD_ANDROID,
                    servicePlatName, thirdPartyToken, thirdPartyOpenid, RegisterModel.class);
            if (thirdPartyLoginModel.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                userPreferences.setToken(thirdPartyLoginModel.getBody().getToken());
                logD("第三方登录接口续Token成功");
                return LOGIN_AGAIN_SUCCESS;
            } else if (thirdPartyLoginModel.getCode() == MiaoHttpManager.STATUS_THIRD_PARTY_TIME_OUT) {
                if (System.currentTimeMillis() - lastTime <= 2000) {
                    return LOGIN_AGAIN_NOT_CONFORM;
                }
                thirdPartGoLogin(application, thirdPartyPlatName);
                return LOGIN_AGAIN_NOT_CONFORM;
            } else {
                throw new Exception("重新登录失败或其他code");
            }
        } else {
            // username为空判断
            if (loginUsername == null || loginUsername.isEmpty()) {
                return LOGIN_AGAIN_NOT_CONFORM;
            }

            MiaoHttpEntity<RegisterModel> loginModel = HttpManager.getInstance().requestPassport().loginSync(CZURConstants.CLOUD_ANDROID, userPreferences.getIMEI(), channel, loginUsername, loginPassword, RegisterModel.class);
            if (loginModel.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                userPreferences.setToken(loginModel.getBody().getToken());
                return LOGIN_AGAIN_SUCCESS;
            } else if (loginModel.getCode() == MiaoHttpManager.STATUS_INVALID_USER_OR_PASSWORD) {
                if (System.currentTimeMillis() - lastTime <= 2000) {
                    return LOGIN_AGAIN_NOT_CONFORM;
                }

                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        lastTime = System.currentTimeMillis();
//                        ToastUtils.showShort(R.string.token_timeout_login_again);
                    }
                });
                if (!(ActivityUtils.getTopActivity() instanceof LoginActivity)) {
                    userPreferences.setIsUserLogin(false);
                    Intent intent = new Intent(application, LoginActivity.class);
                    intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                    application.startActivity(intent);
                }
                return LOGIN_AGAIN_NOT_CONFORM;
            } else {
                throw new Exception("重新登录失败或其他code");
            }
        }
    }

    private void thirdPartGoLogin(final Application application, final String thirdPartyPlatName) {
        handler.post(new Runnable() {
            @Override
            public void run() {
//                ToastUtils.showShort(String.format(application.getString(R.string.third_party_token_timeout_login_again), thirdPartyPlatName));
            }
        });
        if (!(ActivityUtils.getTopActivity() instanceof LoginActivity)) {
            UserPreferences userPreferences = UserPreferences.getInstance(application);
            userPreferences.setIsThirdParty(false);
            userPreferences.setIsUserLogin(false);
            Intent intent = new Intent(application, LoginActivity.class);
            intent.putExtra("platName", thirdPartyPlatName);
            intent.putExtra("isThirdPartyToken", true);
            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
            application.startActivity(intent);
        }
    }

    private <T> MiaoHttpEntity<T> makeEntity(String json, Type type) throws Exception {
        JSONObject jsonObject = new JSONObject(json);
        int code = jsonObject.getInt(DATA_CODE);
        String msg = jsonObject.has(DATA_MSG) ? jsonObject.getString(DATA_MSG) : "";
        MiaoHttpEntity<T> entity = new MiaoHttpEntity<>();
        entity.setCode(code);
        entity.setMsg(msg);
        if (jsonObject.has(DATA_BODY)) {
            String bodyJson = jsonObject.getString(DATA_BODY);
            if (bodyJson.startsWith("{")) {
                T t = gson.fromJson(bodyJson, type);
                entity.setBody(t);
            } else if (bodyJson.startsWith("[")) {
                List<T> list = gson.fromJson(bodyJson, type);
                entity.setBodyList(list);
            } else {
                entity.setBody((T) bodyJson);
            }
        }
        if (jsonObject.has(DATA_DATA)) {
            String bodyJson = jsonObject.getString(DATA_DATA);
            if (bodyJson.startsWith("{")) {
                T t = gson.fromJson(bodyJson, type);
                entity.setData(t);
            } else if (bodyJson.startsWith("[")) {
                List<T> list = gson.fromJson(bodyJson, type);
                entity.setDataList(list);
            } else {
                entity.setData((T) bodyJson);
            }
        }
        return entity;
    }

    private void responseLog(String json, Response response, ArrayList<String> logs) {
        int size = logs.size();
//        String[] array = logs.toArray(new String[size]);
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < size; i++) {
            builder.append(logs.get(i)).append("\n");
        }
        logI("请求参数：", builder.toString());
        logI(response.toString());
        logI("返回数据：", json);
    }
}
