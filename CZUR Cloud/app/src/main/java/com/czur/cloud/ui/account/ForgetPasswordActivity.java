package com.czur.cloud.ui.account;

import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.RegexUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.NoHintEditText;
import com.czur.cloud.ui.component.ProgressButton;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.StringUtils;
import com.czur.cloud.util.validator.StringUtilsKt;
import com.czur.cloud.util.validator.Validator;

import java.util.Locale;

/**
 * Created by Yz on 2018/3/7.
 * Email：<EMAIL>
 */

public class ForgetPasswordActivity extends BaseActivity implements View.OnClickListener {

    private ImageView accountBackBtn;
    private TextView accountTitle;
    private NoHintEditText accountEdt;
    private NoHintEditText codeEdt;
    private TextView getCodeBtn;
    private ProgressButton nextStepBtn;
    private TimeCount timeCount;

    private boolean codeHasContent = false;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private long currentTime;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_forget_password);
        initComponent();
        registerEvent();
    }

    private void initComponent() {
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);

        accountBackBtn = (ImageView) findViewById(R.id.account_back_btn);
        accountTitle = (TextView) findViewById(R.id.account_title);
        accountEdt = (NoHintEditText) findViewById(R.id.forget_password_account_edt);
        codeEdt = (NoHintEditText) findViewById(R.id.forget_password_code_edt);
        getCodeBtn = (TextView) findViewById(R.id.get_code_btn);
        nextStepBtn = (ProgressButton) findViewById(R.id.next_step_btn);
        //设置标题
        accountTitle.setText(R.string.forget_password);

    }

    private void registerEvent() {
        accountBackBtn.setOnClickListener(this);
        getCodeBtn.setOnClickListener(this);
        getCodeBtn.setSelected(true);
        nextStepBtn.setOnClickListener(this);
        nextStepBtn.setSelected(false);
        nextStepBtn.setClickable(false);
        accountEdt.addTextChangedListener(accountTextWatcher);
        codeEdt.addTextChangedListener(codeTextWatcher);
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.get_code_btn:
                validatorAccount();
                break;
            case R.id.next_step_btn:
                confirmIdentifyCode();
                break;
            case R.id.account_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            default:
                break;
        }
    }


    /**
     * @des: 验证手机号或者邮箱
     * @params:[]
     * @return:void
     */
    private void validatorAccount() {

        String accountStr = accountEdt.getText().toString();
        if (Validator.isEmpty(accountStr)) {
            showMessage(R.string.account_empty);
        } else {
            if (RegexUtils.isMobileExact(accountStr)) {
                getIdentifyCode();
            } else if (StringUtilsKt.isValidEmail(accountStr)) {
                getIdentifyCode();
            } else {
                showMessage(R.string.account_format_error);
            }

        }
    }

    private void getIdentifyCode() {

        Locale locale = getResources().getConfiguration().locale;
        String language = locale.toString();
        HttpManager.getInstance().requestPassport().findPwdSendCode(
                CZURConstants.CLOUD_ANDROID,
                userPreferences.getIMEI(),
                userPreferences.getChannel(),
                accountEdt.getText().toString(),
                EtUtils.getLocale(language),
                String.class,
                new MiaoHttpManager.Callback<String>() {
                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        timeCountBegin();
                        showMessage(R.string.toast_code_send);

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {

                        switch (entity.getCode()) {
                            case MiaoHttpManager.STATUS_CODE_1_MIN:
                                showMessage(R.string.toast_code_1_min);
                                break;
                            case MiaoHttpManager.STATUS_COND_1_MAIL:
                                showMessage(R.string.toast_code_1_min);
                                break;
                            case MiaoHttpManager.STATUS_5_MIN_4_TIME:
                                showMessage(R.string.toast_5_min_4_time);
                                break;
                            case MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY:
                                showMessage(R.string.toast_5_time_in_one_day);
                                break;
                            case MiaoHttpManager.STATUS_NOT_USER:
                                showMessage(R.string.toast_user_no_exist);
                                break;
                            case MiaoHttpManager.STATUS_ERROR:
                                showMessage(R.string.toast_internal_error);
                                break;
                            default:
                                showMessage(R.string.request_failed_alert);
                                break;
                        }
                    }

                    @Override
                    public void onError(Exception e) {
                        showMessage(R.string.request_failed_alert);
                    }
                });


    }


    /**
     * @des: 校验 验证码
     * @params:
     * @return:
     */

    private void confirmIdentifyCode() {
        currentTime = System.currentTimeMillis();
        KeyboardUtils.hideSoftInput(this);
        httpManager.requestPassport().findPwdNext(CZURConstants.CLOUD_ANDROID, userPreferences.getIMEI(), userPreferences.getChannel(),
                accountEdt.getText().toString(), codeEdt.getText().toString(), String.class, new MiaoHttpManager.Callback<String>() {
                    @Override
                    public void onStart() {
                        nextStepBtn.startDelayLoading(ForgetPasswordActivity.this);
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        successDelay(entity);

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {

                        if (entity.getCode() == MiaoHttpManager.STATUS_INVALID_CODE) {
                            failedDelay(R.string.toast_code_error);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_NOT_USER) {
                            failedDelay(R.string.toast_user_no_exist1);
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            failedDelay(R.string.toast_internal_error);
                        } else {
                            failedDelay(R.string.request_failed_alert);
                        }
                    }

                    @Override
                    public void onError(Exception e) {

                        failedDelay(R.string.request_failed_alert);
                    }
                });
    }

    public void confirmSuccess(MiaoHttpEntity<String> entity) {
        finish();

        //跳转到ResetPasswordActivity
        Intent intent = new Intent(ForgetPasswordActivity.this, ResetPasswordActivity.class);
        intent.putExtra("account", accountEdt.getText().toString());
        intent.putExtra("resetCode", entity.getBody());
        ActivityUtils.startActivity(intent);
    }

    private void failedDelay(final int failedText) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showMessage(failedText);
                            nextStepBtn.stopLoading();
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }


    private void successDelay(final MiaoHttpEntity<String> entity) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            confirmSuccess(entity);
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }
    /**
     * @des: 计时器
     * @params:
     * @return:
     */

    class TimeCount extends CountDownTimer {

        public TimeCount(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onFinish() {
            getCodeBtn.setText(R.string.resend_code);
            getCodeBtn.setClickable(true);
            getCodeBtn.setSelected(true);
            getCodeBtn.setTextColor(getColor(R.color.black_272f44));
        }

        @Override
        public void onTick(long millisUntilFinished) {
            getCodeBtn.setClickable(false);
            getCodeBtn.setText(millisUntilFinished / 1000 + " s");
            getCodeBtn.setSelected(false);
            getCodeBtn.setTextColor(getColor(R.color.grey_AEAEAE));
        }

    }


    /**
     * @des: 计时器开始倒计时60S
     * @params:
     * @return:void
     */
    private void timeCountBegin() {
        timeCount = new TimeCount(60000, 1000);
        timeCount.start();

    }

    private TextWatcher accountTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

            checkNextStepButtonToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {

            checkNextStepButtonToClick();
        }
    };

    private TextWatcher codeTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            if (s.length() > 0) {
                codeHasContent = true;
            } else {
                codeHasContent = false;
            }

            checkNextStepButtonToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            if (s.length() > 0) {
                codeHasContent = true;
            } else {
                codeHasContent = false;
            }
            checkNextStepButtonToClick();
        }
    };

    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private void checkNextStepButtonToClick() {

        boolean accountIsNotEmpty = Validator.isNotEmpty(accountEdt.getText().toString());
        boolean codeIsNotEmpty = Validator.isNotEmpty(codeEdt.getText().toString());

        if (accountIsNotEmpty && codeIsNotEmpty && codeHasContent) {
            nextStepBtn.setSelected(true);
            nextStepBtn.setClickable(true);
        } else {
            nextStepBtn.setSelected(false);
            nextStepBtn.setClickable(false);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (Validator.isNotEmpty(timeCount)) {
            timeCount.cancel();
        }

    }
}
