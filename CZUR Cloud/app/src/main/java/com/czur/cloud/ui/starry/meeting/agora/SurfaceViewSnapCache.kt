package com.czur.cloud.ui.starry.meeting.agora

import android.graphics.Bitmap

/**
 * Created by 陈丰尧 on 2022/3/3
 * 视频流的截图缓存
 * 这里最好弄成2级缓存, 防止内存溢出
 */
object SurfaceViewSnapCache {
    private val bitmapCache = mutableMapOf<String, Bitmap>()

    fun addBitmap(uid: String, bitmap: Bitmap) {
        bitmapCache[uid] = bitmap
    }

    fun getBitmap(uid: String): Bitmap? = bitmapCache[uid]

    // 清理全部缓存
    fun clearAll(){
        bitmapCache.clear()
    }
}