package com.czur.cloud.ui.starry.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.R
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.common.StarryConstants.RET_DATA_TRUE
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.model.MessageModel
import com.czur.cloud.ui.starry.model.Notice
import com.czur.cloud.ui.starry.network.NoNetworkException
import com.czur.czurutils.log.logE
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class StarryMessageViewModel: ViewModel() {
    private val DEFAULT_PAGE_NUM = 1

    //消息列表
    var mNoticesList = MutableLiveData<List<Notice>>()
    // 全部已读返回
    var flagAllRead = MutableLiveData<Boolean>(false)
    // 记录上次获取的消息的总数量
    var mMessageTotalOld = 0

    // 未读数量
    val mUnreadCount = MutableLiveData<Int>(0)

    // 本次加载更多的数量，比对是否没有更多了
    var mMoreCount = StarryConstants.STARRY_CALL_PAGE_SIZE

    // 当前点选的消息
    var mCurrentNotice = MutableLiveData<Notice>()

    // recycleview当前为选择模式 true:选择模式；false：列表模式
    var mRecycleMode = MutableLiveData<Boolean>(false)

    // 列表数据变化,需要刷新列表的头和尾
    var isRefreshListFlag = MutableLiveData(false)

    // 当前页数
    private var currentPageNum = DEFAULT_PAGE_NUM
    private val allNoticeList = ArrayList<Notice>()

    private val starryPreferences by lazy {
        StarryPreferences.getInstance()
    }

    // 当前选中的message
    var isCheckedMap = MutableLiveData<LinkedHashMap<String, String>>()

    // 切换选择模式
    fun changeRecycleMode(mode: Boolean){
        mRecycleMode.postValue(mode)
    }

    //获取消息详情
    fun getNotice(id: String){
        launch {
            val accountNo = starryPreferences?.accountNo ?: ""

            try {
                val retData = MessageModel.getNotice(id, accountNo)
                mCurrentNotice.postValue(retData)

            }catch (e: Exception){
                logE("StarryMessageViewModel.getNotice.onFailure.e=${e.toString()}")
            }
        }
    }

    // 更改消息状态 all
    fun updateNoticesStatusAllRead() {
        viewModelScope.launch {
            val updateRes = updateNoticesStatus("0", StarryConstants.STARRY_MESSAGE_STATUS_READ)
            delay(300)//不加延迟有可能出现已读但未被删除的情况
            if (updateRes) {
                getNoticesListRefresh()
            }
            // 不要被变量名迷惑了, 👇🏻这个是控制Loading转圈的UI的
            flagAllRead.value = true
        }
    }

    // 更改消息状态
    suspend fun updateNoticesStatus(id: String, status: Int): Boolean {
        val accountNo = starryPreferences?.accountNo ?: ""
        return try {
            MessageModel.updateNoticesStatus(id, status.toString(), accountNo)
        } catch (e: Exception) {
            logE("StarryMessageViewModel.getNoticesList.onFailure.e=${e.toString()}")
            false
        }
    }

    fun getNoticesListMore(){
        currentPageNum ++
        getNoticesList(1)
    }

    // 获取列表，刷新
    fun getNoticesListRefresh(){
        currentPageNum = DEFAULT_PAGE_NUM
        allNoticeList.clear()
        getNoticesList(0)
    }

    // 获取列表数据
    private fun getNoticesList(index: Int){
        viewModelScope.launch {
            val pageNum = currentPageNum.toString()
            val pageSize = StarryConstants.STARRY_CALL_PAGE_SIZE.toString()
            val accountNo = starryPreferences?.accountNo ?: ""

            if (index ==0){
                allNoticeList.clear()
            }

            try {
                val messageData = MessageModel.getNoticesList(pageNum, pageSize, accountNo)
                val listMessage = messageData.notices
                mMessageTotalOld = messageData.total ?: 0
                mMoreCount = listMessage.size ?: 0
                allNoticeList.addAll(listMessage)

                val unreadCount = allNoticeList.count {
                    it.status == StarryConstants.STARRY_MESSAGE_STATUS_UNREAD
                }
                mUnreadCount.postValue(unreadCount)
                mNoticesList.postValue(allNoticeList)
            }catch (e: Exception){
                currentPageNum --
                if (currentPageNum < DEFAULT_PAGE_NUM)
                    currentPageNum = DEFAULT_PAGE_NUM
                logE("StarryMessageViewModel.getNoticesList.onFailure.e=${e.toString()}")
            }
        }
    }

    // 获取列表，更新,保留原来的位置和记录条数
    // 获取列表数据总数
    suspend fun getNoticesListTotal(): Int{
        val pageNum = DEFAULT_PAGE_NUM.toString()
        val pageSize = StarryConstants.STARRY_CALL_PAGE_SIZE.toString()
        val accountNo = starryPreferences?.accountNo ?: ""
        val ret = withContext(Dispatchers.IO) {
            val messageData = MessageModel.getNoticesList(pageNum, pageSize, accountNo)
            val total = messageData.total
            val count = total - mMessageTotalOld
            if (total > mMessageTotalOld) {
                val listMessage = messageData.notices
                var index = 0
                for (item: Notice in listMessage) {
                    allNoticeList.addAll(index, listOf(item))
                    index ++
                    if (index >= count-1){
                        break
                    }
                }
                mNoticesList.postValue(allNoticeList)
            }
            count
        }

        return ret
    }


    // 删除消息all
    fun deleteAllNotices(){
        deleteNotices("0")
    }

    // 删除消息
    fun deleteNotices(id: String) {
        viewModelScope.launch {
            if ("0"==id){
                updateNoticesStatusAllRead()
                delay(300)//不加延迟有可能出现已读但未被删除的情况
            }else{
                updateNoticesStatus(id, StarryConstants.STARRY_MESSAGE_STATUS_READ)
            }

            val accountNo = starryPreferences.accountNo
            try {
                val retFlag = MessageModel.deleteNotices(id, accountNo)
                if (retFlag == RET_DATA_TRUE){
                    getNoticesListRefresh()
                    isRefreshListFlag.postValue(true)
                }
            } catch (netWorkExp:NoNetworkException) {
                ToastUtils.showLong(R.string.starry_network_error_msg)
            } catch (e: Exception){
                logE("StarryMessageViewModel.deleteNotices.onFailure.e=${e.toString()}")
            }
        }
    }

    suspend fun deleteNoticesByIds(syncJson: String):Boolean {
        val ret = withContext(Dispatchers.IO) {
            MessageModel.deleteByIds(syncJson)
        }
        return ret
    }

}