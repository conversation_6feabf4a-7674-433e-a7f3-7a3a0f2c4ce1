<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="MissingPrefix">

    <LinearLayout
        android:layout_width="243dp"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:background="@drawable/btn_rec_8_bg_with_white"
        android:gravity="center_horizontal"
        android:orientation="vertical">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="39dp">

            <ImageView
                android:id="@+id/back_btn"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:padding="10dp"
                android:src="@mipmap/new_popup_close" />

        </RelativeLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_gravity="center_horizontal"
                android:src="@mipmap/change_language" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:gravity="center"
                android:textColor="@color/black_22"
                android:textSize="15sp"
                android:text="@string/change_language" />



        </LinearLayout>




        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/confirm_btn"
            android:layout_width="161dp"
            android:layout_height="36dp"
            android:layout_marginTop="36dp"
            android:layout_marginBottom="15dp"
            android:layout_weight="1"
            android:background="@drawable/btn_rec_8_bg_with_blue_aura_home"
            android:gravity="center"
            android:text="@string/change_text"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </LinearLayout>
</RelativeLayout>