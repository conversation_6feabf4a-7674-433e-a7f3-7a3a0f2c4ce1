package com.czur.cloud.ui.et.wifi;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;

import android.content.Context;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.Nullable;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.entity.realm.EtWifiHistoryEntity;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.DownloadMp3SuccessEvent;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.network.core.NoNetworkException;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.RippleAnimationView;
import com.czur.cloud.ui.et.EtManageActivity;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.io.IOException;

import io.realm.Realm;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class WifiConnectingActivity extends BaseActivity implements View.OnClickListener {
    private ImageView normalBackBtn;
    private MediaPlayer mMediaPlayer;
    private String mp3Name;
    private String folderName;
    private HttpManager httpManager;
    private WeakHandler handler;
    private String createTime;
    private boolean isAlive = true;
    private RippleAnimationView rippleAnimationView;

    private String ssid, password, deviceId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_wifi_connecting);
        initComponent();
        registerEvent();
        EventBus.getDefault().register(this);

    }

    private void initComponent() {
        deviceId = getIntent().getStringExtra("deviceId");
        password = getIntent().getStringExtra("password");
        ssid = getIntent().getStringExtra("ssid");

        httpManager = HttpManager.getInstance();
        handler = new WeakHandler();
        folderName = getCacheDir().getPath() + CZURConstants.TEMP + File.separator;
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        rippleAnimationView = (RippleAnimationView) findViewById(R.id.layout_RippleAnimation);
        rippleAnimationView.startRippleAnimation();

        AudioManager mAudioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        int maxVolume = mAudioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        maxVolume -= 12;
        mAudioManager.setStreamVolume(AudioManager.STREAM_MUSIC, maxVolume, 0);

    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case DOWNLOAD_MP3_SUCCESS:
                DownloadMp3SuccessEvent eventType = (DownloadMp3SuccessEvent) event;
                mp3Name = eventType.getMp3Name();
                deviceId = eventType.getDeviceId();
                createTime = eventType.getCreateTime();
                playAudio(folderName + mp3Name);
                break;
            default:
                break;
        }
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.normal_back_btn:
                ActivityUtils.finishToActivity(EtManageActivity.class,false);
                break;
            default:
                break;
        }
    }
    @Override
    public void onBackPressed() {
        super.onBackPressed();
        ActivityUtils.finishToActivity(EtManageActivity.class,false);
    }
    private void playAudio(String url) {
        try {
            killMediaPlayer();
            mMediaPlayer = new MediaPlayer();
            mMediaPlayer.setDataSource(url);
            mMediaPlayer.prepare();
            mMediaPlayer.start();
            mMediaPlayer.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
                @Override
                public void onCompletion(MediaPlayer mp) {
                    checkIsSuccess();
                }
            });
        } catch (IllegalStateException | IOException e) {
            logE(e.toString());
        }

    }

    private void checkIsSuccess() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                long startTime = System.currentTimeMillis();
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                while (System.currentTimeMillis() - startTime < 30000) {
                    try {
                        final MiaoHttpEntity<String> entity = httpManager.request().deviceOk(deviceId, createTime, String.class);
                        if (MiaoHttpManager.STATUS_SUCCESS == entity.getCode()) {
                            EtWifiHistoryEntity etWifiHistoryEntity = new EtWifiHistoryEntity();
                            etWifiHistoryEntity.setCreateTime(System.currentTimeMillis());
                            etWifiHistoryEntity.setPassword(password);
                            etWifiHistoryEntity.setSsid(ssid);
                            Realm realm = Realm.getDefaultInstance();
                            realm.executeTransaction(new Realm.Transaction() {
                                @Override
                                public void execute(Realm realm) {
                                    realm.copyToRealmOrUpdate(etWifiHistoryEntity);
                                }
                            });
                            realm.close();
                            if (isAlive) {
                                ActivityUtils.startActivity(WifiConnectSuccessActivity.class);
                            }
                            return;
                        }
                        if (!isAlive) {
                            return;
                        }
                        Thread.sleep(2000);
                    } catch (Exception e) {
                        if (e.getCause() instanceof NoNetworkException) {
                            goFailedActivity();
                            break;
                        } else {
                            logE(e.toString());
                        }
                    }
                }
                goFailedActivity();
            }
        }).start();
    }

    private void goFailedActivity() {
        handler.post(new Runnable() {
            @Override
            public void run() {
                ActivityUtils.startActivity(WifiConnectErrorActivity.class);

            }
        });
    }

    private void killMediaPlayer() {
        if (null != mMediaPlayer) {
            mMediaPlayer.release();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        isAlive = false;
        killMediaPlayer();
        removeStickyEvent();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}
