<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/book_page_item"
    android:layout_width="match_parent"
    android:layout_height="152dp"
    android:background="@color/gary_f9">


    <RelativeLayout
        android:layout_width="119.5dp"
        android:layout_height="match_parent"
        android:layout_centerHorizontal="true">


        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:background="@mipmap/shadow_icon" />

        <RelativeLayout
            android:layout_width="105dp"
            android:layout_height="140dp"
            android:layout_centerInParent="true">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white" />

            <TextView
                android:id="@+id/item_num_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentLeft="true"
                android:layout_marginLeft="7.5dp"
                android:layout_marginTop="5.5dp"
                android:ellipsize="end"
                android:lines="1"
                android:text="1"
                android:textColor="@color/black_22"
                android:textSize="15sp"
                android:textStyle="bold" />

            <RelativeLayout
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_alignParentRight="true">


                <ImageView
                    android:id="@+id/book_page_star_img"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="7.5dp"
                    android:layout_marginRight="7dp"
                    android:background="@mipmap/star_icon"
                    android:visibility="gone" />


            </RelativeLayout>
            <ImageView
                android:id="@+id/item_page_img"
                android:layout_width="97.5dp"
                android:layout_height="100dp"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="5dp" />

            <ImageView
                android:visibility="gone"
                android:id="@+id/item_page_shadow"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/whiteOpaque30" />

        </RelativeLayout>


        <RelativeLayout
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:layout_centerInParent="true">

            <CheckBox
                android:id="@+id/check"
                style="@style/NewCheckBox"
                android:layout_width="27dp"
                android:layout_height="27dp"
                android:layout_centerInParent="true"
                android:minHeight="20dp" />




        </RelativeLayout>
    </RelativeLayout>


</RelativeLayout>