#!/bin/sh
# shellcheck disable=SC2112
czur="czur"
fromDirPath="app/build/outputs"
fromApkPath="${fromDirPath}/apk/${czur}"
fromApkReleasePath="${fromApkPath}/release"
fromApkDebugPath="${fromApkPath}/debug"
toApkPath="app/${czur}"
releaseApkFile=""
debugApkFile=""


function copyFile(){
  fromPath=$1
  toPath=$2
  mkdir -p ${toPath}
  find ${fromPath} -name "*.apk" -exec cp '{}' ${toPath} \;
  echo ">>> copyFile OK"
}

function copyAllApk(){
  echo ">>> copyAllApk start"
 
  fromPath=$1
  toPath=$2

  echo "fromPath=${fromPath}"
  echo "toPath=${toPath}"
  copyFile ${fromPath} ${toPath}
  echo ">>> copyAllApk end"
}

function setCompileDependencies() {
   isCompile=$1
   if [ $isCompile = "true" ]; then
      sed -ie 's#compileDependencies=false#compileDependencies=true#g' settings.gradle
   else
      sed -ie 's#compileDependencies=true#compileDependencies=false#g' settings.gradle
   fi
}

function buildApk() {
  echo ">>> BUILDING: buildApk start"
  setCompileDependencies false
#  ./gradlew :app:assembleRelease
  setCompileDependencies true

  copyAllApk ${fromApkReleasePath} "${toApkPath}/release/"
  copyAllApk ${fromApkDebugPath} "${toApkPath}/debug/"

  echo ">>> BUILDING: buildApk end"
}

function getApkAppId(){
  apkPath=$1
  apkanalyzerPath="${HOME}/Library/Android/sdk/tools/bin/apkanalyzer"
  appId=$(${apkanalyzerPath} --human-readable manifest application-id ${apkPath})
  echo "${appId}"
}

function getApkVersionName(){
  apkPath=$1
  apkanalyzerPath="${HOME}/Library/Android/sdk/tools/bin/apkanalyzer"
  versionName=$(${apkanalyzerPath} --human-readable manifest version-code ${apkPath})
  echo "$(echo ${versionName})"
}

function localProp(){
  # cat local.properties | grep $1 | cut -d'=' -f2 | sed 's/\r//'
  cat local.properties | grep $1 | cut -d'=' -f2
}

function parseJson(){
  echo "$1" | sed "s/\"//g" | sed "s/.*$2:\([^,}]*\).*/\1/"
}


function publish2Fir(){
  echo ">>> BUILDING: publish2Fir start"
  outputsApkPath=$1
  outApkFile=$(find ${outputsApkPath} -name "*.apk" -exec echo '{}' \;)
  apkFile=${outApkFile##*/}
  changeLog=$2

  apkName=${apkFile}
  apkPath=${outApkFile}
  apkNameNoExt=$(basename $apkName .apk)

  echo "apkPath=${apkPath}"
  echo "apkName=${apkName}"
  echo "apkNameNoExt=${apkNameNoExt}"

  enable=$(localProp 'publish.fir.enable')
  type="android"
  bundleId=$(getApkAppId $apkPath)
  apiToken=$(localProp 'publish.fir.token')
  file=$apkPath
  xName="成者CZUR"
  # xVersion=$(getApkVersionName $apkPath)
  xVersion=${apkNameNoExt}
  xBuild=$(getApkVersionName $apkPath)
  xChangeLog=${changeLog}

  echo "enable=$enable"
  echo "type=$type"
  echo "bundleId=$bundleId"
  echo "apiToken=$apiToken"
  echo "file=$file"
  echo "xName=$xName"
  echo "xVersion=$xVersion"
  echo "xBuild=$xBuild"
  echo "xChangeLog=$xChangeLog"

  if [[ $enable = "true" ]] ; then
    echo "requesting......"
    curl "http://api.bq04.com/apps/latest/$bundleId?api_token=$apiToken" > tmp.txt
    # xBuild=$(($(parseJson "$(cat tmp.txt)" 'build') + 1))
    # xBuild=$xVersion

    echo "requesting......"
    curl -X "POST" "http://api.bq04.com/apps" \
    -H "Content-Type: application/json" \
    -d "{\"type\":\"$type\", \"bundle_id\":\"$bundleId\", \"api_token\":\"$apiToken\"}" \
    > tmp.txt
    uploadUrl=$(parseJson "$(cat tmp.txt)" 'upload_url')
    qiniuKey=$(parseJson "$(cat tmp.txt)" 'key')
    qiniuToken=$(parseJson "$(cat tmp.txt)" 'token')
    echo "uploadUrl=$uploadUrl"
    echo "qiniuKey=$qiniuKey"
    # echo "qiniuToken=$qiniuToken"
    echo ""

    echo "uploading......"
    curl -F "key=$qiniuKey" \
    -F "token=$qiniuToken" \
    -F "file=@$file" \
    -F "x:name=$xName" \
    -F "x:version=$xVersion" \
    -F "x:build=$xBuild" \
    -F "x:changelog=$xChangeLog" \
    "https://up.qbox.me"

    rm tmp.txt
    echo ""
  fi
  echo ">>> BUILDING: publish2Fir end"
}

function test(){
  outReleaseApkFile=$(find ${fromApkReleasePath} -name "*.apk" -exec echo '{}' \;)
  releaseApkFile=${outReleaseApkFile##*/}
  outDebugApkFile=$(find ${fromApkDebugPath} -name "*.apk" -exec echo '{}' \;)
  debugApkFile=${outDebugApkFile##*/}

  apkPath=${outDebugApkFile}
  apkName=${debugApkFile}
  apkNameNoExt=$(basename $apkName .apk)

  echo "apkPath=${apkPath}"
  echo "apkName=${apkName}"
  echo "apkNameNoExt=${apkNameNoExt}"
}

function main(){
  startTime=$(date +%s)
  echo ">>> BUILD START: $startTime"
  buildApk
#  uploadBuglySymbol

  # outReleaseApkFile=$(find ${fromApkReleasePath} -name "*.apk" -exec echo '{}' \;)
  outApkPath=${fromApkDebugPath}
  changeLog="测试环境"
  publish2Fir ${outApkPath} ${changeLog}

  sleep 3

  outApkPath=${fromApkReleasePath}
  changeLog="生产环境"
  publish2Fir ${outApkPath} ${changeLog}

  endTime=$(date +%s)
  echo ">>> BUILD COMPLETE: Time consuming $(($endTime-$startTime))s"

}

main