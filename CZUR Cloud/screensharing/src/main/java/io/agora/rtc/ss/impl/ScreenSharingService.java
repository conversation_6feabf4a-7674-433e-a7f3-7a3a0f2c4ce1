package io.agora.rtc.ss.impl;

import android.app.Notification;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.media.projection.MediaProjectionManager;
import android.os.Build;
import android.os.IBinder;
import android.os.RemoteCallbackList;
import android.os.RemoteException;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Display;
import android.view.WindowManager;

import androidx.core.app.NotificationCompat;

import io.agora.rtc.ss.Constant;
import io.agora.rtc.ss.R;
import io.agora.rtc.ss.ScreenSharingClient;
import io.agora.rtc.ss.aidl.INotification;
import io.agora.rtc.ss.aidl.IScreenSharing;
import io.agora.rtc.ss.gles.GLRender;
import io.agora.rtc.ss.gles.ImgTexFrame;
import io.agora.rtc.ss.gles.SinkConnector;
import io.agora.rtc2.ChannelMediaOptions;
import io.agora.rtc2.Constants;
import io.agora.rtc2.IRtcEngineEventHandler;
import io.agora.rtc2.RtcConnection;
import io.agora.rtc2.RtcEngine;
import io.agora.rtc2.RtcEngineConfig;
import io.agora.rtc2.RtcEngineEx;
import io.agora.rtc2.ScreenCaptureParameters;
import io.agora.rtc2.video.VideoEncoderConfiguration;

public class ScreenSharingService extends Service {
    public static final String TAG = ScreenSharingService.class.getName();
    private static final String LOG_TAG = ScreenSharingService.class.getSimpleName();

    private ScreenCapture mScreenCapture;
    private GLRender mScreenGLRender;
    private RtcEngineEx mRtcEngine;
    private Context mContext;
//    private ScreenCaptureSource mSCS;
    private Intent projectionIntent;

    private ChannelMediaOptions options = new ChannelMediaOptions();
    private static final int DEFAULT_SHARE_FRAME_RATE = 15;

    private RemoteCallbackList<INotification> mCallbacks
            = new RemoteCallbackList<INotification>();

    private RtcConnection rtcConnection2 = new RtcConnection();

    private final IScreenSharing.Stub mBinder = new IScreenSharing.Stub() {
        public void registerCallback(INotification cb) {
            if (cb != null) mCallbacks.register(cb);
        }

        public void unregisterCallback(INotification cb) {
            if (cb != null) mCallbacks.unregister(cb);
        }

        public void startShare() {
            startCapture();
        }

        public void stopShare() {
            stopCapture();
        }

        public void renewToken(String token) {
            refreshToken(token);
        }
    };

    private void initModules() {
        WindowManager wm = (WindowManager) getApplicationContext().getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics metrics = new DisplayMetrics();
        wm.getDefaultDisplay().getRealMetrics(metrics);

        if (mScreenGLRender == null) {
            mScreenGLRender = new GLRender();
        }
        if (mScreenCapture == null) {
            mScreenCapture = new ScreenCapture(mContext, mScreenGLRender, metrics.densityDpi, getScreenRealWidth(), getScreenRealHeight());
        }

        mScreenCapture.mImgTexSrcConnector.connect(new SinkConnector<ImgTexFrame>() {
            @Override
            public void onFormatChanged(Object obj) {
                Log.d(LOG_TAG, "onFormatChanged " + obj.toString());
            }

            @Override
            public void onFrameAvailable(ImgTexFrame frame) {
                Log.d(LOG_TAG, "onFrameAvailable " + frame.toString() + " " + frame.pts);

                if (mRtcEngine == null) {
                    return;
                }

                ScreenCaptureParameters parameters = new ScreenCaptureParameters();
                parameters.videoCaptureParameters.width = frame.mFormat.mWidth;
                parameters.videoCaptureParameters.height = frame.mFormat.mHeight;
                parameters.videoCaptureParameters.framerate = DEFAULT_SHARE_FRAME_RATE;
                parameters.captureAudio = true;
                // start screen capture and update options
                mRtcEngine.startScreenCapture(parameters);
                options.publishScreenCaptureVideo = true;
                options.publishCameraTrack = false;
                options.publishScreenCaptureAudio = true;
                mRtcEngine.updateChannelMediaOptions(options);

//                if (mSCS.getConsumer() == null) {
//                    Log.w(LOG_TAG, "onFrameAvailable getConsumer 为 null");
//                    return;
//                }
//                mSCS.getConsumer().consumeTextureFrame(frame.mTextureId, AgoraVideoFrame.FORMAT_TEXTURE_OES, frame.mFormat.mWidth,
//                        frame.mFormat.mHeight, 0, frame.pts, frame.mTexMatrix);
                Log.i(LOG_TAG, String.format("On consumeTextureFrame, width: %d, height: %d", frame.mFormat.mWidth, frame.mFormat.mHeight));
            }
        });

        mScreenCapture.setOnScreenCaptureListener(new ScreenCapture.OnScreenCaptureListener() {
            @Override
            public void onStarted() {
                Log.d(LOG_TAG, "Screen Record Started");
            }

            @Override
            public void onError(int err) {
                Log.d(LOG_TAG, "onError " + err);
                switch (err) {
                    case ScreenCapture.SCREEN_ERROR_SYSTEM_UNSUPPORTED:
                        break;
                    case ScreenCapture.SCREEN_ERROR_PERMISSION_DENIED:
                        break;
                }
            }
        });

        initOffscreenPreview(getScreenRealWidth(), getScreenRealHeight());
    }

    private void deInitModules() {

        // 停止屏幕共享
        mRtcEngine.stopScreenCapture();
        options.publishScreenCaptureVideo = false;
        mRtcEngine.updateChannelMediaOptions(options);

        mRtcEngine.leaveChannel();
        RtcEngine.destroy();
        mRtcEngine = null;

        if (mScreenCapture != null) {
            mScreenCapture.release();
            mScreenCapture = null;
        }

        if (mScreenGLRender != null) {
            mScreenGLRender.quit();
            mScreenGLRender = null;
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {

        updateOffscreenPreview(getScreenRealWidth(), getScreenRealHeight());
    }

    /**
     * Init offscreen preview.
     *
     * @param width  offscreen width
     * @param height offscreen height
     * @throws IllegalArgumentException
     */
    public void initOffscreenPreview(int width, int height) throws IllegalArgumentException {
        if (width <= 0 || height <= 0) {
            throw new IllegalArgumentException("Invalid offscreen resolution");
        }

        mScreenGLRender.init(width, height);
    }

    /**
     * Update offscreen preview.
     *
     * @param width  offscreen width
     * @param height offscreen height
     * @throws IllegalArgumentException
     */
    public void updateOffscreenPreview(int width, int height) throws IllegalArgumentException {
        if (width <= 0 || height <= 0) {
            throw new IllegalArgumentException("Invalid offscreen resolution");
        }

        mScreenGLRender.update(width, height);
    }

    private void startCapture() {
        Log.d("ScreenCapture", "启动前台通知 ");
        startForeground(55431, getForeNotification());
        mScreenCapture.start(projectionIntent);
    }

    private Notification getForeNotification() {
        Notification notification;
        String eventTitle = getResources().getString(R.string.app_name);
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, NotificationHelper.generateChannelId(getApplication(), 55431))
                .setCategory(Notification.CATEGORY_CALL);
                .setContentTitle(eventTitle)
                .setContentText(eventTitle);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP)
            builder.setColor(getResources().getColor(android.R.color.black));
        notification = builder.build();
        notification.flags |= Notification.FLAG_ONGOING_EVENT;

        return notification;
    }

    private void stopCapture() {
        mScreenCapture.stop();
        Log.d(LOG_TAG, "停止前台服务");
        stopForeground(true);
    }

    private void refreshToken(String token) {
        if (mRtcEngine != null) {
            mRtcEngine.renewToken(token);
        } else {
            Log.e(LOG_TAG, "rtc engine is null");
        }
    }

    @Override
    public void onCreate() {
        Log.d(TAG,TAG+": onCreate");
        mContext = getApplicationContext();
        initModules();
        startForeground(55431, getForeNotification());
    }

    @Override
    public IBinder onBind(Intent intent) {
        Log.d(TAG,TAG+": onBind");
        projectionIntent = intent.getParcelableExtra(Constant.INTENT);
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
            MediaProjectionManager mpm =  (MediaProjectionManager)
                    mContext.getSystemService(Context.MEDIA_PROJECTION_SERVICE);
            projectionIntent = mpm.createScreenCaptureIntent();
        }

        setUpEngine(intent);
        setUpVideoConfig(intent);
        joinChannel(intent);
        return mBinder;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG,TAG+": onDestroy");
        deInitModules();
    }

    private void joinChannel(Intent intent) {
//        mRtcEngine.setParameters("{\"che.video.mobile_1080p\":true}");
//        mRtcEngine.setClientRole(Constants.CLIENT_ROLE_BROADCASTER);
        // set options
        ChannelMediaOptions mediaOptions = new ChannelMediaOptions();
        mediaOptions.autoSubscribeAudio = false;
        mediaOptions.autoSubscribeVideo = false;
        mediaOptions.publishScreenCaptureVideo = true;
        mediaOptions.publishCameraTrack = false;
        mediaOptions.clientRoleType = Constants.CLIENT_ROLE_BROADCASTER;
        mediaOptions.channelProfile = Constants.CHANNEL_PROFILE_LIVE_BROADCASTING;

        /**Enable video module*/
        mRtcEngine.enableVideo();
        // Setup video encoding configs
//        mRtcEngine.setVideoEncoderConfiguration(new VideoEncoderConfiguration(
//                VD_640x360,
//                FRAME_RATE_FPS_15,
//                STANDARD_BITRATE,
//                ORIENTATION_MODE_ADAPTIVE
//        ));
        /**Set up to play remote sound with receiver*/
//        mRtcEngine.setDefaultAudioRoutetoSpeakerphone(true);
//        mRtcEngine.joinChannel(
//                intent.getStringExtra(Constant.ACCESS_TOKEN),
//                intent.getStringExtra(Constant.CHANNEL_NAME),
//                intent.getIntExtra(Constant.UID, 0),
//                options);

        rtcConnection2.channelId = intent.getStringExtra(Constant.CHANNEL_NAME);
        rtcConnection2.localUid = intent.getIntExtra(Constant.UID, 0);

        mRtcEngine.joinChannelEx(
                intent.getStringExtra(Constant.ACCESS_TOKEN),
                rtcConnection2,
                mediaOptions,
                iRtcEngineEventHandler );

    }
    private void joinChannel123(Intent intent) {

        ChannelMediaOptions option = new ChannelMediaOptions();
        option.autoSubscribeAudio = true;
        option.autoSubscribeVideo = true;
//        options.clientRoleType = Constants.CLIENT_ROLE_BROADCASTER;
//        options.publishCameraTrack = false;
//        options.publishScreenCaptureVideo = false;
//        options.publishMicrophoneTrack = false;
//        options.enableAudioRecordingOrPlayout = false;
//        options.publishEncodedVideoTrack = false;
        mRtcEngine.joinChannel(
                intent.getStringExtra(Constant.ACCESS_TOKEN),
                intent.getStringExtra(Constant.CHANNEL_NAME),
//                "ss_" + Process.myPid(),
                intent.getIntExtra(Constant.UID, 0),
                option);
    }

    private final IRtcEngineEventHandler iRtcEngineEventHandler = new IRtcEngineEventHandler() {
        @Override
        public void onJoinChannelSuccess(String channel, int uid, int elapsed) {
            Log.d(LOG_TAG, "onJoinChannelSuccess " + channel + " " + elapsed);
        }

        @Override
        public void onWarning(int warn) {
            Log.d(LOG_TAG, "onWarning " + warn);
        }

        @Override
        public void onError(int err) {
            Log.d(LOG_TAG, "onError " + err);
        }

        @Override
        public void onRequestToken() {
            final int N = mCallbacks.beginBroadcast();
            for (int i = 0; i < N; i++) {
                try {
                    mCallbacks.getBroadcastItem(i).onError(Constants.ERR_INVALID_TOKEN);
                } catch (RemoteException e) {
                    // The RemoteCallbackList will take care of removing
                    // the dead object for us.
                }
            }
            mCallbacks.finishBroadcast();
        }

        @Override
        public void onTokenPrivilegeWillExpire(String token) {
            final int N = mCallbacks.beginBroadcast();
            for (int i = 0; i < N; i++) {
                try {
                    mCallbacks.getBroadcastItem(i).onTokenWillExpire();
                } catch (RemoteException e) {
                    // The RemoteCallbackList will take care of removing
                    // the dead object for us.
                }
            }
            mCallbacks.finishBroadcast();
        }

        @Override
        public void onConnectionStateChanged(int state, int reason) {
            switch (state) {
                case Constants.CONNECTION_STATE_FAILED:
                    final int N = mCallbacks.beginBroadcast();
                    for (int i = 0; i < N; i++) {
                        try {
                            mCallbacks.getBroadcastItem(i).onError(Constants.CONNECTION_STATE_FAILED);
                        } catch (RemoteException e) {
                            // The RemoteCallbackList will take care of removing
                            // the dead object for us.
                        }
                    }
                    mCallbacks.finishBroadcast();
                    break;
                default:
                    break;
            }
        }
    };

    private void setUpEngine(Intent intent){
        String appId = intent.getStringExtra(Constant.APP_ID);
        try{
            RtcEngineConfig config = new RtcEngineConfig();
            config.mContext = getApplicationContext();
            config.mAppId = appId;
            config.mChannelProfile = Constants.CHANNEL_PROFILE_LIVE_BROADCASTING;
            config.mEventHandler = iRtcEngineEventHandler;
            config.mAudioScenario = Constants.AudioScenario.getValue(Constants.AudioScenario.DEFAULT);

            mRtcEngine = (RtcEngineEx) ScreenSharingClient.rtcEngine;
            if (mRtcEngine == null){
                mRtcEngine = (RtcEngineEx) RtcEngine.create(config);
            }

            mRtcEngine.enableVideo();
            mRtcEngine.muteAllRemoteAudioStreams(true);
            mRtcEngine.muteAllRemoteVideoStreams(true);
            mRtcEngine.disableAudio();

        }catch(Exception e){
            Log.e(LOG_TAG, Log.getStackTraceString(e));
            throw new RuntimeException("NEED TO check rtc sdk init fatal error\n" + Log.getStackTraceString(e));
        }
    }

    private void setUpEngine123(Intent intent) {
        String appId = intent.getStringExtra(Constant.APP_ID);
        try {
            mRtcEngine = (RtcEngineEx)RtcEngine.create(getApplicationContext(), appId, iRtcEngineEventHandler);
        } catch (Exception e) {
            Log.e(LOG_TAG, Log.getStackTraceString(e));

            throw new RuntimeException("NEED TO check rtc sdk init fatal error\n" + Log.getStackTraceString(e));
        }

        mRtcEngine.setLogFile("/sdcard/ss_svr.log");
        mRtcEngine.setChannelProfile(Constants.CHANNEL_PROFILE_LIVE_BROADCASTING);
        mRtcEngine.enableVideo();

        if (mRtcEngine.isTextureEncodeSupported()) {
//            mSCS = new ScreenCaptureSource();
////            mSCS.getConsumer().consumeTextureFrame(frame.mTextureId, AgoraVideoFrame.FORMAT_TEXTURE_OES, frame.mFormat.mWidth,
////                    frame.mFormat.mHeight, 0, frame.pts, frame.mTexMatrix);
//            mRtcEngine.setVideoSource(mSCS);

            DisplayMetrics metrics = new DisplayMetrics();
            WindowManager wm = (WindowManager) getApplicationContext().getSystemService(Context.WINDOW_SERVICE);
            Display display = wm.getDefaultDisplay();
            ScreenCaptureParameters parameters = new ScreenCaptureParameters();
            parameters.videoCaptureParameters.width = 720;
            parameters.videoCaptureParameters.height = (int) (720 * 1.0f / metrics.widthPixels * metrics.heightPixels);
            parameters.videoCaptureParameters.framerate = DEFAULT_SHARE_FRAME_RATE;
            parameters.captureAudio = true;
            // start screen capture and update options
            mRtcEngine.startScreenCapture(parameters);
            options.publishScreenCaptureVideo = true;
            options.publishCameraTrack = false;
            options.publishScreenCaptureAudio = true;
            mRtcEngine.updateChannelMediaOptions(options);

        } else {
            throw new RuntimeException("Can not work on device do not supporting texture" + mRtcEngine.isTextureEncodeSupported());
        }

        mRtcEngine.setClientRole(Constants.CLIENT_ROLE_BROADCASTER);

        mRtcEngine.muteAllRemoteAudioStreams(true);
        mRtcEngine.muteAllRemoteVideoStreams(true);
        mRtcEngine.disableAudio();
    }

    private void setUpVideoConfig(Intent intent) {
//        int width = intent.getIntExtra(Constant.WIDTH, 0);
//        int height = intent.getIntExtra(Constant.HEIGHT, 0);
        int width = getScreenRealWidth();
        int height = getScreenRealHeight();
        int frameRate = intent.getIntExtra(Constant.FRAME_RATE, 15);
        int bitRate = intent.getIntExtra(Constant.BITRATE, 0);
        int orientationMode = intent.getIntExtra(Constant.ORIENTATION_MODE, 0);
        VideoEncoderConfiguration.FRAME_RATE fr;
        VideoEncoderConfiguration.ORIENTATION_MODE om;

        switch (frameRate) {
            case 1:
                fr = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_1;
                break;
            case 7:
                fr = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_7;
                break;
            case 10:
                fr = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_10;
                break;
            case 15:
                fr = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_15;
                break;
            case 24:
                fr = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_24;
                break;
            case 30:
                fr = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_30;
                break;
            default:
                fr = VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_15;
                break;
        }

        switch (orientationMode) {
            case 1:
                om = VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_FIXED_LANDSCAPE;
                break;
            case 2:
                om = VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_FIXED_PORTRAIT;
                break;
            default:
                om = VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_ADAPTIVE;
                break;
        }

         videoEncoderConfiguration = new VideoEncoderConfiguration(
                new VideoEncoderConfiguration.VideoDimensions(width, height), fr, bitRate, om);

        mRtcEngine.setVideoEncoderConfiguration(videoEncoderConfiguration);
    }
    VideoEncoderConfiguration videoEncoderConfiguration = null;

    private int getScreenRealHeight(){
        return getScreenSize(1);
    }

    private int getScreenRealWidth(){
        return getScreenSize(0);
    }

    /**
     *
     * @param type 0获取宽 1获取高
     */
    private int getScreenSize(int type){
        WindowManager wm = (WindowManager) getApplicationContext().getSystemService(Context.WINDOW_SERVICE);

        // 分辨率的设置, 必须按照1280*720的比例,才能显示全屏
        DisplayMetrics outMetrics = new DisplayMetrics();
        wm.getDefaultDisplay().getRealMetrics(outMetrics);
        int screenWidth = outMetrics.widthPixels;
        int screenHeight = outMetrics.heightPixels;

        int vWidth = screenWidth;
        int vHeight = screenHeight;

        Log.d(LOG_TAG, "initModules: ysWidth:" + vWidth + ", ysHeight:" + vHeight);

        if (screenWidth < screenHeight) {//竖屏
            vHeight = 1280;
            float bili = screenHeight / 1280f;
            vWidth = (int) (screenWidth/bili);
        }else {//横屏
            vWidth = 1280;
            float bili = screenWidth / 1280f;
            vHeight = (int) (screenHeight/bili);
        }

        // 有黑边做法
//        if (screenWidth < screenHeight){//竖屏
//            widthRatio = vWidth / 720F;
//            heightRatio = vHeight / 1280F;
//            //迁就大比例,因为大比例需要全部放入画面中
//            if (widthRatio > heightRatio) {//以height为基准
//                vHeight = (int) (1280 * widthRatio);
//            } else {
//                vWidth = (int) (720 * heightRatio);
//            }
//        }else {//横屏
//            widthRatio = vWidth / 1280F;
//            heightRatio = vHeight / 720F;
//
//            if (widthRatio > heightRatio) {//以height为基准
//                vHeight = (int) (720 * widthRatio);
//            } else {
//                vWidth = (int) (1280 * heightRatio);
//            }
//        }


        Log.d(LOG_TAG, "initModules: vWidth:" + vWidth + ", vHeight:" + vHeight);

        if (type ==0){
            return vWidth;
        }else if (type ==1){
            return vHeight;
        }else {
            return 0;
        }
    }
}
