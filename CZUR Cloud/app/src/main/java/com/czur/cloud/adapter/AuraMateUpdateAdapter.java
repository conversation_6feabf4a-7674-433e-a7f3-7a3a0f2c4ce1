package com.czur.cloud.adapter;

import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.czur.cloud.R;
import com.czur.cloud.ui.base.BaseActivity;

import java.util.List;

public class AuraMateUpdateAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder>  {

    private List<String> strings;
    private BaseActivity activity;

    public AuraMateUpdateAdapter(List<String> strings, BaseActivity activity) {
        this.strings = strings;
        this.activity = activity;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(activity.getLayoutInflater().inflate(R.layout.item_auramate_update, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        ViewHolder viewHolder = (ViewHolder) holder;
        viewHolder.tvIndex.setText(String.format("%d. ", position + 1));
        viewHolder.tvContent.setText(strings.get(position));
    }

    @Override
    public int getItemCount() {
        if (strings == null){
            return 0;
        }else {
            return strings.size();
        }
    }

    private static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvContent,tvIndex;

        ViewHolder(View itemView) {
            super(itemView);
            tvContent = (TextView) itemView.findViewById(R.id.tv_content);
            tvIndex = (TextView) itemView.findViewById(R.id.tv_index);
        }
    }
}
