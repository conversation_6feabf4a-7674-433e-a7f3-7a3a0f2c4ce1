package com.czur.cloud.ui.starry.component;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.Layout;
import android.util.AttributeSet;
import android.view.Gravity;

import androidx.appcompat.widget.AppCompatTextView;
import com.czur.cloud.ui.starry.utils.RomUtils;

public class DrawableCenterOneLineTextView extends AppCompatTextView {

    public DrawableCenterOneLineTextView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public DrawableCenterOneLineTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DrawableCenterOneLineTextView(Context context) {
        super(context);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        Drawable[] drawables = getCompoundDrawables();
        final int vGravity = getGravity() & Gravity.VERTICAL_GRAVITY_MASK;
        final int hGravity = getGravity() & Gravity.HORIZONTAL_GRAVITY_MASK;
        if (drawables != null && (vGravity == Gravity.CENTER_VERTICAL || hGravity == Gravity.CENTER_HORIZONTAL)) {
            Drawable drawable = null;
            int index = 0;
            for (Drawable d : drawables) {
                if (d != null) {
                    drawable = d;
                    break;
                }
                index++;
            }
            if (drawable == null) {
                return;
            }
            //compoundDrawable 的宽高
            int drawableW = drawable.getIntrinsicWidth();
            int drawableH = drawable.getIntrinsicHeight();
            Paint pt = getPaint();
            Rect rect = new Rect();
            getPaint().getTextBounds(getText().toString(), 0, getText().toString().length(), rect);
            //TextView 中文字的宽高
            float tHeight = rect.height();
            float tWidth = pt.measureText(getText().toString());
            //TextView 的实际可画区域的宽高
            int vWidth = getMeasuredWidth() - getPaddingLeft() - getPaddingRight();
            int vHeight = getMeasuredHeight() - getPaddingTop() - getPaddingBottom();
            int cp = getCompoundDrawablePadding();
            switch (index) {
                case 0:
                    int top = 0;
                    int bottom = top + drawableH;
                    int left = (int) (vWidth - (tWidth + drawableW + cp)) / 2;
                    int right = left + drawableW;
                    drawable.setBounds(left, top, right, bottom);
                    break;
                case 1:
                    top = (int) (vHeight - (tHeight + drawableH + cp)) / 2;
                    bottom = top + drawableH;
                    left = 0;
                    right = left + drawableW;
                    drawable.setBounds(left, top, right, bottom);
                    break;
                case 2:
                    top = 0;
                    bottom = top + drawableH;
                    left = -((int) (vWidth - (tWidth + drawableW + cp)) / 2);
                    right = left + drawableW;
                    drawable.setBounds(left, top, right, bottom);
                    break;
                case 3:
                    top = -((int) (vHeight - (tHeight + drawableH + cp)) / 2);
                    bottom = top + drawableH;
                    left = 0;
                    right = left + drawableW;
                    drawable.setBounds(left, top, right, bottom);
                    break;
            }
        }


    }
}