package com.czur.cloud.model;

public class AuraFileModel {


        /**
         * id : rkttzcrozkggwat
         * seqNum : 29
         * userId : 9
         * dirId : null
         * mode : 1
         * userSelectMode : 0
         * smartResult : 1
         * orgImgId : 42
         * orgKey : test/9/qaz-147-wsx-258/2018-09-21/05e0c74a-41e9-490e-bbcb-a6fae536b792.jpg
         * org : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/05e0c74a-41e9-490e-bbcb-a6fae536b792.jpg?Expires=1537867588&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=rHZk1XxGkw0FIlXq%2B38nyhV1A%2Fs%3D
         * middleOrg : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/05e0c74a-41e9-490e-bbcb-a6fae536b792.jpg?Expires=1537867588&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=OmAihG8xq4XS%2Biw7Brg4GlXDhKM%3D&x-oss-process=image%2Fresize%2Cm_fixed%2Cw_1080%2Ch_1080
         * smallOrg : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/05e0c74a-41e9-490e-bbcb-a6fae536b792.jpg?Expires=1537867588&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=KopSRAORCRpp%2BycG2LBpVc%2F6Rtk%3D&x-oss-process=image%2Fresize%2Cm_fixed%2Cw_150%2Ch_150
         * fileName : 20180921155011
         * equipmentUID : qaz-147-wsx-258
         * singleId : 43
         * singleKey : test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut191814.jpg
         * single : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut191814.jpg?Expires=1537867588&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=%2FbQPSO95drsGjNuIBOPbIaulr9M%3D
         * smallSingle : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut191814.jpg?Expires=1537867588&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=gJ4aRC5L8PtfiCsjLAgT8vgKMRo%3D&x-oss-process=image%2Fresize%2Cm_fixed%2Cw_150%2Ch_150
         * middleSingle : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut191814.jpg?Expires=1537867588&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=S5NpN83i0kGg9XRKBjZnyqE9c1Q%3D&x-oss-process=image%2Fresize%2Cm_fixed%2Cw_1080%2Ch_1080
         * bookId : null
         * bookKey : null
         * book : null
         * middleBook : null
         * smallBook : null
         * takeOn : 1537516211000
         * createOn : 1537516212000
         * localeTime : null
         * small : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut191814.jpg?Expires=1537867588&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=gJ4aRC5L8PtfiCsjLAgT8vgKMRo%3D&x-oss-process=image%2Fresize%2Cm_fixed%2Cw_150%2Ch_150
         * middle : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut191814.jpg?Expires=1537867588&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=S5NpN83i0kGg9XRKBjZnyqE9c1Q%3D&x-oss-process=image%2Fresize%2Cm_fixed%2Cw_1080%2Ch_1080
         * big : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut191814.jpg?Expires=1537867588&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=%2FbQPSO95drsGjNuIBOPbIaulr9M%3D
         */

        private String id;
        private int seqNum;
        private int userId;
        private Object dirId;
        private int mode;
        private int userSelectMode;
        private int smartResult;
        private int orgImgId;
        private String orgKey;
        private String org;
        private String middleOrg;
        private String smallOrg;
        private String fileName;
        private String equipmentUID;
        private int singleId;
        private String singleKey;
        private String single;
        private String smallSingle;
        private String middleSingle;
        private Object bookId;
        private Object bookKey;
        private Object book;
        private Object middleBook;
        private Object smallBook;
        private String takeOn;
        private long createOn;
        private Object localeTime;
        private String small;
        private String middle;
        private String big;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public int getSeqNum() {
            return seqNum;
        }

        public void setSeqNum(int seqNum) {
            this.seqNum = seqNum;
        }

        public int getUserId() {
            return userId;
        }

        public void setUserId(int userId) {
            this.userId = userId;
        }

        public Object getDirId() {
            return dirId;
        }

        public void setDirId(Object dirId) {
            this.dirId = dirId;
        }

        public int getMode() {
            return mode;
        }

        public void setMode(int mode) {
            this.mode = mode;
        }

        public int getUserSelectMode() {
            return userSelectMode;
        }

        public void setUserSelectMode(int userSelectMode) {
            this.userSelectMode = userSelectMode;
        }

        public int getSmartResult() {
            return smartResult;
        }

        public void setSmartResult(int smartResult) {
            this.smartResult = smartResult;
        }

        public int getOrgImgId() {
            return orgImgId;
        }

        public void setOrgImgId(int orgImgId) {
            this.orgImgId = orgImgId;
        }

        public String getOrgKey() {
            return orgKey;
        }

        public void setOrgKey(String orgKey) {
            this.orgKey = orgKey;
        }

        public String getOrg() {
            return org;
        }

        public void setOrg(String org) {
            this.org = org;
        }

        public String getMiddleOrg() {
            return middleOrg;
        }

        public void setMiddleOrg(String middleOrg) {
            this.middleOrg = middleOrg;
        }

        public String getSmallOrg() {
            return smallOrg;
        }

        public void setSmallOrg(String smallOrg) {
            this.smallOrg = smallOrg;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getEquipmentUID() {
            return equipmentUID;
        }

        public void setEquipmentUID(String equipmentUID) {
            this.equipmentUID = equipmentUID;
        }

        public int getSingleId() {
            return singleId;
        }

        public void setSingleId(int singleId) {
            this.singleId = singleId;
        }

        public String getSingleKey() {
            return singleKey;
        }

        public void setSingleKey(String singleKey) {
            this.singleKey = singleKey;
        }

        public String getSingle() {
            return single;
        }

        public void setSingle(String single) {
            this.single = single;
        }

        public String getSmallSingle() {
            return smallSingle;
        }

        public void setSmallSingle(String smallSingle) {
            this.smallSingle = smallSingle;
        }

        public String getMiddleSingle() {
            return middleSingle;
        }

        public void setMiddleSingle(String middleSingle) {
            this.middleSingle = middleSingle;
        }

        public Object getBookId() {
            return bookId;
        }

        public void setBookId(Object bookId) {
            this.bookId = bookId;
        }

        public Object getBookKey() {
            return bookKey;
        }

        public void setBookKey(Object bookKey) {
            this.bookKey = bookKey;
        }

        public Object getBook() {
            return book;
        }

        public void setBook(Object book) {
            this.book = book;
        }

        public Object getMiddleBook() {
            return middleBook;
        }

        public void setMiddleBook(Object middleBook) {
            this.middleBook = middleBook;
        }

        public Object getSmallBook() {
            return smallBook;
        }

        public void setSmallBook(Object smallBook) {
            this.smallBook = smallBook;
        }

        public String getTakeOn() {
            return takeOn;
        }

        public void setTakeOn(String takeOn) {
            this.takeOn = takeOn;
        }

        public long getCreateOn() {
            return createOn;
        }

        public void setCreateOn(long createOn) {
            this.createOn = createOn;
        }

        public Object getLocaleTime() {
            return localeTime;
        }

        public void setLocaleTime(Object localeTime) {
            this.localeTime = localeTime;
        }

        public String getSmall() {
            return small;
        }

        public void setSmall(String small) {
            this.small = small;
        }

        public String getMiddle() {
            return middle;
        }

        public void setMiddle(String middle) {
            this.middle = middle;
        }

        public String getBig() {
            return big;
        }

        public void setBig(String big) {
            this.big = big;
        }

}
