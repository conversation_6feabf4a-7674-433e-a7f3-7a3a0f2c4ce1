package com.czur.cloud.ui.auramate;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.entity.realm.WifiHistoryEntity;
import com.czur.cloud.event.BaseEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import io.realm.Realm;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class AuraMateConnectSuccessActivity extends AuramateBaseActivity implements View.OnClickListener {
    private ImageView normalBackBtn;
    private TextView auraHomeSuccessBtn;
    private RelativeLayout wifiSuccessLoadingRl;
    private ImageView wifiSuccessLoadingImg;
    private boolean isHide;
    private Realm realm;

    private String ssid;
    private String password;


    private int i;
    private Handler handler = new Handler();
    private Runnable runnable;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_ff);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_home_success);
        initComponent();
        registerEvent();
    }

    @Override
    protected boolean PCNeedFinish() {
        return !TextUtils.isEmpty(equipmentId);
    }

    private void initComponent() {
        realm = Realm.getDefaultInstance();
        isHide = getIntent().getBooleanExtra("isHide", false);
        ssid = getIntent().getStringExtra("ssid");
        password = getIntent().getStringExtra("password");
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        auraHomeSuccessBtn = (TextView) findViewById(R.id.aura_home_success_btn);
        wifiSuccessLoadingRl = (RelativeLayout) findViewById(R.id.wifi_success_loading_rl);
        wifiSuccessLoadingImg = (ImageView) findViewById(R.id.wifi_success_loading_img);
        if (isHide) {
            wifiSuccessLoadingRl.setVisibility(View.GONE);
        } else {
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this);
            }
            Animation imgAnim = AnimationUtils.loadAnimation(
                    this, R.anim.dialog_anim);
            // 使用ImageView显示动画
            wifiSuccessLoadingImg.startAnimation(imgAnim);
            waiting30seconds();
        }
    }

    private void waiting30seconds() {
        runnable = new Runnable() {
            @Override
            public void run() {
                i++;
                if (i == 30) {
                    Intent intent = new Intent(AuraMateConnectSuccessActivity.this, AuraMateConnectFailActivity.class);
                    intent.putExtra("equipmentId",equipmentId);
                    ActivityUtils.startActivity(intent);
                    ActivityUtils.finishActivity(AuraMateConnectSuccessActivity.class);
                    handler.removeCallbacks(this);
                } else {
                    handler.postDelayed(this, 1000);
                }
            }
        };
        handler.postDelayed(runnable, 1000);
    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        auraHomeSuccessBtn.setOnClickListener(this);
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case AURA_BIND_SUCCESS:
                handler.removeCallbacks(runnable);
                WifiHistoryEntity wifiHistoryEntity = realm.where(WifiHistoryEntity.class).equalTo("ssid", ssid).findFirst();
                if (wifiHistoryEntity == null) {
                    realm.executeTransaction(new Realm.Transaction() {
                        @Override
                        public void execute(Realm realm) {
                            WifiHistoryEntity wifiHistoryEntity = realm.createObject(WifiHistoryEntity.class, ssid);
                            wifiHistoryEntity.setPassword(password);
                            wifiHistoryEntity.setCreateTime(System.currentTimeMillis());
                        }
                    });
                } else {
                    realm.executeTransaction(new Realm.Transaction() {
                        @Override
                        public void execute(Realm realm) {
                            wifiHistoryEntity.setPassword(password);
                            wifiHistoryEntity.setCreateTime(System.currentTimeMillis());
                        }
                    });
                }
                wifiSuccessLoadingRl.setVisibility(View.GONE);
                ActivityUtils.finishToActivity(AuraMateActivity.class, false);
                break;
            default:
                break;
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.normal_back_btn:
            case R.id.aura_home_success_btn:
                handler.removeCallbacks(runnable);
                ActivityUtils.finishToActivity(AuraMateActivity.class, false);
                break;
            default:
                break;
        }
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        realm.close();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        handler.removeCallbacksAndMessages(null);
    }
}
