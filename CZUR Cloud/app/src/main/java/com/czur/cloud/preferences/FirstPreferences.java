package com.czur.cloud.preferences;

import android.content.Context;

import com.czur.cloud.util.validator.Validator;

public class FirstPreferences extends BasePreferences {

    private static final String PREF = FirstPreferences.class.getSimpleName();
    private static final String IS_FIRST_NOTICE_NEW_HANDWRITING = "is_first_notice_new_handwriting";
    private static final String IS_FIRST_AURA_GUIDE = "is_first_aura_guide";
    private static final String IS_FIRST_ETS_GUIDE = "is_first_ets_guide";
    private static final String IS_FIRST_AURA_PROMPT = "is_first_aura_prompt";
    private static final String IS_FIRST_CAMERA_GUIDE = "is_first_camera_guide";
    private static final String IS_FIRST_NEW_VERSION = "is_first_new_version";
    private static final String IS_USER_NEW_VERSION = "is_user_new_version";
    private static final String IS_FIRST_BOOK_GUIDE = "is_first_book_guide";
    private static final String IS_FIRST_BOOK_FOLDER_GUIDE = "is_first_book_folder_guide";
    private static final String IS_FIRST_BOOK_PAGE_GUIDE = "is_first_book_page_guide";
    private static final String IS_HANDWRITING_GUIDE = "is_handwriting_guide";
    private static final String IS_SITTING_POSITION_ON = "is_sitting_position_on";
    private static final String IS_ET_PROMPT = "is_et_prompt";
    private static final String IS_UPDATE_FW_PROMPT = "is_update_fw_prompt";
    private static final String IS_AURAMATE_FW_PROMPT= "is_auramate_fw_prompt";

    // 电池优化
    private static final String IS_FIRST_IGNORE_BATTARY = "is_first_ignore_battery";

    //首次进入APP，展示隐私政策弹窗
    private static final String IS_FIRST_ENTER_APP = "is_first_enter_app";

    // debug release区分字符串
    private static final String DEBUG_RELEASE_TYPE = "debug_release_type";

    // GooglePlay location
    private static final String IS_FIRST_MIRROR_LOCATION = "is_first_mirror_location";

    // GooglePlay location
    private static final String IS_FIRST_AURAMATE_LOCATION = "is_first_auramate_location";

    // GooglePlay location
    private static final String IS_FIRST_ET_LOCATION = "is_first_et_location";


    private static FirstPreferences instance;

    public static FirstPreferences getInstance(Context context) {
        if (instance == null) {
            instance = new FirstPreferences(context, PREF);
        }
        return instance;
    }

    public FirstPreferences(Context context, String prefsName) {
        super(context, prefsName);
    }

    //debug release区分字符串
    public void setDebugReleaseType(String typeName) {
        put(DEBUG_RELEASE_TYPE, typeName);
    }

    //debug release区分字符串
    public String getDebugReleaseType() {
        Object obj = get(DEBUG_RELEASE_TYPE);
        if (Validator.isNotEmpty(obj)) {
            return (String) obj;
        }
        return "";
    }

    //首次进入APP
    public void setIsFirstEnterApp(boolean notFirst) {
        put(IS_FIRST_ENTER_APP, notFirst);
    }

    //首次进入APP
    public boolean isFirstEnterApp() {
        Object obj = get(IS_FIRST_ENTER_APP);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    // GooglePlay location
    public void setIsFirstMirrorLocation(boolean notFirst) {
        put(IS_FIRST_MIRROR_LOCATION, notFirst);
    }
    public boolean isFirstMirrorLocation() {
        Object obj = get(IS_FIRST_MIRROR_LOCATION);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    // GooglePlay location
    public void setIsFirstAuraMateLocation(boolean notFirst) {
        put(IS_FIRST_AURAMATE_LOCATION, notFirst);
    }
    public boolean isFirstAuraMateLocation() {
        Object obj = get(IS_FIRST_AURAMATE_LOCATION);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    // GooglePlay location
    public void setIsFirstETLocation(boolean notFirst) {
        put(IS_FIRST_ET_LOCATION, notFirst);
    }
    public boolean isFirstETLocation() {
        Object obj = get(IS_FIRST_ET_LOCATION);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }


    public void setIsFirstNoticeNew(boolean notFirst) {
        put(IS_FIRST_NOTICE_NEW_HANDWRITING, notFirst);
    }

    public boolean isFirstNoticeNew() {
        Object obj = get(IS_FIRST_NOTICE_NEW_HANDWRITING);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }
    public void setIsHandwritingGuide(boolean notFirst) {
        put(IS_HANDWRITING_GUIDE, notFirst);
    }

    public boolean isHandwritingGuide() {
        Object obj = get(IS_HANDWRITING_GUIDE);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setIsEtPrompt(boolean notFirst) {
        put(IS_ET_PROMPT, notFirst);
    }

    public boolean isEtPrompt() {
        Object obj = get(IS_ET_PROMPT);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }
    public void setIsUpdateFwPrompt(boolean notFirst) {
        put(IS_UPDATE_FW_PROMPT, notFirst);
    }

    public boolean isUpdateFwPrompt() {
        Object obj = get(IS_UPDATE_FW_PROMPT);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setIsFirstEtsGuide(boolean notFirst) {
        put(IS_FIRST_ETS_GUIDE, notFirst);
    }

    public boolean isFirstEtsGuide() {
        Object obj = get(IS_FIRST_ETS_GUIDE);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }


    public void setIsAuraMateFwPrompt(boolean notFirst) {
        put(IS_AURAMATE_FW_PROMPT, notFirst);
    }

    public boolean isAuraMateUpdateFwPrompt() {
        Object obj = get(IS_AURAMATE_FW_PROMPT);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }
    public void setIsFirstAuraGuide(boolean notFirst) {
        put(IS_FIRST_AURA_GUIDE, notFirst);
    }

    public boolean isFirstAuraGuide() {
        Object obj = get(IS_FIRST_AURA_GUIDE);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setIsFirstAuraPrompt(boolean notFirst) {
        put(IS_FIRST_AURA_PROMPT, notFirst);
    }

    public boolean isFirstAuraPrompt() {
        Object obj = get(IS_FIRST_AURA_PROMPT);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setIsFirstBookGuide(boolean notFirst) {
        put(IS_FIRST_BOOK_GUIDE, notFirst);
    }

    public boolean isFirstBookGuide() {
        Object obj = get(IS_FIRST_BOOK_GUIDE);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }
    public void setIsFirstBookFolderGuide(boolean notFirst) {
        put(IS_FIRST_BOOK_FOLDER_GUIDE, notFirst);
    }

    public boolean isFirstBookFolderGuide() {
        Object obj = get(IS_FIRST_BOOK_FOLDER_GUIDE);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }
    public void setIsFirstBookPageGuide(boolean notFirst) {
        put(IS_FIRST_BOOK_PAGE_GUIDE, notFirst);
    }

    public boolean isFirstBookPageGuide() {
        Object obj = get(IS_FIRST_BOOK_PAGE_GUIDE);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setIsFirstCameraGuide(boolean notFirst) {
        put(IS_FIRST_CAMERA_GUIDE, notFirst);
    }

    public boolean isFirstCameraGuide() {
        Object obj = get(IS_FIRST_CAMERA_GUIDE);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setIsFirstNewVersion(boolean notFirst) {
        put(IS_FIRST_NEW_VERSION, notFirst);
    }

    public boolean isFirstNewVersion() {
        Object obj = get(IS_FIRST_NEW_VERSION);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setIsUserNewVersion(boolean notFirst) {
        put(IS_USER_NEW_VERSION, notFirst);
    }

    public boolean isUserNewVersion() {
        Object obj = get(IS_USER_NEW_VERSION);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return false;
    }
    public void setIsSittingPositionOn(boolean isOn) {
        put(IS_SITTING_POSITION_ON, isOn);
    }

    public boolean isSittingPositionOn() {
        Object obj = get(IS_SITTING_POSITION_ON);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return false;
    }

    public void resetFirstPreference() {
        setIsUserNewVersion(false);
        setIsFirstNewVersion(true);
    }

    public void setIsFirstIgnoreBattery(boolean notFirst) {
        put(IS_FIRST_IGNORE_BATTARY, notFirst);
    }

    public boolean isFirstIgnoreBattery() {
        Object obj = get(IS_FIRST_IGNORE_BATTARY);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

}
