package com.czur.cloud.ui.auramate.siterror;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.ui.auramate.AuramateBaseActivity;
import com.czur.cloud.util.CzurFrescoHelper;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Created by <PERSON> on 2021/02/26.
 */

@SuppressWarnings("AlibabaAvoidManuallyCreateThread")
public class AuraMateStandarSitPicActivity extends AuramateBaseActivity implements View.OnClickListener {
    private String imgPath;
    private com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView picture_iv;
    private ImageView etFilesBackBtn;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_standar_sit_pic);

        imgPath = getIntent().getStringExtra("imgPath");

        initComponent();
        registerEvent();

    }

    @Override
    public boolean PCNeedFinish() {
        return false;
    }


    private void initComponent() {
        etFilesBackBtn = (ImageView) findViewById(R.id.top_back_btn);

        picture_iv = findViewById(R.id.show_standar_img);
//        picture_iv.setImage(ImageSource.uri(imgPath));
        CzurFrescoHelper.loadBigImage(AuraMateStandarSitPicActivity.this, picture_iv, imgPath, R.mipmap.default_gallery_img, false);

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
    }

    private void registerEvent() {
        etFilesBackBtn.setOnClickListener(this);

        setNetListener();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.top_back_btn:
                ActivityUtils.finishActivity(this);
                break;

            default:
                break;
        }
    }

}
