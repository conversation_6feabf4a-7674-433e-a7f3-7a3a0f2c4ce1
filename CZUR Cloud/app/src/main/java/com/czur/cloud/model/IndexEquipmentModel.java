package com.czur.cloud.model;

/**
 * Created by czur_app001 on 2018/1/19.
 * Email：<EMAIL>
 * (ง •̀_•́)ง
 */

public class IndexEquipmentModel {


    /**
     * id : 135
     * userId : 87
     * equipName : null
     * createOn : 2018-05-11 08:42:49
     * updateOn : 2018-05-11 08:55:33
     * isActive : true
     * aliasId : 1
     * key : 笔记本
     * alias : 笔记本测试
     */

    private int id;
    private int userId;
    private Object equipName;
    private String createOn;
    private String updateOn;
    private boolean isActive;
    private int aliasId;
    private String key;
    private String alias;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public Object getEquipName() {
        return equipName;
    }

    public void setEquipName(Object equipName) {
        this.equipName = equipName;
    }

    public String getCreateOn() {
        return createOn;
    }

    public void setCreateOn(String createOn) {
        this.createOn = createOn;
    }

    public String getUpdateOn() {
        return updateOn;
    }

    public void setUpdateOn(String updateOn) {
        this.updateOn = updateOn;
    }

    public boolean isIsActive() {
        return isActive;
    }

    public void setIsActive(boolean isActive) {
        this.isActive = isActive;
    }

    public int getAliasId() {
        return aliasId;
    }

    public void setAliasId(int aliasId) {
        this.aliasId = aliasId;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

}

