package com.czur.cloud.netty.bean;


import com.czur.cloud.entity.realm.MessageEntity;
import com.czur.cloud.netty.Config;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.Serializable;

public class CZMessage implements Serializable {
    public MessageEntity getMessageEntity() {
        return messageEntity;
    }

    public void setMessageEntity(MessageEntity messageEntity) {
        this.messageEntity = messageEntity;
    }

    protected MessageEntity messageEntity;

    protected String appid; // 应用标识bundle id

    protected Object body; // 消息体内容

    protected String device; // CZUR设备种类，Aura, 投影仪等

    protected String os; // 设备操作系统

    protected String type; // 消息类型



    protected String token; // 消息类型


    protected String udid; // 设备唯一标识

    protected String userid; // 用户ID, 唯一标识


    protected String requestid;


    public String getRequestid() {
        return requestid;
    }

    public void setRequestid(String requestid) {
        this.requestid = requestid;
    }


    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getOs() {
        return os;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getDevice() {
        return device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Object getBody() {
        return body;
    }

    public void setBody(Object body) {
        this.body = body;
    }

    public static String formatJsonResponse(CZMessage message) {
        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        String json = gson.toJson(message);
        json += Config.MESSAGE_DELIMITER;
        return json;
    }

    public static CZMessage parseMessage(String message) {
        return new Gson().fromJson(message, CZMessage.class);
    }
    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    @Override
    public String toString() {
        return "CZMessage{" +
                "appid='" + appid + '\'' +
                ", body=" + body +
                ", device='" + device + '\'' +
                ", os='" + os + '\'' +
                ", type='" + type + '\'' +
                ", token='" + token + '\'' +
                ", udid='" + udid + '\'' +
                ", userid='" + userid + '\'' +
                ", requestid='" + requestid + '\'' +
                '}';
    }
}