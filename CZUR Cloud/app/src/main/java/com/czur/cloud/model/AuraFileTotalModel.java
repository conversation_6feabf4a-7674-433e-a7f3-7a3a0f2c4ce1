package com.czur.cloud.model;

import java.io.Serializable;
import java.util.List;

public class AuraFileTotalModel {


        /**
         * fileList : [{"id":"sqobbyk4kn7i3zc","seqNum":53,"userId":9,"dirId":null,"mode":1,"userSelectMode":0,"smartResult":1,"orgImgId":92,"orgKey":"test/9/qaz-147-wsx-258/2018-10-19/d685f480-1734-4c1d-86c6-418849580e9b.jpg","org":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/d685f480-1734-4c1d-86c6-418849580e9b.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=bmT48ikYSK90PERKQ5wjFdkZ554=","middleOrg":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/d685f480-1734-4c1d-86c6-418849580e9b.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=kxCFWzsl7EpKZZOfyp5lUGH4IB0=&x-oss-process=image/resize,m_fixed,w_1080,h_1080","smallOrg":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/d685f480-1734-4c1d-86c6-418849580e9b.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=433lfOBZeRJNcXxp0Lf4ts7p1Bg=&x-oss-process=image/resize,m_fixed,w_150,h_150","orgFileSize":1887501,"orgFileSizeUnit":"1.8 MB","fileName":"20181019094354","equipmentUID":"qaz-147-wsx-258","singleId":93,"singleKey":"test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg","singleFileSize":1693212,"singleFileSizeUnit":"1.6 MB","single":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=WNAY+YUkHXpmsm3uhZf1Hs0wdjM=","smallSingle":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=uhdPXRp6qNb+GTLe+85RYqZC0mY=&x-oss-process=image/resize,m_fixed,w_150,h_150","middleSingle":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=CYttJu3OZNN+AcHu5VxS3LmWP3I=&x-oss-process=image/resize,m_fixed,w_1080,h_1080","bookId":null,"bookKey":null,"book":null,"middleBook":null,"smallBook":null,"bookFileSize":null,"bookFileSizeUnit":null,"takeOn":"1539913434000","createOn":1539913435000,"localeTime":null,"small":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=uhdPXRp6qNb+GTLe+85RYqZC0mY=&x-oss-process=image/resize,m_fixed,w_150,h_150","middle":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=CYttJu3OZNN+AcHu5VxS3LmWP3I=&x-oss-process=image/resize,m_fixed,w_1080,h_1080","big":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=WNAY+YUkHXpmsm3uhZf1Hs0wdjM=","fileSize":1693212,"fileSizeUnit":"1.6 MB"},{"id":"9d8g1dm9ilgq2ep","seqNum":52,"userId":9,"dirId":null,"mode":1,"userSelectMode":0,"smartResult":1,"orgImgId":90,"orgKey":"test/9/qaz-147-wsx-258/2018-10-19/4859e297-2bbf-4242-b105-d30768ea4a1a.jpg","org":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/4859e297-2bbf-4242-b105-d30768ea4a1a.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=goReZApqxmK0jEQeca3gDMEvCmY=","middleOrg":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/4859e297-2bbf-4242-b105-d30768ea4a1a.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=Sex2nQFtckbJSIq+g6HjtSnA88s=&x-oss-process=image/resize,m_fixed,w_1080,h_1080","smallOrg":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/4859e297-2bbf-4242-b105-d30768ea4a1a.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=14oJPx9tiKuWpQTZyEi/bv2X2V8=&x-oss-process=image/resize,m_fixed,w_150,h_150","orgFileSize":1862981,"orgFileSizeUnit":"1.8 MB","fileName":"20181019094345","equipmentUID":"qaz-147-wsx-258","singleId":91,"singleKey":"test/9/qaz-147-wsx-258/2018-10-19/process/a1385465-0cef-4f3b-b546-b73ffe048435.jpg","singleFileSize":1751571,"singleFileSizeUnit":"1.7 MB","single":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/a1385465-0cef-4f3b-b546-b73ffe048435.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=WEupH50lMtwVRVFm3ruwbhh7kvE=","smallSingle":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/a1385465-0cef-4f3b-b546-b73ffe048435.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=r76JvQADhlKV/K4o0rqQeWCQFTw=&x-oss-process=image/resize,m_fixed,w_150,h_150","middleSingle":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/a1385465-0cef-4f3b-b546-b73ffe048435.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=mlwt3wKhoNZzji/2Y6XtD79Knn4=&x-oss-process=image/resize,m_fixed,w_1080,h_1080","bookId":null,"bookKey":null,"book":null,"middleBook":null,"smallBook":null,"bookFileSize":null,"bookFileSizeUnit":null,"takeOn":"1539913425000","createOn":1539913425000,"localeTime":null,"small":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/a1385465-0cef-4f3b-b546-b73ffe048435.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=r76JvQADhlKV/K4o0rqQeWCQFTw=&x-oss-process=image/resize,m_fixed,w_150,h_150","middle":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/a1385465-0cef-4f3b-b546-b73ffe048435.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=mlwt3wKhoNZzji/2Y6XtD79Knn4=&x-oss-process=image/resize,m_fixed,w_1080,h_1080","big":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/a1385465-0cef-4f3b-b546-b73ffe048435.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=WEupH50lMtwVRVFm3ruwbhh7kvE=","fileSize":1751571,"fileSizeUnit":"1.7 MB"},{"id":"rnve14vrtcftctb","seqNum":51,"userId":9,"dirId":null,"mode":1,"userSelectMode":0,"smartResult":1,"orgImgId":88,"orgKey":"test/9/qaz-147-wsx-258/2018-10-19/e2c86a72-8e01-46e1-b794-e47e731c58dd.jpg","org":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/e2c86a72-8e01-46e1-b794-e47e731c58dd.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=geSR23r7Mahp4UlzQj9AxvshEHY=","middleOrg":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/e2c86a72-8e01-46e1-b794-e47e731c58dd.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=J8UM1gHdQTuMx5+9yqgnTU9Pecc=&x-oss-process=image/resize,m_fixed,w_1080,h_1080","smallOrg":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/e2c86a72-8e01-46e1-b794-e47e731c58dd.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=rxUzL99/tJj609mvLIqopa0qwOQ=&x-oss-process=image/resize,m_fixed,w_150,h_150","orgFileSize":2915374,"orgFileSizeUnit":"2.8 MB","fileName":"20181019094333","equipmentUID":"qaz-147-wsx-258","singleId":89,"singleKey":"test/9/qaz-147-wsx-258/2018-10-19/process/89f6dc2d-e5dd-4eff-a368-4592bc48de84.jpg","singleFileSize":2918210,"singleFileSizeUnit":"2.8 MB","single":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/89f6dc2d-e5dd-4eff-a368-4592bc48de84.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=s/6BjOTNO4/n6c2uJArSLf4UZmo=","smallSingle":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/89f6dc2d-e5dd-4eff-a368-4592bc48de84.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=Ef3lJxDf81GFowcDzPjVYE1Q2Ic=&x-oss-process=image/resize,m_fixed,w_150,h_150","middleSingle":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/89f6dc2d-e5dd-4eff-a368-4592bc48de84.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=or8eRsIaRYNzaDV/lFyVgemvLn0=&x-oss-process=image/resize,m_fixed,w_1080,h_1080","bookId":null,"bookKey":null,"book":null,"middleBook":null,"smallBook":null,"bookFileSize":null,"bookFileSizeUnit":null,"takeOn":"1539913413000","createOn":1539913414000,"localeTime":null,"small":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/89f6dc2d-e5dd-4eff-a368-4592bc48de84.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=Ef3lJxDf81GFowcDzPjVYE1Q2Ic=&x-oss-process=image/resize,m_fixed,w_150,h_150","middle":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/89f6dc2d-e5dd-4eff-a368-4592bc48de84.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=or8eRsIaRYNzaDV/lFyVgemvLn0=&x-oss-process=image/resize,m_fixed,w_1080,h_1080","big":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/89f6dc2d-e5dd-4eff-a368-4592bc48de84.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=s/6BjOTNO4/n6c2uJArSLf4UZmo=","fileSize":2918210,"fileSizeUnit":"2.8 MB"}]
         * offset : 3
         */

        private int offset;
        private List<FileListBean> fileList;

        public int getOffset() {
            return offset;
        }

        public void setOffset(int offset) {
            this.offset = offset;
        }

        public List<FileListBean> getFileList() {
            return fileList;
        }

        public void setFileList(List<FileListBean> fileList) {
            this.fileList = fileList;
        }

        public static class FileListBean implements Serializable {
            /**
             * id : sqobbyk4kn7i3zc
             * seqNum : 53
             * userId : 9
             * dirId : null
             * mode : 1
             * userSelectMode : 0
             * smartResult : 1
             * orgImgId : 92
             * orgKey : test/9/qaz-147-wsx-258/2018-10-19/d685f480-1734-4c1d-86c6-418849580e9b.jpg
             * org : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/d685f480-1734-4c1d-86c6-418849580e9b.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=bmT48ikYSK90PERKQ5wjFdkZ554=
             * middleOrg : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/d685f480-1734-4c1d-86c6-418849580e9b.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=kxCFWzsl7EpKZZOfyp5lUGH4IB0=&x-oss-process=image/resize,m_fixed,w_1080,h_1080
             * smallOrg : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/d685f480-1734-4c1d-86c6-418849580e9b.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=433lfOBZeRJNcXxp0Lf4ts7p1Bg=&x-oss-process=image/resize,m_fixed,w_150,h_150
             * orgFileSize : 1887501
             * orgFileSizeUnit : 1.8 MB
             * fileName : 20181019094354
             * equipmentUID : qaz-147-wsx-258
             * singleId : 93
             * singleKey : test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg
             * singleFileSize : 1693212
             * singleFileSizeUnit : 1.6 MB
             * single : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=WNAY+YUkHXpmsm3uhZf1Hs0wdjM=
             * smallSingle : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=uhdPXRp6qNb+GTLe+85RYqZC0mY=&x-oss-process=image/resize,m_fixed,w_150,h_150
             * middleSingle : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=CYttJu3OZNN+AcHu5VxS3LmWP3I=&x-oss-process=image/resize,m_fixed,w_1080,h_1080
             * bookId : null
             * bookKey : null
             * book : null
             * middleBook : null
             * smallBook : null
             * bookFileSize : null
             * bookFileSizeUnit : null
             * takeOn : 1539913434000
             * createOn : 1539913435000
             * localeTime : null
             * small : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=uhdPXRp6qNb+GTLe+85RYqZC0mY=&x-oss-process=image/resize,m_fixed,w_150,h_150
             * middle : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=CYttJu3OZNN+AcHu5VxS3LmWP3I=&x-oss-process=image/resize,m_fixed,w_1080,h_1080
             * big : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-10-19/process/7ded6826-e3e4-4c7c-b60e-aae694abd201.jpg?Expires=1539920366&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=WNAY+YUkHXpmsm3uhZf1Hs0wdjM=
             * fileSize : 1693212
             * fileSizeUnit : 1.6 MB
             */

            private String id;
            private int seqNum;
            private String userId;
            private Object dirId;
            private int mode;
            private int userSelectMode;
            private int smartResult;
            private int orgImgId;
            private String orgKey;
            private String org;
            private String middleOrg;
            private String smallOrg;
            private int orgFileSize;
            private String orgFileSizeUnit;
            private String fileName;
            private String equipmentUID;
            private int singleId;
            private String singleKey;
            private int singleFileSize;
            private String singleFileSizeUnit;
            private String single;
            private String smallSingle;
            private String middleSingle;
            private Object bookId;
            private String bookKey;
            private String book;
            private String middleBook;
            private String smallBook;
            private int bookFileSize;
            private Object bookFileSizeUnit;
            private String takeOn;
            private long createOn;
            private Object localeTime;
            private String small;
            private String middle;
            private String big;
            private int fileSize;
            private String fileSizeUnit;
            private int colorMode;


            public int getColorMode() {
                return colorMode;
            }

            public void setColorMode(int colorMode) {
                this.colorMode = colorMode;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public int getSeqNum() {
                return seqNum;
            }

            public void setSeqNum(int seqNum) {
                this.seqNum = seqNum;
            }

            public String getUserId() {
                return userId;
            }

            public void setUserId(String userId) {
                this.userId = userId;
            }

            public Object getDirId() {
                return dirId;
            }

            public void setDirId(Object dirId) {
                this.dirId = dirId;
            }

            public int getMode() {
                return mode;
            }

            public void setMode(int mode) {
                this.mode = mode;
            }

            public int getUserSelectMode() {
                return userSelectMode;
            }

            public void setUserSelectMode(int userSelectMode) {
                this.userSelectMode = userSelectMode;
            }

            public int getSmartResult() {
                return smartResult;
            }

            public void setSmartResult(int smartResult) {
                this.smartResult = smartResult;
            }

            public int getOrgImgId() {
                return orgImgId;
            }

            public void setOrgImgId(int orgImgId) {
                this.orgImgId = orgImgId;
            }

            public String getOrgKey() {
                return orgKey;
            }

            public void setOrgKey(String orgKey) {
                this.orgKey = orgKey;
            }

            public String getOrg() {
                return org;
            }

            public void setOrg(String org) {
                this.org = org;
            }

            public String getMiddleOrg() {
                return middleOrg;
            }

            public void setMiddleOrg(String middleOrg) {
                this.middleOrg = middleOrg;
            }

            public String getSmallOrg() {
                return smallOrg;
            }

            public void setSmallOrg(String smallOrg) {
                this.smallOrg = smallOrg;
            }

            public int getOrgFileSize() {
                return orgFileSize;
            }

            public void setOrgFileSize(int orgFileSize) {
                this.orgFileSize = orgFileSize;
            }

            public String getOrgFileSizeUnit() {
                return orgFileSizeUnit;
            }

            public void setOrgFileSizeUnit(String orgFileSizeUnit) {
                this.orgFileSizeUnit = orgFileSizeUnit;
            }

            public String getFileName() {
                return fileName;
            }

            public void setFileName(String fileName) {
                this.fileName = fileName;
            }

            public String getEquipmentUID() {
                return equipmentUID;
            }

            public void setEquipmentUID(String equipmentUID) {
                this.equipmentUID = equipmentUID;
            }

            public int getSingleId() {
                return singleId;
            }

            public void setSingleId(int singleId) {
                this.singleId = singleId;
            }

            public String getSingleKey() {
                return singleKey;
            }

            public void setSingleKey(String singleKey) {
                this.singleKey = singleKey;
            }

            public int getSingleFileSize() {
                return singleFileSize;
            }

            public void setSingleFileSize(int singleFileSize) {
                this.singleFileSize = singleFileSize;
            }

            public String getSingleFileSizeUnit() {
                return singleFileSizeUnit;
            }

            public void setSingleFileSizeUnit(String singleFileSizeUnit) {
                this.singleFileSizeUnit = singleFileSizeUnit;
            }

            public String getSingle() {
                return single;
            }

            public void setSingle(String single) {
                this.single = single;
            }

            public String getSmallSingle() {
                return smallSingle;
            }

            public void setSmallSingle(String smallSingle) {
                this.smallSingle = smallSingle;
            }

            public String getMiddleSingle() {
                return middleSingle;
            }

            public void setMiddleSingle(String middleSingle) {
                this.middleSingle = middleSingle;
            }

            public Object getBookId() {
                return bookId;
            }

            public void setBookId(Object bookId) {
                this.bookId = bookId;
            }

            public String getBookKey() {
                return bookKey;
            }

            public void setBookKey(String bookKey) {
                this.bookKey = bookKey;
            }

            public String getBook() {
                return book;
            }

            public void setBook(String book) {
                this.book = book;
            }

            public String getMiddleBook() {
                return middleBook;
            }

            public void setMiddleBook(String middleBook) {
                this.middleBook = middleBook;
            }

            public String getSmallBook() {
                return smallBook;
            }

            public void setSmallBook(String smallBook) {
                this.smallBook = smallBook;
            }

            public int getBookFileSize() {
                return bookFileSize;
            }

            public void setBookFileSize(int bookFileSize) {
                this.bookFileSize = bookFileSize;
            }

            public Object getBookFileSizeUnit() {
                return bookFileSizeUnit;
            }

            public void setBookFileSizeUnit(Object bookFileSizeUnit) {
                this.bookFileSizeUnit = bookFileSizeUnit;
            }

            public String getTakeOn() {
                return takeOn;
            }

            public void setTakeOn(String takeOn) {
                this.takeOn = takeOn;
            }

            public long getCreateOn() {
                return createOn;
            }

            public void setCreateOn(long createOn) {
                this.createOn = createOn;
            }

            public Object getLocaleTime() {
                return localeTime;
            }

            public void setLocaleTime(Object localeTime) {
                this.localeTime = localeTime;
            }

            public String getSmall() {
                return small;
            }

            public void setSmall(String small) {
                this.small = small;
            }

            public String getMiddle() {
                return middle;
            }

            public void setMiddle(String middle) {
                this.middle = middle;
            }

            public String getBig() {
                return big;
            }

            public void setBig(String big) {
                this.big = big;
            }

            public int getFileSize() {
                return fileSize;
            }

            public void setFileSize(int fileSize) {
                this.fileSize = fileSize;
            }

            public String getFileSizeUnit() {
                return fileSizeUnit;
            }

            public void setFileSizeUnit(String fileSizeUnit) {
                this.fileSizeUnit = fileSizeUnit;
            }
        }
}