package com.czur.cloud.ui.starry.activity

import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.PopupWindow
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryCommonEvent
import com.czur.cloud.ui.mirror.comm.FastBleToolUtils
import com.czur.cloud.ui.starry.base.StarryNewBaseActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.meeting.baselib.utils.addNoSpaceFilter
import com.czur.cloud.ui.starry.meeting.baselib.utils.dp2px
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.model.CommonEnterprise
import com.czur.cloud.ui.starry.model.StarryAddressBookModel
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.ui.starry.viewmodel.StarryContactViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import kotlinx.android.synthetic.main.starry_activity_contact_addto.contact_detail_company_change_btn
import kotlinx.android.synthetic.main.starry_activity_contact_addto.contact_detail_company_name
import kotlinx.android.synthetic.main.starry_activity_contact_addto.contact_detail_company_name_old
import kotlinx.android.synthetic.main.starry_activity_contact_addto.contact_detail_company_name_value_tv
import kotlinx.android.synthetic.main.starry_activity_contact_addto.contact_detail_company_out_ll
import kotlinx.android.synthetic.main.starry_activity_contact_addto.contact_detail_company_title_value_tv
import kotlinx.android.synthetic.main.starry_activity_contact_addto.contact_mobile
import kotlinx.android.synthetic.main.starry_activity_contact_addto.contact_mobile_title1
import kotlinx.android.synthetic.main.starry_activity_contact_addto.contact_nickname
import kotlinx.android.synthetic.main.starry_activity_contact_addto.contact_nickname_rl
import kotlinx.android.synthetic.main.starry_activity_contact_addto.contact_save_to_btn
import kotlinx.android.synthetic.main.starry_activity_contact_addto.contact_user_name
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_detail_company_name_tv
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_detail_company_title_tv
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_nickname_title
import kotlinx.android.synthetic.main.starry_contact_detail_change_company_item.view.select_iv
import kotlinx.android.synthetic.main.starry_contact_detail_change_company_item.view.starry_contact_detail_company_change_cl
import kotlinx.android.synthetic.main.starry_contact_detail_change_company_item.view.title_tv
import kotlinx.android.synthetic.main.starry_layout_top_bar.user_back_btn
import kotlinx.android.synthetic.main.starry_layout_top_bar.user_title
import kotlinx.coroutines.delay
import org.greenrobot.eventbus.EventBus

/**
 */
class StarryContactAddToActivity : StarryNewBaseActivity() {

    private val starryViewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryViewModel::class.java)
    }
    private val viewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryContactViewModel::class.java)
    }

    private lateinit var currentAddressBookModel: StarryAddressBookModel

    private var datasList: ArrayList<CommonEnterprise> = arrayListOf()
    private var popup: PopupWindow? = null
    private var listView: RecyclerView? = null

    private var contactName: String = ""
    private var contactNameOld:String = ""
    private lateinit var userFromType: String
    private val MAX_DEVICE_NAME_LENGTH = 14 // 名称规则：7个汉字或14个字符。

    override fun getLayout(): Int = R.layout.starry_activity_contact_addto

    override fun initViews() {
        super.initViews()

        currentAddressBookModel =
            (intent.getSerializableExtra(StarryConstants.STARRY_USER_MODEL) as StarryAddressBookModel?)!!

        contactName = currentAddressBookModel.name ?: ""

        formatContactName()
        contactNameOld = contactName
        contact_user_name?.apply {
            setText("")
        }
        contact_mobile?.text = ""
        contact_detail_company_name?.text = ""
        contact_detail_company_name_value_tv?.text = ""


        contact_save_to_btn?.visibility = View.VISIBLE

        var meetingNo = ""
        userFromType = intent.getStringExtra(StarryConstants.STARRY_USER_TYPE) ?: StarryConstants.STARRY_USER_TYPE_CONTACT
        when (userFromType){

            StarryConstants.STARRY_USER_TYPE_CONTACT -> {
                meetingNo = currentAddressBookModel.meetingNo
            }

            StarryConstants.STARRY_USER_TYPE_COMPANY -> {
                meetingNo = currentAddressBookModel.meetingAccout
            }

            StarryConstants.STARRY_USER_TYPE_CC -> {
                meetingNo = currentAddressBookModel.meetingNo
            }
       }
        contact_mobile?.text = meetingNo

        // title
        user_title.text = getString(R.string.starry_title_detail_contact)
        // back btn
        user_back_btn?.singleClick {
            ActivityUtils.finishActivity(this)
        }

        // save
        contact_save_to_btn?.singleClick {
            addOrEditRemarkName()
        }

        // 备注输入框监听
        contact_user_name?.addTextChangedListener(object : TextWatcher {
            private var temp: CharSequence? = null
            private var selectionStart = 0
            private var selectionEnd = 0
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                temp = s
            }

            override fun afterTextChanged(s: Editable) {
                selectionStart = contact_user_name?.selectionStart ?: 0
                selectionEnd = contact_user_name?.selectionEnd ?: 0
                val str = temp.toString()
                if (!TextUtils.isEmpty(str)) {
                    val l = Tools.getTextLength(str)
                    if (l > StarryConstants.MAX_DEVICE_NAME_LENGTH) {
                        try{
                            s.delete(selectionStart - 1, selectionEnd)
                            val tempSelection = selectionEnd
                            contact_user_name?.text = s
                            contact_user_name?.setSelection(tempSelection)
                        }catch (e: Exception){
                        }
                    }
                    contactName = s.toString()
                } else {
                    contactName = viewModel.contactDetail.value?.nickName.toString()
                }
            }
        })

        // 添加禁止输入空格的过滤器
        contact_user_name?.addNoSpaceFilter()

        contact_user_name?.apply {
            isFocusable = true
            isFocusableInTouchMode = true
            requestFocus()
            findFocus()
        }
        this.window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)

        contact_detail_company_change_btn?.singleClick {
            changeCompanyBtn(it)
        }

        // 关联企业先spsce隐藏
        contact_detail_company_out_ll?.visibility = View.GONE

        // 重新获取detail
//        viewModel.getContactDetail(meetingNo)
        viewModel.getContactDetailV2(currentAddressBookModel.id)

        if (BuildConfig.IS_OVERSEAS){
            contact_nickname_title?.width = dp2px(this, StarryContactDetailActivity.TITLE_WIDTH)
            contact_mobile_title1?.width = dp2px(this, StarryContactDetailActivity.TITLE_WIDTH)
            contact_detail_company_name_tv?.width = dp2px(this, StarryContactDetailActivity.TITLE_WIDTH)
            contact_detail_company_title_tv?.width = dp2px(this, StarryContactDetailActivity.TITLE_WIDTH)
        }
    }

    // 切换企业按钮
    private fun changeCompanyBtn(view: View) {
        popupWindow()
    }

    // 修改备注名
    private fun addOrEditRemarkName(){

        if (!NetworkUtils.isConnected()) {
            ToastUtils.showLong(R.string.starry_network_error_msg)
            return
        }

        addToAddressBook()
    }

    private fun hideSoftInputFromWindow(editText: EditText) {
        editText.isFocusable = false
        editText.isFocusableInTouchMode = false
        val imm: InputMethodManager = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        if (imm != null) {
            imm.hideSoftInputFromWindow(window.decorView.windowToken, 0)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        viewModel.contactDetail.observe(this){

            val enterpriseId = currentAddressBookModel.enterpriseId //跳转过来的公司id

            var useEnterpriseName = false
            for (company in it.commonEnterprise){
                if (enterpriseId.isEmpty()){
                    contactName = company.memberName
                    useEnterpriseName = true
                    break
                }else if (enterpriseId.isNotEmpty() && company.enterpriseId == enterpriseId){
                    contactName = company.memberName
                    useEnterpriseName = true
                    break
                }
            }

            if (!useEnterpriseName){
                // 从联系人列表查看联系人详情：联系人详情里显示备注（如果没有备注则仅显示昵称，效果同微信）、昵称、账号；
                contactName = it.nickName ?: ""
            }

            formatContactName()
            contact_user_name?.setText(contactName)
//            contact_nickname_rl?.visibility = View.GONE

            // 判断昵称是否存在
            val nickname = it.nickName ?: ""
            if (nickname.isBlank()){
                contact_nickname_rl?.visibility = View.GONE
            }else {
                contact_nickname_rl?.visibility = View.VISIBLE
                contact_nickname?.text = nickname
            }
            contactNameOld = contactName

            contact_mobile?.text = it.meetingNo

        }

        viewModel.commonEnterprise.observe(this){
            if (it.isNotEmpty()){
                contact_detail_company_out_ll?.visibility = View.VISIBLE

                contact_detail_company_change_btn?.visibility = View.GONE
                if (it.size > 1){
                    contact_detail_company_change_btn?.visibility = View.VISIBLE
                }
                datasList.clear()
                datasList.addAll(it)
                viewModel.getCurrentCompany(starryViewModel.currentCompanyModel.value)

            }else{
                contact_detail_company_out_ll?.visibility = View.GONE
            }
        }

        viewModel.currentSelectCompany.observe(this){
            contact_detail_company_name?.text = it.enterpriseName
            contact_detail_company_name_value_tv?.text = it.memberName

            contact_user_name?.setText(it.memberName)
            contact_user_name?.setSelection(it.memberName.length)

            var pos = getString(R.string.starry_home_none)
            if (it.position != null && it.position.isNotEmpty()){
                pos = it.position
            }
            contact_detail_company_title_value_tv?.text = pos
            if (it.expired){
                contact_detail_company_name_old?.visibility = View.VISIBLE
            }else{
                contact_detail_company_name_old?.visibility = View.GONE
            }
        }

    }

    private fun addToAddressBook() {
        val user_name = contactName  //contact_user_name?.text?.toString() ?: ""
        val user_mobile = contact_mobile?.text?.toString() ?: ""

        // 特殊字符检测
        if (Tools.containsEmoji(user_name)){
            ToastUtils.showLong(getString(R.string.starry_add_contact_judge))
            return
        }

        // 判断名称长度
        val tmp_len = Tools.getTextLength(user_name)
        if (tmp_len > StarryConstants.MAX_DEVICE_NAME_LENGTH){
            ToastUtils.showLong(getString(R.string.starry_add_contact_name_long))
            return
        }

        // 本地检查是否重复
        if (checkRepeatAddressBook(user_mobile)){
            ToastUtils.showLong(R.string.starry_contact_add_repeat_msg)
            return
        }

        launch {
            Tools.setViewButtonEnable(contact_save_to_btn, false)
            showProgressDialog()
            val flag = viewModel.addAddressBook(
                "",
                user_mobile,
                user_name
            )
            if (flag) {
                contact_user_name?.setText(contactName)
                contact_user_name?.visibility = View.VISIBLE
                hideSoftInputFromWindow(contact_user_name)
//                contact_user_name_rl?.requestFocus()

                // 重新获取detail
//                viewModel.getContactDetailV2(currentAddressBookModel.id)
                LiveDataBus.get().with(StarryConstants.STARRY_CONTACT_ADDTO_MEETING).value = true
                EventBus.getDefault().post(StarryCommonEvent(EventType.STARRY_CONTACT_ADDTO, ""))
                delay(100)
                hideProgressDialog()
                finish()

            }else{
                Tools.setViewButtonEnable(contact_save_to_btn, true)
                hideProgressDialog()
                ToastUtils.showLong(getString(R.string.starry_add_contact_fail))
            }

        }
    }

    // 本地检查是否重复
    private fun checkRepeatAddressBook(userMobile: String): Boolean {
        val count = starryViewModel.currentContactsList.value?.count{
            it.meetingNo == userMobile
        } ?: 0
        return count > 0
    }

    private fun formatContactName(){
        var newName = ""
        if (!TextUtils.isEmpty(contactName)) {//保留14个字符以内的名字
            val l = FastBleToolUtils.getTextLength(contactName)
            if (l > MAX_DEVICE_NAME_LENGTH) {
                contactName.forEach {
                    newName += it.toString()
                    if (FastBleToolUtils.getTextLength(newName.toString()) > MAX_DEVICE_NAME_LENGTH){
                        newName = newName.substring(0,newName.length-1)
                        return@forEach
                    }else if (FastBleToolUtils.getTextLength(newName.toString()) == MAX_DEVICE_NAME_LENGTH){
                        return@forEach
                    }
                }
                contactName = newName
                contactNameOld = newName
            }
        }

        contact_user_name.postDelayed({
            contact_user_name.setSelection(contactName.length)
            //将光标移至文字末尾
            contact_user_name.visibility = View.VISIBLE
            contact_user_name.isFocusable = true
            contact_user_name.isFocusableInTouchMode = true
            contact_user_name.requestFocus()
            contact_user_name.findFocus()

        },100)
    }
    /**
     * 显示窗口
     */
    private fun popupWindow() {
        if (datasList.isEmpty()){
            return
        }

        popup = PopupWindow(this)
        popup?.apply {
            width = ConstraintLayout.LayoutParams.WRAP_CONTENT  // 650  //ConstraintLayout.LayoutParams.MATCH_PARENT // contact_detail_company_change_btn?.width!!
            height = ConstraintLayout.LayoutParams.WRAP_CONTENT
            setBackgroundDrawable(getDrawable(R.color.transparent))
            val view = LayoutInflater.from(this@StarryContactAddToActivity).inflate(R.layout.starry_contact_detail_change_company_popup_layout,null,false)
            listView = view.findViewById(R.id.rvList)
            listView?.apply {
                isVerticalScrollBarEnabled = false //不显示滑动条
                val lm = LinearLayoutManager(context)
                lm.orientation = LinearLayoutManager.VERTICAL
                layoutManager = lm
                adapter = MyAdapter()
            }
            contentView = view
            isOutsideTouchable = true //点击PopupWindow以外的区域自动关闭该窗口
            showAsDropDown(contact_detail_company_change_btn, 0, 0, Gravity.END) //显示在edit控件的下面0,0代表偏移量
        }
    }

    //适配器
    inner class MyAdapter : RecyclerView.Adapter<MyAdapter.InnerHodler>() {

        inner class InnerHodler(itemView: View): RecyclerView.ViewHolder(itemView) {
        }

        override fun getItemId(position: Int): Long {
            return position.toLong()
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): InnerHodler {
            val itemView = LayoutInflater.from(parent.context).inflate(R.layout.starry_contact_detail_change_company_item, parent, false)
            return InnerHodler(itemView)
        }

        override fun getItemCount(): Int {
            return datasList.size
        }

        override fun onBindViewHolder(holder: InnerHodler, position: Int) {
            val companyModel = datasList[position]
            holder.itemView.title_tv?.text = companyModel.enterpriseName
            if (viewModel.currentSelectCompanyIndex == position) {
                holder.itemView.select_iv?.visibility = View.VISIBLE
            }else{
                holder.itemView.select_iv?.visibility = View.INVISIBLE
            }
            //为listView的每一个子条目设置监听,以区分每个删除按钮
            holder.itemView.starry_contact_detail_company_change_cl?.setOnClickListener(View.OnClickListener {
//                <EMAIL>() //更新ListView的数据
                viewModel.changeCurrentCompany(position)
                popup?.dismiss()
            })

        }
    }


}