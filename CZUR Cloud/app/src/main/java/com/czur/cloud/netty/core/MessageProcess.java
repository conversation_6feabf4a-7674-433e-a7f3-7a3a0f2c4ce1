package com.czur.cloud.netty.core;


import static com.czur.czurutils.log.CZURLogUtilsKt.logD;

import android.util.Log;

import com.blankj.utilcode.util.ServiceUtils;
import com.czur.cloud.netty.bean.CZMessage;
import com.czur.cloud.netty.observer.NettyService;
import com.czur.cloud.netty.observer.NettyUtils;

import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;


public class MessageProcess {

    /**
     * 发送心跳检测数据包
     *
     * @param ctx
     */
    public static void sendHeartbeat(ChannelHandlerContext ctx) {
        Log.d("Heartbeat", "sendHeartbeat");
        CZMessage heartbeat = MessageFactory.heartbeatMessage();
        ctx.writeAndFlush(CZMessage.formatJsonResponse(heartbeat)).addListener(new ChannelFutureListener() {
            @Override
            public void operationComplete(ChannelFuture future) throws Exception {
                if (future.isSuccess()){

                }else {
                    logD("MessageProcess.sendHeartbeat 发送心跳失败,重连");
                    if (ServiceUtils.isServiceRunning(NettyService.class)) {
//                        ServiceUtils.stopService(NettyService.class);
                        NettyUtils.getInstance().stopNettyService();
                    }

                    try{
                        Thread.sleep(200);
                    }catch (Exception e){

                    }
                    if (!ServiceUtils.isServiceRunning(NettyService.class)) {
                        logD("MessageProcess.sendHeartbeat.NettyService.class");
                        NettyUtils.getInstance().startNettyService();
                    }
                }
            }
        });
    }


}
