package com.czur.cloud.ui.starry.activity

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.event.BaseEvent
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryUserListEvent
import com.czur.cloud.netty.bean.StarryRecivedUser
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.starry.adapter.StarryCompanyListContactAdapter
import com.czur.cloud.ui.starry.base.StarryNewBaseActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.component.LetterViewNew
import com.czur.cloud.ui.starry.meeting.MeetingMainActivity
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.bean.UserStatus
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.ui.starry.viewmodel.StarryContactViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import com.czur.czurutils.log.logI
import com.google.gson.Gson
import kotlinx.android.synthetic.main.starry_activity_company_list_contacts.*
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * 某个企业选择人员列表
 */
class StarryCompanyListContactsActivity : StarryNewBaseActivity() {
    val TAG = "StarryCompanyListContactsActivity"

    override fun getLayout(): Int = R.layout.starry_activity_company_list_contacts

    private val viewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryViewModel::class.java)
    }

    private val contactViewModel by lazy {
        ViewModelProvider(
            StarryActivity.mainActivity ?: this
        ).get(StarryContactViewModel::class.java)
    }

    private val linearLayoutManager by lazy { LinearLayoutManager(this) }

    private val mAdapter by lazy {
        StarryCompanyListContactAdapter(this)
    }

    private val selectMaxCountNumber = StarryPreferences.getInstance().starryUserinfoModel.portLimit ?: MeetingModel.memberLimitCount

    private var preClassName = ""

    private var isMaxSelectedCount = false  //是否为当前选中的最大值？

    private val selectType by lazy {
        viewModel.getSelectType() ?: StarryConstants.STARRY_SELECT_TYPE_START
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        when (event.eventType) {

            EventType.STARRY_ROOM_USER_LIST -> {
                if (selectType == StarryConstants.STARRY_SELECT_TYPE_ADD) {
                    val newUserListEvent = event as StarryUserListEvent
                    val bean = newUserListEvent.params
                    val replyBean = bean.body.reply
                    launch {
                        val userJson = replyBean.users as ArrayList<StarryRecivedUser>
                        val isCheckedMapTmp = LinkedHashMap<String, String>()
                        userJson.forEach {
                            if (it.status == UserStatus.STATUS_CALLING||
                                it.status == UserStatus.STATUS_TIMEOUT ||
                                it.status == UserStatus.STATUS_JOINED ||
                                it.status == UserStatus.STATUS_OFFLINE ||
                                it.status == UserStatus.STATUS_IOS_HOLD ||
                                it.status == UserStatus.STATUS_HOLD_ON) {
                                isCheckedMapTmp[it.meetingAccout] = it.name ?: ""
                            }
                        }
                        logI("StarryCompanyListContactsActivity.isCheckedMapTmp=${isCheckedMapTmp}")
                        contactViewModel.isDisableCheckedMap.value = isCheckedMapTmp
                        val checkedList = ArrayList<String>()
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            isCheckedMapTmp.forEach { (t, _) ->
                                checkedList.add(t)
                            }
                            mAdapter.setDisableCheckedAndClearOld(checkedList)
                        }
//                        refreshSelectTv(mAdapter.getCheckMap())
                        val checkMap = mAdapter.getCheckMap()
                        isMaxSelectedCount = checkMap.size >= selectMaxCountNumber
                        contactViewModel.tempCheckedMap.value = checkMap
                    }
                }
            }
            else -> {
            }
        }
    }

    override fun initViews() {
        super.initViews()

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }

        val accountNo = StarryPreferences.getInstance().accountNo

        // top bar
        user_back_btn?.singleClick {
            ActivityUtils.finishActivity(this)
        }
        // 取消
        msg_top_select_cancel?.singleClick {
            ActivityUtils.finishActivity(this)
        }

        // ui.starry.activity.StarryCompanyListActivity
//        preClassName = intent.getStringExtra(StarryConstants.STARRY_PRE_ACTIVITY) ?: ""
        preClassName = viewModel.starryPrePageName
        if (preClassName.isNotEmpty()) {
            msg_top_select_cancel?.visibility = View.GONE
            user_back_btn?.visibility = View.VISIBLE
        }

        // Title
        msg_top_select_title?.text =
            String.format(
                getString(R.string.starry_company_list_contacts_title_num), "1"
            )
        contacts_selected_count_tv?.text =
            String.format(
                getString(R.string.starry_company_list_contacts_selected_count),
                "1",
                selectMaxCountNumber.toString()
            )

        var list = viewModel.currentContactsList.value?.toMutableList() as ArrayList
        mAdapter.setListContacts(list)
        list = mAdapter.getListContacts()
        viewModel.currentContactsSortList.postValue(list)

        // list
        recycler_view_contacts?.apply {
            setHasFixedSize(true)
            layoutManager = linearLayoutManager
            adapter = mAdapter
        }

        mAdapter.setOnItemClickListener(object : StarryCompanyListContactAdapter.OnClickListener {
            override fun onclickSel(position: Int, checkMap: LinkedHashMap<String, String>) {
                logI(
                    "setOnItemClickListener.onclickSel.position=${position},checkMap=${checkMap.size},${checkMap}",
                    "isMaxSelectedCount=${isMaxSelectedCount},selectMaxCountNumber=${selectMaxCountNumber}"
                )
                refreshSelectTv(checkMap)

            }
        })

        // 字母索引定位
        letter_view?.initViewNew(mAdapter.getCharacterList())
        letter_view?.setCharacterListener(object : LetterViewNew.CharacterClickListener {
            override fun clickCharacter(character: String?) {
                character?.let { mAdapter.getScrollPosition(it) }?.let {
                    linearLayoutManager.scrollToPositionWithOffset(it, 0)
                }
            }

            override fun clickArrow() {
                linearLayoutManager.scrollToPositionWithOffset(0, 0)
            }
        })

        // 搜索
        contacts_search_rl?.setOnClickListener {
            val list = viewModel.currentContactsSortList.value?.toMutableList() as ArrayList
            val intent = Intent(this, StarrySearchCompanyContactActivity::class.java)
            intent.putExtra(StarryConstants.STARRY_CONTACT_LIST, list)
            intent.putExtra(StarryConstants.STARRY_USER_TYPE, StarryConstants.STARRY_USER_TYPE_COMPANY)
            ActivityUtils.startActivityForResult(this, intent, StarryConstants.RESULT_SUCCESS_CODE)
        }

        // 发起会议
        contact_new_call_btn?.setOnClickListener {
            logI("${TAG}.contact_new_call_btn")
            if (viewModel.clickNoNetwork()) {
                return@setOnClickListener
            }

            Tools.setViewButtonEnable(contact_new_call_btn, false)
            val enterpriseList = viewModel.enterpriseList.value ?: listOf()
            // 要先调佣一下getCheckedMemberInfo,
            // 在刷新getEnterpriseMembers前,先 获取选中的用户的信息
            contactViewModel.getCheckedMemberInfo(enterpriseList)
            onCheckPCEnter()

        }

        // 会议中添加成员
        contact_add_btn?.setOnClickListener {
            contactViewModel.isCheckedMap.value = contactViewModel.tempCheckedMap.value
            logI("contact_add_btn.contactViewModel.isCheckedMap.value=${contactViewModel.isCheckedMap.value}")

            val meetingAccountsList = ArrayList<String>()
            contactViewModel.isCheckedMap.value?.forEach { (t, u) ->
                if (t != accountNo) {
                    meetingAccountsList.add(t)
                }
            }
            contactViewModel.isDisableCheckedMap.value?.forEach { (dis_k, dis_v) ->
                if (meetingAccountsList.contains(dis_k)) {
                    meetingAccountsList.remove(dis_k)
                }
            }

            val meetingAccounts = Gson().toJson(meetingAccountsList)
            logI("contact_add_btn.meetingAccountsList=${meetingAccountsList}")
            logI("contact_add_btn.meetingAccounts=${meetingAccounts}")

            viewModel.inviteMemberPC(meetingAccountsList.toList())

            if (preClassName.isNotEmpty()) {
                ActivityUtils.finishActivity(StarryCompanyListActivity::class.java)
            }
            finish()
        }

        // 设置选中的项
        if (selectType == StarryConstants.STARRY_SELECT_TYPE_ADD) {
            // 设置选中的成员
            val checkedList = ArrayList<String>()
            contactViewModel.isDisableCheckedMap.value?.forEach { (t, u) ->
                checkedList.add(t)
            }
            mAdapter.setDisableChecked(checkedList)

            contact_new_call_btn?.visibility = View.GONE
            contact_add_btn?.visibility = View.VISIBLE

        } else {
            // 设置选中的自己
            val checkedList = ArrayList<String>()
            checkedList.add(accountNo)
            mAdapter.setDisableChecked(checkedList)

            contact_new_call_btn?.visibility = View.VISIBLE
            contact_add_btn?.visibility = View.GONE
        }

        // 初始化时，把原来的isCheckMap内容带入
        if (contactViewModel.isCheckedMap.value != null && contactViewModel.isCheckedMap.value?.size ?: 0 > 0) {
            // 设置原有的选择项
            val checkedListAll = ArrayList<String>()
            contactViewModel.isCheckedMap.value?.forEach { (t, u) ->
                checkedListAll.add(t)
            }
            mAdapter.setAllreadyChecked(checkedListAll)
        }

        val tempMap1 = contactViewModel.tempCheckedMap.value
        val tempMap2 = mAdapter.getCheckMap()
        val tempMap = if (tempMap1.isNullOrEmpty()) tempMap2 else tempMap1.plus(tempMap2)
        if (tempMap.isNotEmpty()) {
            contactViewModel.tempCheckedMap.value = tempMap as LinkedHashMap<String, String>?
        }

        if (BuildConfig.IS_OVERSEAS){
            contact_new_call_btn?.textSize = 14f
        }
    }

    // 同一账号如果pc端已在会议中
    private fun onCheckPCEnter(){
        if (MeetingModel.isPCEnter){
            StarryCommonPopup.Builder(this)
                .setTitle(getString(R.string.starry_popupwindow_title))
                .setMessage(getString(R.string.starry_callout_dialog_msg))
                .setPositiveTitle(getString(R.string.starry_callout_dialog_goon))
                .setNegativeTitle(getString(R.string.starry_callout_dialog_cancel))
                .setOnPositiveListener { dialog, _ ->
                    showProgressDialog()
                    viewModel.getEnterpriseMembers {
                        checkCompanyPerson()
                    }
                    dialog?.dismiss()
                }
                .setOnNegativeListener { dialog, _ ->
                    Tools.setViewButtonEnable(contact_new_call_btn, true)
                    dialog.dismiss()
                }
                .create()
                .show()
        }else{
            showProgressDialog()
            viewModel.getEnterpriseMembers{
                checkCompanyPerson()
            }
        }
    }

    /**
     * 刷新选择人数textview
     */
    fun refreshSelectTv(checkMap: LinkedHashMap<String, String>) {
        if (isMaxSelectedCount && (checkMap.size >= selectMaxCountNumber)) {
            ToastUtils.showLong(
                String.format(
                    getString(R.string.starry_company_list_contacts_selected_more),
                    (selectMaxCountNumber - 1).toString()
                )
            )
        }
        isMaxSelectedCount = checkMap.size >= selectMaxCountNumber
        contactViewModel.tempCheckedMap.value = checkMap
    }


    private fun checkCompanyPerson() {
        // 成员有效性检查contactViewModel.checkedMeetingMemberList
        val enterpriseList = viewModel.enterpriseList.value ?: listOf()
//        logI("checkCompanyPerson.enterpriseList=${enterpriseList}")
//        logI("checkCompanyPerson.checkedMeetingMemberList=${contactViewModel.checkedMeetingMemberList}")
        // 没有返回的数据,则认为是弱网环境,提示网络错误
        if (enterpriseList.isEmpty()){
            ToastUtils.showLong(R.string.starry_network_error_msg)
            Tools.setViewButtonEnable(contact_new_call_btn, true)
            hideProgressDialog()
            return
        }
        if (!contactViewModel.checkValidMembers(this, enterpriseList)){
            Tools.setViewButtonEnable(contact_new_call_btn, true)
            hideProgressDialog()
            return
        }

        // 呼叫中，不能发起会议
        if (!MeetingModel.checkIsMeetingCall()){
            Tools.setViewButtonEnable(contact_new_call_btn, true)
            hideProgressDialog()
            return
        }

        logI("checkCompanyPerson.contactViewModel.checkedMeetingMemberList=${contactViewModel.checkedMeetingMemberList}")
        val jsonParam = viewModel.startNewMeeting(contactViewModel.checkedMeetingMemberList)
        logI("checkCompanyPerson.jsonParam=${jsonParam}")

        val i = Intent(this@StarryCompanyListContactsActivity, MeetingMainActivity::class.java)
        i.putExtra(MeetingMainActivity.KEY_BOOT_DATA, jsonParam)
        i.putExtra(MeetingMainActivity.KEY_BOOT_TYPE, MeetingMainActivity.BOOT_TYPE_START)
        startActivity(i)
        contactViewModel.checkedMeetingMemberList.clear()

        if (preClassName.isNotEmpty()) {
            ActivityUtils.finishActivity(StarryCompanyListActivity::class.java)
        }
        finish()
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        contactViewModel.isDisableCheckedMap.observe(this) {
            // 设置选中的项
            if (selectType == StarryConstants.STARRY_SELECT_TYPE_ADD) {
                // 设置选中的成员
                val checkedList = ArrayList<String>()
                contactViewModel.isDisableCheckedMap.value?.forEach { (t, u) ->
                    checkedList.add(t)
                }
                mAdapter.setDisableChecked(checkedList)
                logI("isDisableCheckedMap.observe.checkedList=${checkedList}")
            }
            logI("isDisableCheckedMap.observe.checkedList=${it}")
        }

        viewModel.currentContactsList.observe(this) {
            var list = it?.toMutableList() as ArrayList
            mAdapter.setListContacts(list)
            list = mAdapter.getListContacts()
            viewModel.currentContactsSortList.postValue(list)
        }

        contactViewModel.tempCheckedMap.observe(this) {
            val checkCount = it.size ?: 1
            Log.i("Jason", "contactViewModel.tempCheckedMap=${checkCount},${contactViewModel.tempCheckedMap.value}")
            if (checkCount >= selectMaxCountNumber) {
                mAdapter.setSelectedMore(false)
            } else {
                mAdapter.setSelectedMore(true)
            }

            msg_top_select_title?.text =
                String.format(
                    getString(R.string.starry_company_list_contacts_title_num),
                    checkCount.toString()
                )
            contacts_selected_count_tv?.text =
                String.format(
                    getString(R.string.starry_company_list_contacts_selected_count),
                    checkCount.toString(),
                    selectMaxCountNumber.toString()
                )

            if (selectType == StarryConstants.STARRY_SELECT_TYPE_ADD) {
                if (it.size > contactViewModel.isDisableCheckedMap.value?.size ?: 0) {
                    Tools.setViewButtonEnable(contact_add_btn, true)
                } else {
                    Tools.setViewButtonEnable(contact_add_btn, false)
                }
            } else {
                Tools.setViewButtonEnable(contact_new_call_btn, true)

            }

            // 初始化时，把原来的isCheckMap内容带入
            if (contactViewModel.tempCheckedMap.value != null && contactViewModel.tempCheckedMap.value?.size ?: 0 > 0) {
                // 设置原有的选择项
                val checkedListAll = ArrayList<String>()
                contactViewModel.tempCheckedMap.value?.forEach { (t, u) ->
                    checkedListAll.add(t)
                }
                mAdapter.setAllreadyChecked(checkedListAll)
            }
        }

    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == StarryConstants.RESULT_SUCCESS_CODE && resultCode == RESULT_OK) {
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }
    }

}