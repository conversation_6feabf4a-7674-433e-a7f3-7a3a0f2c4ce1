package com.czur.cloud.event;

public class RenameDeviceEvent extends BaseEvent {
    private String deviceName;

    public RenameDeviceEvent(EventType eventType, String deviceName) {
        super(eventType);
        this.deviceName = deviceName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    @Override
    public boolean match(Object obj) {
        return true;
    }
}
