package com.czur.cloud.model;

/**
 * Created by <PERSON> on 2020/11/23.
 */
public class AuraMateReportModelSub extends AuraMateReportModel {

//    private String title; //标题
//    private int type; ////类型 1为日报 2为周报 3为月报
    private boolean published; ////是否发布
    private String fromEnd; //"2020/11/16-2020/11/22",//起始结束时间
    private String localeTime; //"2020-11-21 15:30:00",//本地时间
    private int timezone;  //"":8,//时区
    private int dayUsingDuration;  //"":277, //日平均使用时间
    private String rank;//排名
    private String rankNo;//排名
    private String trend; //"持平",//趋势
    private int dayRemindCount; //41,//日平均提醒次数
    private int totalRemindCount; //41,//总提醒次数
    private String level; //"优良",
    private String relationId; //"relationId",

//    private int rightDuration;//正确使用时长
//    private int seriousErrorDuration;//严重使用时长
//    private int mildErrorDuration;//中度使用时长
//    private int moderateErrorDuration;//轻度使用时长
//    private String rightProportion;//正确占比
//    private String seriousProportion;//严重占比
//    private String mildProportion;//中度占比
//    private String moderateProportion;//轻度占比


//    public void setTitle(String title) {
//        this.title = title;
//    }
//
//    public void setType(int type) {
//        this.type = type;
//    }


    public String getRelationId() {        return relationId;    }

    public void setRelationId(String relationId) {        this.relationId = relationId;    }

    public String getLevel() {        return level;    }

    public void setLevel(String level) {        this.level = level;    }

    public void setPublished(boolean published) {
        this.published = published;
    }

    public void setFromEnd(String fromEnd) {
        this.fromEnd = fromEnd;
    }

    public void setLocaleTime(String localeTime) {
        this.localeTime = localeTime;
    }

    public void setTimezone(int timezone) {
        this.timezone = timezone;
    }

    public void setDayUsingDuration(int dayUsingDuration) {
        this.dayUsingDuration = dayUsingDuration;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }

    public void setRankNo(String rankNo) {
        this.rankNo = rankNo;
    }

    public void setTrend(String trend) {
        this.trend = trend;
    }

    public void setDayRemindCount(int dayRemindCount) {
        this.dayRemindCount = dayRemindCount;
    }

    public void setTotalRemindCount(int totalRemindCount) {
        this.totalRemindCount = totalRemindCount;
    }

//    public String getTitle() {
//        return title;
//    }
//
//    public int getType() {
//        return type;
//    }

    public boolean isPublished() {
        return published;
    }

    public String getFromEnd() {
        return fromEnd;
    }

    public String getLocaleTime() {
        return localeTime;
    }

    public int getTimezone() {
        return timezone;
    }

    public int getDayUsingDuration() {
        return dayUsingDuration;
    }

    public String getRank() {
        return rank;
    }

    public String getRankNo() {
        return rankNo;
    }

    public String getTrend() {
        return trend;
    }

    public int getDayRemindCount() {
        return dayRemindCount;
    }

    public int getTotalRemindCount() {
        return totalRemindCount;
    }
}
