package com.czur.cloud.adapter;

import android.content.Intent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.czur.cloud.R;
import com.czur.cloud.entity.realm.EtWifiHistoryEntity;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.et.wifi.GenerateMp3Service;
import com.czur.cloud.ui.et.wifi.WifiConnectResetActivity;

import java.util.List;

public class EtHistoryNetAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private List<EtWifiHistoryEntity> wifiHistoryList;
    private BaseActivity activity;
    private String deviceId;

    public EtHistoryNetAdapter(List<EtWifiHistoryEntity> wifiHistoryList, BaseActivity activity, String deviceId) {
        this.wifiHistoryList = wifiHistoryList;
        this.activity = activity;
        this.deviceId = deviceId;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(activity.getLayoutInflater().inflate(R.layout.item_history_wifi, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        EtWifiHistoryEntity entity = wifiHistoryList.get(position);
        ViewHolder viewHolder = (ViewHolder) holder;
        viewHolder.tvSsid.setText(entity.getSsid());
        viewHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(activity, GenerateMp3Service.class);
                intent.putExtra("ssid", entity.getSsid());
                intent.putExtra("password", entity.getPassword());
                intent.putExtra("deviceId", deviceId);
                activity.startService(intent);

                Intent intent2 = new Intent(activity, WifiConnectResetActivity.class);
                intent2.putExtra("ssid", entity.getSsid());
                intent2.putExtra("password", entity.getPassword());
                intent2.putExtra("deviceId", deviceId);
                activity.startActivity(intent2);

            }
        });
    }

    @Override
    public int getItemCount() {
        return Math.min(wifiHistoryList.size(), 3);
    }

    private static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvSsid;

        ViewHolder(View itemView) {
            super(itemView);
            tvSsid = (TextView) itemView.findViewById(R.id.tv_ssid);
        }
    }

}

