package com.czur.cloud.ui.auramate.siterror;

import android.app.Activity;
import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.cache.AuraCustomImageRequest;
import com.czur.cloud.model.AuraErrorSitPictureModel;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.request.ImageRequestBuilder;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by <PERSON> on 20210118
 */

public class AuraErrorSitAdapter extends RecyclerView.Adapter<ViewHolder> {

    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<AuraErrorSitPictureModel> datas;
    //是否进入选择
    private boolean isSelectItem;
    private LayoutInflater mInflater;
    private LinkedHashMap<String, String> isCheckedMap = new LinkedHashMap<>();

    /**
     * 构造方法
     */
    public AuraErrorSitAdapter(Activity activity, List<AuraErrorSitPictureModel> datas, boolean isSelectItem) {
        this.mActivity = activity;
        this.isSelectItem = isSelectItem;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(boolean isSelectItem) {
        this.isSelectItem = isSelectItem;
        notifyDataSetChanged();
    }

    public void refreshData(List<AuraErrorSitPictureModel> datas, boolean isSelectItem, LinkedHashMap<String, String> isCheckedMap) {
        this.datas = datas;
        this.isSelectItem = isSelectItem;
        this.isCheckedMap = isCheckedMap;
        notifyDataSetChanged();
    }

    public void refreshData(List<AuraErrorSitPictureModel> datas) {
        this.datas = datas;
        notifyDataSetChanged();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new EtFilesHolder(mInflater.inflate(R.layout.item_aura_errorsit , parent, false));
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {
        if (holder instanceof EtFilesHolder) {
            final EtFilesHolder mHolder = (EtFilesHolder) holder;
            mHolder.mItem = datas.get(position);
            Uri lowResUri = Uri.parse(getSmallUrl(mHolder.mItem));
            AuraCustomImageRequest imageRequest = new AuraCustomImageRequest(ImageRequestBuilder.newBuilderWithSource(lowResUri));
            imageRequest.setImageRequestType(1);
            imageRequest.setImageRequestObject("small");
            DraweeController controller = Fresco.newDraweeControllerBuilder()
                    .setImageRequest(imageRequest)
                    .setOldController(mHolder.etFilesImg.getController())
                    .build();
            mHolder.etFilesImg.setController(controller);
            if (isSelectItem) {
                mHolder.checkBox.setVisibility(View.VISIBLE);
                mHolder.checkBox.setTag(mHolder.mItem.getId());
            } else {
                mHolder.checkBox.setVisibility(View.GONE);
            }
            mHolder.checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        if (!isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //选中时添加
                            isCheckedMap.put(mHolder.mItem.getId(), getOssKey(mHolder.mItem));
                        }
                    } else {
                        if (isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //没选中时移除
                            isCheckedMap.remove(mHolder.mItem.getId());
                        }
                    }
                    if (onItemCheckListener != null) {
                        onItemCheckListener.onItemCheck(position, mHolder.mItem, isCheckedMap, datas.size());
                    }
                }
            });
            if (isCheckedMap != null) {
                mHolder.checkBox.setChecked(isCheckedMap.containsKey(mHolder.mItem.getId()));
            } else {
                mHolder.checkBox.setChecked(false);
            }
            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onEtFilesClickListener != null) {
                        onEtFilesClickListener.onEtFilesClick(mHolder.mItem, position, mHolder.checkBox);
                    }
                }
            });
        }
    }

    private String getSmallUrl(AuraErrorSitPictureModel item) {
        return item.getSmallErrorImgUrl();
    }

    private String getOssKey(AuraErrorSitPictureModel item) {
        return item.getErrorImgOSSKey();
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }

    private static class EtFilesHolder extends ViewHolder {
        public final View mView;
        AuraErrorSitPictureModel mItem;
        SimpleDraweeView etFilesImg;
        CheckBox checkBox;

        EtFilesHolder(View itemView) {
            super(itemView);
            mView = itemView;
            etFilesImg = (SimpleDraweeView) itemView.findViewById(R.id.et_files_img);
            checkBox = (CheckBox) itemView.findViewById(R.id.check);
            ViewGroup.LayoutParams layoutParams = itemView.getLayoutParams();
            layoutParams.width = (ScreenUtils.getScreenWidth() - SizeUtils.dp2px(16)) / 3;
            layoutParams.height = (int) (layoutParams.width * 1.33f);
            itemView.setLayoutParams(layoutParams);
        }
    }

    public int getTotalSize() {
        return datas.size();
    }

    public View inflate(Context context, int layoutId) {
        if (layoutId <= 0) {
            return null;
        }
        return LayoutInflater.from(context).inflate(layoutId, null);
    }

    private OnEtFilesClickListener onEtFilesClickListener;

    public void setOnEtFilesClickListener(OnEtFilesClickListener onEtFilesClickListener) {
        this.onEtFilesClickListener = onEtFilesClickListener;
    }

    public interface OnEtFilesClickListener {
        void onEtFilesClick(AuraErrorSitPictureModel filesBean, int position, CheckBox checkBox);
    }

    private OnItemCheckListener onItemCheckListener;

    public void setOnItemCheckListener(OnItemCheckListener onItemCheckListener) {
        this.onItemCheckListener = onItemCheckListener;
    }

    public interface OnItemCheckListener {
        void onItemCheck(int position, AuraErrorSitPictureModel filesBean, LinkedHashMap<String, String> isCheckedMap, int totalSize);
    }

}
