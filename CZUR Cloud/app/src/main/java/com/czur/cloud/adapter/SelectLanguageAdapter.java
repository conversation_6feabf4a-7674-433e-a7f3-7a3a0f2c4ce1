package com.czur.cloud.adapter;

/**
 * Created by czur_app001 on 2018/1/19.
 * Email：<EMAIL>
 * (ง •̀_•́)ง
 */

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.czur.cloud.R;
import com.czur.cloud.model.LanguageModel;
import com.czur.cloud.preferences.UserPreferences;

import java.util.List;


public class SelectLanguageAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private static final int TYPE_NORMAL = 0;
    private static final int TYPE_ADD_EQUIPMENT = 1;
    private List<LanguageModel> datas;
    private Context context;
    private int lastPosition;
    private final UserPreferences userPreferences;


    public SelectLanguageAdapter(Context context, List<LanguageModel> datas, int lastPosition) {
        this.datas = datas;
        this.context = context;
        this.lastPosition = lastPosition;
        userPreferences = UserPreferences.getInstance(context);
    }

    public void setData(List<LanguageModel> datas) {
        this.datas = datas;
    }

    public void refreshData(List<LanguageModel> datas) {
        this.datas = datas;
        notifyDataSetChanged();
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {

        if (viewType == TYPE_NORMAL) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_select_language, parent, false);
            return new NormalViewHolder(view);
        } else {
            return null;
        }
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, final int position) {

        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);
            mHolder.selectTv.setText(mHolder.mItem.getName());
            if (mHolder.mItem.isSelected()) {
                mHolder.selectTv.setTextColor(context.getResources().getColor(R.color.blue_29b0d7));
                mHolder.selectImg.setVisibility(View.VISIBLE);
            } else {
                mHolder.selectTv.setTextColor(context.getResources().getColor(R.color.normal_blue));
                mHolder.selectImg.setVisibility(View.GONE);
            }

            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    datas.get(lastPosition).setSelected(false);
                    datas.get(position).setSelected(true);
                    lastPosition=position;
                    notifyDataSetChanged();
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(position, mHolder.mItem);
                    }
                }
            });
        }


    }

    @Override
    public int getItemViewType(int position) {
        return TYPE_NORMAL;
    }

    @Override
    public int getItemCount() {
        return datas.size();

    }


    public class NormalViewHolder extends RecyclerView.ViewHolder {
        public final View mView;
        LinearLayout itemView;
        TextView selectTv;
        ImageView selectImg;
        LanguageModel mItem;


        public NormalViewHolder(View view) {
            super(view);
            mView = view;
            itemView = (LinearLayout) view.findViewById(R.id.language_rl);
            selectTv = (TextView) view.findViewById(R.id.chinese_tv);
            selectImg = (ImageView) view.findViewById(R.id.select_img);
        }

    }


    public OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(int position, LanguageModel LanguageModel);

    }
}