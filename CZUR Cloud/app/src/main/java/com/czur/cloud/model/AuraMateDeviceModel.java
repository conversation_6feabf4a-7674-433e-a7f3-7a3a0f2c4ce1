package com.czur.cloud.model;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

public class AuraMateDeviceModel extends RealmObject {
    /**
     * id : 7
     * equipmentUid : 2222222222
     * ownerId : 2855
     * memberId : 87
     * deviceName : Aura Mate001
     * createOn : 1561712528000
     */
    @PrimaryKey
    private String releationId;
    private int id;
    private String equipmentUid;
    private int ownerId;
    private int memberId;
    private String deviceName;
    private long createOn;
    private boolean isSelect;
    private String lastFileTimestamp;

    public String getLastFileTimestamp() {
        return lastFileTimestamp;
    }

    public void setLastFileTimestamp(String lastFileTimestamp) {
        this.lastFileTimestamp = lastFileTimestamp;
    }

    public boolean isSelect() {
        return isSelect;
    }

    public void setSelect(boolean select) {
        isSelect = select;
    }


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getEquipmentUid() {
        return equipmentUid;
    }

    public void setEquipmentUid(String equipmentUid) {
        this.equipmentUid = equipmentUid;
    }

    public int getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(int ownerId) {
        this.ownerId = ownerId;
    }

    public int getMemberId() {
        return memberId;
    }

    public void setMemberId(int memberId) {
        this.memberId = memberId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public long getCreateOn() {
        return createOn;
    }

    public void setCreateOn(long createOn) {
        this.createOn = createOn;
    }

    public String getReleationId() {
        return releationId;
    }

    public void setReleationId(String releationId) {
        this.releationId = releationId;
    }

}
