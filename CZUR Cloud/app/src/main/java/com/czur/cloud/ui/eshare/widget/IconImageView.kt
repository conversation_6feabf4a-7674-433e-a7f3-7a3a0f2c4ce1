package com.czur.cloud.ui.eshare.widget;

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.czur.cloud.R
import com.czur.cloud.ui.eshare.myentity.FileBrowserEntity

class IconImageView(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    AppCompatImageView(context, attrs, defStyleAttr) {

    constructor(context: Context) : this(context, null)
    constructor(context: Context,attrs: AttributeSet? = null) : this(context, attrs,0)

    init {
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        setImageResource(getIconRes(FileBrowserEntity.FileEntity(fileType = "LOAD_INIT")))
        setBackgroundColor(0)
    }

    private fun getIconRes(
        fileEntity: FileBrowserEntity.FileEntity,
    ): Int {
        return when (fileEntity.fileType) {
            "ROOT" -> getRootIcon(fileEntity.belongTo)
            "EXCEL" -> R.mipmap.file_icon_excel
            "DOC" -> R.mipmap.file_icon_word
            "PDF" -> R.mipmap.file_icon_pdf
            "IMAGE" -> R.mipmap.file_icon_pic
            "FOLDER" -> R.mipmap.file_icon_folder
            "PPT" -> R.mipmap.file_icon_ppt
            "OTHER" -> R.mipmap.file_icon_other
            "AUDIO" -> R.mipmap.file_icon_audio
            "VIDEO" -> R.mipmap.file_icon_video
            "DOCUMENT" -> R.mipmap.file_icon_txt
            "ZIP" -> R.mipmap.file_icon_zip
            "APK" -> R.mipmap.file_icon_apk
            "LOAD_INIT" -> R.mipmap.file_icon_other //改用other一样图
            "MEET_RECORD" -> R.mipmap.file_icon_audio // TODO 先用音频文件的图标
            else -> {
                R.mipmap.file_icon_other
            }
        }
    }

    /**
     * 手动设置的
     */
    private fun getRootIcon(belongTo: String?): Int {
        return when (belongTo) {
            "Root-Local" -> {
                R.mipmap.icon_root_local
            }

            "Root-Download" -> {
                R.mipmap.icon_root_download
            }

            "Root-Share" -> {
                R.mipmap.icon_root_share
            }

            "Root-Meeting" -> {
                R.mipmap.ic_metting_record_files
            }

            "Root-Picture" -> {
                R.mipmap.ic_photo_files
            }

            else -> {
                R.mipmap.ic_other_files
            }
//            "Root-Other" -> {
//                R.mipmap.icon_root_local
//            }
        }
    }

    fun changeIconByEntity(fileEntity: FileBrowserEntity.FileEntity) {
        setImageResource(getIconRes(fileEntity))
    }
}