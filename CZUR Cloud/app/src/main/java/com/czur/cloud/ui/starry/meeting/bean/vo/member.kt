package com.czur.cloud.ui.starry.meeting.bean.vo

import com.czur.cloud.ui.starry.meeting.bean.User
import com.czur.cloud.ui.starry.meeting.bean.UserStatus
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_JOINED
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_OFFLINE
import com.czur.cloud.ui.starry.meeting.bean.UserStreamStatus
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import com.czur.cloud.ui.starry.meeting.model.ModelManager

/**
 * Created by 陈丰尧 on 4/29/21
 */

// 在会议中,一个成员只取查一次他的昵称
//private val nickNameMap = hashMapOf<String, NickName>()

/**
 * 成员的基本数据
 */
data class MemberInfo(
    val nickName: String,
    val headImg: String?
)
/**
 * 成员列表数据
 * 数据来源是长连接 和 联系人
 */
data class Member(
    val czurID: String,     // 用来与声网数据关联使用
    var nickName: String,   // 昵称
    val accountId: String,   // 会议ID 用来与联系人数据匹配
    val isStranger: Boolean, // 是否是陌生人
    val headImg: String?, // 头像Url
    val isAdmin: Boolean, // 是否是管理员
    val status: Int,         // 成员状态    // 0: 呼叫中，1:未接听超时, 2:加入, 3: 拒接, 4: 被移除, 5:离开
    var sharing: Boolean,// 是否正在共享
    var shareStream: String?,// 共享的通道ID
    var audioStatus: Int,    // 音频状态
    var videoStatus: Int,    // 视频状态
) {

    /**
     * 用户是否已经加入到会议中
     */
    val isInMeeting: Boolean
        get() = (status == STATUS_JOINED || status == STATUS_OFFLINE || status == UserStatus.STATUS_IOS_HOLD)


    /**
     * 该用户是否是自己
     */
    val isSelf: Boolean = czurID == UserHandler.czurId.toString()

    /**
     * 视频流正在被使用
     */
    val isVideoInUse: Boolean = videoStatus == UserStreamStatus.STREAM_IN_USE

    /**
     * 音频流正在被使用
     */
    val isAudioInUse: Boolean = audioStatus == UserStreamStatus.STREAM_IN_USE


    companion object {
        /**
         * 根据长连接数据创建User
         */
        fun create(user: User): Member {
            // 获取该用户的昵称
//            val nickName = nickNameMap.getOrPut(user.accountID.toString()) {
//                val name = MeetingModel.getNickName(user.accountID.toString(), user.name)
////                logI("Member.create.name=${name}")
//                NickName(user.accountID.toString(), name, false)
//            }

//            val name = MeetingModel.getNickName(user.accountID.toString(), user.name)
//            val nickName = NickName(user.accountID.toString(), user.name, false)

            var nickName = user.name
            val addressList = ModelManager.membersModel.addressBookList.value
            if (addressList != null) {
                for (contact in addressList){
                    if (contact.meetingNo == user.accountID){
//                        logI("Member.for.contact=${contact}")
                        nickName = contact.remark ?: user.name
                        break
                    }
                }
            }

            return Member(
                    czurID = user.czurId.toString(),
                    nickName = nickName,
                    accountId = user.accountID.toString(),
                    isStranger = false,
                    headImg = user.headImage,
                    isAdmin = user.isAdmin,
                    status = user.status,
                    sharing = user.sharing,
                    shareStream = user.shareStream,
                    audioStatus = user.audioStatus,
                    videoStatus = user.videoStatus

            )
        }
    }


}

/**
 * 联系人选择页面的数据类
 */
data class ChooseContactMember(
        val accountId: String,      // 会议ID
        val name: String,           // 联系人昵称
        val pinyin: String,          // 拼音
        val headImg: String?,         // 头像
        val isDevices: Boolean,      // 是否是投影端
        var joined: Boolean = false, // 是否已经加入
        var choose: Boolean = false // 是否已经选中
)
