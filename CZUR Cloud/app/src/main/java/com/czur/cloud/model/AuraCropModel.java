package com.czur.cloud.model;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/9/9.
 */
public class AuraCropModel implements Serializable {


        /**
         * fileId : rkttzcrozkggwat
         * ossKey : test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut714870.jpg
         * ossKeyUrl : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut714870.jpg?Expires=1538125588&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=APbVveQfEJpzQKSF0xSXS5uNTRE%3D
         * ossMiddleKeyUrl : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut714870.jpg?Expires=1538125588&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=x5aQSCb%2Bp2mokT9T8XRDrrmvKmM%3D&x-oss-process=image%2Fresize%2Cm_fixed%2Cw_1080%2Ch_1080
         * ossSmallKeyUrl : https://czur-aura.oss-cn-beijing.aliyuncs.com/test/9/qaz-147-wsx-258/2018-09-21/process/94fa4117-ee91-4b01-a5c7-fa6b45d950d9_cut714870.jpg?Expires=1538125588&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=ZEDx4LLGlni9KJFUrEQkqU7b2S8%3D&x-oss-process=image%2Fresize%2Cm_fixed%2Cw_150%2Ch_150
         * fileSize : 1019
         */

        private String fileId;
        private String ossKey;
        private String ossKeyUrl;
        private String ossMiddleKeyUrl;
        private String ossSmallKeyUrl;
        private String fileSize;

        public String getFileId() {
            return fileId;
        }

        public void setFileId(String fileId) {
            this.fileId = fileId;
        }

        public String getOssKey() {
            return ossKey;
        }

        public void setOssKey(String ossKey) {
            this.ossKey = ossKey;
        }

        public String getOssKeyUrl() {
            return ossKeyUrl;
        }

        public void setOssKeyUrl(String ossKeyUrl) {
            this.ossKeyUrl = ossKeyUrl;
        }

        public String getOssMiddleKeyUrl() {
            return ossMiddleKeyUrl;
        }

        public void setOssMiddleKeyUrl(String ossMiddleKeyUrl) {
            this.ossMiddleKeyUrl = ossMiddleKeyUrl;
        }

        public String getOssSmallKeyUrl() {
            return ossSmallKeyUrl;
        }

        public void setOssSmallKeyUrl(String ossSmallKeyUrl) {
            this.ossSmallKeyUrl = ossSmallKeyUrl;
        }

        public String getFileSize() {
            return fileSize;
        }

        public void setFileSize(String fileSize) {
            this.fileSize = fileSize;
        }

}
