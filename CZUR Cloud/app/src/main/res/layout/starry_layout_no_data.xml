<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/starry_no_data_cl"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    >

    <ImageView
        android:id="@+id/nodata_iv"
        android:layout_width="@dimen/starryMargin70"
        android:layout_height="@dimen/starryMargin100"
        android:src="@mipmap/starry_msg_nodata"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginBottom="@dimen/starryMargin100"
        />

    <TextView
        android:id="@+id/nodata_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/nodata_iv"
        android:gravity="center"
        android:text="@string/starry_msg_no_data"
        android:textColor="@color/gray_93"
        android:textSize="@dimen/starryFontSize18"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/nodata_iv" />

</androidx.constraintlayout.widget.ConstraintLayout>