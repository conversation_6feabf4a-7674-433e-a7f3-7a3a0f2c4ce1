<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <permission
        android:name="com.czur.cloud.permission.JPUSH_MESSAGE"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.czur.cloud.permission.JPUSH_MESSAGE" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS"
        tools:ignore="ProtectedPermissions" />

    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />

    <!--允许获取网络信息状态 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!--允许访问Wi-Fi网络状态信息 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!--允许改变Wi-Fi连接状态 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <!--允许改变网络连接状态-->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!--相机 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <!--8.0安装-->
    <!--  TODO OVERSEAS 海外注掉  -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <!-- Optional for location -->
    <!-- 在屏幕最顶部显示addview-->
    <uses-permission android:name="android.permissionmission.SYSTEM_OVERLAY_WINDOW" />
    <uses-permission android:name="com.czur.cloud.permission.permission.JPUSH_MESSAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> <!-- 用于开启 debug 版本的应用在6.0 系统上 层叠窗口权限 -->
    <!--    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />&lt;!&ndash; Android Q后台定位权限&ndash;&gt;-->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />

    <!-- Android 12以下才需要定位权限， Android 9以下官方建议申请ACCESS_COARSE_LOCATION -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <!-- Android 12在不申请定位权限时，必须加上android:usesPermissionFlags="neverForLocation"，否则搜不到设备 -->
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="s" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <!-- 厂商自启动权限 -->
    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
    <uses-permission android:name="com.huawei.permission.external_app_settings.USE_COMPONENT" />

    <!--  发送全屏通知-->
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />

    <!--  Android 13 通知权限的调整  -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

<!--    <permission-group android:name="com.czur.cloud.andpermission" />-->

    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <uses-permission android:name="android.permission.READ_SECURE_SETTINGS" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.front"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.front.autofocus"
        android:required="false" />

    <application
        android:name="com.czur.cloud.ui.base.CzurCloudApplication"
        android:allowBackup="true"
        android:extractNativeLibs="true"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_czur_logo"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:preserveLegacyExternalStorage="true"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_czur_logo_round"
        android:testOnly="false"
        android:theme="@style/CZURAppTheme"
        android:usesCleartextTraffic="true"
        tools:ignore="GoogleAppIndexingWarning"
        tools:replace="android:icon, android:label, android:name">
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${appName}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"
                tools:replace="android:resource" />
        </provider>

        <meta-data
            android:name="UMENG_CHANNEL"
            android:value="${UMENG_CHANNEL_VALUE}" />
        <meta-data
            android:name="android.notch_support"
            android:value="true" />
        <meta-data
            android:name="android.max_aspect"
            android:value="2.1" />

        <activity
            android:name="com.czur.cloud.ui.home.WelcomeActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!--引入shortcuts资源-->
            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/shortcuts" />

        </activity>

        <activity
            android:name="com.czur.cloud.ui.home.AddEquipmentActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.home.IndexActivity"
            android:configChanges="locale|layoutDirection|keyboard|keyboardHidden|navigation"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait">

            <!--            <intent-filter>-->
            <!--                <action android:name="android.intent.action.VIEW" />-->
            <!--                <category android:name="android.intent.category.DEFAULT" />-->
            <!--                <category android:name="android.intent.category.BROWSABLE" />-->

            <!--                <data-->
            <!--                    android:scheme="starry"-->
            <!--                    android:port="9007"-->
            <!--                    android:host="com.czur.cloud"/>-->
            <!--            </intent-filter>-->

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <data
                    android:host="com.czur.cloud"
                    android:path="/deeplink"
                    android:scheme="vpushscheme" />
            </intent-filter>


            <intent-filter>
                <action android:name="com.czur.cloud.ui.home.IndexActivity" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <action android:name="CZUR_AURA_MATE_FCM_MESG" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.czur.cloud.ui.starry.livedatabus.BlankActivityForNotification"
            android:configChanges="locale|layoutDirection|keyboard|keyboardHidden|navigation"
            android:exported="true"
            android:launchMode="singleTask">

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="com.czur.cloud"
                    android:port="9007"
                    android:scheme="starry" />
            </intent-filter>

            <intent-filter>
                <action android:name="com.czur.cloud.ui.starry.livedatabus.BlankActivityForNotification" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

        </activity>

        <activity
            android:name="com.czur.cloud.ui.account.LoginActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTask"
            android:windowSoftInputMode="adjustResize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.account.ThirdPartyBindActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.account.ThirdPartyRegisterActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.account.RegisterActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.account.ForgetPasswordActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.account.ResetPasswordActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.home.AddEquipmentFailedActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.aura.AuraRemoteActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.aura.AuraInfoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"

            />
        <activity
            android:name="com.czur.cloud.ui.aura.AuraMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.user.AboutActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.user.UserChangeUserNameActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.user.UserChangePhoneActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.user.UserFeedbackActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.user.UserChangePasswordActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.user.UserBindEmailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.user.UserBindPhoneActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.user.UserChangeEmailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.album.SelectAlbumPhotoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.album.ImageCropActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.album.ImageGridActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.user.UserPrivacyActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.user.UserRemoveAccountActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.user.UserRemoveAccountTwoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />


        <activity
            android:name="com.czur.cloud.ui.books.BookShelfActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.books.BookMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.books.BookPagePreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.books.SelectLanguageActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.books.AddBookActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.czur.cloud.ui.camera.gallery.ImagePreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.books.BookPdfActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.books.PdfPreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.books.HandwritingCountActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.czur.cloud.ui.books.HandwritingGuideActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.czur.cloud.ui.books.HandwritingRecognitionActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.books.HandwritingResultActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />


        <activity
            android:name="com.czur.cloud.ui.books.BookPageActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.books.EditTagActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.camera.CameraActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.market.WebViewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />


        <service
            android:name="com.czur.cloud.ui.user.download.DownloadAFileService"
            android:enabled="true"
            android:exported="false" />

        <activity
            android:name="com.czur.cloud.ui.user.download.AndroidOPermissionActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <service android:name="com.czur.cloud.ui.books.sync.SyncService"
            android:foregroundServiceType="connectedDevice"
            />
        <service android:name="com.czur.cloud.ui.books.sync.AutoSyncTimeCountService"
            android:foregroundServiceType="connectedDevice"
            />

        <activity
            android:name="com.czur.cloud.ui.et.EtManageActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.et.EtFilesActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.ScanActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.zxing.android.CaptureActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.EtMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.EtUserManageActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.EtTransferActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.EtAddUserActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.EtDeleteShareUserActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.et.wifi.EtWifiHistoryActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.et.wifi.EtWifiListActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.wifi.WifiConnectSuccessActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.wifi.WifiRetryActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.wifi.WifiCheckActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.et.wifi.WifiConnectErrorActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.wifi.WifiConnectResetActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.wifi.WifiConnectActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.wifi.WifiConnectingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.BindDeviceFailedActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.BindDeviceSuccessActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.BindDeviceActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.EtMoveActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.et.EtPdfActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.et.EtPdfPreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.EtPreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.et.EtCropImageActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.et.EtOcrActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.OcrResultActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.et.EtChangeDeviceNameActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />


        <service android:name="com.czur.cloud.ui.et.wifi.GenerateMp3Service"
            android:exported="true"/>

        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.auramate.SittingPositionMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateSettingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateConnectSuccessActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateWifiActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateWifiConnectActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateWifiQRcodeActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateRecordActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMatePreRemoteVideoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:excludeFromRecents="true"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait"
            android:taskAffinity="" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateRemoteVideoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape" />

        <service
            android:name="com.czur.cloud.netty.observer.NettyService"
            android:foregroundServiceType="connectedDevice"
            android:priority="1000" />
        <service android:name="com.czur.cloud.netty.observer.CheckTimeOutService"
            android:foregroundServiceType="connectedDevice"/>

        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateMoveActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateFilesActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMatePreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraCropImageActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateOcrActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMatePdfActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateReportActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateReportNewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateAddUserActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.EditQuestionTagActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateAddWrongTagActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateMissedCallActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateSelectWrongQuestionActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.AuraMateWrongQuestionActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTop"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.WrongQuestionPreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.auramate.ChangeLanguageActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.auramate.AuraMateSittingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.auramate.AuraMateLightActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.auramate.AuraMateWifiHistoryActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.auramate.AuraMateSearchWifiActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.auramate.AuraMateConnectFailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name=".ui.auramate.AuraMateUpdateActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ui.et.EtPicsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.auramate.reportfragment.ShowMoreDialog"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            tools:ignore="Instantiatable" />

        <!-- Sitting Position-->
        <activity
            android:name="com.czur.cloud.ui.mirror.SmartSittingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingReportActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingLongReportActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingHappyReportActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingVolumeActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingSensitivityActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingLongSitActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingDeviceNameActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingConnectActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingHomeShareActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingStandarActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingConnectResultActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingLightActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.happytime.SittingSitPreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.happytime.SittingHappyTimePicActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingModelActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingUpdateActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingStandarNoteActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingLongSitValueActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.mirror.SittingWebViewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />


        <activity
            android:name="com.czur.cloud.ui.auramate.reportfragment.AuraMateHDViewAlbumActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape" />

        <activity
            android:name="com.czur.cloud.ui.auramate.siterror.AuraMateErrorSitActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.auramate.siterror.AuraMateSitPreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/ErrorSitPicTheme" />

        <activity
            android:name="com.czur.cloud.ui.auramate.siterror.AuraMateStandarSitPicActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarryActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|navigation"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarryMenuActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|navigation"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarryWebViewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarryContactsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarryContactAddToActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarryMessageActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarryCompanyDetailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarryContactDetailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarrySearchActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarryCallInActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:excludeFromRecents="true"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait"
            android:taskAffinity="" />

        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarryMeetingDetailActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarryContactAddActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />

        <!--        <activity-->
        <!--            android:name="com.czur.cloud.ui.starry.meeting.MeetingMainActivity"-->
        <!--            android:configChanges="orientation|keyboardHidden|screenSize"-->
        <!--            android:launchMode="singleTask"-->
        <!--            android:screenOrientation="portrait"-->
        <!--            android:screenOrientation="fullSensor"-->
        <!--            android:hardwareAccelerated="false"-->
        <!--            />-->
        <activity
            android:name="com.czur.cloud.ui.starry.meeting.MeetingMainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:hardwareAccelerated="false"
            android:launchMode="standard"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />


        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarryCompanyListActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarryCompanyListContactsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.starry.activity.StarrySearchCompanyContactActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />


        <activity
            android:name="com.czur.cloud.ui.eshare.EShareActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.eshare.FindDeviceActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />

        <activity
            android:name="com.czur.cloud.ui.eshare.EShareScanActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.eshare.EShareMenuActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.czur.cloud.ui.eshare.EShareOpenPainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.czur.cloud.ui.starry.livedatabus.BlankActivityForAlert"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:launchMode="standard"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentTheme" />
        <activity
            android:name="com.czur.cloud.ui.starry.livedatabus.BlankActivityForAlertLand"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:launchMode="standard"
            android:screenOrientation="landscape"
            android:theme="@style/TranslucentTheme" />

        <!--    多次弹窗使用同一个Aty实例, 所以使用singleTop    -->
        <activity
            android:name="com.czur.cloud.ui.starry.livedatabus.BlankActivityForJoinMeeting"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/TranslucentTheme" />

        <activity
            android:name="com.czur.cloud.ui.starry.livedatabus.ShortCutTrampolineActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:excludeFromRecents="true"
            android:launchMode="singleInstance"
            android:screenOrientation="portrait"
            android:taskAffinity="com.taskAffinity.ShortCutTrampolineActivity"
            android:theme="@style/TranslucentTheme" />

        <activity
            android:name="com.czur.cloud.ui.eshare.EShareEmptyActivity"
            android:allowTaskReparenting="true"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            android:excludeFromRecents="true"
            android:theme="@style/TranslucentTheme" />

        <!-- EShare Widget-->
        <receiver
            android:name="com.czur.cloud.ui.eshare.EShareWidgetProvider"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/eshare_widget_info" />
        </receiver>

        <activity
            android:name=".ui.eshare.transmitfile.TransmitFileUpLoadActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".ui.eshare.transmitfile.TransmitFileDownloadActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".ui.eshare.transmitfile.TransmitFileBrowserActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".ui.eshare.transmitfile.TransmitChildFileBrowserActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|keyboard|navigation"
            android:screenOrientation="portrait"
            />
        <service android:name=".ui.eshare.service.EShareHeartBeatService"
            android:foregroundServiceType="connectedDevice"
            android:priority="1000"
            />

    </application>

    <queries>
        <!--WeChat-->
        <package android:name="com.tencent.mm" />
        <!--QQ-->
        <package android:name="com.tencent.mobileqq" />
        <!--新浪微博-->
        <package android:name="com.sina.weibo" />
    </queries>

</manifest>
