<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/equipment_rl"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/home_item_shadow_img"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginLeft="7.5dp"
        android:layout_marginRight="7.5dp"
        android:background="@mipmap/shadow_icon"/>

    <RelativeLayout
        android:id="@+id/home_item_rl"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginLeft="17.5dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="17.5dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/btn_rec_5_bg_with_white">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/equipment_name_tv"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="23.5dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="@color/black_22"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/equipment_name_img"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true"
            android:scaleType="fitEnd" />

    </RelativeLayout>

</RelativeLayout>
