package com.czur.cloud.ui.component.dialog;

import android.app.Activity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.czur.cloud.R;

import java.util.List;

public class CommonPickAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private List<String> list;

    public int getPosition() {
        return position;
    }

    private int position = -1;
    private Activity activity;
    private CommonPickDialog.OnItemPickListener onItemPickListener;

    public CommonPickAdapter(Activity context, List<String> list, int position) {
        this.activity = context;
        this.list = list;
        this.position = position;
    }

    @NonNull
    @Override
    public ItemHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ItemHolder(activity.getLayoutInflater().inflate(R.layout.item_pick, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int i) {
        ItemHolder itemHolder = (ItemHolder) holder;
        itemHolder.textView.setText(list.get(i));
        if (position == i) {
            itemHolder.textView.setSelected(true);
            itemHolder.textView.getPaint().setFakeBoldText(true);
            itemHolder.imageView.setVisibility(View.VISIBLE);
        } else {
            itemHolder.textView.setSelected(false);
            itemHolder.textView.getPaint().setFakeBoldText(false);
            itemHolder.imageView.setVisibility(View.GONE);
        }

        itemHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                position = i;
                notifyDataSetChanged();
                if (onItemPickListener != null) {
                    onItemPickListener.onItemClick(i);
                }
            }
        });
    }


    @Override
    public int getItemCount() {
        return list.size();
    }

    public void setOnItemPickListener(CommonPickDialog.OnItemPickListener onItemPickListener) {
        this.onItemPickListener = onItemPickListener;
    }

    private static class ItemHolder extends RecyclerView.ViewHolder {
        TextView textView;
        ImageView imageView;

        ItemHolder(View itemView) {
            super(itemView);
            textView = itemView.findViewById(R.id.tv_item);
            imageView = itemView.findViewById(R.id.img_check);
        }
    }
}
