package com.czur.cloud.adapter;

/**
 * Created by czur_app001 on 2018/1/19.
 * Email：<EMAIL>
 * (ง •̀_•́)ง
 */

import android.app.Activity;
import android.graphics.Point;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.czur.cloud.R;
import com.czur.cloud.entity.ImageFolder;
import com.czur.cloud.util.EtUtils;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.backends.pipeline.PipelineDraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.common.ResizeOptions;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;

import java.util.ArrayList;
import java.util.List;



/**
 * Created by Yz on 2018/3/14
 * Email：<EMAIL>
 */

public class AlbumFolderAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {


    private List<ImageFolder> datas;
    private Activity context;
    private int mImageSize;
    private int lastSelected = 0;

    public AlbumFolderAdapter(Activity context, List<ImageFolder> folders) {
        this.context = context;
        if (folders != null && folders.size() > 0) {
            datas = folders;
        } else {
            datas = new ArrayList<>();
        }
        mImageSize = EtUtils.getImageItemWidth(context);
    }

    public void refreshData(List<ImageFolder> folders) {
        if (folders != null && folders.size() > 0) {
            datas.clear();
            notifyDataSetChanged();
            datas.addAll(folders);
            notifyItemRangeInserted(0,datas.size());
        }

    }




    public void setData(List<ImageFolder> datas) {
        this.datas = datas;
    }


    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {

        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_album_folder_layout, parent, false);
        return new NormalViewHolder(view);

    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, final int position) {
        final NormalViewHolder mHolder = (NormalViewHolder) holder;
        mHolder.mItem = datas.get(position);
        String albumFolderName = mHolder.mItem.name;
        String albumPicQuantity = mHolder.mItem.images.size() + "";
        String finalStr = String.format("(%s)", albumPicQuantity);
        Uri uri = Uri.parse("file://" +  mHolder.mItem.cover.path);
        Point size = new Point(mImageSize, mImageSize);
        ImageRequest request = ImageRequestBuilder
                .newBuilderWithSource(uri)
                .setResizeOptions(new ResizeOptions(size.x, size.y))
                .build();
        PipelineDraweeController controller = (PipelineDraweeController) Fresco.newDraweeControllerBuilder().setOldController(mHolder.albumFirstPhoto.getController()).setImageRequest(request).build();
        mHolder.albumFirstPhoto.setController(controller);
        mHolder.albumFolderNameTv.setText(albumFolderName + finalStr);
        mHolder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onItemClickListener != null) {
                    onItemClickListener.OnItemClick(position, (ImageFolder) mHolder.mItem);
                }
            }
        });


    }


    @Override
    public int getItemCount() {
        return datas.size();

    }


    public class NormalViewHolder extends RecyclerView.ViewHolder {
        public final View mView;
        TextView albumFolderNameTv;
        SimpleDraweeView albumFirstPhoto;
        ImageFolder mItem;
        RelativeLayout itemView;

        public NormalViewHolder(View view) {
            super(view);
            mView = view;
            itemView = (RelativeLayout) view.findViewById(R.id.item_album_folder_rl);
            albumFirstPhoto = (SimpleDraweeView) view.findViewById(R.id.album_folder_first_photo);
            albumFolderNameTv = (TextView) view.findViewById(R.id.album_folder_name_tv);
        }

    }


    public OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void OnItemClick(int position, ImageFolder imageFolder);
    }
}