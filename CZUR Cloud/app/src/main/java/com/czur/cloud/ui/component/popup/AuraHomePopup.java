package com.czur.cloud.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;

import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.AuraMatePopupAdapter;
import com.czur.cloud.model.AuraDeviceModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class AuraHomePopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;

    public AuraHomePopup(Context context) {
        super(context);
    }

    public AuraHomePopup(Context context, int theme) {
        super(context, theme);
    }


    public static class Builder {
        private Context context;

        private String title;
        private View contentsView;

        private OnClickListener positiveListener;
        private OnClickListener onNegativeListener;
        private OnDismissListener onDismissListener;

        private ImageView img;
        private RecyclerView auraHomeRecyclerView;
        private AuraMatePopupAdapter adapter;
        private List<AuraDeviceModel> datas;

        public Builder(Context context) {
            this.context = context;

        }

        public Builder(Context context, List<AuraDeviceModel> datas) {
            datas = new ArrayList<>();
            this.context = context;
            this.datas = datas;
        }

        public Builder setDatas(List<AuraDeviceModel> datas) {
            this.datas = new ArrayList<>();
            this.datas = datas;
            return this;
        }
//        public Builder setOnClickListener(OnItemClickListener onItemClickListener) {
//            this.onItemClickListener = onItemClickListener;
//            return this;
//        }
        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public void refreshDatas(List<AuraDeviceModel> datas) {
            this.datas = datas;
            adapter.notifyDataSetChanged();
        }

        public AuraHomePopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            AuraHomePopup dialog = new AuraHomePopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);

            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;

            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final AuraHomePopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            dialog.setCancelable(false);
            dialog.setCanceledOnTouchOutside(false);

            View layout = inflater.inflate(R.layout.aura_home_dialog, null, false);
            layout.measure(View.MeasureSpec.makeMeasureSpec(SizeUtils.dp2px(250), View.MeasureSpec.EXACTLY),
                    View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(layout.getMeasuredWidth(),
                    layout.getMeasuredHeight());
            dialog.addContentView(layout, params);
            WindowManager.LayoutParams windowParams  = dialog.getWindow().getAttributes();
            windowParams.width = layout.getMeasuredWidth();
            windowParams.height = layout.getMeasuredHeight();
            dialog.getWindow().setAttributes(windowParams);
            img = (ImageView) layout.findViewById(R.id.img);
            img.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });

            return layout;
        }


    }

}
