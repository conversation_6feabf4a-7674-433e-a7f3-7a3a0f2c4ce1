<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="45dp">

    <TextView
        android:id="@+id/tv_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="18dp"
        android:textColor="@color/normal_blue"
        android:textSize="15sp" />

    <ImageView
        android:id="@+id/img_check"
        android:layout_centerVertical="true"
        android:layout_marginEnd="18dp"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:visibility="gone"
        android:layout_alignParentEnd="true"
        android:src="@mipmap/sitting_ok"/>
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/gray_f7"
        android:layout_marginStart="18dp"
        android:layout_alignParentBottom="true"/>
</RelativeLayout>