package com.czur.cloud.ui.mirror.model;

import java.io.Serializable;

public class SittingUpdateModel implements Serializable {

/*
"version": "V3.0",
"update": 1,
"force": 0,
"packageUrl": "https://resource.czur.cc/mirror/device/mirror/2fe8ac14-2c2e-440c-bdf4-009427f0f799.zip",
"md5": "0064314ff407da1981f4c2551bc9aa48",
"fileSize": 2709,
"note": "1.萨哈水电费"
* */
    private String version;     // "version":"0.1_20190725",新版本的版本号
    private int update;         // "update":1,是否更新，1：更新，0：不更新
    private int force;          // "force":0,,是否强制更新，1：强制更新，0：不强制更新
    private String packageUrl;  //"packageUrl":"https://resource.czur.com/ota/auramate/update_X14_20190718.img",升级包地址
    private String md5;         //"md5":"e0b68390ff4b53ee2cfae68e5ba477a3", 升级包md5值
    private long fileSize;      //"fileSize":448508376  文件的大小，字节数
    private String note;         //"note": "1.萨哈水电费"

    private long fromServerSize = 0L;       // 从服务器接收的文件大小
    private long toDeviceSize = 0L;         // 已经发给设备的文件大小记录

    public void initSittingUpdateModel(){
        this.version = "";
        this.update = 0;
        this.force = 0;
        this.packageUrl = "";
        this.md5 = "";
        this.fileSize = 0;
        this.note = "";
        this.fromServerSize = 0L;
        this.toDeviceSize = 0L;
    }

    public void testSittingUpdateModel(){
        this.version = "M.C.2103051035";
        this.update = 1;
        this.force = 0;
        this.packageUrl = "http://osscdn.czur.com/resource/software/android/czur/成者CZUR_V2.3.137.apk";
        this.md5 = "";
        this.fileSize = 22000000;
        this.note = "1.【新增】高清查看时新增高清相册功能，方便查看高清图像\n2.【修复】修复优化了其他已知问题";
        this.fromServerSize = 0L;
        this.toDeviceSize = 0L;
    }

    public long getFromServerSize() {
        return fromServerSize;
    }

    public void setFromServerSize(long fromServerSize) {
        this.fromServerSize = fromServerSize;
    }

    public long getToDeviceSize() {
        return toDeviceSize;
    }

    public void setToDeviceSize(long toDeviceSize) {
        this.toDeviceSize = toDeviceSize;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public int getUpdate() {
        return update;
    }

    public void setUpdate(int update) {
        this.update = update;
    }

    public int getForce() {
        return force;
    }

    public void setForce(int force) {
        this.force = force;
    }

    public String getPackageUrl() {
        return packageUrl;
    }

    public void setPackageUrl(String packageUrl) {
        this.packageUrl = packageUrl;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public String getNote() {        return note;    }

    public void setNote(String note) {        this.note = note;    }
}
