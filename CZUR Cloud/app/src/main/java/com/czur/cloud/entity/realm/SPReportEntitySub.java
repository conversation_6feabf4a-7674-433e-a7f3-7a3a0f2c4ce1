package com.czur.cloud.entity.realm;

import io.realm.RealmObject;
import io.realm.annotations.PrimaryKey;

/**
 * Created by Jason 2020/11/24.
 */
public class SPReportEntitySub extends RealmObject {

    @PrimaryKey
    private int id;
    private String equipmentUuid;
    private String beginTime;
    private String endTime;
    private String pushTime;

    private int errorDuration;
    private int usingDuration;
    private String proportion;
    private String createTime;

    /**
     * 2020/4/1新增
     */
    private int rightDuration;
    private int seriousErrorDuration;

    private int mildErrorDuration;
    private int moderateErrorDuration;

    private String rightProportion;
    private String seriousProportion;
    private String mildProportion;
    private String moderateProportion;

    private int haveRead;

    //////////
    private String title; //标题
    private int type; ////类型 1为日报 2为周报 3为月报
    private boolean published; ////是否发布
    private String fromEnd; //"2020/11/16-2020/11/22",//起始结束时间
    private String localeTime; //"2020-11-21 15:30:00",//本地时间
    private int timezone;  //"":8,//时区
    private int dayUsingDuration;  //"":277, //日平均使用时间
    private String rank;//排名
    private String rankNo;//排名
    private String trend; //"持平",//趋势
    private int dayRemindCount; //41,//日平均提醒次数
    private int totalRemindCount; //41,//总提醒次数
    private String reportId;
    private String level;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getReportId() {        return reportId;    }
    public void setReportId(String reportId) {        this.reportId = reportId;    }

    public String getLevel() {        return level;    }

    public void setLevel(String level) {        this.level = level;    }

    public String getEquipmentUuid() {
        return equipmentUuid;
    }

    public void setEquipmentUuid(String equipmentUuid) {
        this.equipmentUuid = equipmentUuid;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getPushTime() {
        return pushTime;
    }

    public void setPushTime(String pushTime) {
        this.pushTime = pushTime;
    }

    public int getErrorDuration() {
        return errorDuration;
    }

    public void setErrorDuration(int errorDuration) {
        this.errorDuration = errorDuration;
    }

    public int getUsingDuration() {
        return usingDuration;
    }

    public void setUsingDuration(int usingDuration) {
        this.usingDuration = usingDuration;
    }

    public String getProportion() {
        return proportion;
    }

    public void setProportion(String proportion) {
        this.proportion = proportion;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public int getRightDuration() {
        return rightDuration;
    }

    public void setRightDuration(int rightDuration) {
        this.rightDuration = rightDuration;
    }

    public int getSeriousErrorDuration() {
        return seriousErrorDuration;
    }

    public void setSeriousErrorDuration(int seriousErrorDuration) {
        this.seriousErrorDuration = seriousErrorDuration;
    }

    public int getMildErrorDuration() {
        return mildErrorDuration;
    }

    public void setMildErrorDuration(int mildErrorDuration) {
        this.mildErrorDuration = mildErrorDuration;
    }

    public int getModerateErrorDuration() {
        return moderateErrorDuration;
    }

    public void setModerateErrorDuration(int moderateErrorDuration) {
        this.moderateErrorDuration = moderateErrorDuration;
    }

    public String getRightProportion() {
        return rightProportion;
    }

    public void setRightProportion(String rightProportion) {
        this.rightProportion = rightProportion;
    }

    public String getSeriousProportion() {
        return seriousProportion;
    }

    public void setSeriousProportion(String seriousProportion) {
        this.seriousProportion = seriousProportion;
    }

    public String getMildProportion() {
        return mildProportion;
    }

    public void setMildProportion(String mildProportion) {
        this.mildProportion = mildProportion;
    }

    public String getModerateProportion() {
        return moderateProportion;
    }

    public void setModerateProportion(String moderateProportion) {
        this.moderateProportion = moderateProportion;
    }

    public int getHaveRead() {
        return haveRead;
    }

    public void setHaveRead(int haveRead) {
        this.haveRead = haveRead;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public boolean isPublished() {
        return published;
    }

    public void setPublished(boolean published) {
        this.published = published;
    }

    public String getFromEnd() {
        return fromEnd;
    }

    public void setFromEnd(String fromEnd) {
        this.fromEnd = fromEnd;
    }

    public String getLocaleTime() {
        return localeTime;
    }

    public void setLocaleTime(String localeTime) {
        this.localeTime = localeTime;
    }

    public int getTimezone() {
        return timezone;
    }

    public void setTimezone(int timezone) {
        this.timezone = timezone;
    }

    public int getDayUsingDuration() {
        return dayUsingDuration;
    }

    public void setDayUsingDuration(int dayUsingDuration) {
        this.dayUsingDuration = dayUsingDuration;
    }

    public String getRank() {
        return rank;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }

    public String getRankNo() {
        return rankNo;
    }

    public void setRankNo(String rankNo) {
        this.rankNo = rankNo;
    }

    public String getTrend() {
        return trend;
    }

    public void setTrend(String trend) {
        this.trend = trend;
    }

    public int getDayRemindCount() {
        return dayRemindCount;
    }

    public void setDayRemindCount(int dayRemindCount) {
        this.dayRemindCount = dayRemindCount;
    }

    public int getTotalRemindCount() {
        return totalRemindCount;
    }

    public void setTotalRemindCount(int totalRemindCount) {
        this.totalRemindCount = totalRemindCount;
    }
}
