package com.czur.cloud.event;

import com.czur.cloud.model.FlattenImageModel;

public class SwitchFlattenEvent extends BaseEvent {
    private FlattenImageModel flattenImageModel;
    private boolean isFolder;



    private boolean isTwoPage;

    private int position;

    public SwitchFlattenEvent(EventType eventType, int position, boolean isFolder,  boolean isTwoPage, FlattenImageModel flattenImageModel) {
        super(eventType);
        this.position = position;
        this.isTwoPage = isTwoPage;
        this.isFolder = isFolder;
        this.flattenImageModel = flattenImageModel;
    }
    public boolean isTwoPage() {
        return isTwoPage;
    }
    public boolean isFolder() {
        return isFolder;
    }

    public FlattenImageModel getFlattenImageModel() {
        return flattenImageModel;
    }

    public int getPosition() {
        return position;
    }


    @Override
    public boolean match(Object obj) {
        return true;
    }
}
