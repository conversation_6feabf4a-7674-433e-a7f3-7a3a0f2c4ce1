package com.czur.cloud.event;

import com.czur.cloud.ui.mirror.model.SittingDeviceModel;

public class SittingModelChangeEvent extends BaseEvent {

	private SittingDeviceModel deviceModel;

	public SittingModelChangeEvent(EventType eventType, SittingDeviceModel model) {
		super(eventType);
		this.deviceModel = model;
	}

	public SittingDeviceModel getDeviceModel(){
		return deviceModel;
	}

	@Override
	public boolean match(Object obj) {
		return true;
	}
}
