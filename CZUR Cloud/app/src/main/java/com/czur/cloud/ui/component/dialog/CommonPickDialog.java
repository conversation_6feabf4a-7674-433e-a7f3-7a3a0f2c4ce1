package com.czur.cloud.ui.component.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.czur.cloud.R;

import java.util.List;
import java.util.Objects;

public class CommonPickDialog extends Dialog {
    private List<String> itemList;
    private OnItemPickListener onItemPickListener;

    public void setOnEnsureListener(OnEnsureListener onEnsureListener) {
        this.onEnsureListener = onEnsureListener;
    }

    private OnEnsureListener onEnsureListener;
    private Activity context;
    private int position;

    public CommonPickDialog(@NonNull Activity context, List<String> itemList, OnItemPickListener onItemPickListener, int position) {
        super(context, R.style.BottomDialog);
        this.context = context;
        this.itemList = itemList;
        this.onItemPickListener = onItemPickListener;
        this.position = position;
    }

    public interface OnItemPickListener {
        void onItemClick(int position);
    }

    public interface OnEnsureListener {
        void onEnsure(int position);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        View layout = getLayoutInflater().inflate(R.layout.dialog_common_pick, null, false);
        setContentView(layout);
        RecyclerView recyclerView = layout.findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));
        recyclerView.setHasFixedSize(true);
        CommonPickAdapter commonPickAdapter = new CommonPickAdapter(context, itemList, position);
        commonPickAdapter.setOnItemPickListener(onItemPickListener);
        recyclerView.setAdapter(commonPickAdapter);
        recyclerView.getLayoutManager().scrollToPosition(position);
        WindowManager.LayoutParams windowParams = Objects.requireNonNull(getWindow()).getAttributes();
        windowParams.gravity = Gravity.BOTTOM;
        windowParams.width =  WindowManager.LayoutParams.MATCH_PARENT;
        windowParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        windowParams.windowAnimations = R.style.BottomDialogAnimation;
        getWindow().setAttributes(windowParams);
        TextView ensureBtn = layout.findViewById(R.id.btn_ensure);
        ensureBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (onEnsureListener != null) {
                    onEnsureListener.onEnsure(commonPickAdapter.getPosition());
                }
                dismiss();
            }
        });
        layout.findViewById(R.id.image_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }

}
