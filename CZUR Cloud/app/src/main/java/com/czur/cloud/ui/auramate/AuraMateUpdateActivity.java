package com.czur.cloud.ui.auramate;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.adapter.AuraMateUpdateAdapter;
import com.czur.cloud.event.AuraMateReadyUpdateEvent;
import com.czur.cloud.event.AuraMateUpdateEvent;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.aurahome.ATCheckDeviceIsOnlineEvent;
import com.czur.cloud.model.AuraUpdateModel;
import com.czur.cloud.netty.bean.ReceivedMsgBodyBean;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.ui.component.popup.AuraMateUpdatingPopup;
import com.czur.cloud.ui.component.recyclerview.RecycleViewDivider;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.Gson;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class AuraMateUpdateActivity extends AuramateBaseActivity implements View.OnClickListener {

    private ImageView imgUpdateState, imgBack;
    private TextView tvTip, tvWarn;
    private TextView tvUpdate;
    private TextView tvTitle;
    private RecyclerView rvUpdateLog;
    private AuraMateUpdateAdapter adapter;
    private final int STATE_PREPARE = 0;
    private final int STATE_READY = 1;
    private final int STATE_UPDATING = 2;//废弃
    private boolean readyForOTAUpdate;
    private AuraUpdateModel auraUpdateModel;

    private int version;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_auramate_update);
        initView();
        initData();
        initListener();
    }

    @Override
    public boolean PCNeedFinish() {
         return !TextUtils.isEmpty(equipmentId);
    }

    private void initView() {
        tvWarn = findViewById(R.id.tv_warn);
        tvTip = findViewById(R.id.tv_tip);
        tvTitle = findViewById(R.id.tv_title);
        tvUpdate = findViewById(R.id.tv_update);
        imgUpdateState = findViewById(R.id.img_state);
        imgBack = findViewById(R.id.img_back);
        rvUpdateLog = findViewById(R.id.rv_update_log);
        rvUpdateLog.setLayoutManager(new LinearLayoutManager(this));
        //添加自定义分割线：可自定义分割线高度和颜色
        rvUpdateLog.addItemDecoration(new RecycleViewDivider(
                this, LinearLayoutManager.HORIZONTAL, SizeUtils.dp2px(8), getColor(R.color.white)));
    }

    private void initData() {
        version = getResources().getInteger(R.integer.message_api_version);
        readyForOTAUpdate = getIntent().getBooleanExtra("readyForOTAUpdate", false);
        checkUpdateList();
    }

    private void initListener() {
        tvUpdate.setOnClickListener(this);
        imgBack.setOnClickListener(this);
        setNetListener();
    }

    private String getPushLanguage() {
        Locale locale = getResources().getConfiguration().locale;
        if (locale.toString().contains("zh_CN")) {
            return "en_us";
        } else if (locale.toString().contains("zh_HK") || locale.toString().contains("zh_TW") || locale.toString().contains("zh_MO")) {
            return "zh_tw";
        }
        return "en_us";

    }


    private void checkUpdateList() {
        showProgressDialog();
        String clearCacheStr = "?" + UUID.randomUUID().toString();
        Request checkRequest = new Request.Builder().url(BuildConfig.CHECK_AURA_MATE_UPDATE_URL + clearCacheStr).get().build();
        Call checkCall = new OkHttpClient().newCall(checkRequest);
        checkCall.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                hideProgressDialog();
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.body() != null) {
                    String retStr = response.body().string();
                    auraUpdateModel = new Gson().fromJson(retStr, AuraUpdateModel.class);
                    if (auraUpdateModel != null) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                List<String> versionInfo;
                                Locale locale = getResources().getConfiguration().locale;
                                if (locale.toString().contains("zh_CN")) {
                                    versionInfo = auraUpdateModel.getVersion_information();
                                } else if (locale.toString().contains("zh_HK") || locale.toString().contains("zh_TW") || locale.toString().contains("zh_MO")) {
                                    versionInfo = auraUpdateModel.getVersion_information_zh_tw();
                                } else {
                                    versionInfo = auraUpdateModel.getVersion_information();
                                }
                                if (versionInfo == null){
                                    versionInfo = new ArrayList<>();
//                                    versionInfo.add("");
                                }
                                adapter = new AuraMateUpdateAdapter(versionInfo, AuraMateUpdateActivity.this);
                                rvUpdateLog.setAdapter(adapter);
                                if (readyForOTAUpdate) {
                                    changeUIState(STATE_READY);
                                } else {
                                    changeUIState(STATE_PREPARE);
                                    CZURTcpClient.getInstance().needCheckOTAUpdate(AuraMateUpdateActivity.this, version, equipmentId);
                                }
                            }
                        });
                    }
                }
                hideProgressDialog();
            }
        });
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tv_update:
                showProgressDialog();
                CZURTcpClient.getInstance().updateFw(AuraMateUpdateActivity.this, equipmentId);
//                CZURTcpClient.getInstance().deviceCheckIsOnline(AuraMateUpdateActivity.this, equipmentId);
                break;
            case R.id.img_back:
                finish();
                break;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case CHECK_DEVICE_IS_ONLINE:
                ATCheckDeviceIsOnlineEvent onlineEvent = (ATCheckDeviceIsOnlineEvent) event;
                ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean statusBean = onlineEvent.getStatusBean();

                if (Validator.isNotEmpty(statusBean)){
                    if (onlineEvent.getStatusBean().getFirmware_need_update().equals("1")) {
                        changeUIState(STATE_READY);
                    }else{
                    }
                }

                break;

            case AURA_MATE_READY_UPDATE:
                AuraMateReadyUpdateEvent auraMateReadyUpdateEvent = (AuraMateReadyUpdateEvent) event;
                if (auraMateReadyUpdateEvent.getStatusBean().getFirmware_need_update().equals("1")) {
                    changeUIState(STATE_READY);
                }
                break;
            case AURA_MATE_UPDATE:
                AuraMateUpdateEvent auraMateUpdateEvent = (AuraMateUpdateEvent) event;
                int isUpdate = auraMateUpdateEvent.getIsUpdate();
                if (isUpdate == 1) {
                    hideProgressDialog();
                    new AuraMateUpdatingPopup.Builder(AuraMateUpdateActivity.this)
                            .setOnPositiveListener(new AuraMateUpdatingPopup.Builder.OnBtnClickListener() {
                                @Override
                                public void onClick() {
                                    ActivityUtils.finishToActivity(AuraMateActivity.class, false);
                                }
                            })
                            .create()
                            .show();
                }
                break;
            default:
                break;
        }
    }

    private void changeUIState(int state) {
        switch (state) {
            case STATE_PREPARE:
                imgUpdateState.setImageResource(R.mipmap.update_state_prepare);
                tvWarn.setVisibility(View.GONE);
                tvTip.setText(getString(R.string.auramate_v2_tip_2));
                tvTitle.setText(String.format(getString(R.string.auramate_v2), auraUpdateModel.getVersion_name()));
                tvUpdate.setEnabled(false);
                tvTip.setTextColor(getColor(R.color.red_e44e4e));
                tvUpdate.setText(getResources().getString(R.string.update_now));
                break;
            case STATE_READY:
                imgUpdateState.setImageResource(R.mipmap.update_state_before);
                tvWarn.setVisibility(View.VISIBLE);
                tvTip.setText(getResources().getString(R.string.auramate_v2_tip_1));
                tvTitle.setText(String.format(getString(R.string.auramate_v2), auraUpdateModel.getVersion_name()));
                tvUpdate.setEnabled(true);
                tvTip.setTextColor(getColor(R.color.red_e44e4e));
                tvUpdate.setText(getResources().getString(R.string.update_now));
                break;
            case STATE_UPDATING:
                imgUpdateState.setImageResource(R.mipmap.update_state_working);
                tvWarn.setVisibility(View.GONE);
                tvTip.setText(getResources().getString(R.string.auramate_v2_tip_3));
                tvTitle.setText(String.format(getString(R.string.auramate_v2_updating), auraUpdateModel.getVersion_name()));
                tvTip.setTextColor(getColor(R.color.gray_bb));
                tvUpdate.setEnabled(false);
                tvUpdate.setText(getResources().getString(R.string.updating));
                break;
        }

    }

}
