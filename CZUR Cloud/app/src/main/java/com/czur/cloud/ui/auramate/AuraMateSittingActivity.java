package com.czur.cloud.ui.auramate;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.AuraMateSittingPickAdapter;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.aurahome.ATCheckDeviceIsOnlineEvent;
import com.czur.cloud.netty.bean.ReceivedMsgBodyBean;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.util.validator.Validator;
import com.github.iielse.switchbutton.SwitchView;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Arrays;

/**
 * <AUTHOR>
 * 久坐提醒界面
 */

public class AuraMateSittingActivity extends AuramateBaseActivity implements View.OnClickListener {
    private TextView tvTitle;
    private TextView tvTip;
    private ImageView imgBack;
    private RecyclerView recyclerView;
    private String sedentaryReminderSwitch;//"On","Off"
    private int sedentaryReminderDuration;// 0, 1, 2
    private SwitchView switchButton;
    private AuraMateSittingPickAdapter adapter;
    private CZURTcpClient client;
    private int lastPosition;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_sitting);
        initComponent();
        setNetListener();
    }

    @Override
    public boolean PCNeedFinish() {
          return !TextUtils.isEmpty(equipmentId);
    }

    private void initComponent() {
        sedentaryReminderSwitch = getIntent().getStringExtra("sedentaryReminderSwitch");
        sedentaryReminderDuration = getIntent().getIntExtra("sedentaryReminderDuration", 1);

        tvTitle = findViewById(R.id.normal_title);
        tvTitle.setText(R.string.aura_home_sedentary_remind);
        imgBack = findViewById(R.id.normal_back_btn);
        imgBack.setOnClickListener(this);
        tvTip = findViewById(R.id.tv_tip);
        recyclerView = findViewById(R.id.recycler_view);
        recyclerView.setHasFixedSize(true);
        switchButton = findViewById(R.id.switch_long_sitting);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        String[] arrays = {getResources().getString(R.string.aura_sitting_30), getResources().getString(R.string.aura_sitting_45), getResources().getString(R.string.aura_sitting_60)};
        adapter = new AuraMateSittingPickAdapter(this, Arrays.asList(arrays), sedentaryReminderDuration);
        lastPosition = sedentaryReminderDuration;
        adapter.setOnItemPickListener(new AuraMateSittingPickAdapter.OnItemPickListener() {
            @Override
            public void onItemPick(int position) {
                client.sedentaryReminderDuration(AuraMateSittingActivity.this, equipmentId, position + "", lastPosition + "");
                lastPosition = position;
                sedentaryReminderDuration = position;
            }
        });
        recyclerView.setAdapter(adapter);
        if ((!TextUtils.isEmpty(sedentaryReminderSwitch) && sedentaryReminderSwitch.equals("On"))) {
            switchButton.toggleSwitch(true);
        }else{
            switchButton.toggleSwitch(false);
        }
        if (!TextUtils.isEmpty(sedentaryReminderSwitch) && sedentaryReminderSwitch.equals("On")) {
            tvTip.setVisibility(View.VISIBLE);
            recyclerView.setVisibility(View.VISIBLE);
        } else {
            tvTip.setVisibility(View.GONE);
            recyclerView.setVisibility(View.GONE);
        }
        client = CZURTcpClient.getInstance();
        switchButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (switchButton.isOpened()){
                    tvTip.setVisibility(View.VISIBLE);
                    recyclerView.setVisibility(View.VISIBLE);
                    sedentaryReminderSwitch = "On";
                }else{
                    tvTip.setVisibility(View.GONE);
                    recyclerView.setVisibility(View.GONE);
                    sedentaryReminderSwitch = "Off";
                }
                client.sedentaryReminderSwitch(AuraMateSittingActivity.this, equipmentId, switchButton.isOpened() ? "On" : "Off", switchButton.isOpened() ? "Off" : "on");
            }
        });

    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case SEDENTARY_REMINDER_SWITCH:
            case SEDENTARY_REMINDER_DURATION:
            case CHECK_DEVICE_IS_ONLINE:
                setRefreshUI(event);
                break;
            default:
                break;
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.normal_back_btn:
                finish();
                break;
        }
    }


    private void setRefreshUI(BaseEvent event) {
        ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean statusBean = null;
        String dataBegin = "";
        if (event instanceof ATCheckDeviceIsOnlineEvent) {
            ATCheckDeviceIsOnlineEvent onlineEvent = (ATCheckDeviceIsOnlineEvent) event;
            statusBean = onlineEvent.getStatusBean();
            if (null != onlineEvent.getDataBegin()) {
                dataBegin = onlineEvent.getDataBegin();
            }
        }
        if (Validator.isNotEmpty(dataBegin)) {
            if (event.getEventType().equals(EventType.SEDENTARY_REMINDER_SWITCH)) {
                sedentaryReminderSwitch = dataBegin;
            } else if (event.getEventType().equals(EventType.SEDENTARY_REMINDER_DURATION)) {
                sedentaryReminderDuration = Integer.parseInt(dataBegin);
            }
        } else {
            if (statusBean == null) {
                return;
            }
            sedentaryReminderSwitch = statusBean.getSedentary_reminder_switch();
            sedentaryReminderDuration = statusBean.getSedentary_reminder_duration();
            if (!TextUtils.isEmpty(sedentaryReminderSwitch) && sedentaryReminderSwitch.equals("On")) {
                switchButton.setOpened(true);
                tvTip.setVisibility(View.VISIBLE);
                recyclerView.setVisibility(View.VISIBLE);
            }else{
                switchButton.setOpened(false);
                tvTip.setVisibility(View.GONE);
                recyclerView.setVisibility(View.GONE);
            }
            adapter.setData(sedentaryReminderDuration);
        }

    }


}
