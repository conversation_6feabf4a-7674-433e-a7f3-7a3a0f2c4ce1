package com.czur.cloud.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.R;
import com.czur.cloud.model.AuraMateDeviceModel;
import com.czur.cloud.model.AuraRecordModel;

import org.jetbrains.annotations.NotNull;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class AuraMateRecordFilesAdapter extends RecyclerView.Adapter<ViewHolder> {

    private static final int ITEM_TYPE_NORMA = 0;
    private Context mActivity;
    //当前需要显示的所有的图片数据
    private ArrayList<AuraRecordModel.ResultDTO> datas =  new ArrayList<>();
    private LayoutInflater mInflater;

    private boolean isCheckMode = false;
    SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss", Locale.CHINA);



    /**
     * 构造方法
     */
    public AuraMateRecordFilesAdapter(Context activity) {
        this.mActivity = activity;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(ArrayList<AuraRecordModel.ResultDTO> datas) {
        this.datas = datas;
        notifyDataSetChanged();
    }

    public void setCheckMode(boolean checkMode) {
        isCheckMode = checkMode;
        notifyDataSetChanged();
    }

    public ArrayList<AuraRecordModel.ResultDTO> getDatas(){
        return datas;
    }

    @NotNull
    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new NormalViewHolder(mInflater.inflate(R.layout.item_aura_record_file, parent, false));
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {
        NormalViewHolder normalViewHolder = (NormalViewHolder) holder;
        AuraRecordModel.ResultDTO data = datas.get(holder.getBindingAdapterPosition());
        if (isCheckMode) {
            normalViewHolder.fileCb.setVisibility(View.VISIBLE);
            normalViewHolder.movieIv.setImageResource(R.mipmap.ic_aura_files_movie_grey);
            normalViewHolder.fileNameTv.setTextColor(mActivity.getColor(R.color.grey_EAEAEA));
            normalViewHolder.duringTimeTv.setTextColor(mActivity.getColor(R.color.grey_EAEAEA));
            normalViewHolder.arrowIv.setVisibility(View.GONE);
        } else {
            normalViewHolder.fileCb.setVisibility(View.GONE);
            normalViewHolder.movieIv.setImageResource(R.mipmap.ic_aura_files_movie_blue);
            normalViewHolder.fileNameTv.setTextColor(mActivity.getColor(R.color.grey_989898));
            normalViewHolder.duringTimeTv.setTextColor(mActivity.getColor(R.color.grey_989898));
            normalViewHolder.arrowIv.setVisibility(View.VISIBLE);
        }
        long time = data.getEndTime() - data.getStartTime();  // your long value
// 创建一个新的 Date 对象，参数为你的长整型值
        Date date = new Date(time);

// 创建一个新的 SimpleDateFormat 对象，使用你想要的格式 ("mm:ss")
        SimpleDateFormat sdf = new SimpleDateFormat("mm:ss");

// 将时区设为 UTC，以防止时区的影响
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));

        normalViewHolder.fileNameTv.setText(data.getFileName());
        normalViewHolder.duringTimeTv.setText(sdf.format(date));
        normalViewHolder.fileCb.setChecked(data.isChecked());

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onItemClickListener.onItemClick(holder.getBindingAdapterPosition());
            }
        });

    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }

    private static class NormalViewHolder extends ViewHolder {
        public final View mView;
        CheckBox fileCb;
        ImageView movieIv;
        TextView fileNameTv;
        TextView duringTimeTv;
        ImageView arrowIv;

        NormalViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            fileCb = (CheckBox) itemView.findViewById(R.id.file_cb);
            movieIv = (ImageView) itemView.findViewById(R.id.movie_iv);
            fileNameTv = (TextView) itemView.findViewById(R.id.file_name_tv);
            duringTimeTv = (TextView) itemView.findViewById(R.id.during_time_tv);
            arrowIv = (ImageView) itemView.findViewById(R.id.arrow_iv);
        }
    }

    private onItemClickListener onItemClickListener;

    public void setOnItemClickListener(onItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface onItemClickListener {
        void onItemClick(int position);
    }


}
