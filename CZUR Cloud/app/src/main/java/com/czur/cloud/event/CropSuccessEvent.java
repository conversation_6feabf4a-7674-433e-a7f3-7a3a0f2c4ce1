package com.czur.cloud.event;

import com.czur.cloud.model.CropModel;

public class CropSuccessEvent extends BaseEvent {
    private CropModel cropModel;
    private int position;
    private boolean isFolder;

    public CropSuccessEvent(EventType eventType, int position,boolean isFolder,CropModel cropModel) {
        super(eventType);
        this.isFolder=isFolder;
        this.position = position;
        this.cropModel = cropModel;
    }

    public boolean isFolder() {
        return isFolder;
    }
    public CropModel getCropModel() {
        return cropModel;
    }

    public int getPosition() {
        return position;
    }


    @Override
    public boolean match(Object obj) {
        return true;
    }
}
