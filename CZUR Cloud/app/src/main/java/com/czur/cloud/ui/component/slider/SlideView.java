package com.czur.cloud.ui.component.slider;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.LayerDrawable;
import android.os.Build;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.annotation.RequiresApi;
import androidx.core.content.ContextCompat;

import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;

public class SlideView extends RelativeLayout implements SeekBar.OnSeekBarChangeListener {

    protected Slider slider;
    protected Drawable slideBackground;
    protected Drawable buttonBackground;
    protected Drawable buttonImage;
    protected Drawable buttonImageDisabled;
    protected TextView slideTextView;
    protected LayerDrawable buttonLayers;
    protected ColorStateList slideBackgroundColor;
    protected ColorStateList buttonBackgroundColor;
    protected boolean animateSlideText;

    public SlideView(Context context) {
        super(context);
        init(null, 0);
    }

    public SlideView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(attrs, 0);
    }

    public SlideView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs, defStyleAttr);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public SlideView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(attrs, defStyleAttr);
    }

    void init(AttributeSet attrs, int defStyle) {
        inflate(getContext(), R.layout.sv_slide_view, this);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            setBackground(ContextCompat.getDrawable(getContext(), R.drawable.sv_view_bg));
        } else {
            setBackgroundDrawable(ContextCompat.getDrawable(getContext(), R.drawable.sv_view_bg));
        }
        slideTextView = findViewById(R.id.sv_text);
        slider = findViewById(R.id.sv_slider);
        slider.setOnSeekBarChangeListener(this);
        slideBackground = getBackground();
        buttonLayers = (LayerDrawable) slider.getThumb();

        TypedArray a = getContext().getTheme().obtainStyledAttributes(attrs, R.styleable.SlideView,
                defStyle, defStyle);

        int strokeColor;
        float slideTextSize = Util.spToPx(16, getContext());
        String slideText;
        ColorStateList sliderTextColor;
        try {
            animateSlideText = a.getBoolean(R.styleable.SlideView_sv_animateSlideText, true);
            strokeColor = a.getColor(R.styleable.SlideView_sv_strokeColor, ContextCompat.
                    getColor(getContext(), R.color.white));
            slideText = a.getString(R.styleable.SlideView_sv_slideText);
            sliderTextColor = a.getColorStateList(R.styleable.SlideView_sv_slideTextColor);
            slideTextSize = a.getDimension(R.styleable.SlideView_sv_slideTextSize, slideTextSize);
            slideTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, slideTextSize);
            setText(slideText);
            setTextColor(sliderTextColor == null ? slideTextView.getTextColors() : sliderTextColor);
            int buttonImageId = a.getResourceId(R.styleable.SlideView_sv_buttonImage, R.mipmap.dialog_on_icon);
            setButtonImage(ContextCompat.getDrawable(getContext(), buttonImageId));

            if (a.hasValue(R.styleable.SlideView_sv_strokeColor)) {
                Util.setDrawableStroke(slideBackground, strokeColor);
            }

        } finally {
            a.recycle();
        }
    }

    public void setTextColor(@ColorInt int color) {
        slideTextView.setTextColor(color);
    }

    public void setTextColor(ColorStateList colors) {
        slideTextView.setTextColor(colors);
    }

    public void setText(CharSequence text) {
        slideTextView.setText(text);
    }

    public void setTextSize(int size) {
        slideTextView.setTextSize(size);
    }

    public TextView getTextView() {
        return slideTextView;
    }

    public void setButtonImage(Drawable image) {
        buttonImage = image;
//        buttonImage = getNewDrawable(getContext(), R.mipmap.dialog_on_icon, SizeUtils.dp2px(60),SizeUtils.dp2px(60));
//        Bitmap bitmap1 = ImageUtils.getBitmap(R.mipmap.dialog_on_icon);
//        Bitmap bitmap = ImageUtils.scale(bitmap1, SizeUtils.dp2px(60), SizeUtils.dp2px(60));


        buttonImage= ConvertUtils.bitmap2Drawable(ImageUtils.scale(ImageUtils.getBitmap(R.mipmap.dialog_on_icon),SizeUtils.dp2px(58),SizeUtils.dp2px(58)));
        buttonLayers.setDrawableByLayerId(R.id.buttonImage, buttonImage);
    }

    public void setButtonImageDisabled(Drawable image) {
        buttonImageDisabled = image;
    }


    public void setButtonBackgroundColor(ColorStateList color) {
        buttonBackgroundColor = color;
        Util.setDrawableColor(buttonBackground, color.getDefaultColor());
    }


    public void setSlideBackgroundColor(ColorStateList color) {
        slideBackgroundColor = color;
        Util.setDrawableColor(slideBackground, color.getDefaultColor());
    }

    public Slider getSlider() {
        return slider;
    }

    public void setOnSlideCompleteListener(OnSlideCompleteListener listener) {
        slider.setOnSlideCompleteListenerInternal(listener, this);
    }

    @Override
    public void setEnabled(boolean enabled) {
        super.setEnabled(enabled);
        for (int i = 0; i < getChildCount(); i++) {
            getChildAt(i).setEnabled(enabled);
        }
        buttonLayers.setDrawableByLayerId(R.id.buttonImage, enabled ? buttonImage :
                buttonImageDisabled == null ? buttonImage : buttonImageDisabled);

        Util.setDrawableColor(slideBackground, slideBackgroundColor.getColorForState(
                enabled ? new int[]{android.R.attr.state_enabled} : new int[]{-android.R.attr.state_enabled}
                , ContextCompat.getColor(getContext(), R.color.white)));
    }

    @Override
    public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
        if (animateSlideText) {
            slideTextView.setAlpha(1 - (progress / 100f));
        }
    }

    @Override
    public void onStartTrackingTouch(SeekBar seekBar) {

    }

    @Override
    public void onStopTrackingTouch(SeekBar seekBar) {

    }

    public interface OnSlideCompleteListener {
        void onSlideComplete(SlideView slideView);
    }

    public BitmapDrawable getNewDrawable(Context context, int restId, int dstWidth, int dstHeight) {
        Bitmap Bmp = BitmapFactory.decodeResource(
                context.getResources(), restId);
        Bitmap bmp = Bmp.createScaledBitmap(Bmp, dstWidth, dstHeight, true);
        BitmapDrawable d = new BitmapDrawable(bmp);
        Bitmap bitmap = d.getBitmap();

        return d;
    }
}