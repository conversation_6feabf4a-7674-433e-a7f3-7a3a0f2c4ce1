package com.czur.cloud.cache;

import android.net.Uri;

import androidx.annotation.Nullable;

import com.facebook.cache.common.CacheKey;
import com.facebook.cache.common.SimpleCacheKey;
import com.facebook.imagepipeline.cache.BitmapMemoryCacheKey;
import com.facebook.imagepipeline.cache.CacheKeyFactory;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.Postprocessor;

public class CustomCacheKeyFactory implements CacheKeyFactory {

    private static CustomCacheKeyFactory sInstance = null;

    private CustomCacheKeyFactory() {
    }

    public static synchronized CustomCacheKeyFactory getInstance() {
        if (sInstance == null) {
            sInstance = new CustomCacheKeyFactory();
        }
        return sInstance;
    }

    @Override
    public CacheKey getBitmapCacheKey(ImageRequest request, Object callerContext) {
        return new BitmapMemoryCacheKey(
                getCacheKeySourceUri(getCustomKey(request)).toString(),
                request.getResizeOptions(),
                request.getRotationOptions(),
                request.getImageDecodeOptions(),
                null,
                null,
                callerContext);
    }

    @Override
    public CacheKey getPostprocessedBitmapCacheKey(ImageRequest request, Object callerContext) {
        final Postprocessor postprocessor = request.getPostprocessor();
        final CacheKey postprocessorCacheKey;
        final String postprocessorName;
        if (postprocessor != null) {
            postprocessorCacheKey = postprocessor.getPostprocessorCacheKey();
            postprocessorName = postprocessor.getClass().getName();
        } else {
            postprocessorCacheKey = null;
            postprocessorName = null;
        }
        return new BitmapMemoryCacheKey(
                getCacheKeySourceUri(getCustomKey(request)).toString(),
                request.getResizeOptions(),
                request.getRotationOptions(),
                request.getImageDecodeOptions(),
                postprocessorCacheKey,
                postprocessorName,
                callerContext);
    }

    @Override
    public CacheKey getEncodedCacheKey(ImageRequest request, @Nullable Object callerContext) {
        return getEncodedCacheKey(request, getCustomKey(request), callerContext);
    }

    @Override
    public CacheKey getEncodedCacheKey(
            ImageRequest request,
            Uri sourceUri,
            @Nullable Object callerContext) {
        return new SimpleCacheKey(getCacheKeySourceUri(sourceUri).toString());
    }

    /**
     * @return a {@link Uri} that unambiguously indicates the source of the image.
     */
    protected Uri getCacheKeySourceUri(Uri sourceUri) {
        return sourceUri;
    }

    private Uri getCustomKey(ImageRequest request) {
        if (request instanceof AuraCustomImageRequest) {
            String url = request.getSourceUri().toString();
            int index = url.indexOf("x-oss-process");
            if (index == -1) {
                return Uri.parse(url.substring(0, url.indexOf("?")));
            } else {
                String pureUrl = url.substring(0, url.indexOf("?"));
                String substring = url.substring(index, url.length());
                return Uri.parse(pureUrl + substring);
            }
        } else if (request instanceof WrongQuestionImageRequest) {
            String url = request.getSourceUri().toString();
            int index = url.indexOf("Expires");
            if (index == -1) {
                return Uri.parse(url.substring(0, url.indexOf("?")));
            } else {
                String pureUrl = url.substring(0, url.indexOf("?"));
                String substring = url.substring(index, url.length());
                return request.getSourceUri();
            }
        } else {
            try {
                String url = request.getSourceUri().toString();
                int index = url.indexOf("?");
                if (index != -1) {
                    String value = Uri.parse(url).getQueryParameter("x-oss-process");
                    url = url.substring(0, index) + value;
                    return Uri.parse(url);
                } else {
                    return request.getSourceUri();
                }
            } catch (Exception e) {
                e.printStackTrace();
                return request.getSourceUri();
            }
        }
    }
}