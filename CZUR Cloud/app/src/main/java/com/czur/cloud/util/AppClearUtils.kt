package com.czur.cloud.util

import android.Manifest
import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import com.badoo.mobile.util.WeakHandler
import com.blankj.utilcode.util.*
import com.blankj.utilcode.util.ThreadUtils.SimpleTask
import com.blankj.utilcode.util.ThreadUtils.runOnUiThread
import com.czur.cloud.R
import com.czur.cloud.common.CZURConstants
import com.czur.cloud.entity.realm.*
import com.czur.cloud.event.EventType
import com.czur.cloud.event.LogoutEvent
import com.czur.cloud.event.StopServiceEvent
import com.czur.cloud.event.StopSyncTimeCountEvent
import com.czur.cloud.event.UserInfoEvent
import com.czur.cloud.model.AuraMateDeviceModel
import com.czur.cloud.model.AuraMateNewFileRemind
import com.czur.cloud.model.UserInfoModel
import com.czur.cloud.model.UserInfoStarryModel
import com.czur.cloud.netty.observer.NettyService
import com.czur.cloud.netty.observer.NettyUtils
import com.czur.cloud.network.HttpManager
import com.czur.cloud.network.core.MiaoHttpEntity
import com.czur.cloud.network.core.MiaoHttpManager
import com.czur.cloud.preferences.*
import com.czur.cloud.ui.account.LoginActivity
import com.czur.cloud.ui.base.CzurCloudApplication
import com.czur.cloud.ui.eshare.EShareActivity
import com.czur.cloud.ui.starry.activity.StarryActivity
import com.czur.cloud.ui.starry.api.StarryRepository
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.model.StarryUserInfoModel
import com.czur.cloud.ui.starry.utils.RomUtils.isOppo
import com.czur.cloud.vendorPush.VendorPushTask
import com.czur.czurutils.log.logI
import com.facebook.drawee.backends.pipeline.Fresco
import io.realm.Realm
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import java.io.File

private const val TAG = "AppClearUtils"

object AppClearUtils {

    private var context: Application? = null
    private val userPreferences: UserPreferences by lazy {
        UserPreferences.getInstance()
    }
    private val firstPreferences: FirstPreferences by lazy {
        FirstPreferences.getInstance(context)
    }
    private val realm: Realm by lazy {
        Realm.getDefaultInstance()
    }
    private val starryPreferences: StarryPreferences by lazy {
        StarryPreferences.getInstance()
    }
    private val mirrorPreferences: MirrorOfflinePreferences by lazy {
        MirrorOfflinePreferences.getInstance()
    }
    private val esharePreferences: ESharePreferences by lazy {
        ESharePreferences.getInstance()
    }

    //添加注释变成真正的静态方法
    @JvmStatic
    fun clearAllUserData(application: Application) {
        context = application
        StarryActivity.mainActivity = null // 为了清除starryViewModel
        //清空sp
        userPreferences.resetUser()
        firstPreferences.resetFirstPreference()
//        userPreferences.clear()
//        firstPreferences.clear()
        mirrorPreferences.clear()
        starryPreferences.clear()
        esharePreferences.clear()

        val model = StarryUserInfoModel()
        StarryPreferences.getInstance().starryUserinfoModel = model

        // 同时需要清理长连接
        if (ServiceUtils.isServiceRunning(NettyService::class.java)) {
//            ServiceUtils.stopService(NettyService::class.java)
            NettyUtils.getInstance().stopNettyService()
        }
        try {
            Thread.sleep(200)
        } catch (e: Exception) {
        }
        if (ServiceUtils.isServiceRunning(NettyService::class.java)) {
//            ServiceUtils.stopService(NettyService::class.java)
            NettyUtils.getInstance().stopNettyService()
        }

        //清空数据库
        realm.executeTransaction {
            realm.where<SPReportEntity>(SPReportEntity::class.java).findAll().deleteAllFromRealm()
            realm.where<SPReportEntitySub>(SPReportEntitySub::class.java).findAll()
                .deleteAllFromRealm()
            realm.where<SPReportSittingEntity>(SPReportSittingEntity::class.java).findAll()
                .deleteAllFromRealm()
            realm.where<PageEntity>(PageEntity::class.java).findAll().deleteAllFromRealm()
            realm.where<TagEntity>(TagEntity::class.java).findAll().deleteAllFromRealm()
            realm.where<BookEntity>(BookEntity::class.java).findAll().deleteAllFromRealm()
            realm.where<PdfEntity>(PdfEntity::class.java).findAll().deleteAllFromRealm()
            realm.where<BookPdfEntity>(BookPdfEntity::class.java).findAll().deleteAllFromRealm()
            realm.where<SyncPdfEntity>(SyncPdfEntity::class.java).findAll().deleteAllFromRealm()
            realm.where<OcrEntity>(OcrEntity::class.java).findAll().deleteAllFromRealm()
            realm.where<SyncTagEntity>(SyncTagEntity::class.java).findAll().deleteAllFromRealm()
            realm.where<SyncPageEntity>(SyncPageEntity::class.java).findAll().deleteAllFromRealm()
            realm.where<SyncBookEntity>(SyncBookEntity::class.java).findAll().deleteAllFromRealm()
            realm.where<PdfDownloadEntity>(PdfDownloadEntity::class.java).findAll()
                .deleteAllFromRealm()
            realm.where<DownloadEntity>(DownloadEntity::class.java).findAll().deleteAllFromRealm()
            realm.where<HomeCacheEntity>(HomeCacheEntity::class.java).findAll().deleteAllFromRealm()
            realm.where<AuraMateNewFileRemind>(AuraMateNewFileRemind::class.java).findAll()
                .deleteAllFromRealm()
            realm.where<WifiHistoryEntity>(WifiHistoryEntity::class.java).findAll()
                .deleteAllFromRealm()
            realm.where<AuraMateDeviceModel>(AuraMateDeviceModel::class.java).findAll()
                .deleteAllFromRealm()
            realm.where<MessageEntity>(MessageEntity::class.java).findAll().deleteAllFromRealm()
            realm.where<MissedCallEntity>(MissedCallEntity::class.java).findAll()
                .deleteAllFromRealm()
        }
        Fresco.getImagePipeline().clearCaches()
        CleanUtils.cleanCustomDir(Utils.getApp().filesDir.toString() + File.separator + CZURConstants.PDF_PATH)
        firstPreferences.setIsFirstAuraPrompt(true)

    }

    //添加注释变成真正的静态方法
    @JvmStatic
    fun startNettyStarryService() {
        if (NetworkUtils.isConnected()) {
            if (!ServiceUtils.isServiceRunning(NettyService::class.java)) {
                logI("AppClearUtils.startNettyStarryService.NettyService.class")
                NettyUtils.getInstance().startNettyService()
            }
        }
    }

    // 长连接断开重连--网络原因，长连接没断，但是无法通信了
    @JvmStatic
    fun reStartNettyStarryService() {
        if (NetworkUtils.isConnected()) {
            NettyUtils.getInstance().restartNettyService()
        }
    }


    //添加注释变成真正的静态方法
    @JvmStatic
    fun requestUserInfo() {
        HttpManager.getInstance().request().userInfo(
            UserPreferences.getInstance().userId,
            UserInfoModel::class.java,
            object : MiaoHttpManager.CallbackNetwork<UserInfoModel?> {
                override fun onNoNetwork() {}
                override fun onStart() {}
                override fun onResponse(entity: MiaoHttpEntity<UserInfoModel?>?) {}
                override fun onFailure(entity: MiaoHttpEntity<UserInfoModel?>?) {}
                override fun onError(e: java.lang.Exception) {}
            })
    }

    @JvmStatic
    fun getStarryUserInfo() {
        HttpManager.getInstance().requestStarry().getStarryUserInfo(
            userPreferences.userId,
            UserInfoStarryModel::class.java,
            object : MiaoHttpManager.Callback<UserInfoStarryModel?> {
                override fun onStart() {}
                override fun onResponse(entity: MiaoHttpEntity<UserInfoStarryModel?>?) {
                    logI("AppClearUtils.getStarryUserInfo.entity=${entity}")
                }

                override fun onFailure(entity: MiaoHttpEntity<UserInfoStarryModel?>?) {}
                override fun onError(e: java.lang.Exception) {}
            })
    }

    @JvmStatic
    fun getStarryUserAccount() {
        GlobalScope.launch {
            val user = StarryRepository().getStarryUserAccount()
            starryPreferences.setNewUserInfo(user)
            logI("AppClearUtils.getStarryUserAccount.user=${user}")
            logI("AppClearUtils.getStarryUserAccount.starryUserinfoModel=${StarryPreferences.getInstance().starryUserinfoModel}")
        }
    }

    // 针对海外，同步starry的accountNo到user的mobile
    @JvmStatic
    fun syncStarryUserAccount() {
        GlobalScope.launch {
            val user = StarryRepository().getStarryUserAccount()
            starryPreferences.setNewUserInfo(user)
            val user1 = UserPreferences.getInstance().user
            val mobile = user.accountNo
            user1.mobile = mobile
            UserPreferences.getInstance().user = user1

            EventBus.getDefault().post(UserInfoEvent(EventType.CHANGE_USER_ID))

            logI("AppClearUtils.syncStarryUserAccount.user1:id=${user1.id},name=${user1.name},mobile=${user1.mobile},email=${user1.email}")
        }

    }

    // 从userPreferences同步基本信息到starryPreferences中
    @JvmStatic
    fun syncStarryUserInfo() {
        val user = StarryUserInfoModel(
            id = "0",
            czurId = userPreferences.userId ?: "",
            accountNo = userPreferences.userMobile ?: "",
            mobile = userPreferences.userMobile ?: "",
            name = userPreferences.userName ?: "",
        )
//        starryPreferences.starryUserinfoModel = user
        starryPreferences.setNewUserInfo(user)

        logI("AppClearUtils.syncStarryUserInfo.starryUserinfoModel=${StarryPreferences.getInstance().starryUserinfoModel}")
    }


    // 清理一下旧的apk
    // downloadApkDir: apk的下载目录
    // apkFileName: 当前版本的apk名字
    @JvmStatic
    fun checkOldApkFiles(downloadApkDir: String, apkFileName: String) {
        // oppo的下载文件名有个后缀.oppodownload
        if (isOppo()) {
            return
        }
        val czurStr = "成者CZUR_"
        val ext = ".apk"
        println("apkPath=$downloadApkDir,apkName=$apkFileName")

        val apkDir = File(downloadApkDir)
        val allList = apkDir.listFiles() ?: arrayOf()
        println("allList=${allList.size},${allList}")
        val apkList = allList.filter {
            it.name.lowercase().contains(czurStr.lowercase()) && it.name.lowercase().contains(ext)
        }

        for (f: File in apkList) {
            //println("f=[${if (f.isFile) 'f' else 'd'}]${f.name}")
            val fName = f.name
            if (fName.lowercase() == apkFileName.lowercase()) {
                // 保留
                println("current apk：${fName}")
            } else {
                FileUtils.delete(f)
                println("delete apk:${fName}")
            }
        }
    }

    private fun checkPermissions(context: Context): Boolean {
        // 如果Android版本大于31
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            Settings.canDrawOverlays(context)
                    && PermissionUtils.isGranted(Manifest.permission.RECORD_AUDIO)
                    && PermissionUtils.isGranted(Manifest.permission.BLUETOOTH_CONNECT)
        } else {
            Settings.canDrawOverlays(context)
                    && PermissionUtils.isGranted(Manifest.permission.RECORD_AUDIO)
        }
    }

    // EShare Permission
    @JvmStatic
    @JvmOverloads
    fun checkESharePermission(context: Context, activity: Activity, isMoreBtn: Boolean = false) {
        if (checkPermissions(context)) {
            //权限开启状态
            ESharePreferences.getInstance().moreBtnVisible = isMoreBtn
            ESharePreferences.getInstance().backBtnVisible = true
            ActivityUtils.startActivity(EShareActivity::class.java)
        } else { //权限关闭状态
            val explainStr = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                context.getString(R.string.eshare_mic_overlay_bt_permission_explain)
            } else {
                context.getString(R.string.eshare_mic_overlay_permission_explain)
            }

            PermissionUtil.checkPermissionWithDialog(
                context,
                context.getString(R.string.starry_popupwindow_title),
                explainStr,
                context.getString(R.string.starry_go_open_permission),
                context.getString(R.string.starry_background_start_msg_cancel)
            ) {
                if (it != null) { //点击去设置
                    val permissionList = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        arrayOf(
                            Manifest.permission.RECORD_AUDIO,
                            Manifest.permission.BLUETOOTH_CONNECT
                        )
                    } else {
                        arrayOf(Manifest.permission.RECORD_AUDIO)
                    }

                    PermissionUtil.useToolsRequestPermission(
                        permissionList,
                        object : PermissionCallBack {
                            override fun execute() {
                                if (!Settings.canDrawOverlays(context)) {
                                    LogUtils.i("${TAG}.checkPermission.当前无权限，请授权")
                                    ActivityUtils.startActivityForResult(
                                        activity,
                                        Intent(
                                            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                                            Uri.parse("package:" + context!!.packageName)
                                        ),
                                        StarryConstants.RESULT_CHECK_OVERLAYS_CODE
                                    )
                                }
                            }
                        }
                    )
                } else {//点击取消
                }
            }
        }
    }

    // 开启/关闭前台服务（长连接）
    @JvmStatic
    fun startScreenNotify() {
        logI("${TAG}.startScreenNotify()")
        if (!ServiceUtils.isServiceRunning(NettyService::class.java)) {
            ServiceUtils.startService(NettyService::class.java)
        }
    }

    // 开启/关闭前台服务（长连接）
    @JvmStatic
    fun stopScreenNotify() {
        if (ServiceUtils.isServiceRunning(NettyService::class.java)) {
            ServiceUtils.stopService(NettyService::class.java)
        }
    }

    @JvmStatic
    fun logoutAndCleanData(context: Application) {
        CzurCloudApplication.isOtherLogin = true
        VendorPushTask.off()
        EventBus.getDefault().post(StopServiceEvent(EventType.STOP_SYNC))
        EventBus.getDefault().post(StopSyncTimeCountEvent(EventType.STOP_SYNC_TIME_COUNT))
        UserPreferences.getInstance(context).setIsUserLogin(false)
        ServiceUtils.stopService(NettyService::class.java)
        EventBus.getDefault().post(LogoutEvent(EventType.LOG_OUT))
        val intent = Intent(ActivityUtils.getTopActivity(), LoginActivity::class.java)
        ActivityUtils.startActivity(intent)
        clearLastUserData(context)
        WeakHandler().postDelayed({ ActivityUtils.finishAllActivitiesExceptNewest() }, 1000)
    }

    @JvmStatic
    fun clearLastUserData(context: Application) {
        ThreadUtils.executeByIo<Void>(object : SimpleTask<Void?>() {
            override fun doInBackground(): Void? {
                logI("clean last user file and sp")
                val filePath: String =
                    context.getFilesDir().toString() + File.separator + userPreferences.userId
                FileUtils.deleteAllInDir(File(filePath))
                runOnUiThread(Runnable {
                    clearAllUserData(context)
                    FirstPreferences.getInstance(context).setIsFirstAuraPrompt(true)
                })
                return null
            }

            override fun onSuccess(result: Void?) {


            }
        })
    }
}