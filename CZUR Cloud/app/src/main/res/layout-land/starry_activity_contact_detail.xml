<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/starry_land_chat_width"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_weight="0.5"
    android:weightSum="1"
    android:stretchColumns="1"
    android:animateLayoutChanges="true"
    android:background="@color/starry_comm_gray_bg"
    android:orientation="vertical"
    android:layout_gravity="end"
    android:elevation="10dp"
    app:bl_solid_color="@color/white"
    app:layout_constraintRight_toRightOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"
    app:bl_corners_topLeftRadius="@dimen/starry_meeting_member_bg_corners_radius"
    app:bl_corners_bottomLeftRadius="@dimen/starry_meeting_member_bg_corners_radius">

    <include
        android:id="@+id/starry_layout_top_bar_rl"
        layout="@layout/starry_layout_top_bar_land" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/starry_comm_gray_bg"
        android:layout_weight="1">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:orientation="vertical"
        >

    <LinearLayout
        android:id="@+id/content_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        >

        <RelativeLayout
            android:id="@+id/contact_user_name_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin100"
            android:layout_gravity="center_vertical"
            android:background="@color/starry_white_bg"
            >

            <TextView
                android:id="@+id/contact_user_name"
                style="@style/starry_contact_name_text_style"
                android:paddingEnd="@dimen/starryMargin60"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="备注" />

            <EditText
                android:id="@+id/contact_user_name_ed"
                style="@style/starry_contact_name_text_style"
                android:background="@color/transparent"
                android:visibility="gone"
                android:singleLine="true"
                android:hint="@string/starry_add_contact_name"
                android:paddingEnd="20dp"
                android:textColorHint="@color/starry_title_gray"
                android:textCursorDrawable="@drawable/edittext_cursor_blue"
                />

            <ImageView
                android:id="@+id/contact_user_name_edit_btn"
                android:layout_width="@dimen/starryMargin35"
                android:layout_height="@dimen/starryMargin35"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:padding="@dimen/dp_5"
                android:src="@mipmap/starry_contact_edit" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/contact_nickname_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin60"
            android:layout_gravity="center_vertical"
            android:background="@color/starry_white_bg"
            >

            <View
                android:id="@+id/contact_nickname_line"
                style="@style/starry_list_space_line_style"
                android:layout_marginStart="@dimen/starryMargin15"/>

            <TextView
                android:id="@+id/contact_nickname_title"
                style="@style/starry_contact_cell_title_style"
                android:text="@string/starry_company_nickname" />

            <TextView
                android:id="@+id/contact_nickname"
                style="@style/starry_contact_cell_value_style"
                android:layout_toEndOf="@+id/contact_nickname_title" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/contact_mobile_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin60"
            android:layout_gravity="center_vertical"
            android:background="@color/starry_white_bg"
            >

            <View
                style="@style/starry_list_space_line_style"
                android:id="@+id/contact_mobile_line"
                android:layout_marginStart="@dimen/starryMargin15"/>

            <TextView
                android:id="@+id/contact_mobile_title"
                style="@style/starry_contact_cell_title_style"
                android:text="@string/starry_company_mobile" />

            <TextView
                android:id="@+id/contact_mobile"
                style="@style/starry_contact_cell_value_style"
                android:layout_toEndOf="@+id/contact_mobile_title" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin10"
            />

        <LinearLayout
            android:id="@+id/contact_detail_company_out_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

        <LinearLayout
            android:id="@+id/contact_detail_company_name_ll"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin60"
            android:orientation="horizontal"
            android:gravity="center"
            android:background="@color/starry_white_bg"
            >

            <ImageView
                android:id="@+id/contact_detail_company_icon_iv"
                android:layout_width="@dimen/starryMargin15"
                android:layout_height="@dimen/starryMargin15"
                android:layout_marginStart="@dimen/starryMargin20"
                android:src="@mipmap/starry_home_comapny_icon"/>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/contact_detail_company_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:padding="@dimen/starryMargin5"
                    android:textColor="@color/starry_title_value"
                    android:textSize="@dimen/starryCompanySubTitle"
                    android:ellipsize="end"
                    android:maxLines="1"
                    tools:text="企业名称" />

                <TextView
                    android:id="@+id/contact_detail_company_name_old"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/contact_detail_company_name"
                    android:layout_marginStart="@dimen/starryMargin5"
                    android:gravity="center_vertical"
                    android:text="@string/starry_company_old"
                    android:textColor="@color/starry_delete_red"
                    android:textSize="@dimen/starryCompanyBtnTitle"
                    android:visibility="gone" />

            </RelativeLayout>

            <ImageView
                android:id="@+id/contact_detail_company_change_btn"
                android:layout_width="@dimen/starryMargin35"
                android:layout_height="@dimen/starryMargin35"
                android:layout_marginEnd="@dimen/starryMargin20"
                android:padding = "@dimen/dp_5"
                android:src="@mipmap/starry_change_company"/>

        </LinearLayout>

        <View
            style="@style/starry_list_space_line_style"/>

        <RelativeLayout
            android:id="@+id/contact_detail_company_name_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin60"
            android:layout_gravity="center_vertical"
            android:background="@color/starry_white_bg"
            >

            <TextView
                android:id="@+id/contact_detail_company_name_tv"
                style="@style/starry_contact_cell_title_style"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:text="@string/starry_contact_company_name" />

            <TextView
                android:id="@+id/contact_detail_company_name_value_tv"
                style="@style/starry_contact_cell_value_style"
                android:layout_toEndOf="@+id/contact_detail_company_name_tv" />

        </RelativeLayout>

        <View
            style="@style/starry_list_space_line_style"/>

        <RelativeLayout
            android:id="@+id/contact_detail_company_title_rl"
            android:layout_width="match_parent"
            android:layout_height="@dimen/starryMargin60"
            android:layout_gravity="center_vertical"
            android:background="@color/starry_white_bg"
            >

            <TextView
                android:id="@+id/contact_detail_company_title_tv"
                style="@style/starry_contact_cell_title_style"
                android:text="@string/starry_contact_company_title" />

            <TextView
                android:id="@+id/contact_detail_company_title_value_tv"
                style="@style/starry_contact_cell_value_style"
                android:layout_toEndOf="@+id/contact_detail_company_title_tv"
                 />

        </RelativeLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/starryMargin20"
            android:orientation="vertical">

            <TextView
                android:id="@+id/contact_save_to_btn"
                style="@style/starry_contact_detail_btn_style"
                android:text="@string/starry_save_contact_btn"
                android:visibility="gone" />

            <TextView
                android:id="@+id/contact_add_to_btn"
                style="@style/starry_contact_detail_btn_style"
                android:text="@string/starry_add_to_contact_btn"
                android:background="@drawable/starry_btn_rec_5_bg_with_white_gray"
                android:textColor="@color/starry_text_color_blue"
                android:layout_marginBottom="@dimen/starryMargin20"
                android:visibility="visible" />

            <com.czur.cloud.ui.starry.component.DrawableCenterOneLineTextView
                android:id="@+id/contact_new_call_btn"
                style="@style/starry_contact_detail_btn_style"
                android:text="@string/starry_new_call_btn"
                android:visibility="visible"
                android:drawableStart="@mipmap/starry_call_btn_logo"
                android:drawablePadding="@dimen/dp10"
                android:layout_gravity="center"
                android:layout_marginBottom="@dimen/starryMargin20"
                android:lines="1"
                />
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/not_get_content_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

            <View
                android:layout_width="wrap_content"
                android:layout_height="50dp"/>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="80dp"
                android:src="@mipmap/starry_not_get_contact"
                android:scaleType="fitCenter"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/starry_not_get_content"
                android:layout_marginTop="@dimen/dp10"
                android:textStyle="bold"
                android:textSize="16sp"
                android:paddingStart="@dimen/starryMargin20"
                android:paddingEnd="@dimen/starryMargin20"
                android:textColor="@color/starry_text_color_gray"/>

        </LinearLayout>

    </LinearLayout>
    </ScrollView>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/starryMargin20"
        app:bl_corners_bottomLeftRadius="@dimen/starry_meeting_member_bg_corners_radius"
        android:id="@+id/space_view"/>

</LinearLayout>

