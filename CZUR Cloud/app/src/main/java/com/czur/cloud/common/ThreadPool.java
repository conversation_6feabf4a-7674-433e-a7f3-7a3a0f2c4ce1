package com.czur.cloud.common;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ThreadPool {

    private ThreadPool() {
    }

    public static ThreadPool getInstance() {
        return SingletonHolder.instance;
    }

    private static class SingletonHolder {
        protected static final ThreadPool instance = new ThreadPool();
        protected static final ExecutorService executorServiceDownloadOriginalImage = Executors.newSingleThreadExecutor();
        protected static final ExecutorService executorServiceShareImage = Executors.newSingleThreadExecutor();
        protected static final ExecutorService executorServiceOCR = Executors.newSingleThreadExecutor();
    }

    public void runOriginalImage(Runnable runnable) {
        SingletonHolder.executorServiceDownloadOriginalImage.execute(runnable);
    }

    public void runShareImage(Runnable runnable) {
        SingletonHolder.executorServiceShareImage.execute(runnable);
    }

    public void runOCR(Runnable runnable) {
        SingletonHolder.executorServiceOCR.execute(runnable);
    }
}
