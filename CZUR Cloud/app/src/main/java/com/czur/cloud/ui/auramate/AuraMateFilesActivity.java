package com.czur.cloud.ui.auramate;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.AuraFilesAdapter;
import com.czur.cloud.event.AuraCropSuccessEvent;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.DeleteFilesEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.SwitchAuraFlattenEvent;
import com.czur.cloud.event.SwitchAuraMateColorEvent;
import com.czur.cloud.model.AuraCropModel;
import com.czur.cloud.model.AuraHomeFileModel;
import com.czur.cloud.model.AuraMateColorModel;
import com.czur.cloud.model.AuraResultModel;
import com.czur.cloud.model.PdfModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.component.popup.ProgressPopup;
import com.czur.cloud.ui.component.progressbar.RoundedRectProgressBar;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

@SuppressWarnings("AlibabaAvoidManuallyCreateThread")
public class AuraMateFilesActivity extends AuramateBaseActivity implements View.OnClickListener {


    private AuraFilesAdapter auraFilesAdapter;
    private LinkedHashMap<String, String> isCheckedMap;
    private EditText dialogEdt;
    private UserPreferences userPreferences;
    private HttpManager httpManager;
    private SimpleDateFormat formatter;
    private ImageView etFilesBackBtn;
    private TextView etFilesSelectAllBtn;
    private TextView etFilesNoTitleTv;
    private TextView etFilesTitleTv;
    private TextView etFilesCancelBtn;
    private RelativeLayout etFilesUnselectedTopBarRl;
    private RelativeLayout etFilesMultiSelectBtn;
    private RecyclerView recyclerView;
    private LinearLayout etFilesBottomLl;
    private RelativeLayout etFilesDeleteRl;
    private RelativeLayout etFilesPdfRl;
    private RelativeLayout etFilesShareRl;
    private LinearLayout emptyLayout;
    private RelativeLayout etFilesMoveRl;
    private SmartRefreshLayout refreshLayout;

    private String folderId;
    private List<AuraHomeFileModel.FilesBean> filesBeans;
    private List<AuraHomeFileModel.FilesBean> addFilesBeans;
    private String seqNum;

    private List<String> fileIds;
    private List<String> pdfIds;
    private String folderName;
    private ProgressPopup progressPopup;
    private RoundedRectProgressBar progressBar;
    private TextView pdfDialogTitle;

    private boolean isPdfRun = true;
    private String ownerId;
    List<AuraHomeFileModel.FilesBean> refreshBeans;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_files);
        initComponent();
        initEtFilesRecyclerView();
        registerEvent();
        getRefreshList();
    }

    @Override
    public boolean PCNeedFinish() {
        return false;
    }


    private void initComponent() {
        folderName = getIntent().getStringExtra("folderName");
        folderId = getIntent().getStringExtra("folderId");
        ownerId = getIntent().getStringExtra("ownerId");
        String folderId = getIntent().getStringExtra("folderId");
        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        etFilesBackBtn = (ImageView) findViewById(R.id.et_files_back_btn);
        etFilesSelectAllBtn = (TextView) findViewById(R.id.et_files_select_all_btn);
        etFilesNoTitleTv = (TextView) findViewById(R.id.et_files_no_title_tv);
        etFilesTitleTv = (TextView) findViewById(R.id.et_files_title_tv);
        etFilesCancelBtn = (TextView) findViewById(R.id.et_files_cancel_btn);
        etFilesUnselectedTopBarRl = (RelativeLayout) findViewById(R.id.et_files_unselected_top_bar_rl);
        etFilesMultiSelectBtn = (RelativeLayout) findViewById(R.id.et_files_multi_select_btn);

        recyclerView = (RecyclerView) findViewById(R.id.recycler_view);
        refreshLayout = findViewById(R.id.refresh_layout);
        refreshLayout.setEnableOverScrollDrag(false);
        refreshLayout.setEnableOverScrollBounce(false);
        refreshLayout.setEnableAutoLoadMore(true);
        refreshLayout.setEnableRefresh(true);
        refreshLayout.setEnableNestedScroll(false);
        refreshLayout.setEnableFooterFollowWhenNoMoreData(true);
        refreshLayout.setEnableLoadMoreWhenContentNotFull(false);
        refreshLayout.setEnableLoadMore(true);
        refreshLayout.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                recyclerView.stopScroll();
                loadMore();
            }
        });
        refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                resetToFresh();
                getRefreshList();
            }
        });
        etFilesBottomLl = (LinearLayout) findViewById(R.id.et_folder_bottom_ll);
        etFilesDeleteRl = (RelativeLayout) findViewById(R.id.et_folder_delete_rl);
        etFilesPdfRl = (RelativeLayout) findViewById(R.id.et_folder_pdf_rl);
        etFilesShareRl = (RelativeLayout) findViewById(R.id.et_folder_share_rl);
        etFilesShareRl.setVisibility(View.GONE);
        etFilesMoveRl = (RelativeLayout) findViewById(R.id.et_folder_move_rl);
        emptyLayout = findViewById(R.id.ll_empty);
        etFilesNoTitleTv.setText(folderName);
        etFilesNoTitleTv.setSelected(true);
    }

    /**
     * @des: 是否显示空文件夹提示区域
     * @params:
     * @return:
     */

    private void isShowEmptyPrompt() {
        if (filesBeans != null && filesBeans.size() > 0) {
            recyclerView.setVisibility(View.VISIBLE);
            emptyLayout.setVisibility(View.GONE);
        } else {
            recyclerView.setVisibility(View.GONE);
            emptyLayout.setVisibility(View.VISIBLE);
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case AURA_MOVE_SUCCESS:
                resetToFresh();
                getRefreshList();
                break;
            case AURA_CROP_SUCCESS:
                AuraCropSuccessEvent cropSuccessEvent = (AuraCropSuccessEvent) event;
                if (cropSuccessEvent.isFolder()) {
                    int position = cropSuccessEvent.getPosition();
                    int cropSuccessEventPosition = cropSuccessEvent.getPosition();
                    AuraCropModel cropModel = cropSuccessEvent.getCropModel();
                    filesBeans.get(position).setMiddle(cropModel.getOssMiddleKeyUrl());
                    filesBeans.get(position).setSmall(cropModel.getOssSmallKeyUrl());
                    filesBeans.get(position).setSingleKey(cropModel.getOssKey());
                    filesBeans.get(position).setId(cropModel.getFileId());
                    filesBeans.get(position).setBig(cropModel.getOssKeyUrl());
                    filesBeans.get(position).setSingle(cropModel.getOssKeyUrl());
                    filesBeans.get(position).setMiddleSingle(cropModel.getOssMiddleKeyUrl());
                    filesBeans.get(position).setSmallSingle(cropModel.getOssSmallKeyUrl());
                    filesBeans.get(position).setFileSize(Integer.parseInt(cropModel.getFileSize()));
                    auraFilesAdapter.refreshData(filesBeans);
                }

                break;
            case AURA_SWITCH_FLATTEN_SUCCESS:
                SwitchAuraFlattenEvent switchSuccessEvent = (SwitchAuraFlattenEvent) event;
                if (switchSuccessEvent.isFolder()) {
                    AuraResultModel flattenImageModel = switchSuccessEvent.getFlattenImageModel();
                    int position = switchSuccessEvent.getPosition();
                    filesBeans.get(position).setMiddle(flattenImageModel.getOssMiddleKeyUrl());
                    filesBeans.get(position).setSmall(flattenImageModel.getOssSmallKeyUrl());
                    filesBeans.get(position).setBig(flattenImageModel.getUrl());
                    filesBeans.get(position).setSingle(flattenImageModel.getUrl());
                    filesBeans.get(position).setMiddleSingle(flattenImageModel.getOssMiddleKeyUrl());
                    filesBeans.get(position).setSmallSingle(flattenImageModel.getOssSmallKeyUrl());
                    filesBeans.get(position).setUserSelectMode(switchSuccessEvent.getUserSelectMode());
                    filesBeans.get(position).setFileSize(flattenImageModel.getFileSize());
                    filesBeans.get(position).setSingleKey(flattenImageModel.getOssKey());
                    auraFilesAdapter.refreshData(filesBeans);
                }
                break;
            case AURA_SWITCH_COLOR_FAILED:
            case AURA_SWITCH_COLOR_SUCCESS:
                SwitchAuraMateColorEvent switchAuraMateColorEvent = (SwitchAuraMateColorEvent) event;
                if (switchAuraMateColorEvent.isFolder()) {
                    AuraMateColorModel auraMateColorModel = switchAuraMateColorEvent.getAuraMateColorModel();
                    int position = switchAuraMateColorEvent.getPosition();
                    filesBeans.get(position).setMiddle(auraMateColorModel.getOssMiddleKeyUrl());
                    filesBeans.get(position).setSmall(auraMateColorModel.getOssSmallKeyUrl());
                    filesBeans.get(position).setBig(auraMateColorModel.getUrl());
                    filesBeans.get(position).setSingleKey(auraMateColorModel.getOssKey());
                    filesBeans.get(position).setSingle(auraMateColorModel.getUrl());
                    filesBeans.get(position).setMiddleSingle(auraMateColorModel.getOssMiddleKeyUrl());
                    filesBeans.get(position).setSmallSingle(auraMateColorModel.getOssSmallKeyUrl());
                    filesBeans.get(position).setFileSize(auraMateColorModel.getFileSize().intValue());
                    auraFilesAdapter.refreshData(filesBeans);
                }
                break;
            default:
                break;
        }
    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initEtFilesRecyclerView() {
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        filesBeans = new ArrayList<>();
        addFilesBeans = new ArrayList<>();
        isCheckedMap = new LinkedHashMap<>();
        auraFilesAdapter = new AuraFilesAdapter(this, filesBeans, false);
        auraFilesAdapter.setOnItemCheckListener(onItemCheckListener);
        auraFilesAdapter.setOnEtFilesClickListener(onItemClickListener);
        recyclerView.setHasFixedSize(true);
        recyclerView.setLayoutManager(new GridLayoutManager(this, 3));
        recyclerView.setAdapter(auraFilesAdapter);
    }

    private void registerEvent() {
        etFilesSelectAllBtn.setOnClickListener(this);
        etFilesCancelBtn.setOnClickListener(this);
        etFilesMultiSelectBtn.setOnClickListener(this);
        etFilesDeleteRl.setOnClickListener(this);
        etFilesBackBtn.setOnClickListener(this);
        etFilesMoveRl.setOnClickListener(this);
        etFilesPdfRl.setOnClickListener(this);
        setNetListener();
    }

    /**
     * @des: 下拉加载
     * params:
     * @return:
     */

    private void loadMore() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                addFilesBeans = getFilesLoadMore(seqNum);
                if (addFilesBeans == null) {
                    return null;
                }
                if (Validator.isNotEmpty(addFilesBeans)) {
                    filesBeans.addAll(addFilesBeans);
                    getSeqNum(addFilesBeans);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                checkSize(isCheckedMap, auraFilesAdapter.getTotalSize());
                if (addFilesBeans == null) {
                    refreshLayout.finishLoadMore(false);
                } else if (Validator.isEmpty(addFilesBeans)) {
                    refreshLayout.finishLoadMoreWithNoMoreData();
                } else {
                    auraFilesAdapter.refreshData(filesBeans);
                    refreshLayout.finishLoadMore(true);
                }
            }
        });
    }


    /**
     * @des: 刷新列表
     * @params:
     * @return:
     */
    public void getRefreshList() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                refreshBeans = getEtFiles();
                if (Validator.isNotEmpty(refreshBeans)) {
                    filesBeans.addAll(refreshBeans);
                }
                getSeqNum(refreshBeans);
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                if (refreshBeans == null) {
                    refreshLayout.finishRefresh(false);
                } else if (!Validator.isNotEmpty(refreshBeans)) {
                    refreshLayout.finishRefresh();
                } else {
                    refreshLayout.finishRefresh();
                }
                isShowEmptyPrompt();
                refreshFiles();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                if (!NetworkUtils.isConnected()) {
                    showMessage(R.string.toast_no_connection_network);
                }
                refreshLayout.finishRefresh(false);
            }
        });
    }

    /**
     * @des: 重置选中状态并且刷新
     * @params:
     * @return:
     */
    private void refreshFiles() {
        isMultiSelect = false;
        isSelectAll = false;
        hideSelectTopBar();
        auraFilesAdapter.refreshData(filesBeans, isMultiSelect, isCheckedMap);
    }

    /**
     * @des: 重置准备刷新
     * @params:
     * @return:
     */

    private void resetToFresh() {
        refreshLayout.resetNoMoreData();
        refreshLayout.closeHeaderOrFooter();
        filesBeans = new ArrayList<>();
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
    }

    private void resetCheckList() {
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        etFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
    }

    /**
     * @des: 图片列表接口
     * @params:
     * @return:
     */
    private List<AuraHomeFileModel.FilesBean> getEtFiles() {
        if (seqNum == null) {
            seqNum = "root";
        }
        try {
            final MiaoHttpEntity<AuraHomeFileModel> etFileEntity = httpManager.request().getAuraHomeFileSync(equipmentId, folderId, "0", "50", "1", userPreferences.getUserId(), ownerId, AuraHomeFileModel.class);
            if (etFileEntity == null) {
                return null;
            } else if (etFileEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return etFileEntity.getBody().getFiles();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @des: 获得下拉加载ID
     * @params:
     * @return:
     */
    private void getSeqNum(List<AuraHomeFileModel.FilesBean> seqNumBeans) {
        if (seqNumBeans.size() > 0) {
            seqNum = seqNumBeans.get(seqNumBeans.size() - 1).getSeqNum() + "";
        }
    }

    /**
     * @des: 下拉加载接口
     * @params:
     * @return:
     */
    private List<AuraHomeFileModel.FilesBean> getFilesLoadMore(String seqNum) {
        try {
            final MiaoHttpEntity<AuraHomeFileModel> etFileEntity = httpManager.request().getAuraHomeFileSync(equipmentId, folderId, seqNum, "50", "1", userPreferences.getUserId(), ownerId, AuraHomeFileModel.class);
            if (etFileEntity == null) {
                return null;
            } else if (etFileEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return etFileEntity.getBody().getFiles();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @des: Item选中监听
     * @params:
     * @return:
     */

    private AuraFilesAdapter.OnItemCheckListener onItemCheckListener = new AuraFilesAdapter.OnItemCheckListener() {
        @Override
        public void onItemCheck(int position, AuraHomeFileModel.FilesBean filesBean, LinkedHashMap<String, String> isCheckedMap, int totalSize) {
            AuraMateFilesActivity.this.isCheckedMap = isCheckedMap;
            checkSize(isCheckedMap, totalSize);
        }

    };
    /**
     * @des: Item点击监听
     * @params:
     * @return:
     */

    private AuraFilesAdapter.OnEtFilesClickListener onItemClickListener = new AuraFilesAdapter.OnEtFilesClickListener() {


        @Override
        public void onEtFilesClick(AuraHomeFileModel.FilesBean filesBean, int position, CheckBox checkBox) {
            if (isMultiSelect) {
                checkBox.setChecked(!checkBox.isChecked());
            } else {
                Intent intent = new Intent(AuraMateFilesActivity.this, AuraMatePreviewActivity.class);
                intent.putExtra("ownerId", ownerId);
                intent.putExtra("size", filesBean.getFileSize() + "");
                intent.putExtra("equipmentId", equipmentId);
                intent.putExtra("mode", filesBean.getUserSelectMode());
                intent.putExtra("folderId", folderId);
                intent.putExtra("seqNum", filesBean.getSeqNum() + "");
                Date date = new Date(Long.parseLong(filesBean.getTakeOn()));
                intent.putExtra("date", formatter.format(date));
                ActivityUtils.startActivity(intent);
            }
        }
    };

    /**
     * @des: 检查选中个数
     * @params:
     * @return:
     */

    private void checkSize(LinkedHashMap<String, String> isCheckedMap, int totalSize) {
        judgeToShowBottom(isCheckedMap);
        if (isCheckedMap.size() == 1) {
            etFilesTitleTv.setText(R.string.select_one_et);
            etFilesBottomLl.setVisibility(View.VISIBLE);
        } else if (isCheckedMap.size() > 1) {
            etFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
            etFilesBottomLl.setVisibility(View.VISIBLE);
        } else {
            if (isMultiSelect) {
                etFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
                etFilesBottomLl.setVisibility(View.GONE);
            }
        }
        checkSelectAll(isCheckedMap);
    }

    /**
     * @des: 根据数量显示bottom
     * @params:
     * @return:
     */

    private void judgeToShowBottom(LinkedHashMap<String, String> isCheckedMap) {
        pdfIds = new ArrayList<>();
        fileIds = new ArrayList<>();
        for (Map.Entry<String, String> stringStringEntry : isCheckedMap.entrySet()) {
            fileIds.add(stringStringEntry.getKey());
            pdfIds.add(stringStringEntry.getValue());
        }

    }

    private void checkSelectAll(LinkedHashMap<String, String> isCheckedMap) {
        //如果选择不是全部Item  text变为取消全选
        if (isCheckedMap.size() < auraFilesAdapter.getTotalSize()) {
            etFilesSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        } else {
            etFilesSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;
        }
    }

    private boolean isMultiSelect = false;
    private boolean isSelectAll = false;

    /**
     * @des: 多选
     * @params:
     * @return:
     */

    private void multiSelect() {
        if (Validator.isNotEmpty(filesBeans)) {
            isMultiSelect = !isMultiSelect;
            auraFilesAdapter.refreshData(isMultiSelect);
            if (isMultiSelect) {
                showSelectTopBar();
            } else {
                hideSelectTopBar();
            }
        }
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.et_files_cancel_btn:
                cancelEvent();
                break;
            case R.id.et_files_select_all_btn:
                selectAll();
                break;
            case R.id.et_files_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.et_files_multi_select_btn:
                multiSelect();
                break;
            case R.id.et_folder_move_rl:
                Intent intent = new Intent(AuraMateFilesActivity.this, AuraMateMoveActivity.class);
                String files = EtUtils.transFiles(fileIds);
                intent.putExtra("equipmentId", equipmentId);
                intent.putExtra("isRoot", false);
                intent.putExtra("files", files);
                intent.putExtra("ownerId", ownerId);
                intent.putExtra("folderId", folderId);
                ActivityUtils.startActivity(intent);
                resetCheckList();
                refreshFiles();
                break;
            case R.id.et_folder_pdf_rl:
                if (pdfIds.size() > 100) {
                    showMessage(R.string.pdf_100_files_tip);
                } else {
                    generatePdfDialog();
                }
                break;
            case R.id.et_folder_delete_rl:
                confirmDeleteDialog();
                break;
            default:
                break;
        }
    }

    /**
     * @des: 创建或者重命名文件夹
     * @params:
     * @return:
     */

    private void generatePdfDialog() {
        final CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMateFilesActivity.this, CloudCommonPopupConstants.EDT_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.input_pdf_name));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (Validator.isNotEmpty(dialogEdt.getText().toString())) {
                    //不能含有表情
                    if (EtUtils.containsEmoji(dialogEdt.getText().toString())) {
                        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMateFilesActivity.this, CloudCommonPopupConstants.COMMON_ONE_BUTTON);
                        builder.setTitle(getResources().getString(R.string.prompt));
                        builder.setMessage(getResources().getString(R.string.nickname_toast_symbol));
                        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                dialog.dismiss();
                            }
                        });
                        CloudCommonPopup commonPopup = builder.create();
                        commonPopup.show();
                    } else {
                        generatePdf();
                        dialog.dismiss();
                    }
                } else {
                    showMessage(R.string.tip_file_rename_length_toast);
                }
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        dialogEdt = (EditText) commonPopup.getWindow().findViewById(R.id.edt);
        commonPopup.show();
    }

    /**
     * @des: 生成Pdf
     * @params:
     * @return:
     */

    private void generatePdf() {
        final ProgressPopup.Builder builder = new ProgressPopup.Builder(AuraMateFilesActivity.this);
        builder.setTitle(getResources().getString(R.string.pdf_ready_text));
        builder.setProgress(0);
        progressPopup = builder.create();
        progressBar = (RoundedRectProgressBar) progressPopup.getWindow().findViewById(R.id.progress);
        pdfDialogTitle = (TextView) progressPopup.getWindow().findViewById(R.id.title);
        progressPopup.show();
        requestServerProgress();
    }

    /**
     * @des: 请求服务器生成pdf百分比
     * @params:
     * @return:
     */
    @SuppressWarnings("AlibabaAvoidManuallyCreateThread")
    private void requestServerProgress() {
        isPdfRun = true;
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    MiaoHttpEntity<PdfModel> generatePdfEntity = HttpManager.getInstance().request().auraPdf(userPreferences.getUserId(), EtUtils.transFiles(pdfIds), dialogEdt.getText().toString(), ownerId, PdfModel.class);
                    if (generatePdfEntity == null) {
                        generatePdfFailed();
                    } else if (generatePdfEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                        while (isPdfRun) {
                            MiaoHttpEntity<PdfModel> resultEntity = HttpManager.getInstance().request().auraPdfResult(userPreferences.getUserId(), generatePdfEntity.getBody().getId(), generatePdfEntity.getBody().getRandomKey(), ownerId, PdfModel.class);
                            if (resultEntity.getCode() == MiaoHttpManager.STATUS_PDF_RETURN_CODE) {
                                Thread.sleep(1000);
                            } else if (resultEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                                final PdfModel pdfModel = resultEntity.getBody();
                                if (pdfModel.getPercent() != null) {
                                    runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            pdfDialogTitle.setText(getString(R.string.pdf_server_generating) + String.format("%.0f", Double.parseDouble(pdfModel.getPercent())) + "%");
                                            progressBar.setProgress(EtUtils.stringToInt(pdfModel.getPercent()));
                                        }
                                    });
                                } else {
                                    generatePdfSuccess();
                                }
                            } else {
                                generatePdfFailed();
                            }
                        }
                    } else {
                        generatePdfFailed();
                    }
                } catch (Exception e) {
                    generatePdfFailed();
                }
            }
        }).start();
    }


    private void generatePdfFailed() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                isPdfRun = false;
                progressPopup.dismiss();
                showMessage(R.string.request_server_error);
                resetCheckList();
                refreshFiles();
            }
        });
    }

    private void generatePdfSuccess() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                isPdfRun = false;
                progressPopup.dismiss();
                showMessage(R.string.pdf_server_generating_success);
                resetCheckList();
                refreshFiles();
                ActivityUtils.startActivity(AuraMatePdfActivity.class);
            }
        });
    }


    /**
     * @des: 显示选择TopBar
     * @params:
     * @return:
     */

    private void showSelectTopBar() {
        etFilesBackBtn.setVisibility(View.GONE);
        etFilesUnselectedTopBarRl.setVisibility(View.GONE);
        etFilesCancelBtn.setVisibility(View.VISIBLE);
        etFilesSelectAllBtn.setVisibility(View.VISIBLE);
        etFilesCancelBtn.setText(R.string.cancel);
        etFilesTitleTv.setVisibility(View.VISIBLE);
        etFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
        etFilesNoTitleTv.setVisibility(View.GONE);
        etFilesSelectAllBtn.setText(R.string.select_all);
    }

    /**
     * @des: 隐藏选择TopBar
     * @params:
     * @return:
     */

    private void hideSelectTopBar() {
        etFilesBottomLl.setVisibility(View.GONE);
        etFilesUnselectedTopBarRl.setVisibility(View.VISIBLE);
        etFilesBackBtn.setVisibility(View.VISIBLE);
        etFilesCancelBtn.setVisibility(View.GONE);
        etFilesSelectAllBtn.setVisibility(View.GONE);
        etFilesTitleTv.setVisibility(View.GONE);
        etFilesNoTitleTv.setVisibility(View.VISIBLE);
        etFilesNoTitleTv.setText(folderName);
    }

    /**
     * @des: 显示删除Dialog
     * @params:
     * @return:
     */

    private void confirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMateFilesActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.confirm_delete));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                delete();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }


    /**
     * @des: 选中所有
     * @params:
     * @return:
     */

    private void selectAll() {
        if (!isSelectAll) {
            for (int i = 0; i < filesBeans.size(); i++) {
                if (!isCheckedMap.containsKey((filesBeans.get(i).getId()))) {
                    isCheckedMap.put(filesBeans.get(i).getId(), filesBeans.get(i).getSingleKey());
                }
            }
            etFilesBottomLl.setVisibility(View.VISIBLE);
            etFilesSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;
        } else {
            etFilesBottomLl.setVisibility(View.GONE);
            isCheckedMap.clear();
            isCheckedMap = new LinkedHashMap<>();
            etFilesSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        }
        etFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
        auraFilesAdapter.refreshData(filesBeans, true, isCheckedMap);
    }

    /**
     * @des: 取消事件
     * @params:
     * @return:
     */

    private void cancelEvent() {
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        isMultiSelect = false;
        isSelectAll = false;
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        auraFilesAdapter.refreshData(filesBeans, false, isCheckedMap);
        hideSelectTopBar();
    }

    /**
     * @des: 删除文件或者图片
     * @params:
     * @return:
     */

    private void delete() {
        httpManager.request().deleteAuraFolder(userPreferences.getUserId(), EtUtils.transFiles(fileIds), "", ownerId, String.class, new MiaoHttpManager.CallbackNetwork<String>() {
            @Override
            public void onNoNetwork() {
                showMessage(R.string.toast_no_connection_network);
            }

            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                String key = EtUtils.transFiles(fileIds);
                hideProgressDialog();
                refreshAfterDeleteSuccess(key);
                EventBus.getDefault().post(new DeleteFilesEvent(EventType.AURA_DELETE_FILE,key));
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                resetCheckList();
                refreshFiles();
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                resetCheckList();
                refreshFiles();
            }
        });
    }

    private void refreshAfterDeleteSuccess(String key) {
        Iterator<AuraHomeFileModel.FilesBean> it = filesBeans.iterator();
        while (it.hasNext()) {
            if (key.contains(it.next().getId())) {
                it.remove();
            }
        }
        cancelEvent();
        isShowEmptyPrompt();
    }



}
