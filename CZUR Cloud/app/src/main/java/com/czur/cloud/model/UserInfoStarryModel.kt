package com.czur.cloud.model

data class UserInfoStarryModel(
    //{
    //"id": 4331,
    //"accountNo": "********",
    //"czurId": 5104,
    //"name": "<EMAIL>",
    //"kind": "2",
    //"type": 0,
    //"createTime": "2022-08-04 13:32:27",
    //"updateTime": "2022-08-04 13:32:27",
    //"pinyin": "<EMAIL>",
    //"countryCode": "AFG",
    //"mail": "<EMAIL>",
    //"num": 0,
    //"bucketName": "czur-starry-na",
    //"inEnterprise": false,
    //"portLimit": 0,
    //"remain": "",
    //"endpoint": "oss-us-west-1.aliyuncs.com",
    //"admin": false
    //}
    val id: Int,
    val accountNo: String,
    val czurId: Int,
    val name: String,
    val kind: String,
    val type: Int,
    val createTime: String,
    val updateTime: String,
    val pinyin: String,
    val countryCode: String,
    val mail: String,
    val num: Int,
    val bucketName: String,
    val inEnterprise: Boolean,
    val portLimit: Int,
    val remain: String,
    val endpoint: String,
    val admin: Boolean
)