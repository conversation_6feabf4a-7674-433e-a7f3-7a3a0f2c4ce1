package com.czur.cloud.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.R;
import com.czur.cloud.entity.LanguageEntity;

import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class LanguageAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_NORMA = 0;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<LanguageEntity> datas;
    private LayoutInflater mInflater;


    /**
     * 构造方法
     */
    public LanguageAdapter(Activity activity, List<LanguageEntity> datas) {
        this.mActivity = activity;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<LanguageEntity> datas) {
        this.datas = datas;
        notifyDataSetChanged();

    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new NormalViewHolder(mInflater.inflate(R.layout.item_select_language, parent, false));
    }


    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {

        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);
            if (mHolder.mItem.isSelect()) {
                mHolder.selectImg.setVisibility(View.VISIBLE);
            }else {
                mHolder.selectImg.setVisibility(View.GONE);
            }
            mHolder.selfTv.setText(mHolder.mItem.getName());
            mHolder.chineseTv.setText(mHolder.mItem.getCnName());
            mHolder.languageRl.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
//                    for (int i = 0; i < datas.size(); i++) {
//                        if(i==position){
//                            datas.get(i).setSelect(true);
//                        }else {
//                            datas.get(i).setSelect(false);
//                        }
//                    }
//                    notifyDataSetChanged();

                    if (onItemClickListener!=null){
                        onItemClickListener.onItemClick(position,mHolder.mItem);
                    }
                }
            });

        }
    }


    @Override
    public int getItemViewType(int position) {

        return ITEM_TYPE_NORMA;
    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }


    private class NormalViewHolder extends ViewHolder {
        public final View mView;
        LanguageEntity mItem;
        LinearLayout languageRl;
        TextView chineseTv;
        TextView selfTv;
        ImageView selectImg;

        NormalViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            languageRl = (LinearLayout) itemView.findViewById(R.id.language_rl);
            chineseTv = (TextView) itemView.findViewById(R.id.chinese_tv);
            selfTv = (TextView) itemView.findViewById(R.id.self_tv);
            selectImg = (ImageView) itemView.findViewById(R.id.select_img);

        }

    }


    private onItemClickListener onItemClickListener;

    public void setOnItemClickListener(onItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface onItemClickListener {
        void onItemClick(int position, LanguageEntity LanguageEntity);
    }


}
