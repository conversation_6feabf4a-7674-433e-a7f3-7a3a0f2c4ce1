package com.czur.cloud.ui.et;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;

import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.EncodeUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.SpanUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.OcrAgainEvent;
import com.czur.cloud.model.BaiduOcrSpotWay;
import com.czur.cloud.model.BaiduTokenModel;
import com.czur.cloud.model.BaiduWordModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.ProgressButton;
import com.czur.cloud.ui.component.cropper.CropImageView;
import com.czur.cloud.ui.component.dialog.CommonPickDialog;
import com.czur.cloud.util.validator.Validator;
import com.facebook.common.executors.CallerThreadExecutor;
import com.facebook.common.references.CloseableReference;
import com.facebook.datasource.DataSource;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.imagepipeline.core.ImagePipeline;
import com.facebook.imagepipeline.datasource.BaseBitmapDataSubscriber;
import com.facebook.imagepipeline.image.CloseableImage;
import com.facebook.imagepipeline.request.ImageRequest;
import com.facebook.imagepipeline.request.ImageRequestBuilder;
import com.google.gson.Gson;
import com.yunmai.docsmatter.engine.DOcrEngine;
import com.yunmai.docsmatter.vo.Document;

import org.greenrobot.eventbus.EventBus;

import java.io.IOException;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class EtOcrActivity extends BaseActivity implements View.OnClickListener {

    public static final int MIN_CLICK_DELAY_TIME = 3000;
    private long lastClickTime = 0;
    private boolean isRun = false;
    private CropImageView cropImageView;
    private Bitmap originalBitmap;
    private int position;
    private String url;
    private int type = 2;
    private String fileId;
    private UserPreferences userPreferences;
    private Bitmap bigBitmap;
    private ProgressButton ocrBtn;
    private long currentLoginTime;
    private String resultText;
    private CommonPickDialog commonPickDialog;
    private OkHttpClient okHttpClient;
    private String[] languages;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.black_2a);
        BarUtils.setNavBarColor(this,getColor(R.color.black_2a));
        BarUtils.setStatusBarLightMode(this, false);
        setContentView(R.layout.activity_et_ocr);
        userPreferences = UserPreferences.getInstance(this);
        initLanguage();
        initLanguageType();
        url = getIntent().getStringExtra("url");
        fileId = getIntent().getStringExtra("fileId");
        position = getIntent().getIntExtra("position", 0);
        findViewById(R.id.et_ocr_choose_btn).setOnClickListener(this);
        ocrBtn = (ProgressButton) findViewById(R.id.et_ocr_btn);
        ocrBtn.setOnClickListener(this);
        ocrBtn.setOnProgressFinishListener(onProgressFinish);
        findViewById(R.id.img_back).setOnClickListener(this);
        cropImageView = (CropImageView) findViewById(R.id.cropImageView);
        okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.MINUTES)
                .readTimeout(10, TimeUnit.MINUTES)
                .build();
        final ImageRequest imageRequest = ImageRequestBuilder.newBuilderWithSource(Uri.parse(url)).build();
        final ImagePipeline imagePipeline = Fresco.getImagePipeline();
        DataSource<CloseableReference<CloseableImage>> dataSource = imagePipeline.fetchDecodedImage(imageRequest, this);
        dataSource.subscribe(new BaseBitmapDataSubscriber() {
            @Override
            protected void onNewResultImpl(final Bitmap bitmap) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        originalBitmap = bitmap;
                        cropImageView.setImageBitmap(originalBitmap);
                    }
                });
            }

            @Override
            protected void onFailureImpl(DataSource<CloseableReference<CloseableImage>> dataSource) {
            }
        }, CallerThreadExecutor.getInstance());
    }


    private void initLanguage() {
        if (BuildConfig.IS_OVERSEAS) {
            languages = new String[]{getString(R.string.Simplified_CHN), getString(R.string.English), getString(R.string.Traditional_CHN), getString(R.string.Japanese), getString(R.string.French), getString(R.string.Spanish), getString(R.string.Portuguese), getString(R.string.Italian), getString(R.string.Russian), getString(R.string.Danish), getString(R.string.Swedish)};
        } else {
            languages = new String[]{getString(R.string.Simplified_CHN), getString(R.string.English), "中简+英", getString(R.string.Traditional_CHN), getString(R.string.Japanese), getString(R.string.French), getString(R.string.Spanish), getString(R.string.Portuguese), getString(R.string.Italian), getString(R.string.Russian), getString(R.string.Danish), getString(R.string.Swedish)};
        }
    }


    private void chooseType(String item) {
        if ("中简+英".equals(item)) {
            type = -1;
        } else if (getString(R.string.Simplified_CHN).equals(item)) {
            type = 2;
        } else if (getString(R.string.Traditional_CHN).equals(item)) {
            type = 21;
        } else if (getString(R.string.English).equals(item)) {
            type = 1;
        } else if (getString(R.string.French).equals(item) || getString(R.string.Spanish).equals(item) || getString(R.string.Portuguese).equals(item) || getString(R.string.Swedish).equals(item) || getString(R.string.Danish).equals(item) || getString(R.string.Italian).equals(item)) {
            type = 3;
        } else if (getString(R.string.Russian).equals(item)) {
            type = 4;
        } else if (getString(R.string.Japanese).equals(item)) {
            type = 6;
        }
    }

    private void requestCloudOcrToken(Bitmap bitmap) {
        showProgressDialog(true);
        FormBody formBody = new FormBody.Builder()
                .add("grant_type", CZURConstants.BAIDU_GRANT_TYPE)
                .add("client_id", CZURConstants.BAIDU_CLIENT_ID)
                .add("client_secret", CZURConstants.BAIDU_CLIENT_SECRET)
                .build();
        Request request = new Request.Builder().url(CZURConstants.BAIDU_TOKEN_URL)
                .post(formBody).build();

        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.code() == 200) {
                    BaiduTokenModel json = new Gson().fromJson(Objects.requireNonNull(response.body()).string(), BaiduTokenModel.class);
                    String urlEncode = EncodeUtils.base64Encode2String(ImageUtils.bitmap2Bytes(bitmap, Bitmap.CompressFormat.JPEG, 90));
                    //精准识别precision 通用识别common
                    String type = getSpotWay();
                    if (TextUtils.isEmpty(type)) {
                        type = "common";
                    }
                    cloudOcr(json.getAccess_token(), urlEncode, type);
                } else {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            hideProgressDialog();
                            showMessage(R.string.request_failed_alert);
                        }
                    });
                }
            }
        });
    }

    private String getSpotWay() {
        final MiaoHttpEntity<BaiduOcrSpotWay> entity = HttpManager.getInstance().request().getSpotWaySync(userPreferences.getUserId(), BaiduOcrSpotWay.class);
        if (entity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
            return entity.getBody().getWay();
        } else {
            return null;
        }
    }


    private void cloudOcr(String access_token, String image, String type) {
        FormBody formBody = new FormBody.Builder()
                .add("access_token", access_token)
                .add("image", image)
                .build();
        String url;
        if (type.equals("common")) {
            url = CZURConstants.BAIDU_OCR_GENERAL_URL;
        } else {
            url = CZURConstants.BAIDU_OCR_ACCURATE_URL;
        }
        Request request = new Request.Builder().url(url)
                .post(formBody).build();
        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.code() == 200) {
                    String body = response.body().string();
                    BaiduWordModel baiduWordModel = new Gson().fromJson(body, BaiduWordModel.class);
                    SpanUtils stringSpan = new SpanUtils();
                    List<BaiduWordModel.WordsResultBean> words_result = baiduWordModel.getWords_result();
                    if (words_result != null) {
                        for (BaiduWordModel.WordsResultBean wordsResultBean : words_result) {
                            stringSpan.appendLine(wordsResultBean.getWords());
                        }
                        Looper.prepare();
                        new Handler().post(new Runnable() {
                            @Override
                            public void run() {
                                resultText = stringSpan.create().toString();
                                successDelay();
                                hideProgressDialog();
                            }
                        });
                        Looper.loop();
                    } else {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                hideProgressDialog();
                                showMessage(R.string.recognition_defeat);
                            }
                        });

                    }


                } else {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            hideProgressDialog();
                            showMessage(R.string.request_failed_alert);
                        }
                    });

                }
            }
        });

    }

    @Override
    public void onClick(final View v) {
        switch (v.getId()) {
            case R.id.img_back:
                finish();
                break;
            case R.id.et_ocr_btn:
                long currentTime = Calendar.getInstance().getTimeInMillis();
                if (currentTime - lastClickTime > MIN_CLICK_DELAY_TIME && !isRun) {
                    lastClickTime = currentTime;
                    cropImage();
                }
                break;
            case R.id.et_ocr_choose_btn:
                showLanguageDialog();
                break;
        }
    }

    private void initLanguageType() {
        List<String> list = Arrays.asList(languages);
        String lastCheckLanguage = userPreferences.getOcrLanguageString();
        for (int i = 0; i < list.size(); i++) {
            if (lastCheckLanguage.equals(list.get(i))) {
                position = i;
                break;
            }
        }
        chooseType(list.get(position));
    }

    private void showLanguageDialog() {
        int position = -1;
        List<String> list = Arrays.asList(languages);
        String lastCheckLanguage = userPreferences.getOcrLanguageString();
        for (int i = 0; i < list.size(); i++) {
            if (lastCheckLanguage.equals(list.get(i))) {
                position = i;
                break;
            }
        }
        commonPickDialog = new CommonPickDialog(this, list, new CommonPickDialog.OnItemPickListener() {
            @Override
            public void onItemClick(int position) {
                userPreferences.setOcrLanguageString(list.get(position));
            }
        }, position);

        commonPickDialog.setOnEnsureListener(new CommonPickDialog.OnEnsureListener() {
            @Override
            public void onEnsure(int position) {
                chooseType(list.get(position));
            }
        });

        if (isActive) {
            commonPickDialog.show();
            commonPickDialog.getWindow().setWindowAnimations(R.style.BottomDialog_Animation);
        }
    }

    private void cropImage() {
        isRun = false;
        final Bitmap cropped = cropImageView.getCroppedImage();
        ocrBtn.startLoading();
        currentLoginTime = System.currentTimeMillis();
        if (cropped.getWidth() < 400 && cropped.getHeight() < 400) {
            bigBitmap = getBigBitmap(cropped);
            excuteBitmap(bigBitmap);
        } else {
            excuteBitmap(cropped);
        }
    }

    private void failedDelay(final int failedText) {
        showMessage(failedText);
        ocrBtn.postDelayed(new Runnable() {
            @Override
            public void run() {
                ocrBtn.reset();
            }
        }, 1000);
    }


    private void successDelay() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentLoginTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentLoginTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            ocrBtn.stopLoadingSuccess();
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }

    private void excuteBitmap(final Bitmap bitmap) {
        if (type == -1) {
            //百度识别特殊处理
            requestCloudOcrToken(bitmap);
            return;
        }
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<String>() {
            @Override
            public String doInBackground() {
                DOcrEngine ocrEngine = new DOcrEngine(EtOcrActivity.this);
                try {
                    Document doc = new Document();
                    int result = ocrEngine.startOCR(type);
                    if (1 == result) {
                        int res = ocrEngine.recognize(ImageUtils.bitmap2Bytes(bitmap, Bitmap.CompressFormat.JPEG, 90), doc, false); // 第一参数：图片数据
                        // 第二参数：传入一个对象
                        // 第三个数据是是否设置模糊判断
                        if (1 == res) {
                            return doc.getContent();
                        } else {
                            ocrFailed(res);
                            return null;
                        }
                    } else {
                        ocrEngine.closeOCR();
                        ocrFailed(-2);
                        return null;
                    }
                } catch (Exception e) {
                    logE(e.toString());
                    ocrFailed(-2);
                    return null;
                } finally {
                    ocrEngine.finalize();
                    ocrEngine = null;
                }

            }

            @Override
            public void onSuccess(String result) {
                resultText = result;
                successDelay();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                ocrFailed(-2);
            }
        });
    }

    private ProgressButton.OnProgressFinish onProgressFinish = new ProgressButton.OnProgressFinish() {
        @Override
        public void onFinish() {
            recycleBitmap();
            if (Validator.isNotEmpty(resultText)) {
                EventBus.getDefault().post(new OcrAgainEvent(EventType.AURA_MATE_OCR_AGAIN, resultText));
                Intent intent = new Intent(EtOcrActivity.this, OcrResultActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                intent.putExtra("url", url);
                intent.putExtra("fileId", fileId);
                intent.putExtra("position", position);
                intent.putExtra("resultText", resultText);
                intent.putExtra("isHandwritingRecognition", true);
                ActivityUtils.startActivity(intent);
                ActivityUtils.finishActivity(EtOcrActivity.this);
            } else {
                failedDelay(R.string.ocr_failed);
            }
        }
    };

    public void recycleBitmap() {
        if (null != bigBitmap) {
            //释放bitmap
            if (!bigBitmap.isRecycled()) {
                bigBitmap.recycle();
                bigBitmap = null;
            }
        }
    }

    private Bitmap getBigBitmap(Bitmap src) {
        if (src == null) {
            return null;
        }
        Bitmap bitmap = Bitmap.createBitmap(800, 800, Bitmap.Config.RGB_565);
        bitmap.eraseColor(Color.parseColor("#FFFFFF"));
        try {
            Canvas cv = new Canvas(bitmap);
            cv.drawBitmap(src, 0, 0, null);
            cv.save();
            cv.restore();
        } catch (Exception e) {
            bitmap = null;
            e.getStackTrace();
        }
        return bitmap;
    }

    private void ocrFailed(final int code) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (code == 2) {
                    failedDelay(R.string.ocr_language_failed);
                } else if (code == 3) {
                    failedDelay(R.string.ocr_blur_failed);
                } else {
                    failedDelay(R.string.ocr_failed);
                }

            }
        });
    }

    @Override
    public void onBackPressed() {
        if (!isRun) {
            super.onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (commonPickDialog != null && commonPickDialog.isShowing()) {
            commonPickDialog.dismiss();
        }
    }
}
