package com.czur.cloud.ui.auramate;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.PowerManager;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.entity.realm.MissedCallEntity;
import com.czur.cloud.model.AuraDeviceModel;
import com.czur.cloud.model.AuraMateDeviceModel;
import com.czur.cloud.model.AuraMateNewFileRemind;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.FirstPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.home.IndexActivity;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.reflect.TypeToken;

import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import io.realm.Realm;
import io.realm.RealmResults;

/**
 * Created by Yz on 2018/2/15
 * Email：<EMAIL>
 */

public class AuraMateActivity extends BaseActivity implements View.OnClickListener {

    private RelativeLayout filesTabLl;
    private RelativeLayout filesTabMovieLl;
    private ImageView filesTabImg;
    private ImageView filesTabMovieImg;
    private LinearLayout indexTabLl;
    private ImageView indexTabImg;
    private TextView indexTabTv;
    private TextView filesTabTv;
    private TextView filesTabMovieTv;
    private AuraMateFragmentAttacher auraMateFragmentAttacher;
    private int currentShowFragmentIndex;
    private int willShowFragmentIndex;
    private Fragment showFragment;
    private RelativeLayout indexBottomBar;
    private FirstPreferences firstPreferences;
    private ImageView auraHomeBackBtn;
    private RelativeLayout auraHomeMoreBtn;
    private ImageView missedCallGuideImg;
    private TextView missedCallGuideTv;
    private View missedCallPoint;
    private View filesTabRedTip;
    private View moviesTabRedTip;
    private List<AuraDeviceModel> entity;
    private UserPreferences userPreferences;
    private Realm realm;
    private List<AuraMateDeviceModel> list;
    private View topBar;
    private String device, relationId;
    private AtomicBoolean taskIsRunning;
    private TextView tvIpPortConfig;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(getWindow(), true);
        setContentView(R.layout.activity_aura_home);
        initComponent();
        registerEvent();
        handlePush();
        initIndexFragment(savedInstanceState);
        gotoSettingIgnoringBatteryOptimizations();

    }


    /**
     * 获取红点
     */
    public void getRedTip() {
        if (NetworkUtils.isConnected()) {
            boolean auraMoviesRedPoint = UserPreferences.getInstance().getAuraMoviesRedPoint();
            if (auraMoviesRedPoint){
                moviesTabRedTip.setVisibility(View.VISIBLE);
            }else{
                moviesTabRedTip.setVisibility(View.GONE);
            }


            if (!taskIsRunning.get()) {
                //获取所有当前用户可以查看文件的设备列表和每台设备最后一个文件的时间戳
                ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
                    @Override
                    public Void doInBackground() throws Throwable {
                        taskIsRunning.set(true);
                        list = getAuraShareDevice();
                        return null;
                    }

                    @Override
                    public void onCancel() {
                        super.onCancel();
                        taskIsRunning.set(false);
                    }

                    @Override
                    public void onFail(Throwable t) {
                        super.onFail(t);
                        taskIsRunning.set(false);
                    }

                    @Override
                    public void onSuccess(Void result) {
                        if (list != null) {//数据容错
                            //第一进入
                            if (FirstPreferences.getInstance(AuraMateActivity.this).isFirstAuraPrompt()) {
                                FirstPreferences.getInstance(AuraMateActivity.this).setIsFirstAuraPrompt(false);
                                //当前用户第一次调用接口，只将数据保存本地数据库，不对比
                                //将本次数据保存
                                realm.executeTransaction(new Realm.Transaction() {
                                    @Override
                                    public void execute(@NotNull Realm realm) {
                                        for (AuraMateDeviceModel deviceModel : list) {
                                            realm.copyToRealmOrUpdate(deviceModel);
                                        }
                                    }
                                });
                                taskIsRunning.set(false);
                                return;
                            } else {
                                for (AuraMateDeviceModel it : list) {
                                    if (!TextUtils.isEmpty(it.getLastFileTimestamp())) {
                                        if (realm.isClosed()) {
                                            return;
                                        }
                                        //根据当次遍历设备的relationId在本地记录中查找
                                        AuraMateDeviceModel auraMateDeviceModel = realm.where(AuraMateDeviceModel.class).equalTo("equipmentUid", it.getEquipmentUid()).equalTo("releationId", it.getReleationId()).findFirst();
                                        //查找有结果，说明这台之前存在，继续对比逻辑
                                        if (auraMateDeviceModel != null) {
                                            //当次遍历设备的时间戳与查找结果的时间戳对比
                                            if (TextUtils.isEmpty(auraMateDeviceModel.getLastFileTimestamp())) {
                                                //这台本地之前有记录的设备之前没有文件，这次有了新文件
                                                //新文件红点记录
                                                insertRedTip(it);
                                            } else {
                                                //这台本地之前有记录的设备有了新文件
                                                long lastTime = Long.parseLong(auraMateDeviceModel.getLastFileTimestamp().trim());
                                                long time = Long.parseLong(it.getLastFileTimestamp().trim());
                                                if (time - lastTime > 0) {
                                                    //新文件红点记录
                                                    insertRedTip(it);
                                                }
                                            }
                                        } else {
                                            //查找无结果，说明为新绑定设备或新加入共享用户
                                            //新文件红点记录
                                            insertRedTip(it);
                                        }
                                    }
                                }
                                //将本次数据保存
                                realm.executeTransaction(new Realm.Transaction() {
                                    @Override
                                    public void execute(@NotNull Realm realm) {
                                        for (AuraMateDeviceModel deviceModel : list) {
                                            realm.copyToRealmOrUpdate(deviceModel);
                                        }
                                    }
                                });
                            }
                            //查找红点个数
                            RealmResults<AuraMateNewFileRemind> redlist = realm.where(AuraMateNewFileRemind.class).equalTo("haveRead", false).findAll();
                            if (redlist != null && redlist.size() > 0) {
                                filesTabRedTip.setVisibility(View.VISIBLE);
                                AuraMateFilesFragment filesFragment = (AuraMateFilesFragment) auraMateFragmentAttacher.getFragment(2);
                                if (filesFragment != null) {
                                    filesFragment.setNeedRefresh(true);
                                }
                            } else {
                                filesTabRedTip.setVisibility(View.GONE);
                            }
                        }
                        taskIsRunning.set(false);
                    }
                });
            }
        }
    }


    private void insertRedTip(AuraMateDeviceModel it) {
        AuraMateNewFileRemind auraMateNewFileRemind = realm.where(AuraMateNewFileRemind.class).equalTo("releationId", it.getReleationId()).findFirst();
        if (auraMateNewFileRemind == null) {
            realm.executeTransaction(new Realm.Transaction() {
                @Override
                public void execute(Realm realm) {
                    AuraMateNewFileRemind auraMateNewFileRemind = realm.createObject(AuraMateNewFileRemind.class, it.getReleationId());
                    auraMateNewFileRemind.setHaveRead(false);
                    auraMateNewFileRemind.setTimeStamp(it.getLastFileTimestamp());
                }
            });
        } else {
            realm.executeTransaction(new Realm.Transaction() {
                @Override
                public void execute(Realm realm) {
                    auraMateNewFileRemind.setHaveRead(false);
                }
            });
        }
    }

    private List<AuraMateDeviceModel> getAuraShareDevice() {
        try {
            final MiaoHttpEntity<AuraMateDeviceModel> auraShareDeviceEntity = HttpManager.getInstance().request().getAuraShareDeviceSync(
                    userPreferences.getUserId(), new TypeToken<List<AuraMateDeviceModel>>() {
                    }.getType());
            if (auraShareDeviceEntity == null) {
                return null;
            }
            if (auraShareDeviceEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return auraShareDeviceEntity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private void gotoSettingIgnoringBatteryOptimizations() {

        if (!FirstPreferences.getInstance(this).isFirstIgnoreBattery()) {
            return;
        }
        PowerManager powerManager = (PowerManager) getSystemService(Context.POWER_SERVICE);
        Boolean hasIgnored =
                powerManager.isIgnoringBatteryOptimizations(getPackageName());
        FirstPreferences.getInstance(this).setIsFirstIgnoreBattery(false);
        //  判断当前APP是否有加入电池优化的白名单，如果没有，弹出加入电池优化的白名单的设置对话框。
        if (!hasIgnored) {
            Intent intent = new Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
            intent.setData(Uri.parse("package:"+getPackageName()));
            if (intent.resolveActivity(getPackageManager()) != null) {
                startActivity(intent);
            }
        }
    }

    private void initComponent() {
        taskIsRunning = new AtomicBoolean(false);
        realm = Realm.getDefaultInstance();
        userPreferences = UserPreferences.getInstance(this);
        firstPreferences = FirstPreferences.getInstance(this);
        filesTabRedTip = findViewById(R.id.files_tab_red_point);
        moviesTabRedTip = findViewById(R.id.movies_tab_red_point);
        missedCallGuideImg = (ImageView) findViewById(R.id.missed_call_guide_img);
        missedCallGuideTv = (TextView) findViewById(R.id.missed_call_guide_tv);
        missedCallPoint = findViewById(R.id.aura_mate_top_red_point);
        indexTabLl = (LinearLayout) findViewById(R.id.index_tab_ll);
        indexTabImg = (ImageView) findViewById(R.id.index_tab_img);
        indexTabTv = (TextView) findViewById(R.id.index_tab_tv);
        filesTabMovieLl = (RelativeLayout) findViewById(R.id.files_tab_movie_ll);
        filesTabMovieImg = (ImageView) findViewById(R.id.files_tab_movie_img);
        filesTabMovieTv = (TextView) findViewById(R.id.files_tab_movie_tv);
        filesTabLl = (RelativeLayout) findViewById(R.id.files_tab_ll);
        filesTabImg = (ImageView) findViewById(R.id.files_tab_img);
        filesTabTv = (TextView) findViewById(R.id.files_tab_tv);
        indexBottomBar = (RelativeLayout) findViewById(R.id.index_bottom_bar);
        auraHomeBackBtn = (ImageView) findViewById(R.id.aura_home_back_btn);
        auraHomeMoreBtn = (RelativeLayout) findViewById(R.id.aura_home_more_btn);

        missedCallGuideImg.setVisibility(View.GONE);
        missedCallGuideTv.setVisibility(View.GONE);
        missedCallPoint.setVisibility(View.GONE);
        topBar = findViewById(R.id.aura_home_top_bar);
        //平时测试用
        topBar.setOnClickListener(this);
        tvIpPortConfig = findViewById(R.id.tv_ip_port_config);
        tvIpPortConfig.setOnClickListener(this);

        if (BuildConfig.IS_OVERSEAS){
            filesTabMovieLl.setVisibility(View.GONE);
        }
    }

    /**
     * 处理从IndexActivity传来的消息跳转
     */
    private void handlePush() {
        if (getIntent().hasExtra("device")) {
            device = getIntent().getStringExtra("device");
            if (getIntent().hasExtra("relationId")) {
                //新文件
                relationId = getIntent().getStringExtra("relationId");
                currentShowFragmentIndex = 2;
            } else {
                currentShowFragmentIndex = 0;
            }
        }

    }


    public List<AuraDeviceModel> getDevice() {
        return entity;
    }

    public void setDevice(List<AuraDeviceModel> entity) {
        this.entity = entity;
    }

    /**
     * @des: 初始化Fragment
     * @params:[savedInstanceState]
     * @return:void
     */
    private void initIndexFragment(Bundle savedInstanceState) {
        FragmentManager fm = getSupportFragmentManager();
        auraMateFragmentAttacher = new AuraMateFragmentAttacher(fm, device, relationId);
        if (savedInstanceState != null) {
            for (int i = 0; i < 2; i++) {
                Fragment fragment = auraMateFragmentAttacher.getFragment(i);
                if (Validator.isNotEmpty(fragment)) {
                    fm.beginTransaction().hide(fragment).commit();
                }
            }
        }
        changeDefaultPage();
    }


    private void registerEvent() {
        filesTabLl.setOnClickListener(this);
        filesTabMovieLl.setOnClickListener(this);
        indexTabLl.setOnClickListener(this);
        auraHomeBackBtn.setOnClickListener(this);
        auraHomeMoreBtn.setOnClickListener(this);
    }

    private void changeDefaultPage() {
        changeTabIcon(currentShowFragmentIndex);
        showFragment = auraMateFragmentAttacher.showFragment(currentShowFragmentIndex);

    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            // TODO 长连接ip端口设置秘籍
            case R.id.tv_ip_port_config:
//                if (AppUtils.isAppDebug()) {
//                    new IpConfigDialog(this).show();
//                }
//                break;
            case R.id.aura_home_top_bar:
//                if (AppUtils.isAppDebug()) {
//                    new VersionEditDialog(this).show();
//                }
                break;
            case R.id.index_tab_ll:
                changeFragment(v.getId());
                changeTabIcon(0);
                break;
            case R.id.files_tab_movie_ll:
                UserPreferences.getInstance().setAuraMoviesRedPoint(false);
                changeFragment(v.getId());
                changeTabIcon(1);
                getRedTip();
                break;
            case R.id.files_tab_ll:
                changeFragment(v.getId());
                changeTabIcon(2);
                break;
            case R.id.aura_home_back_btn:
                ActivityUtils.finishToActivity(IndexActivity.class, false);
                break;
            case R.id.aura_home_more_btn:
                int size = realm.where(MissedCallEntity.class).findAll().size();
                if (size > 0) {
                    UserPreferences.getInstance(this).setAuramateIsNoitcedMissedCall(false);
                    missedCallGuideImg.setVisibility(View.GONE);
                    missedCallGuideTv.setVisibility(View.GONE);
                }
                ActivityUtils.startActivity(AuraMateMenuActivity.class);
                break;
            default:
                break;
        }
    }


    /**
     * @des: 选择主页tab
     * @params:[index]
     * @return:
     */

    private void changeTabIcon(int index) {
        switch (index) {
            case 0:
                indexTabImg.setSelected(true);
                filesTabMovieImg.setSelected(false);
                filesTabImg.setSelected(false);

                indexTabTv.setSelected(true);
                filesTabMovieTv.setSelected(false);
                filesTabTv.setSelected(false);
                break;
            case 1:
                indexTabImg.setSelected(false);
                filesTabMovieImg.setSelected(true);
                filesTabImg.setSelected(false);

                indexTabTv.setSelected(false);
                filesTabMovieTv.setSelected(true);
                filesTabTv.setSelected(false);
                break;
            case 2:
                indexTabImg.setSelected(false);
                filesTabMovieImg.setSelected(false);
                filesTabImg.setSelected(true);

                indexTabTv.setSelected(false);
                filesTabMovieTv.setSelected(false);
                filesTabTv.setSelected(true);
                break;

            default:
                break;
        }
    }

    /**
     * @des: 切换tab
     * @params:[index]
     * @return:
     */

    public void changeFragment(int id) {
        switch (id) {
            case R.id.index_tab_ll:
                changeTabIcon(0);
                willShowFragmentIndex = 0;
                break;
            case R.id.files_tab_movie_ll:
                changeTabIcon(1);
                willShowFragmentIndex = 1;
                break;
            case R.id.files_tab_ll:
                changeTabIcon(2);
                willShowFragmentIndex = 2;
                break;
            default:
                break;
        }
        showFragment = auraMateFragmentAttacher.showFragment(willShowFragmentIndex, currentShowFragmentIndex);
        currentShowFragmentIndex = willShowFragmentIndex;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        realm.close();
    }

}
