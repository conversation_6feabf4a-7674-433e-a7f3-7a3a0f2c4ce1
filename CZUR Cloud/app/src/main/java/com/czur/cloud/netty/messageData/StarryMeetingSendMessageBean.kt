package com.czur.cloud.netty.messageData

data class StarryMeetingSendMessageBean(
    var action: String,
//    var data: StarryMeetingSendMessageData?,
    var module: String,
    var room: String,
    var udid_from: String,
    var udid_to: String,
    var userid_from: String
) {
    lateinit var data: StarryMeetingSendMessageData

    constructor() : this("",
    "",
    "",
    "",
    "",
    "") {

    }
}

data class StarryMeetingSendMessageData(
    val cmd: String,            // 指令类型
    val meeting_no: String,     // 发送方的会议号
    val target: String,         // 目标成员会议号
    val audioStatus: Int,       // 音频状态 -1 不可用， 0 关闭， 1 开启
    val videoStatus: Int,       // 视频状态 -1 不可用， 0 关闭， 1 开启
    val share_stream: String    // 分享端的视频流
)