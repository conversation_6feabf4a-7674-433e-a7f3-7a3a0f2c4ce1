package com.czur.cloud.ui.mirror.comm;

import static com.blankj.utilcode.util.StringUtils.getString;
import static com.czur.czurutils.log.CZURLogUtilsKt.logE;

import android.content.Context;
import android.net.TrafficStats;
import android.os.Build;
import android.view.View;

import androidx.annotation.RequiresApi;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.clj.fastble.data.BleDevice;
import com.clj.fastble.utils.HexUtil;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.ui.auramate.reportfragment.ReportUtil;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class FastBleToolUtils {

    public static String getCurrentDay(){
        String ret;
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        Date curDate =  new Date(System.currentTimeMillis());
        ret = formatter.format(curDate);
        return ret;
    }

    // 把取得的list的string，转成list
    public static List<String> getStringToList(String strJson){
        List<String> retList = new ArrayList<>();
        try{

            //Json的解析类对象
            JsonParser parser = new JsonParser();
            //将JSON的String 转成一个JsonArray对象
            JsonArray jsonArray = parser.parse(strJson).getAsJsonArray();

            Gson gson = new Gson();
            //加强for循环遍历JsonArray
            for (JsonElement user : jsonArray) {
                //使用GSON，直接转成Bean对象
                String str = gson.fromJson(user, String.class);
                retList.add(str);
            }
        }catch (Exception e){

        }


        return retList;
    }

    // 获取指定目录下的所有文件
    public static List<String> getPathAllFiles(String path) {
        List<String> listFiles = new ArrayList<>();
        File file;
        if (FileUtils.createOrExistsDir(path)) {
            file = new File(path);
        }else {
            file = new File(path);
        }
        File[] files = file.listFiles();
        if (files == null) {
            return listFiles;
        }
        for (File f : files) {
            if (f.isDirectory()) {
                //getFiles(f.getAbsolutePath());
            } else {
//                Map<String, String> map = new HashMap<>();
//                map.put(FastBleConstants.POSE_FILE_NAME, f.getName());
//                map.put(FastBleConstants.POSE_FILE_PATH, f.getPath());
//                pathList.add(map);
                listFiles.add(f.getPath());

            }
        }
        return listFiles;
    }

    // 使按钮置灰、不可点击 false 或 可以点击 true
    @RequiresApi(api = Build.VERSION_CODES.HONEYCOMB)
    public static void setViewButtonEnable(View view, boolean flag, float alpha){
        if (Validator.isEmpty(view)){
            return;
        }

        if (flag){
            view.setAlpha(FastBleConstants.BUTTON_NO_ALPHA);
        }else{
            view.setAlpha(alpha);
        }
        view.setVisibility(View.VISIBLE);
        view.setEnabled(flag);
        view.setClickable(flag);
    }

    @RequiresApi(api = Build.VERSION_CODES.HONEYCOMB)
    public static void setViewButtonEnable(View view, boolean flag){
        setViewButtonEnable(view, flag, FastBleConstants.BUTTON_DISABLE_ALPHA4);
    }

    // 使按钮置灰=false 或 按钮恢复=true；均可以点击
    @RequiresApi(api = Build.VERSION_CODES.HONEYCOMB)
    public static void setViewButtonGray(View view, boolean flag, float alpha){
        if (Validator.isEmpty(view)){
            return;
        }

        if (flag){
            view.setAlpha(FastBleConstants.BUTTON_NO_ALPHA);
        }else{
            view.setAlpha(alpha);
        }
        view.setVisibility(View.VISIBLE);
    }

    @RequiresApi(api = Build.VERSION_CODES.HONEYCOMB)
    public static void setViewButtonGray(View view, boolean flag){
        setViewButtonGray(view, flag, FastBleConstants.BUTTON_DISABLE_ALPHA4);
    }

    public static void onNoNetWorkButtonClick(){
        ToastUtils.showLong(ActivityUtils.getTopActivity().getString(R.string.sitting_no_network_alert));
    }

    // 格式化：分钟时间--小时分钟
    public static String getDayUsingAllTime(int t){
        String h_name = getString(R.string.sitting_report_unit_hour);
        String h_names = getString(R.string.sitting_report_unit_hours);
        String m_name = getString(R.string.sitting_report_unit_min);
        String retName = "";
        int hours = t / 60; //since both are ints, you get an int
        int minutes = t % 60;
        String sHour="";
        if ( hours > 0) {
            if (hours>1){
                h_name = h_names;
            }
            sHour = hours + h_name;
        }
        retName =  sHour + minutes + m_name;
        return retName;
    }

    public static String getFormateReportTitle(String sDateTime,String type){
        //"fromEnd": "2021-05-19",
        //"fromEnd": "2021/05/17-2021/05/23",
        //"fromEnd": "2021.05",

        // 2021.05.19
        // 周报 2021.05.17-05.23
        // 月报 2021.05
        String formater="yyyy.MM.dd";
        String sTtitle = CZURConstants.REPORT_PRE_TITLE[Integer.parseInt(type)];

        String sDateOld = sDateTime;
        String sDateNew = "";
        if (type.equals(CZURConstants.SITTING_REPORT_STR_MONTH)){
            sDateNew = sDateOld;
        }else if (type.equals(CZURConstants.SITTING_REPORT_STR_WEEK)){
            String s3 = sDateOld;
            int a = s3.indexOf("-");
            String s31 = s3.substring(0,a);
            s31 = ReportUtil.foramtDateTime(s31, formater);
            String s32 = s3.substring(a+1);
            s32 = ReportUtil.foramtDateTime(s32, formater);
            sDateNew = s31 + "-" + s32.substring(5);
        }else{
            sDateNew = ReportUtil.foramtDateTime(sDateOld, formater);
        }

        String txtTitle = sTtitle + " " + sDateNew;

        return txtTitle;
    }

    // 检测是否含有乱码
    public static boolean isMessyCode(String strName) {
        try {
            Pattern p = Pattern.compile("\\s*|\t*|\r*|\n*");
            Matcher m = p.matcher(strName);
            String after = m.replaceAll("");
            String temp = after.replaceAll("\\p{P}", "");
            char[] ch = temp.trim().toCharArray();

            int length = (ch != null) ? ch.length : 0;
            for (int i = 0; i < length; i++) {
                char c = ch[i];
                if (!Character.isLetterOrDigit(c)) {
                    String str = "" + ch[i];
                    if (!str.matches("[\u4e00-\u9fa5]+")) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return false;
    }


    // 获取设备修改后的广播中的名字
    public static String getDeviceName(BleDevice bleDevice){
        byte[] scanRecord = bleDevice.getScanRecord();
        String dev_name = FastBleOperationUtils.parseDeviceName(scanRecord);
        if (dev_name == null || dev_name.equals("")){
            dev_name = bleDevice.getName();
        }

        return dev_name;
    }

    // 获取格式化的mac地址
    public static String getDeviceMac(BleDevice bleDevice){
        String str = "";
        String mac = "";

        byte[] scanRecord = bleDevice.getScanRecord();
        byte[] devData = FastBleOperationUtils.parseBroadcastData(scanRecord);
        String groupFourth = HexUtil.formatHexString(devData);
        str = FastBleOperationUtils.parseAdvDataUUID(groupFourth);

        if (str.length()<12){
            return "";
        }
        for (int i =0;i<str.length();i+=2){
            mac = mac + str.substring(i,i+2)+":";
        }
        mac = mac.substring(0,mac.length()-1);
        mac = mac.toUpperCase();

        return mac;
    }

    // "mac": "A5-E4-AE-EF-22-22", ==> a5e4aeef2222
    public static String changeMacAddress(String mac){
        String ret=mac;
        ret = mac.replace("-","").replace(":","");

        return ret.toLowerCase();
    }

    /**
     * 判断是否包含特殊字符
     * @return  false:未包含 true：包含
     */
    public static boolean inputJudge(String editText) {
//        String speChat = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        String speChat = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern pattern = Pattern.compile(speChat);
        Matcher matcher = pattern.matcher(editText);
        if (matcher.find()) {
            return true;
        } else {
            return false;
        }
    }

    // 获取文件的大小的显示MB、KB、B
    public static String getShowFileSize(long totale){
        String ret="";
        float iSize = 0f;
        String unit = "";

        if (totale > FastBleConstants.DEFINE_BYTE_1024 * FastBleConstants.DEFINE_BYTE_1024){
            iSize = (float)(totale*100 / FastBleConstants.DEFINE_BYTE_1024 / FastBleConstants.DEFINE_BYTE_1024)/100;
            unit = "MB";
            ret = String.format("%.2f", iSize);
        }else if(totale > FastBleConstants.DEFINE_BYTE_1024 ){
            iSize = (float)(totale*100 / FastBleConstants.DEFINE_BYTE_1024)/100;
            unit = "KB";
            ret = String.format("%.2f", iSize);
        }else{
            iSize = totale;
            unit = "B";
            ret = String.format("%.0f", iSize);
        }

        return ret+unit;
    }

    //删除文件
    public static void deleteOnlyFile(String filename) {
        File file = new File(filename);
        deleteOnlyFile(file);
    }

    //删除文件
    public static void deleteOnlyFile(File file) {
        if (file == null || !file.exists() || file.isDirectory())
            return;

        if (file.isFile())
            file.delete(); // 删除所有文件
    }

    //删除文件夹里面的文件
    public static void deleteAllFile(File dir) {
        if (dir == null || !dir.exists() || !dir.isDirectory())
            return;
        for (File file : dir.listFiles()) {
            if (file.isFile())
                file.delete(); // 删除所有文件
        }
    }

    //删除文件夹和文件夹里面的文件
    public static void deleteDirWithFile(File dir) {
        if (dir == null || !dir.exists() || !dir.isDirectory())
            return;
        for (File file : dir.listFiles()) {
            if (file.isFile())
                file.delete(); // 删除所有文件
            else if (file.isDirectory())
                deleteDirWithFile(file); // 递规的方式删除文件夹
        }
        dir.delete();// 删除目录本身
    }

    @RequiresApi(api = Build.VERSION_CODES.GINGERBREAD)
    public static <T> byte[] concat(byte[] first, byte[] second) {
        byte[] result = Arrays.copyOf(first, first.length + second.length);
        System.arraycopy(second, 0, result, first.length, second.length);
        return result;
    }

    // Android获取网速和下载速度
    @RequiresApi(api = Build.VERSION_CODES.FROYO)
    private long getTotalRxBytes() {
        //  return TrafficStats.getUidRxBytes(getApplicationInfo().uid) == TrafficStats.UNSUPPORTED ? 0 :(TrafficStats.getTotalRxBytes()/1024);//转为KB
        return TrafficStats.getTotalRxBytes()/1024;//转为KB
    }

    // 比对2个json是否相同
    public static boolean isCompTwoJson(String sendStr, String jsonFileName){
        // 1,查找本地json文件userid+uuid+app_config.json ； userid+uuid+sp_param_config.json；
        // 2，有，则比对，相同，不发送设备；不相同，替换本地json文件，发送设备！
        // 3, 没有，保存本地json文件，发送设备；
        String readStr = readLocalJson(jsonFileName);
        // 本地没有对应的json文件
        if (readStr == null || readStr.equals("")) {
            boolean flag = saveLocalJson(jsonFileName, sendStr);
            return false;
        }else{
            JsonParser parser = new JsonParser();
            JsonObject obj = (JsonObject) parser.parse(sendStr);
            JsonParser parser1 = new JsonParser();
            JsonObject obj1 = (JsonObject) parser1.parse(readStr);
            boolean flag = obj.equals(obj1);
            if (!flag){
                // 替换最新的json内容
                boolean flag1 = saveLocalJson(jsonFileName, sendStr);
            }
            return flag;
        }
    }

    public static File JasonDir = new File(CZURConstants.MIRROR_PATH);

    public static void setLocalJsonDir(String dir){
        FastBleToolUtils.JasonDir = new File(dir);
    }
    // 从本地读取json
    public static String readLocalJson(String fileName) {
        StringBuilder sb = new StringBuilder();
        try {
            File file = new File(JasonDir, fileName);
            if (!file.exists()){
                return "";
            }
            InputStream in = null;
            in = new FileInputStream(file);
            int tempbyte;
            while ((tempbyte = in.read()) != -1) {
                sb.append((char) tempbyte);
            }
            in.close();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
        return sb.toString();
    }

// 保存json到本地
    public static boolean saveLocalJson(String filename, String content) {
    try {
        File file = new File(JasonDir, filename);
        if (!file.exists()){
            JasonDir.mkdirs(); //create folders where write files
        }
        OutputStream out = new FileOutputStream(file);
        out.write(content.getBytes());
        out.close();
    } catch (Exception e) {
        e.printStackTrace();
        logE("FastBleHexUtils.saveLocalJson=保存失败");
        return false;
    }

    return true;
}

    // 保存report json到本地
    public static boolean saveLocalReportJson(String jsonDir, String filename, String content) {
        try {
            File fDir = new File(jsonDir);
            File file = new File(fDir, filename);
            if (!file.exists()){
                fDir.mkdirs(); //create folders where write files
            }
            OutputStream out = new FileOutputStream(file);
            out.write(content.getBytes());
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
            logE("FastBleHexUtils.saveLocalReportJson=保存失败");
            return false;
        }

        return true;
    }

    // 从本地读取report json
    public static String readLocalReportJson(String fileFullName) {
        StringBuilder sb = new StringBuilder();
        try {
            File file = new File(fileFullName);
            if (!file.exists()){
                return "";
            }
            InputStream in = null;
            in = new FileInputStream(file);
            int tempbyte;
            while ((tempbyte = in.read()) != -1) {
                sb.append((char) tempbyte);
            }
            in.close();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
        return sb.toString();
    }


    /**
     * 获取字符数量 汉字占2个长度，英文占1个长度
     * @param text
     * @return
     */
    public static int getTextLength(String text) {
        int length = 0;
        for (int i = 0; i < text.length(); i++) {
            if (text.charAt(i) > 255) {
                length += 2;
            } else {
                length++;
            }
        }
        return length;
    }

    /**
     * 获取当前时区
     * @return
     */
    public static String getCurrentTimeZone() {
        TimeZone tz = TimeZone.getDefault();
        String strTz = tz.getDisplayName(false, TimeZone.SHORT);
        return strTz;
    }

    // 获取格式化后的日期字符串，"20210323160001"--》"2021-03-23"
    public static String getDateTimeFormate(String t){
        String retStr = "";
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            if (t.length()<8) {
                t = sdf.format(new Date());
                Date dt = sdf.parse(t);
                retStr = sdf.format(dt);
            }else{
                retStr = t.substring(0,4)+"-"+t.substring(4,6)+"-"+t.substring(6,8);
            }

        }catch (Exception e){
            e.printStackTrace();
        }

        return retStr;
    }

    // 获取格式化后的日期字符串，"20210323160001"--》"2021-03-23 16:00:01"
    public static String getDateTimeFullFormate(String t){
        String retStr = "";
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (t.length()<13) {
                t = sdf.format(new Date());
                Date dt = sdf.parse(t);
                retStr = sdf.format(dt);
            }else{
                retStr = t.substring(0,4)+"-"+t.substring(4,6)+"-"+t.substring(6,8)
                        +" "+t.substring(8,10)
                        +":"+t.substring(10,12)
                        +":"+t.substring(12,14);
            }

        }catch (Exception e){
            e.printStackTrace();
        }

        return retStr;
    }

    /**
     * 获取当前系统语言格式
     * @param mContext
     * @return
     */
    public static String getCurrentLanguage(Context mContext){
        String lc="";
        try {
            if (mContext != null) {
                Locale locale = mContext.getResources().getConfiguration().locale;
                String language = locale.getLanguage();
                String country = locale.getCountry();
                lc = language + "_" + country;
            }
        }catch(Exception e){

        }
        return lc;
    }

    public static String getCurrentSysTime(){
        String ret;
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date curDate =  new Date(System.currentTimeMillis());
        ret = formatter.format(curDate);
        return ret;
    }

    public static String getCurrentSysTimeLong(){
        String ret;
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        Date curDate =  new Date(System.currentTimeMillis());
        ret = formatter.format(curDate);
        return ret;
    }

    /**
     * 是否包含表情
     * @return
     */
    public static boolean containsEmoji(String source) {
        int len = source.length();
        for (int i = 0; i < len; i++) {
            char codePoint = source.charAt(i);
            if (!isEmojiCharacter(codePoint)) { // 如果不能匹配,则该字符是Emoji表情
                return true;
            }
        }
        return false;
    }

    private static boolean isEmojiCharacter(char codePoint) {
        return (codePoint == 0x0) || (codePoint == 0x9) || (codePoint == 0xA) || (codePoint == 0xD)
                || ((codePoint >= 0x20) && (codePoint <= 0xD7FF)) || ((codePoint >= 0xE000) && (codePoint <= 0xFFFD))
                || ((codePoint >= 0x10000) && (codePoint <= 0x10FFFF));
    }


}
