package com.czur.cloud.ui.mirror.comm;

public class FastBleConstants {

    public static final int SEND_REPORT_COUNT = 10;      // 发送离线报告json的数量
    public static final int BAR_MAX_COUNT = 7;      // barchart的X轴标签数量

    public static final int DATA_PRE_NUM = 14;      // 错误坐姿、开心图片图片头部文件日期长度
    public static final int DATA_MD5_NUM = 32;      // 图片MD5长度

    public static final float BUTTON_NO_ALPHA=1.0f;    //按钮灰掉的透明度
    public static final float BUTTON_DISABLE_ALPHA=0.7f;    //按钮灰掉的透明度
    public static final float BUTTON_DISABLE_ALPHA4=0.4f;    //按钮灰掉的透明度

    public static final String DICT_DATE_STRING = "dateString";
    public static final String DICT_LOCALE_TIME = "localeTime";
    public static final String DICT_TYPE = "type";
    public static final String DICT_UUID = "uuid";
    public static final String DICT_IMG = "img";
    public static final String OSS_PARAMS_UID = "userId";
    public static final String OSS_PARAMS_UDID = "udid";
    public static final String OSS_PARAMS_IMG_DATA = "imgDatas";

    public static final String SITTING_TREND_UP="up";
    public static final String SITTING_TREND_FLAT="flat";
    public static final String SITTING_TREND_DOWN="down";


    public static final int REQUEST_CODE_OPEN_BLE   = 1000;
    public static final int REQUEST_CODE_LONGSIT    = 1001;

    public static final int[] DELAY_TIMES_ARRAY={2,3,3,3,5,5,5,5,          // 30s--8
            10,10,10,          // 30s---3
            15,15,15,15,              // 60s---4
            20,20,20,           //60s---3
            30,30,          //60s--2
            60,             // 60s ---1,        //== 5min---21times
    };

    public final static int MIN_CLICK_TIME = 1000;
    public static final int RUN_DELAY_TIMES1000 = 1000;    // 延迟1秒

    public static final int BLE_CONNECT_TIMEOUT     = 10000; //设置连接超时时间（毫秒），默认5秒
    public static final int BLE_SACN_TIMEOUT        = 60000; //设置扫描超时时间（毫秒），默认5秒
    public static final int BLE_OPERATE_TIMEOUT     = 10000; //设置连接超时时间（毫秒），默认5秒
    public static final int BLE_RECONNECT_TIMES     = 100; //设置连接时重连次数和重连间隔（毫秒），默认为0次不重连
    public static final int BLE_RECONNECT_COUNT     = 0; //设置连接时重连次数和重连间隔（毫秒），默认为0次不重连

    public static final int RUN_DELAY_TIMES500      = 500;    // 延迟时间，默认
    public static final int RUN_DELAY_TIMES300      = 300;    // 延迟时间，默认
    public static final int RUN_DELAY_TIMES100      = 100;    // 延迟时间，默认
    public static final int RUN_DELAY_TIMES10      = 10;    // 延迟时间，默认
    public static final int RUN_DELAY_TIMES50      = 50;    // 延迟时间，默认
    public static final int RUN_DELAY_TIMES_FOR_GET_IMAGE = RUN_DELAY_TIMES1000;    // 延迟n秒获取图片

    public static final int RUN_DELAY_TIMES_FOR_UPDATE  = RUN_DELAY_TIMES1000;    // 延迟1s
    public static final int RUN_DELAY_TIMES_BASE        = RUN_DELAY_TIMES1000;    // 延迟1分钟
    //9,  发aglo的config  （延迟15s）60*1
    public static final int RUN_DELAY_TIMES_FOR_AGLO    = RUN_DELAY_TIMES_BASE*20;    // 延迟n秒
    //    发app的config   （延迟30s）60*2
    public static final int RUN_DELAY_TIMES_FOR_APP     = RUN_DELAY_TIMES_BASE*25;    // 延迟n秒
    //10, 获取happy图片   （延迟60*2s）60*3
    public static final int RUN_DELAY_TIMES_FOR_HAPPY   = RUN_DELAY_TIMES_BASE*30;    // 延迟n秒
    //    获取error图片   （延迟60*4s）60*4
    public static final int RUN_DELAY_TIMES_FOR_ERROR   = RUN_DELAY_TIMES_BASE*35;    // 延迟n秒

    public static final int TIMEOUT_FOR_EXPERIENCE   = RUN_DELAY_TIMES1000 * 60;    // 体验模式超时

    public static final int DEFINE_BYTE_1024 = 1024;

    public static final int MAX_DEVICE_NAME_LENGTH = 14;    // 名称规则：7个汉字或14个字符。

    public static final String PARAMS_FIRST_CONNECT = "SITTING_FIRST_CONNECT";
    public static final String PARAMS_SECOND_CONNECT = "SITTING_SECOND_CONNECT";
    public static final String PARAMS_THIRD_CONNECT = "SITTING_THIRD_CONNECT";

    //@"/sitPic.jpg" @"/sitErrorPic.jpg" @"/sitHappy.jpg"
    public static final String SITTING_PICTURE_NAME_STANDAR = "/sitPic.jpg";
    public static final String SITTING_PICTURE_NAME_ERROR = "sitErrorPic.jpg";
    public static final String SITTING_PICTURE_NAME_HAPPY = "sitHappy.jpg";
    public static final String SITTING_PICTURE_PATH_STANDAR = "/SitFile/";
    public static final String SITTING_PICTURE_PATH_ERROR = "/ErrorFile/";
    public static final String SITTING_PICTURE_PATH_HAPPY = "/HappyFile/";
    public static final String SITTING_PICTURE_PATH_STANDAR_ERR = "/StandarErr/";
    public static final String SITTING_PICTURE_NAME_STANDAR_ERROR = "/sitStandarErrorPic.jpg";
    public static final String SITTING_POSE_REPORT_PATH = "/PoseReport/";
    public static final String SITTING_ALGO_DATA_PATH = "/AlgoData/";

    public static final int MAX_OPEN_NOTIFY_RETRY_TIMES = 20;
    public static final int MAX_MTU_RETRY_TIMES = 50;
    public static final int MAX_MTU_RETRY_TIMES1 = 5;

    //升级的状态定义：
    // 0:准备升级 1：正在升级 2：下载固件 3：传输固件 5：设备正在升级 6：升级成功 7：升级失败
    public static final int STATE_NEW         = 0;    //没有升级,当前最新
    public static final int STATE_PREPARE     = 1;	//准备升级
    public static final int STATE_UPDATING    = 2;	//正在升级
    public static final int STATE_DOWNLOAD    = 3;	//下载固件
    public static final int STATE_SENDING     = 4;	//传输固件
    public static final int STATE_RESEND      = 5;	//续传固件
    public static final int STATE_REBOOT      = 6;	//设备正在升级
    public static final int STATE_UPDATE_SUCESS = 7;  //升级成功
    public static final int STATE_UPDATE_FAIL = 8;	//升级失败
    public static final int STATE_UPDATE_PC   = 9;    //无法升级（PC升级）
    public static final int STATE_READY       = 10;	//

    public static final String ADV_VID = "1b2e";
    public static final String ADV_PID = "0208";

    public static int MAX_MTU_NUMBER = 512;
    public static int MIN_MTU_NUMBER = 23;
    public static int MINUS_MTU_NUMBER = 3;
    public static final int MAX_VOLUME_NUMBER = 5;
    public static final int MIN_VOLUME_NUMBER = 1;
    public static final int MAX_LIGHT_NUMBER = 5;
    public static final int MIN_LIGHT_NUMBER = 1;
    public static final int HEAD_LENGTH_COUNT = 7;

    public static final int SITTING_MODEL_INT=0;

    public static final int SITTING_HAPPYTIME_INT = 0;

    public static final int SITTING_DEF_STANDARY_SUCESS_INT = 1;
    //2.坐姿灵敏度：适中
    public static final int SITTING_DEF_SENSITIVITY_INT = 2;
    //3.久坐时长：45分钟（可选范围：30分钟/45分钟/1小时）
    public static final int SITTING_DEF_LONG_SIT_VALUE_INT = 45;
    public static final int SITTING_DEF_LONG_SIT_INT = 1;
    //5.设备音量：3（可选范围：1-5）
    public static final int SITTING_DEF_VOLUME_INT = 3;
    //6.提示灯亮度：中，可设置激光灯亮度//字符串：// 0-5
    public static final int SITTING_DEF_LIGHT_INT = 5;
//    public static final int SITTING_DEF_LIGHT_VALUE_INT = 5;

    // 设备端返回：0， 提醒静音；1，提醒非静音
    public static final int SITTING_SILENT_INT_YES = 0;
    public static final int SITTING_SILENT_INT_NO = 1;


    // 设定：1  //参数设定：5
    //算法参数：0
    public static final String SITTING_SET_PARAMS_ALGO = "sp_param_config";
    //APP参数：1
    public static final String SITTING_SET_PARAMS_APP = "app_config";
    //系统默认配置： 2
    public static final String SITTING_SET_PARAMS_DEF = "sys_config";

    /*
    SCAN名称：包含CZUR Mirror（不区分大小写）字样的设备
    EXPORT特性数量：6
    EXPORT特性值：
    SEND：dfd4416e-1810-47f7-8248-eb8be3dc47f9
    RECV: 9884d812-1810-4a24-94d3-b2c11a851fac
    WIFI: 00009999-0000-1000-8000-00805F9B34FB(后续扩展配网用)
    SEND_PIC：0af0cac5-3808-4c06-98a7-8dd0f57c1815 (标准坐姿、表情图片)
    SEND_ERR_PIC：e40ecc6a-1e1e-45ad-8dcd-ce5b50c83d9e （错误坐姿图片）
    RECV_FW：39cab313-17d2-4d08-b5e4-b1665a3c18ab （设备固件接收）
    EXPORT服务： 0000180A-0000-1000-8000-00805F9B34FB
    */
    //9884d812-1810-4a24-94d3-b2c11a851fac
    public static final String exportUUID = "0000180a-0000-1000-8000-00805f9b34fb";

    public static final String readUUID = "dfd4416e-1810-47f7-8248-eb8be3dc47f9";
    public static final String writeUUID = "9884d812-1810-4a24-94d3-b2c11a851fac";
    public static final String wifiUUID = "00009999-0000-1000-8000-00805F9B34FB";
    public static final String readPicUUID = "0af0cac5-3808-4c06-98a7-8dd0f57c1815";    //(标准坐姿、表情图片)
    public static final String readErrPicUUID = "e40ecc6a-1e1e-45ad-8dcd-ce5b50c83d9e"; //（错误坐姿图片）
    public static final String writeFwUUID = "39cab313-17d2-4d08-b5e4-b1665a3c18ab";    //（设备固件接收）

    // 鉴权
    public static final String HiCZUR = "Hi CZUR";  // "Hi CZUR";// Hi Rockchip
    // 鉴权  Authentication 000000
    public static final String HEAD_AUTH_WRITE = "000000";  //固定字符串"Hi CZUR"
    //APP端鉴权：1
    public static final String HEAD_AUTH_READ = "000100";   //字符串：SN号
    //授权返回：2
    public static final String HEAD_AUTH_BACK = "000200";   //字符串：0：成功；1：失败
    //已绑定设备鉴权：3
    public static final String HEAD_AUTH_ALREADY = "000300";   //字符串：0：成功；1：失败
    //前次绑定设备解绑完成：4
    public static final String HEAD_AUTH_CLEAR_OK = "000400";   //字符串：0：成功；1：失败

    ////// 设定：1
    //// 音频：0
    // 音量：0
    public static final String HEAD_SETTING_VOICE_VOLUME = "010000";  //1BYTE：音量值
    public static final String HEAD_SETTING_VOICE_SWITCH = "010001";  //1BYTE：0：关；1：开
    //// 激光灯：1
    // 开关：0
    public static final String HEAD_SETTING_LASER_SWITCH = "010100";  //1BYTE：0：关；1：开
    // 亮度：1
    public static final String HEAD_SETTING_LASER_LIGHT = "010101";  //1BYTE：0-15
    //// 提醒：2
    // 灵敏度：0 sensitivity
    public static final String HEAD_SETTING_ALERT_SENSITIVITY = "010200";  //1BYTE：0：低；1：中；2：高
    // 坐姿开关：1
    public static final String HEAD_SETTING_ALERT_SITTING = "010201";  //1BYTE：0：关；1：开
    // 久坐开关：2 Sedentary
    public static final String HEAD_SETTING_ALERT_SEDENTARY = "010202";  //1BYTE：0：关；1：开
    // 久坐时长：3	RECV/SEND	字符串：分钟数值久坐时长：3	RECV/SEND	字符串：分钟数值
    public static final String HEAD_SETTING_ALERT_LONGSIT = "010203";  //分钟数值

    //// 解绑：3
    public static final String HEAD_SETTING_UNBUND = "010300";  //null
    //// 语言时区 4
    public static final String HEAD_SETTING_ZONE = "010400";   //"JSON： //dateTime：yyyy-MM-dd hh:mm:ss  //timeZone：格林威治分钟   //timeDst：分钟    //lang：依据实际语言传输"
    //// 参数设定：5
    // 算法参数：0
    public static final String HEAD_SETTING_PARAM0 = "010500";  //JSON
    // APP参数：1
    public static final String HEAD_SETTING_PARAM1 = "010501";  //JSON
    // 系统默认配置： 2
    public static final String HEAD_SETTING_PARAM2 = "010502";  //JSON
    //// 坐姿录入：6
    public static final String HEAD_SETTING_INPUT = "010600";  //1BYTE：0，初次录入；1，重新录入
    //// 设备命名：7
    public static final String HEAD_SETTING_NAME = "010700";  //可变字符串，设备名称
    //// 坐姿体验状态： 8
    // 退出体验状态：0
    public static final String HEAD_SETTING_EXPER = "010800";  //null
    //// adb后门：9	0	RECV/SEND	"字符串：
    public static final String HEAD_SETTING_ADB = "010900";  ////关闭adb：0     //打开adb：1"
    ////    进入烧写模式：10	0	RECV/SEND	NULL
    public static final String HEAD_SETTING_BURN = "010a00";  ////
    ////    设定坐姿监控模式：11	0	RECV/SEND
    public static final String HEAD_SETTING_MONITOR = "010b00";  ////字符串：0，智能模式；1，自定义模式
    ////    错误坐姿收集开关：12	0	RECV/SEND	字符串：0，关闭；1，打开
    public static final String HEAD_SETTING_SWITCH_ERROR = "010c00";  ////字符串：0，关闭；1，打开
    ////    开心图片收集开关：13	0	RECV/SEND	字符串：0，关闭；2，打开
    public static final String HEAD_SETTING_SWITCH_HAPPY = "010d00";  ////字符串：0，关闭；1，打开

    //////APP获取/设备端发送：2
    ////设备状态：0
    //"RECV / SNED"	"APP端：NULL    //设备端返回：字符串    //0：智能坐姿监控；    //1：自定义坐姿监控；"
    public static final String HEAD_APPGET_STATUS = "020000";  //null
    //// 报告：1
    // 当前报告：0
    public static final String HEAD_APPGET_REPORT0 = "020100";  //null
    // 全部未传报告：1
    public static final String HEAD_APPGET_REPORT1 = "020101";  //null
    //// 标准坐姿：2
    public static final String HEAD_APPGET_STANDER = "020200";  //null
    //// 错误坐姿：3
    public static final String HEAD_APPGET_ERROR = "020300";  //null
    //// 最开心图：4
    public static final String HEAD_APPGET_HAPPY = "020400";  //null
    //// 标准坐姿错误图：5
    public static final String HEAD_APPGET_STANDER_ERROR = "020500";  //null
    //// 历史报告发送状态：6	0	"RECV / SNED"	"APP端：NULL     //设备端返回：0， 发送完成；1，未发送完成"
    public static final String HEAD_APPGET_REPORT_HISTORY = "020600";  //null

    //// 静音状态：7 // "APP端：NULL   //设备端返回：0， 提醒静音；1，提醒非静音"
    // "APP端连接后获取；      //设备连接状态时设备端主动发送变化；"
    public static final String HEAD_APPGET_SILENT = "020700";

    //// 算法学习更新json数据：8     // 设备端发送：算法返回json数据
    // "设备连接状态设备端每次算法更新时主发；
    //设备端非连接状态设备端记录算法更新结果（最近100条数据），手机连接后补发（业务逻辑发送完成后补发）；
    //手机端转发后台"
    public static final String HEAD_APPGET_ALGO = "020800";  //algorithm


    ////// 设备端发送：3
    //// 设备状态：0
    // "JSON： monitorState：0：空闲；1：监控中"
    public static final String HEAD_DEVSEND_STATU = "030000";  //null
    //// 报告：1
    // 当前报告：0
    public static final String HEAD_DEVSEND_REPORT0 = "030100";  //报告JSON；
    // 全部未传报告：1
    public static final String HEAD_DEVSEND_REPORT1 = "030101";  //报告JSON*N(可能为0，直接返回0值)；

    ////// 设备反馈：4
    //// 配置结果：0
    public static final String HEAD_FEEDBACK_SETTING = "040000";  //"3BYTE：命令名三级参数  1BYTE：0，成功；1，失败"
    //// 坐姿录入结果：1
    public static final String HEAD_FEEDBACK_INPUT = "040100";  //"1BYTE：0，初次成功；1，重新录入成功 2，录入失败。"
    //// 退出坐姿体验:2	0	SEND	NULL
    public static final String HEAD_FEEDBACK_EXIT = "040200";  //NULL
    ////    MTU协商结果：3	0	SEND	字符串：MTU长度
    public static final String HEAD_FEEDBACK_MTU = "040300";  //字符串：MTU长度


    ////// WIFI组网：5         //暂不实装
    ////// 固件升级：6           //暂不实装
    // 固件版本查询：0	0	"RECV       //SNED"	"APP端：NULL
    public static final String HEAD_UPDATE_QUERY = "060000";  // 设备端返回：固件版本号字符串"

    // 新固件版本发送：1	0	RECV
    public static final String HEAD_UPDATE_NEW_VER = "060100";  // 新版本固件版本号

    // 固件类型：2	0	RECV
    public static final String HEAD_UPDATE_VERTYPE = "060200";  // 字符串：0, 非强制升级；1，强制升级。

    //固件发送：3	    0	RECV
    public static final String HEAD_UPDATE_SEND = "060300";  // 固件数据+MD5校验值	"暂定：初始版本都走BLE，调通后再优化安卓速度

    // 固件续发：4	0	"RECV SNED"
    public static final String HEAD_UPDATE_RESEND = "060400";  //"设备端：续发文件偏移位置   //APP：固件偏移数据+MD5校验值"

    // 固件接收完毕：5	0	SNED
    public static final String HEAD_UPDATE_RECV_OK = "060500";  //字符串：0，接收成功；1，接收数据发生错误	设备端必须保证数据接收结果被正确发送给APP一次

    // 设备开始升级：6	0	"RECV SNED"	            // "APP端下发升级开始命令   //设备端开始升级过程，BT连接将断开"
    public static final String HEAD_UPDATE_UPDATEING = "060600";  // "APP端：NULL    //设备端返回：0，开始升级；1，其他错误"

    // 设备升级结果：7	0	SNED	    //设备端必须保证固件升级结果被正确发送给APP一次
    public static final String HEAD_UPDATE_RESULT = "060700";  //字符串：0，升级成功；1，升级发生错误

    // 固件接收长度：8	0	SNED	设备端每10s回复一次
    public static final String HEAD_UPDATE_RECV_SIZE = "060800";  //字符串：当前固件已接收长度

    //取消固件升级：9	0	"RECV/SNED"	"APP: 固件升级取消
    //设备端：0， 取消成功；1，取消失败"	APP端务必监听设备端返回，因为升级过程已经进行时无法取消
    public static final String HEAD_UPDATE_CANCEL = "060900";


}
