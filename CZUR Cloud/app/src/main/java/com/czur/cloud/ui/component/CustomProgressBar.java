package com.czur.cloud.ui.component;

import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.czur.cloud.event.EventType;
import com.czur.cloud.event.OriginalProgressEvent;
import com.facebook.drawee.drawable.DrawableUtils;

import org.greenrobot.eventbus.EventBus;

public class CustomProgressBar extends Drawable {
    private final Paint mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    @Override
    protected boolean onLevelChange(int level) {
        EventBus.getDefault().post(new OriginalProgressEvent(EventType.ORIGINAL_PROGRESS,level));
        return true;
    }

    @Override
    public void setAlpha(int alpha) {

    }

    @Override
    public void draw(@NonNull Canvas canvas) {

    }

    @Override
    public void setColorFilter(@Nullable ColorFilter colorFilter) {

    }

    @Override
    public int getOpacity() {
        return DrawableUtils.getOpacityFromColor(mPaint.getColor());
    }
}