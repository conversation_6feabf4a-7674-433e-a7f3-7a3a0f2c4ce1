package com.czur.cloud.network;

import com.czur.cloud.entity.AuraMateWrongQuestionModel;
import com.czur.cloud.entity.AuraMateWrongTagModel;
import com.czur.cloud.entity.EtSummaryDayEntity;
import com.czur.cloud.entity.realm.SyncEntity;
import com.czur.cloud.model.AuraCropModel;
import com.czur.cloud.model.AuraDeviceModel;
import com.czur.cloud.model.AuraErrorSitPictureListModel;
import com.czur.cloud.model.AuraErrorSitPictureModel;
import com.czur.cloud.model.AuraFileModel;
import com.czur.cloud.model.AuraFileTotalModel;
import com.czur.cloud.model.AuraHomeFileModel;
import com.czur.cloud.model.AuraMateColorModel;
import com.czur.cloud.model.AuraMateDeviceModel;
import com.czur.cloud.model.AuraMateFilesModel;
import com.czur.cloud.model.AuraMateReportModel;
import com.czur.cloud.model.AuraMateReportModelSub;
import com.czur.cloud.model.AuraMateShareUserModel;
import com.czur.cloud.model.AuraRecordModel;
import com.czur.cloud.model.AuraResultModel;
import com.czur.cloud.model.BaiduOcrSpotWay;
import com.czur.cloud.model.BaseModel;
import com.czur.cloud.model.ChannelModel;
import com.czur.cloud.model.CropModel;
import com.czur.cloud.model.DeviceConnectModel;
import com.czur.cloud.model.EtEquipmentModel;
import com.czur.cloud.model.EtFileModel;
import com.czur.cloud.model.EtPreviewModel;
import com.czur.cloud.model.FlattenImageModel;
import com.czur.cloud.model.FwModel;
import com.czur.cloud.model.HandwritingCountModel;
import com.czur.cloud.model.IndexEquipmentModel;
import com.czur.cloud.model.MissedCallModel;
import com.czur.cloud.model.NettyModel;
import com.czur.cloud.model.NettyModelStarry;
import com.czur.cloud.model.NotificationModel;
import com.czur.cloud.model.OriginalModel;
import com.czur.cloud.model.OssModel;
import com.czur.cloud.model.PdfModel;
import com.czur.cloud.model.ShareModel;
import com.czur.cloud.model.UserDeviceModel;
import com.czur.cloud.model.UserInfoModel;
import com.czur.cloud.model.UserInfoStarryModel;
import com.czur.cloud.model.UserShareModel;
import com.czur.cloud.model.VideoTokenModel;
import com.czur.cloud.model.WrongQuestionModel;
import com.czur.cloud.model.WrongQuestionPreviewModel;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpGet;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.network.core.MiaoHttpParam;
import com.czur.cloud.network.core.MiaoHttpPath;
import com.czur.cloud.network.core.MiaoHttpPost;
import com.czur.cloud.network.core.MiaoRetry;
import com.czur.cloud.ui.mirror.comm.SittingOssTokenModel;
import com.czur.cloud.ui.mirror.model.SittingDeviceModel;
import com.czur.cloud.ui.mirror.model.SittingHappyTimePictureModel;
import com.czur.cloud.ui.mirror.model.SittingPositionModel;
import com.czur.cloud.ui.mirror.model.SittingReportModelSub;
import com.czur.cloud.ui.mirror.model.SittingSitPictureListModel;
import com.czur.cloud.ui.starry.model.StarryUserInfoModel;
import com.czur.cloud.ui.starry.network.StarryHttpManager;

import java.lang.reflect.Type;

public interface HttpServices {

    ///////// 投影仪  //////////
    //投影仪 获取netty地址
    @MiaoHttpGet("netty/node")
    void getNettyUrl(@MiaoHttpParam("u_id") String uId,
                     Class<NettyModelStarry> clazz,
                     StarryHttpManager.Callback<NettyModelStarry> callback);

    // 成者云账号获取用户信息
    @MiaoHttpPost("czur/accountInfo")
    void getStarryUserInfo(
            @MiaoHttpParam("u_id") String uId2,
            Type type,
            MiaoHttpManager.Callback<StarryUserInfoModel> callback);


    //分页
    @MiaoHttpPost("v3/aura/getAllDir")
    void getAuraMateAllDir(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("ownerId") String ownerId, @MiaoHttpParam("equipmentUID") String equipmentUID, Type type, MiaoHttpManager.CallbackNetwork<AuraMateFilesModel> callback);

    //设置用户通知开关
    @MiaoHttpPost("v3/aura/public/getToken")
    void getVideoToken(@MiaoHttpParam("uid") String u_id, @MiaoHttpParam("channelName") String channelName, Class<VideoTokenModel> clazz, MiaoHttpManager.CallbackNetwork<VideoTokenModel> callback);

    //设置用户通知开关
    @MiaoHttpPost("v3/aura/getUserNoticeSettings")
    void getNoticeSetting(@MiaoHttpParam("u_id") String u_id, @MiaoHttpParam("udid") String udid, Class<NotificationModel> clazz, MiaoHttpManager.Callback<NotificationModel> callback);

    //设置用户通知开关
    @MiaoHttpPost("v3/aura/saveUserNoticeSettings")
    void setNoticeSetting(@MiaoHttpParam("u_id") String u_id, @MiaoHttpParam("udid") String udid, @MiaoHttpParam("notification") String notification, Class<String> clazz, MiaoHttpManager.Callback<String> callback);
    //设置用户通知开关-使用通知、离线通知、新增文件通知
    @MiaoHttpPost("v3/aura/saveUserNoticeSettings")
    void setNoticeUseSetting(@MiaoHttpParam("u_id") String u_id,
                             @MiaoHttpParam("udid") String udid,
                             @MiaoHttpParam("useNotification") String useNotification,
                             Class<String> clazz,
                             MiaoHttpManager.Callback<String> callback);
    @MiaoHttpPost("v3/aura/saveUserNoticeSettings")
    void setNoticeOfflineSetting(@MiaoHttpParam("u_id") String u_id,
                             @MiaoHttpParam("udid") String udid,
                             @MiaoHttpParam("offlineNotification") String offlineNotification,
                             Class<String> clazz,
                             MiaoHttpManager.Callback<String> callback);
    @MiaoHttpPost("v3/aura/saveUserNoticeSettings")
    void setNoticeFileSetting(@MiaoHttpParam("u_id") String u_id,
                                 @MiaoHttpParam("udid") String udid,
                                 @MiaoHttpParam("newFileNotification") String newFileNotification,
                                 Class<String> clazz,
                                 MiaoHttpManager.Callback<String> callback);

    //设置用户错误坐姿查看图像的开关
    // Jason 20210125
    //设置用户通知开关
    @MiaoHttpPost("v3/aura/getUserNoticeSettings")
    void getErrorSitSetting(@MiaoHttpParam("u_id") String u_id, @MiaoHttpParam("udid") String udid, Class<NotificationModel> clazz, MiaoHttpManager.Callback<NotificationModel> callback);

    @MiaoHttpPost("v3/aura/saveDataCollection")
    //dataCollection	true	int  0:关，1:开
    void setErrorSitSetting(@MiaoHttpParam("u_id") String u_id, @MiaoHttpParam("udid") String udid, @MiaoHttpParam("dataCollection") String dataCollection, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //获取服务器时间
    @MiaoHttpPost("v3/books/serverTime")
    void getReportServerTime(@MiaoHttpParam("userId") String userId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //获取错题前后
    @MiaoHttpGet("v3/aura/getBeforeAndAfterWrongQuestionById")
    void getWrongQuestionBeforeAndAfter(@MiaoHttpParam("u_id") String u_id, @MiaoHttpParam("ownerId") String ownerId, @MiaoHttpParam("tagId") String tagId, @MiaoHttpParam("questionId") String questionId, @MiaoHttpParam("size") String size, Class<WrongQuestionPreviewModel> clazz, MiaoHttpManager.Callback<WrongQuestionPreviewModel> callback);

    //获取错题列表
    @MiaoHttpGet("v3/aura/getAllWrongQuestion")
    MiaoHttpEntity<AuraMateWrongQuestionModel> getWrongQuestionSync(@MiaoHttpParam("u_id") String u_id, @MiaoHttpParam("ownerId") String ownerId, @MiaoHttpParam("tagId") String tagId, @MiaoHttpParam("questionId") String questionId, @MiaoHttpParam("size") String size, Type type);

    //删除错题
    @MiaoHttpPost("v3/aura/deleteWrongQuestion")
    void deleteWrongQuestionQuestion(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("ownerId") String ownerId, @MiaoHttpParam("questionIdStr") String questionIdStr, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //获取错题
    @MiaoHttpPost("v3/aura/getAllWrongQuestionTag")
    void getWrongQuestion(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("ownerId") String ownerId, @MiaoHttpParam("tagId") String tagId, Type type, MiaoHttpManager.Callback<AuraMateWrongTagModel> callback);

    //添加共享用户
    @MiaoHttpGet("v3/aura/transfer/{deviceId}")
    void transferAuraMateShareUser(@MiaoHttpPath("deviceId") String deviceId, @MiaoHttpParam("u_id") String uId, @MiaoHttpParam("transferUserId") String transferUserId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //添加共享用户
    @MiaoHttpPost("v3/aura/deleteShareMember")
    void deleteAuraMateShareUser(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("equipmentUid") String equipmentUid, @MiaoHttpParam("memberId") String memberId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //添加共享用户
    @MiaoHttpPost("v3/aura/exitShareMember")
    void exitShareMember(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("equipmentUid") String equipmentUid, @MiaoHttpParam("ownerId") String ownerId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //获取共享用户列表
    //添加共享用户
    @MiaoHttpPost("v3/aura/addShareMember")
    void addAuraMateShareUser(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("equipmentUid") String equipmentUid, @MiaoHttpParam("memberId") String memberId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //获取共享用户列表
    @MiaoHttpPost("v3/aura/getAllShareMember")
    void getAuraMateShareUser(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("equipmentUid") String equipmentUid, @MiaoHttpParam("ownerId") String ownerId, Type type, MiaoHttpManager.Callback<AuraMateShareUserModel> callback);

    //新增错题库
    @MiaoHttpPost("v3/aura/addWrongQuestion")
    @MiaoRetry(false)
    void wrongQuestionCrop(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("fileId") String fileId, @MiaoHttpParam("mode") String mode, @MiaoHttpParam("width") String width,
                           @MiaoHttpParam("height") String height, @MiaoHttpParam("angle") String angle, @MiaoHttpParam("tagId") String tagId, @MiaoHttpParam("ownerId") String ownerId, @MiaoHttpParam("questionStr") String questionStr, Class<BaseModel> clazz, MiaoHttpManager.Callback<BaseModel> callback);

    //新增错题库
    @MiaoHttpPost("v3/aura/addWrongQuestion")
    @MiaoRetry(false)
    MiaoHttpEntity<BaseModel> questionCrop(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("fileId") String fileId, @MiaoHttpParam("mode") String mode, @MiaoHttpParam("width") String width,
                                           @MiaoHttpParam("height") String height, @MiaoHttpParam("angle") String angle, @MiaoHttpParam("tagId") String tagId, @MiaoHttpParam("ownerId") String ownerId, @MiaoHttpParam("questionStr") String questionStr, Class<BaseModel> clazz);

    //PDF结果
    @MiaoHttpPost("v3/aura/addWrongQuestion/result")
    MiaoHttpEntity<WrongQuestionModel> questionCropResult(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("randomKey") String randomKey, Class<WrongQuestionModel> clazz);

    //添加推送UUID信息接口
    @MiaoHttpPost("v3/aura/addPushUuid")
    void getUUID(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("type") String type, @MiaoHttpParam("bundle") String bundle, @MiaoHttpParam("platform") String platform,
                 @MiaoHttpParam("uuid") String uuid, @MiaoHttpParam("token") String token, @MiaoHttpParam("voip") String voip, @MiaoHttpParam("vendor") String vendor, @MiaoHttpParam("lang") String lang, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //y推送UUID信息接口
    @MiaoHttpPost("v3/aura/removePushUuid")
    void removeUUID(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("type") String type, @MiaoHttpParam("bundle") String bundle, @MiaoHttpParam("platform") String platform, @MiaoHttpParam("uuid") String uuid, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //获取netty地址
    @MiaoHttpGet("v3/tcp/nodes")
    void getNettyUrl(@MiaoHttpParam("u_id") String uId,
                     Class<NettyModel> clazz,
                     MiaoHttpManager.Callback<NettyModel> callback);

    //获取netty地址
    @MiaoHttpGet("netty/node")
    void getNettyStarryUrl(@MiaoHttpParam("u_id") String uId,
                     Class<NettyModel> clazz,
                     MiaoHttpManager.Callback<NettyModel> callback);


    //未接通通话列表
    @MiaoHttpPost("v3/aura/getMissedCall")
    void getMissedCall(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("orderDate") String orderDate, Type type, MiaoHttpManager.Callback<MissedCallModel> callback);

    //未接通通话列表
    @MiaoHttpPost("v3/aura/getMissedCall")
    MiaoHttpEntity<MissedCallModel> getMissedCallSync(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("orderDate") String orderDate, Type type);

    //获取用户使用报告
    @MiaoHttpPost("v3/aura/getUseReport")
    void getUserReport(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("equipmentUuid") String equipmentUid, @MiaoHttpParam("orderDate") String orderDate, Type type, MiaoHttpManager.Callback<AuraMateReportModel> callback);

    // Jason
    //获取用户使用报告
    @MiaoHttpPost("v3/aura/getAllUseReport")
    MiaoHttpEntity<AuraMateReportModel> getAllUseReport(
            @MiaoHttpParam("u_id") String uId,
            @MiaoHttpParam("equipmentUuid") String equipmentUid,
            @MiaoHttpParam("orderDate") String orderDate,
            Type type);

    //获取之前的日周月报
    @MiaoHttpPost("v3/aura/beforeUseReportList")
    MiaoHttpEntity<AuraMateReportModelSub> beforeUseReportList(
            @MiaoHttpParam("u_id") String uId,
            @MiaoHttpParam("equipmentUuid") String equipmentUid,
            @MiaoHttpParam("type") String type1,
            @MiaoHttpParam("reportId") String reportId,
            @MiaoHttpParam("size") String size,
            Type type);

    //获取之后的日周月报
    @MiaoHttpPost("v3/aura/afterUseReportList")
    MiaoHttpEntity<AuraMateReportModelSub> afterUseReportList(
            @MiaoHttpParam("u_id") String uId,
            @MiaoHttpParam("equipmentUuid") String equipmentUid,
            @MiaoHttpParam("type") String type1,
            @MiaoHttpParam("reportId") String reportId,
            @MiaoHttpParam("size") String size,
            Type type);

    //获取之前之后的日周月报
    @MiaoHttpPost("v3/aura/beforeAfterUseReports")
    MiaoHttpEntity<AuraMateReportModelSub> beforeAfterUseReports(
            @MiaoHttpParam("u_id") String uId,
            @MiaoHttpParam("equipmentUuid") String equipmentUid,
            @MiaoHttpParam("type") String type1,
            @MiaoHttpParam("reportId") String reportId,
            @MiaoHttpParam("size") String size,
            Type type);

    @MiaoHttpGet("v3/aura/color/{fileId}/{color}")
    MiaoHttpEntity<AuraMateColorModel> getAuraMateColorImage(@MiaoHttpPath("fileId") String fileId, @MiaoHttpPath("color") String color, @MiaoHttpParam("u_id") String uId, @MiaoHttpParam("ownerId") String ownerId, Class<AuraMateColorModel> clazz);

    @MiaoHttpGet("v3/aura/color/result/{fileId}/{color}")
    MiaoHttpEntity<AuraMateColorModel> getAuraMateColorImageResponse(@MiaoHttpPath("fileId") String fileId, @MiaoHttpPath("color") String color, @MiaoHttpParam("u_id") String uId, @MiaoHttpParam("ownerId") String ownerId, Class<AuraMateColorModel> clazz);

    //获取文件列表中的共享设备
    @MiaoHttpGet("v3/aura/getAuraShareDevice")
    MiaoHttpEntity<AuraMateDeviceModel> getAuraShareDeviceSync(@MiaoHttpParam("u_id") String uId, Type type);

    //获取科目
    @MiaoHttpPost("v3/aura/getAllWrongQuestionTag")
    void getWrongQuestionTag(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("equipmentUid") String equipmentUid, @MiaoHttpParam("ownerId") String ownerId, Type type, MiaoHttpManager.Callback<AuraMateWrongTagModel> callback);

    //获取科目(同步）
    @MiaoHttpPost("v3/aura/getAllWrongQuestionTag")
    MiaoHttpEntity<AuraMateWrongTagModel> getWrongQuestionTagSync(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("equipmentUid") String equipmentUid, @MiaoHttpParam("ownerId") String ownerId, Type type);

    //删除科目
    @MiaoHttpPost("v3/aura/deleteWrongQuestionTag")
    void deleteWrongQuestionTag(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("equipmentUid") String equipmentUid, @MiaoHttpParam("ownerId") String ownerId, @MiaoHttpParam("tagId") String tagId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //添加科目
    @MiaoHttpPost("v3/aura/addWrongQuestionTag")
    void addWrongQuestionTag(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("equipmentUid") String equipmentUid, @MiaoHttpParam("ownerId") String ownerId, @MiaoHttpParam("tagName") String tagName, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //获取pdf列表
    @MiaoHttpPost("v3/aura/pdf/time")
    MiaoHttpEntity<PdfModel> auraPdfListSync(@MiaoHttpParam("u_id") String uId, Type type);

    //pdf删除
    @MiaoHttpPost("v3/aura/pdf/delete")
    @MiaoRetry(false)
    void auraPdfDelete(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("fileIds") String fileIds, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //生成PDF
    @MiaoHttpPost("v3/aura/pdf/generate")
    @MiaoRetry(false)
    MiaoHttpEntity<PdfModel> auraPdf(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("ossKeys") String ossKeys, @MiaoHttpParam("fileName") String fileName, @MiaoHttpParam("ownerId") String ownerId, Class<PdfModel> clazz);

    //PDF结果
    @MiaoHttpPost("v3/aura/pdf/generate/result")
    MiaoHttpEntity<PdfModel> auraPdfResult(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("id") String pdfId, @MiaoHttpParam("randomKey") String randomKey, @MiaoHttpParam("ownerId") String ownerId, Class<PdfModel> clazz);

    //aurahome图片分享
    @MiaoHttpPost("v3/aura/share")
    void auraShare(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("fileKey") String fileKey, Class<ShareModel> clazz, MiaoHttpManager.Callback<ShareModel> callback);

    //重命名Aura文件夹
    @MiaoHttpPost("v1/account/register/device")
    void postAuraHomeUUID(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("token") String token, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //删除Aura文件夹
    @MiaoHttpPost("v3/aura/delete")
    void deleteAuraFolder(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("fileIds") String fileIds, @MiaoHttpParam("dirIds") String dirId, @MiaoHttpParam("ownerId") String ownerId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //移动Aura文件夹
    @MiaoHttpPost("v3/aura/move")
    void moveAuraFolder(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("fileIds") String fileIds, @MiaoHttpParam("dirId") String dirId, @MiaoHttpParam("ownerId") String ownerId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //重命名Aura文件夹
    @MiaoHttpPost("v3/aura/dir/rename")
    void changeAuraFolderName(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("dirId") String dirId, @MiaoHttpParam("name") String name, @MiaoHttpParam("ownerId") String ownerId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //创建Aura文件夹
    @MiaoHttpPost("v3/aura/dir/create")
    void createAuraFolder(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("equipmentUid") String equipmentUid, @MiaoHttpParam("name") String name, @MiaoHttpParam("ownerId") String ownerId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //分页
    @MiaoHttpGet("v3/aura/category/latest/paging/{equUID}/{dirId}/{seq}/{size}/{type}")
    void getAuraPage(@MiaoHttpPath("equUID") String equUID, @MiaoHttpPath("dirId") String dirId, @MiaoHttpPath("seq") String seq, @MiaoHttpPath("size") String size, @MiaoHttpPath("type") String type, @MiaoHttpParam("u_id") String uId, @MiaoHttpParam("ownerId") String ownerId, Class<AuraHomeFileModel> clazz, MiaoHttpManager.CallbackNetwork<AuraHomeFileModel> callback);

    @MiaoHttpGet("v3/aura/category/latest/paging/{equUID}/{dirId}/{seq}/{size}/{type}")
    MiaoHttpEntity<AuraHomeFileModel> getAuraHomeFileSync(@MiaoHttpPath("equUID") String equUID, @MiaoHttpPath("dirId") String dirId, @MiaoHttpPath("seq") String seq, @MiaoHttpPath("size") String size, @MiaoHttpPath("type") String type, @MiaoHttpParam("u_id") String uId, @MiaoHttpParam("ownerId") String ownerId, Class<AuraHomeFileModel> clazz);

    @MiaoHttpGet("v3/aura/category/after/{equipmentUID}/{dirId}/{seqNum}/{size}")
    MiaoHttpEntity<AuraHomeFileModel> getAuraMateMoreFileSync(@MiaoHttpPath("equipmentUID") String equipmentUID, @MiaoHttpPath("dirId") String dirId, @MiaoHttpPath("seq") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String uId, Class<AuraHomeFileModel> clazz);

    //坐姿监控信息接口
    @MiaoHttpGet("v3/aura/getConfig")
    void getSigetMissedCallttingPositionInfo(@MiaoHttpParam("u_id") String uId2, @MiaoHttpParam("equipmentUID") String equipmentUID, Class<SittingPositionModel> clazz, MiaoHttpManager.Callback<SittingPositionModel> callback);

    //坐姿提醒配置
    @MiaoHttpGet("v3/aura/config")
    void sittingPositionSetting(@MiaoHttpParam("u_id") String uId2, @MiaoHttpParam("equipmentUID") String equipmentUID, @MiaoHttpParam("status") String isOpenSittingPosition, @MiaoHttpParam("level") String level, @MiaoHttpParam("standPositionImage") String standPositionImage, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //获取之后Aura文件
    @MiaoHttpGet("v3/aura/category/after/{equipmentUID}/{dirId}/{seqNum}/{size}")
    void getAfterAuraFiles(@MiaoHttpPath("equipmentUID") String equipmentUID, @MiaoHttpPath("dirId") String dirId, @MiaoHttpPath("seqNum") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String uId, Type type, MiaoHttpManager.Callback<AuraFileModel> callback);

    //获取之后Aura文件
    @MiaoHttpGet("v3/aura/category/after/{equipmentUID}/{dirId}/{seqNum}/{size}")
    MiaoHttpEntity<AuraFileModel> getAfterAuraFilesSync(@MiaoHttpPath("equipmentUID") String equipmentUID, @MiaoHttpPath("dirId") String dirId, @MiaoHttpPath("seqNum") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String uId, Type type);


    //获取之前Aura文件
    @MiaoHttpGet("v3/aura/category/before/{equipmentUID}/{dirId}/{seqNum}/{size}")
    void getBeforeAuraFiles(@MiaoHttpPath("equipmentUID") String equipmentUID, @MiaoHttpPath("dirId") String dirId, @MiaoHttpPath("seqNum") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String uId, Type type, MiaoHttpManager.Callback<AuraFileModel> callback);

    //获取Aura文件
    @MiaoHttpGet("v3/aura/category/beforeandafter/{equipmentUID}/{dirId}/{seqNum}/{size}")
    void getAuraFiles(@MiaoHttpPath("equipmentUID") String equipmentUID, @MiaoHttpPath("dirId") String dirId, @MiaoHttpPath("seqNum") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpParam("ownerId") String ownerId, @MiaoHttpParam("u_id") String uId, Class<AuraFileTotalModel> clazz, MiaoHttpManager.Callback<AuraFileTotalModel> callback);


    //Aura图片编辑
    @MiaoHttpPost("v3/aura/cut")
    @MiaoRetry(false)
    void auraCrop(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("fileId") String fileId, @MiaoHttpParam("mode") String mode, @MiaoHttpParam("width") String width,
                  @MiaoHttpParam("height") String height, @MiaoHttpParam("angle") String angle, @MiaoHttpParam("cutX") String cutX, @MiaoHttpParam("cutY") String cutY, @MiaoHttpParam("cutWidth") String cutWidth,
                  @MiaoHttpParam("cutHeight") String cutHeight, @MiaoHttpParam("ownerId") String ownerId, Class<AuraCropModel> clazz, MiaoHttpManager.Callback<AuraCropModel> callback);

    //轮询获取图像处理结果
    @MiaoHttpGet("v3/aura/flatten/{fileId}/{mode}")
    void getAuraFlatten(@MiaoHttpPath("fileId") String fileId, @MiaoHttpPath("mode") String mode, @MiaoHttpParam("u_id") String uId, Class<AuraResultModel> clazz, MiaoHttpManager.Callback<AuraResultModel> callback);

    //轮询获取图像处理结果
    @MiaoHttpGet("v3/aura/flatten/{fileId}/{mode}")
    MiaoHttpEntity<AuraResultModel> getAuraFlattenSync(@MiaoHttpPath("fileId") String fileId, @MiaoHttpPath("mode") String mode, @MiaoHttpParam("u_id") String uId, Class<AuraResultModel> clazz);

    //轮询获取图像处理结果
    @MiaoHttpGet("v3/aura/flatten/result/{fileId}/{mode}")
    void getAuraFlattenResult(@MiaoHttpPath("fileId") String fileId, @MiaoHttpPath("mode") String mode, @MiaoHttpParam("u_id") String uId, Class<AuraResultModel> clazz, MiaoHttpManager.Callback<AuraResultModel> callback);

    //获取之后Aura文件
    @MiaoHttpGet("v3/aura/flatten/result/{fileId}/{mode}")
    MiaoHttpEntity<AuraResultModel> getAuraSyncFlattenResult(@MiaoHttpPath("fileId") String fileId, @MiaoHttpPath("mode") String mode, @MiaoHttpParam("u_id") String uId, Class<AuraResultModel> clazz);

    //修改Aura设备别名
    @MiaoHttpPost("v3/aura/set/alias/{deviceId}")
    void changeAuraName(@MiaoHttpPath("deviceId") String deviceId, @MiaoHttpParam("u_id") String uId, @MiaoHttpParam("alias") String alias, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //获取可用设备列表
    @MiaoHttpGet("v3/aura/list")
    void getAuraDevices(@MiaoHttpParam("u_id") String uId2, Type type, MiaoHttpManager.Callback<AuraDeviceModel> callback);

    //获取可用设备列表
    @MiaoHttpGet("v3/aura/list")
    MiaoHttpEntity<AuraDeviceModel> getAuraDevicesSync(@MiaoHttpParam("u_id") String uId2, Type type);

    //设备入网绑定前检查
    @MiaoHttpPost("v3/aura/key")
    void getAuraKey(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("udid") String udid, Class<String> clazz, MiaoHttpManager.CallbackNetwork<String> callback);

    //设备入网绑定前检查
    @MiaoHttpGet("v3/aura/unbind/{deviceId}")
    void unbindAuraMate(@MiaoHttpParam("u_id") String uId, @MiaoHttpPath("deviceId") String deviceId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //检查FW版本是否需要更新
    @MiaoHttpGet("v1/device/checkFw")
    MiaoHttpEntity<FwModel> checkFwUpdate(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("deviceId") String deviceId, Class<FwModel> clazz);

    //获取channel
    @MiaoHttpGet("v3/endpoint")
    void channel(Class<ChannelModel> clazz, MiaoHttpManager.Callback<ChannelModel> callback);

    //【笔记本】getFileSize
    @MiaoHttpPost("v3/books/v3/books/getFileSize")
    MiaoHttpEntity<String> getFileSize(@MiaoHttpParam("u_id") String userId, @MiaoHttpParam("request") String request, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //获取app上传oss token
    @MiaoHttpPost("v3/saomiao/apptoken")
    MiaoHttpEntity<OssModel> getOssInfo(@MiaoHttpParam("clientId") String clientId, Class<OssModel> clazz);

    //【笔记本】根据用户id删除所有本和页
    @MiaoHttpPost("v3/books/deleteAll")
    MiaoHttpEntity<String> deleteServerBooks(@MiaoHttpParam("u_id") String userId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //【笔记本】数据同步接口
    @MiaoHttpPost("v3/books/synchronous")
    MiaoHttpEntity<String> syncDataToServer(@MiaoHttpParam("synJson") String synJson, Class<String> clazz);

    //【笔记本】数据同步接口
    @MiaoHttpPost("v3/books/synchronous")
    void syncDataToServer(@MiaoHttpParam("synJson") String synJson, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //按时间（可选）获取文件列表
    @MiaoHttpPost("v3/books/getByTime")
    MiaoHttpEntity<SyncEntity> getFileByTime(@MiaoHttpParam("u_id") String userId, @MiaoHttpParam("synTime") String synTime, Class<SyncEntity> clazz);

    //获取服务器时间
    @MiaoHttpPost("v3/books/serverTime")
    MiaoHttpEntity<String> getServerTime(@MiaoHttpParam("userId") String userId, Class<String> clazz);

    //笔记本手写体识别次数扣除
    @MiaoHttpPost("v3/books/charge")
    void handwritingCharge(@MiaoHttpParam("userId") String userId, Class<HandwritingCountModel> clazz, MiaoHttpManager.Callback<HandwritingCountModel> callback);

    //笔记本手写体识别次数充值
    @MiaoHttpPost("v3/books/recharge")
    void handwritingRecharge(@MiaoHttpParam("userId") String userId, @MiaoHttpParam("code") String code, Class<HandwritingCountModel> clazz, MiaoHttpManager.Callback<HandwritingCountModel> callback);

    //检查是否提示过用户免费获得50次手写体识别功能次数
    @MiaoHttpPost("v3/books/check/notice")
    void checkNoticeHandwriting(@MiaoHttpParam("userId") String userId, Class<HandwritingCountModel> clazz, MiaoHttpManager.Callback<HandwritingCountModel> callback);

    //获取笔记本手写体识别次数
    @MiaoHttpPost("v3/books/getByUid")
    void getHandwritingCount(@MiaoHttpParam("userId") String userId, Class<HandwritingCountModel> clazz, MiaoHttpManager.Callback<HandwritingCountModel> callback);

    //获取所有微联功能列表
    @MiaoHttpGet("v3/mini/getAllFunc")
    void getEquipmentList(@MiaoHttpParam("userId") String userId, Type type, MiaoHttpManager.Callback<IndexEquipmentModel> callback);

    //添加微联功能列表
    @MiaoHttpPost("v3/mini/addFunc")
    void addEquipment(@MiaoHttpParam("userId") String userId, @MiaoHttpParam("equipName") String equipName, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //移除微联功能列表
    @MiaoHttpPost("v3/mini/delFunc")
    void removeEquipment(@MiaoHttpParam("userId") String userId, @MiaoHttpParam("equipName") String equipName, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //    // 用户信息
    @MiaoHttpGet("v1/user/info")
    MiaoHttpEntity<UserInfoModel> userInfo(@MiaoHttpParam("u_id") String uId2, Class<UserInfoModel> clazz);

    //  Starry用户信息
    @MiaoHttpPost("account/getPersonalInfo")
    void getStarryUserInfo(@MiaoHttpParam("u_id") String uId2,
                           Class<UserInfoStarryModel> clazz,
                           MiaoHttpManager.Callback<UserInfoStarryModel> callback);

    @MiaoHttpGet("v1/user/info")
    void userInfo(@MiaoHttpParam("u_id") String uId2, Class<UserInfoModel> clazz, MiaoHttpManager.Callback<UserInfoModel> callback);

    //按文件 最新文件
    @MiaoHttpGet("v1/file/category/latest/{folderId}/{size}")
    void getEtFiles(@MiaoHttpPath("folderId") String folderId, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String uId, Class<EtFileModel> clazz, MiaoHttpManager.Callback<EtFileModel> callback);

    //按文件 最新文件Sync
    @MiaoHttpGet("v1/file/category/latest/{folderId}/{size}")
    MiaoHttpEntity<EtFileModel> getEtFileSync(@MiaoHttpPath("folderId") String folderId, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String uId, Class<EtFileModel> clazz);


    //按文件的日期查询数据
    @MiaoHttpGet("v1/file/day/query")
    MiaoHttpEntity<EtFileModel.FilesBean> getEtFileByDaySync(@MiaoHttpParam("day") String day, @MiaoHttpParam("size") String size, @MiaoHttpParam("u_id") String uId, Type type);

    // 按日期视图获取某个seqnum后N条（按天倒序，天内正序）
    @MiaoHttpGet("v1/file/day/query")
    MiaoHttpEntity<EtFileModel.FilesBean> getFilesLoadMoreByDay(@MiaoHttpParam("day") String day, @MiaoHttpParam("seqNum") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String uId,
                                                                Type type);

    // 按文件 向上刷新
    @MiaoHttpGet("v1/file/category/after/{folderId}/{seqNum}/{size}")
    void afterGetFilesInFolder(@MiaoHttpPath("folderId") String folderId, @MiaoHttpPath("seqNum") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String uId,
                               Type type, MiaoHttpManager.Callback<EtFileModel.FilesBean> callback);

    // 按文件 向上刷新(同步)
    @MiaoHttpGet("v1/file/category/after/{folderId}/{seqNum}/{size}")
    MiaoHttpEntity<EtFileModel.FilesBean> getFilesLoadMore(@MiaoHttpPath("folderId") String folderId, @MiaoHttpPath("seqNum") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String uId,
                                                           Type type);


    //ET一级列表
    @MiaoHttpGet("v1/file/category/latest/paging/{seqNum}/{size}/{type}")
    void getEtFolderAndFiles(@MiaoHttpPath("seqNum") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String uId,
                             Type type, MiaoHttpManager.Callback<EtFileModel> callback);

    //ET一级列表
    @MiaoHttpGet("v1/file/category/latest/paging/{seqNum}/{size}/{type}")
    MiaoHttpEntity<EtFileModel> getEtFolderAndFilesSync(@MiaoHttpPath("seqNum") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpPath("type") String kind, @MiaoHttpParam("u_id") String uId,
                                                        Type type);

    //获取文件的日期统计数据
    @MiaoHttpGet("v1/file/summary/day")
    MiaoHttpEntity<EtSummaryDayEntity> getEtSummaryDaySync(@MiaoHttpParam("u_id") String uId, Type type);

    //按日期视图初始一级列表（按天倒序，天内正序）
    @MiaoHttpGet("v1/file/time/day/new/{size}")
    MiaoHttpEntity<EtFileModel.FilesBean> getFileByTime(@MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String uId, Type type);


    //按日期视图获取某个seqnum后N条（按天倒序，天内正序）
    @MiaoHttpGet("v1/file/time/day/after/{size}")
    MiaoHttpEntity<EtFileModel.FilesBean> getFileByTimeAfter(@MiaoHttpParam("seqNum") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String userId, Type type);

    //按时间下拉刷新
    @MiaoHttpGet("v1/file/time/before/{seqNum}/{size}")
    void atTimeBeforeFiles(@MiaoHttpPath("seqNum") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String userId, Type type, MiaoHttpManager.Callback<EtFileModel.FilesBean> callback);

    //按时间上拉刷新
    @MiaoHttpGet("v1/file/time/after/{seqNum}/{size}")
    void atTimeAfterFiles(@MiaoHttpPath("seqNum") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String userId, Type type, MiaoHttpManager.Callback<EtFileModel.FilesBean> callback);

    @MiaoHttpGet("v1/file/flatten/{fileId}/{mode}")
    MiaoHttpEntity<FlattenImageModel> getFlattenImage(@MiaoHttpPath("fileId") String fileId, @MiaoHttpPath("mode") String mode, @MiaoHttpParam("u_id") String uId, Class<FlattenImageModel> clazz);

    @MiaoHttpGet("v1/file/flatten/result/{fileId}/{mode}")
    MiaoHttpEntity<FlattenImageModel> getFlattenImageResponse(@MiaoHttpPath("fileId") String fileId, @MiaoHttpPath("mode") String mode, @MiaoHttpParam("u_id") String uId, Class<FlattenImageModel> clazz);

    @MiaoHttpGet("v1/file/color/{fileId}/{color}")
    MiaoHttpEntity<FlattenImageModel> getColorImage(@MiaoHttpPath("fileId") String fileId, @MiaoHttpPath("color") String color, @MiaoHttpParam("u_id") String uId, Class<FlattenImageModel> clazz);

    @MiaoHttpGet("v1/file/color/result/{fileId}/{color}")
    MiaoHttpEntity<FlattenImageModel> getColorImageResponse(@MiaoHttpPath("fileId") String fileId, @MiaoHttpPath("color") String color, @MiaoHttpParam("u_id") String uId, Class<FlattenImageModel> clazz);


    // 显示原图
    @MiaoHttpGet("v1/file/picture/{fileId}/{type}/{mode}")
    void getOriginalImage(@MiaoHttpPath("fileId") String fileId, @MiaoHttpPath("type") String type, @MiaoHttpPath("mode") String mode, @MiaoHttpParam("u_id") String uId, Class<OriginalModel> clazz, MiaoHttpManager.Callback<OriginalModel> callback);


    //批量删除设备的分享用户
    @MiaoHttpPost("v1/device/delete/users")
    void deleteShareUsers(@MiaoHttpParam("u_id") String userId, @MiaoHttpParam("deviceId") String deviceId, @MiaoHttpParam("userIds") String userIds, Type type, MiaoHttpManager.Callback<UserInfoModel> callback);

    //获取设备列表
    @MiaoHttpGet("v1/device/list")
    void getDevice(@MiaoHttpParam("u_id") String uId, Type type, MiaoHttpManager.Callback<EtEquipmentModel> callback);

    //获取设备列表
    @MiaoHttpGet("v1/device/list")
    MiaoHttpEntity<EtEquipmentModel> getDeviceSync(@MiaoHttpParam("u_id") String uId, Type type);

    //获取设备分享用户列表
    @MiaoHttpGet("v1/device/user/list/{deviceId}")
    void getShareUser(@MiaoHttpPath("deviceId") String deviceId, @MiaoHttpParam("u_id") String userId, Type type, MiaoHttpManager.Callback<UserShareModel> callback);

    //添加设备分享用户
    @MiaoHttpGet("v1/device/add/user/{deviceId}/{userId}")
    void addDeviceShareUser(@MiaoHttpPath("deviceId") String deviceId, @MiaoHttpPath("userId") String userId, @MiaoHttpParam("u_id") String uId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    // 删除设备分享用户
    @MiaoHttpGet("v1/device/delete/user/{deviceId}/{userId}")
    void removeDeviceShareUser(@MiaoHttpPath("deviceId") String deviceId, @MiaoHttpPath("userId") String userId, @MiaoHttpParam("u_id") String uId, Class<BaseModel> clazz, MiaoHttpManager.Callback<BaseModel> callback);

    // 修改设备名称
    @MiaoHttpPost("v1/device/set/alias/{deviceId}")
    void changerDeviceName(@MiaoHttpPath("deviceId") String deviceId, @MiaoHttpParam("u_id") String uId, @MiaoHttpParam("alias") String alias, Class<BaseModel> clazz, MiaoHttpManager.Callback<BaseModel> callback);

    // 设备转让
    @MiaoHttpGet("v1/device/transfer/user/{deviceId}/{userId}")
    void transferDevice(@MiaoHttpPath("deviceId") String deviceId, @MiaoHttpPath("userId") String userId, @MiaoHttpParam("u_id") String uId, Class<BaseModel> clazz, MiaoHttpManager.Callback<BaseModel> callback);

    // 解绑设备
    @MiaoHttpGet("v1/device/unbind/{deviceId}")
    void unbindDevice(@MiaoHttpPath("deviceId") String deviceId, @MiaoHttpParam("u_id") String uId, Class<BaseModel> clazz, MiaoHttpManager.Callback<BaseModel> callback);

    //
    // 申请使用
    @MiaoHttpGet("v1/device/apply/using/{deviceId}")
    void applyDevice(@MiaoHttpPath("deviceId") String deviceId, @MiaoHttpParam("u_id") String uId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);


    //按文件 最新文件
    @MiaoHttpGet("v1/file/category/latest/{folderId}/{size}")
    void classifyGetFile(@MiaoHttpPath("folderId") String folderId, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String uId, Class<EtFileModel> clazz, MiaoHttpManager.Callback<EtFileModel> callback);

    @MiaoHttpPost("v2/pdf/generate")
    @MiaoRetry(false)
    MiaoHttpEntity<PdfModel> pdf(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("ossKeys") String ossKeys, @MiaoHttpParam("fileName") String fileName, @MiaoHttpParam("pdfType") String pdfType, @MiaoHttpParam("qualityType") String qualityType, @MiaoHttpParam("horizontal") String horizontal, Class<PdfModel> clazz);

    //PDF结果
    @MiaoHttpPost("v2/pdf/generate/result")
    MiaoHttpEntity<PdfModel> pdfResult(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("id") String pdfId, @MiaoHttpParam("randomKey") String randomKey, Class<PdfModel> clazz);

    // 分享
    @MiaoHttpPost("v1/file/share")
    MiaoHttpEntity<ShareModel> share(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("fileKey") String fileKey, Class<ShareModel> clazz);

    @MiaoHttpPost("v1/file/share")
    void share(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("fileKey") String fileKey, Class<ShareModel> clazz, MiaoHttpManager.Callback<ShareModel> callback);

    //（按时间）点击图片 获取之前和之后的图片
    @MiaoHttpGet("v1/file/time/day/beforeandafter/{size}")
    void clickFile(@MiaoHttpParam("seqNum") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String uId,
                   Class<EtPreviewModel> clazz, MiaoHttpManager.Callback<EtPreviewModel> callback);

    //（按分类）点击图片 获取之前和之后的图片
    @MiaoHttpGet("v1/file/category/beforeandafter/{folderId}/{seqNum}/{size}")
    void clickFileAtCategory(@MiaoHttpPath("folderId") String folderId, @MiaoHttpPath("seqNum") String seqNum, @MiaoHttpPath("size") String size, @MiaoHttpParam("u_id") String uId,
                             Class<EtPreviewModel> clazz, MiaoHttpManager.Callback<EtPreviewModel> callback);
//
//    //获取ocr语言
//    @MiaoHttpGet("v1/file/ocr/language/{fileId}")
//    void getOcrLanguage(@MiaoHttpPath("fileId") String fileId, @MiaoHttpParam("u_id") String uId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);
//

    //设备入网绑定前检查
    @MiaoHttpPost("v1/device/network")
    void deviceBindInspect(@MiaoHttpParam("s_num") String s_num, Class<UserInfoModel> clazz, MiaoHttpManager.Callback<UserInfoModel> callback);

    //设备绑定（共享）
    @MiaoHttpPost("v1/device/pbind")
    void deviceBindPublicBind(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("s_num") String sNum, Class<UserDeviceModel> clazz, MiaoHttpManager.Callback<UserDeviceModel> callback);

    //设备绑定
    @MiaoHttpPost("v1/device/bind")
    void deviceBind(@MiaoHttpParam("u_id") String uid, @MiaoHttpParam("s_num") String sNum, Class<UserDeviceModel> clazz, MiaoHttpManager.Callback<UserDeviceModel> callback);

    //设备共享开关
    @MiaoHttpPost("v1/device/ispublic")
    void publicDevice(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("deviceId") String deviceId, @MiaoHttpParam("switchPublic") String switchPublic, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //
//    //设备联网中（mp3下载）
////    @MiaoHttpPost("device/network/audio")
////    void connectDevice(@MiaoHttpParam("s_num") String sNum, @MiaoHttpParam("ssid") String ssid, @MiaoHttpParam("password") String pwd,
////                       Class<DeviceConnectEntity> clazz, MiaoHttpManager.Callback<DeviceConnectEntity> callback);
//
    //生成音频文件
    @MiaoHttpPost("v1/device/network/audio/{deviceId}")
    MiaoHttpEntity<DeviceConnectModel> connectDevice(@MiaoHttpPath("deviceId") String deviceId, @MiaoHttpParam("ssid") String ssid,
                                                     @MiaoHttpParam("password") String pwd, Class<DeviceConnectModel> clazz);


    // 判断设备是否联网成功
    @MiaoHttpPost("v1/device/network/ok/{deviceId}")
    void deviceNetwork(@MiaoHttpPath("deviceId") String s_num, @MiaoHttpParam("createTime") String createTime, Class<String> clazz, MiaoHttpManager.Callback<String> callback);


    //判断设备是否联网成功（同步）
    @MiaoHttpPost("v1/device/network/ok/{deviceId}")
    MiaoHttpEntity<String> deviceOk(@MiaoHttpPath("deviceId") String deviceId, @MiaoHttpParam("createTime") String createTime, Class<String> clazz);

    //图片编辑
    @MiaoHttpPost("v1/file/cut")
    @MiaoRetry(false)
    void mCrop(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("fileId") String fileId, @MiaoHttpParam("type") String type, @MiaoHttpParam("mode") String mode, @MiaoHttpParam("width") String width,
               @MiaoHttpParam("height") String height, @MiaoHttpParam("angle") String angle, @MiaoHttpParam("cutX") String cutX, @MiaoHttpParam("cutY") String cutY, @MiaoHttpParam("cutWidth") String cutWidth,
               @MiaoHttpParam("cutHeight") String cutHeight, Class<CropModel> clazz, MiaoHttpManager.Callback<CropModel> callback);

    //v1.3
    //创建文件夹
    @MiaoHttpPost("v1/file/dir/create")
    void createFolder(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("name") String name, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //删除文件夹
    @MiaoHttpPost("v1/file/delete")
    @MiaoRetry(false)
    void deleteFiles(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("fileIds") String fileIds, @MiaoHttpParam("dirIds") String dirIds, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //重命名文件夹
    @MiaoHttpPost("v1/file/dir/rename")
    void fileRename(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("dirId") String dirId, @MiaoHttpParam("name") String name, Class<String> clazz, MiaoHttpManager.Callback<String> callback);


    //移动文件到文件夹
    @MiaoHttpPost("v1/file/move")
    void fileMove(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("fileIds") String fileIds, @MiaoHttpParam("dirId") String dirId, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //获取pdf列表
    @MiaoHttpPost("v2/pdf/time")
    void pdfList(@MiaoHttpParam("u_id") String uId, Type type, MiaoHttpManager.Callback<PdfModel> callback);

    //获取pdf列表
    @MiaoHttpPost("v2/pdf/time")
    MiaoHttpEntity<PdfModel> pdfListSync(@MiaoHttpParam("u_id") String uId, Type type);

    //pdf删除
    @MiaoHttpPost("v2/pdf/delete")
    @MiaoRetry(false)
    void pdfDelete(@MiaoHttpParam("u_id") String uId, @MiaoHttpParam("fileIds") String fileIds, Class<String> clazz, MiaoHttpManager.Callback<String> callback);

    //百度Ocr识别精确度选取
    @MiaoHttpPost("v1/user/getSpotWay")
    MiaoHttpEntity<BaiduOcrSpotWay> getSpotWaySync(@MiaoHttpParam("u_id") String uId, Class<BaiduOcrSpotWay> clazz);

    //获取30秒内是否有通话
    //Jason20210126
    @MiaoHttpPost("v3/aura/recent/missedCall")
    MiaoHttpEntity<MissedCallModel>  getMissedCall30s(@MiaoHttpParam("u_id") String userId,
                                                      Type type);

    //获取日报数据错误坐姿图片
    @MiaoHttpPost("v3/aura/report/error/after")
    MiaoHttpEntity<AuraErrorSitPictureModel> getAuraErrorSitPictureAfter(
            @MiaoHttpParam("u_id") String u_id,
            @MiaoHttpParam("udid") String udid,
            @MiaoHttpParam("dateString") String dateString,
            @MiaoHttpParam("offsetId") String offsetId,
            @MiaoHttpParam("relationId") String relationId,
            Type type);

    @MiaoHttpPost("v3/aura/report/error/before")
    MiaoHttpEntity<AuraErrorSitPictureModel> getAuraErrorSitPictureBefore(
            @MiaoHttpParam("u_id") String u_id,
            @MiaoHttpParam("udid") String udid,
            @MiaoHttpParam("dateString") String dateString,
            @MiaoHttpParam("offsetId") String offsetId,
            @MiaoHttpParam("relationId") String relationId,
            Type type);


    //获取日报数据错误坐姿图片
    @MiaoHttpPost("v3/aura/report/error/beforeafter")
    MiaoHttpEntity<AuraErrorSitPictureListModel> getAuraErrorSitPictureBeforeAfter(
            @MiaoHttpParam("u_id") String u_id,
            @MiaoHttpParam("udid") String udid,
            @MiaoHttpParam("dateString") String dateString,
            @MiaoHttpParam("offsetId") String offsetId,
            @MiaoHttpParam("relationId") String relationId,
            Type type);

    //删除错误图片
    @MiaoHttpPost("v3/aura/report/error/delete")
    void deleteAuraErrorSitPicture(
            @MiaoHttpParam("u_id") String u_id,
            @MiaoHttpParam("relationId") String relationId,
            @MiaoHttpParam("dataIds") String dataId,
            Class<String> clazz,
            MiaoHttpManager.Callback<String> callback);

    // 反馈错误坐姿识别错误
    //{   "code":1000,
    //    "msg":"Success",
    //    "body":null }
    @MiaoHttpPost("v3/aura/report/error/feedback")
    void feedbackAuraErrorSit(
            @MiaoHttpParam("u_id") String u_id,             // 用户ID
            @MiaoHttpParam("dataId") String dataId,         // 图片ID
            @MiaoHttpParam("relationId") String relationId, // 图片关系ID
            Class<String> clazz,
            MiaoHttpManager.Callback<String> callback);

    // 获取错误坐姿对应的标准坐姿
    // {  "code":1000,
    //    "msg":"Success",
    //    "body":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/3162/CET15A2001P00003/posture/197f54cc-d5da-4105-93cb-4516f7984f4c.jpg?Expires=1614249162&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=fE6wE3qhhvrm0dmlWyLdYHDaY78%3D" }
    @MiaoHttpPost("v3/aura/report/error/standard")
    MiaoHttpEntity<String> getStandarSitPicture(
            @MiaoHttpParam("u_id") String u_id,             // 用户ID
            @MiaoHttpParam("dataId") String dataId,         // 图片ID
            @MiaoHttpParam("relationId") String relationId, // 图片关系ID
            Class<String> clazz);

    ///////// 坐姿仪  //////////
    //获取可用设备列表
    @MiaoHttpPost("v3/mirror/device/list")
    void getSittingDevices(@MiaoHttpParam("u_id") String uId2,
                           Type type,
                           MiaoHttpManager.Callback<SittingDeviceModel> callback);

    //设备绑定
    @MiaoHttpPost("v3/mirror/bind")
    void bindSittingDevice(@MiaoHttpParam("u_id") String uid,
                           @MiaoHttpParam("equipmentUuid") String equipmentUuid,
                           @MiaoHttpParam("mac") String mac,
                           Class<String> clazz,
                           MiaoHttpManager.Callback<String> callback);

    //Mirror设备解绑
    @MiaoHttpPost("v3/mirror/unbind")
    void unbindSitting(@MiaoHttpParam("u_id") String uId,
                       @MiaoHttpParam("equipmentUuid") String equipmentUuid,
                       @MiaoHttpParam("mac") String mac,
                       Class<String> clazz,
                       MiaoHttpManager.Callback<String> callback);

    //设备设置（音量 坐姿灵敏度 久坐时长）
    @MiaoHttpPost("v3/mirror/config/set")
    void setSittingDeviceLevel(@MiaoHttpParam("u_id") String u_id,
                               @MiaoHttpParam("equipmentUID") String equipmentUID,
                               @MiaoHttpParam("level") String level,     //integer 坐姿识别灵敏度 0 - 慢 1 - 适中 2 - 快
                               Class<String> clazz,
                               MiaoHttpManager.Callback<String> callback);
    @MiaoHttpPost("v3/mirror/config/set")
    void setSittingDeviceStatus(@MiaoHttpParam("u_id") String u_id,
                                @MiaoHttpParam("equipmentUID") String equipmentUID,
                                @MiaoHttpParam("status") String status,   //boolean	是否开启坐姿提醒
                                Class<String> clazz,
                                MiaoHttpManager.Callback<String> callback);
    @MiaoHttpPost("v3/mirror/config/set")
    void setSittingDeviceSound(@MiaoHttpParam("u_id") String u_id,
                               @MiaoHttpParam("equipmentUID") String equipmentUID,
                               @MiaoHttpParam("sound") String sound,     //integer	音量大小
                               Class<String> clazz,
                               MiaoHttpManager.Callback<String> callback);
    @MiaoHttpPost("v3/mirror/config/set")
    void setSittingDeviceSedentaryTime(@MiaoHttpParam("u_id") String u_id,
                                       @MiaoHttpParam("equipmentUID") String equipmentUID,
                                       @MiaoHttpParam("sedentaryTime") String sedentaryTime, //integer	久坐时长
                                       Class<String> clazz,
                                       MiaoHttpManager.Callback<String> callback);
    @MiaoHttpPost("v3/mirror/config/set")
    void setSittingDeviceLightLevel(@MiaoHttpParam("u_id") String u_id,
                               @MiaoHttpParam("equipmentUID") String equipmentUID,
                               @MiaoHttpParam("light") String light,     //提示灯亮度 0-5
                               Class<String> clazz,
                               MiaoHttpManager.Callback<String> callback);
    @MiaoHttpPost("v3/mirror/config/set")
    void setSittingDeviceInputStatus(@MiaoHttpParam("u_id") String u_id,
                                    @MiaoHttpParam("equipmentUID") String equipmentUID,
                                    @MiaoHttpParam("inputStatus") String inputStatus,     //录入状态
                                    Class<String> clazz,
                                    MiaoHttpManager.Callback<String> callback);
    @MiaoHttpPost("v3/mirror/config/set")
    void setSittingDeviceStandPositionImage(@MiaoHttpParam("u_id") String u_id,
                                     @MiaoHttpParam("equipmentUID") String equipmentUID,
                                     @MiaoHttpParam("standPositionImage") String standPositionImage,     //标准坐姿图片
                                     Class<String> clazz,
                                     MiaoHttpManager.Callback<String> callback);
    @MiaoHttpPost("v3/mirror/config/set")
    void setSettingPostureMode(@MiaoHttpParam("u_id") String u_id,
                             @MiaoHttpParam("equipmentUID") String equipmentUID,
                             @MiaoHttpParam("postureMode") String postureMode,     //1为智能坐姿 2位自定义坐姿
                             Class<String> clazz,
                             MiaoHttpManager.Callback<String> callback);
    @MiaoHttpPost("v3/mirror/config/set")
    void setSettingPostureSwitch(@MiaoHttpParam("u_id") String u_id,
                             @MiaoHttpParam("equipmentUID") String equipmentUID,
                             @MiaoHttpParam("postureSwitch") String postureSwitch,     //坐姿开关 0为关 1为开
                             Class<String> clazz,
                             MiaoHttpManager.Callback<String> callback);
    @MiaoHttpPost("v3/mirror/config/set")
    void setSettingLightSwitch(@MiaoHttpParam("u_id") String u_id,
                                 @MiaoHttpParam("equipmentUID") String equipmentUID,
                                 @MiaoHttpParam("lightSwitch") String postureSwitch,     //灯光开关 1为开 0为关
                                 Class<String> clazz,
                                 MiaoHttpManager.Callback<String> callback);
    @MiaoHttpPost("v3/mirror/config/set")
    void setSettingHappySwitch(@MiaoHttpParam("u_id") String u_id,
                               @MiaoHttpParam("equipmentUID") String equipmentUID,
                               @MiaoHttpParam("happySwitch") String happySwitch,     //happySwitch	false	integer	开心坐姿开关 1为开 0为关
                               Class<String> clazz,
                               MiaoHttpManager.Callback<String> callback);

    @MiaoHttpPost("v3/mirror/config/set")
    void setSettingAll(@MiaoHttpParam("u_id") String u_id,
                       @MiaoHttpParam("equipmentUID") String equipmentUID,
                       @MiaoHttpParam("level") String level,     //integer 坐姿识别灵敏度 0 - 慢 1 - 适中 2 - 快
                       @MiaoHttpParam("status") String status,   //boolean	是否开启坐姿提醒
                       @MiaoHttpParam("sound") String sound,     //integer	音量大小
                       @MiaoHttpParam("sedentaryTime") String sedentaryTime, //integer	久坐时长
                       @MiaoHttpParam("light") String light,     //提示灯亮度 0-5
                       @MiaoHttpParam("inputStatus") String inputStatus,     //录入状态
                       @MiaoHttpParam("standPositionImage") String standPositionImage,     //标准坐姿图片
                       @MiaoHttpParam("postureMode") String postureMode,     //1为智能坐姿 2位自定义坐姿
                       @MiaoHttpParam("postureSwitch") String postureSwitch,     //坐姿开关 0为关 1为开
                       @MiaoHttpParam("lightSwitch") String lightSwitch,     //灯光开关 1为开 0为关
                       @MiaoHttpParam("happySwitch") String happySwitch,     //happySwitch	false	integer	开心坐姿开关 1为开 0为关
                       Class<String> clazz,
                       MiaoHttpManager.Callback<String> callback);

    // 设置设备名称
    @MiaoHttpPost("v3/mirror/setAlias")
    void setDeviceAlias(@MiaoHttpParam("u_id") String u_id,
                        @MiaoHttpParam("equipmentUuid") String equipmentUuid,
                        @MiaoHttpParam("alias") String status,   //名称
                        Class<String> clazz,
                        MiaoHttpManager.Callback<String> callback);

    //获取之前的日周月报
    @MiaoHttpPost("v3/mirror/beforeUseReportList")
    MiaoHttpEntity<SittingReportModelSub> getBeforeSittingReports(
            @MiaoHttpParam("u_id") String uId,
            @MiaoHttpParam("equipmentUuid") String equipmentUid,
            @MiaoHttpParam("type") String type1,
            @MiaoHttpParam("reportId") String reportId,
            @MiaoHttpParam("size") String size,
            Type type);

    //获取之后的日周月报
    @MiaoHttpPost("v3/mirror/afterUseReportList")
    MiaoHttpEntity<SittingReportModelSub> getAfterSittingReports(
            @MiaoHttpParam("u_id") String uId,
            @MiaoHttpParam("equipmentUuid") String equipmentUid,
            @MiaoHttpParam("type") String type1,
            @MiaoHttpParam("reportId") String reportId,
            @MiaoHttpParam("size") String size,
            Type type);

    //获取用户使用报告-获取之前之后的日周月报
    @MiaoHttpPost("v3/mirror/beforeAfterUseReports")
    MiaoHttpEntity<SittingReportModelSub> getBeforeAfterSittingReports(
            @MiaoHttpParam("u_id") String uId,
            @MiaoHttpParam("equipmentUuid") String equipmentUid,
            @MiaoHttpParam("type") String type,
            @MiaoHttpParam("reportId") String reportId,
            @MiaoHttpParam("size") String size,
            Type type1);

    //获取上传标准坐姿oss token
    @MiaoHttpPost("v3/mirror/oss/token")
    MiaoHttpEntity<SittingOssTokenModel> getSittingOssInfo(
            @MiaoHttpParam("u_id") String u_id,
            @MiaoHttpParam("clientId") String clientId,
            Class<SittingOssTokenModel> clazz);

    //获取日报数据愉悦心情瞬间坐姿图片
    @MiaoHttpPost("v3/mirror/report/img/after")
    MiaoHttpEntity<SittingHappyTimePictureModel> getSittingHappyTimePictureAfter(
            @MiaoHttpParam("u_id") String u_id,
            @MiaoHttpParam("udid") String udid,
            @MiaoHttpParam("type") String typeClass,        //请求类型1为错误瞬间 2为愉悦瞬间
            @MiaoHttpParam("offsetId") String offsetId,     //指定id位置
            @MiaoHttpParam("dateString") String dateString, //日报日期字符串 如“2021-01-12”
            Type type);
    @MiaoHttpPost("v3/mirror/report/img/before")
    MiaoHttpEntity<SittingHappyTimePictureModel> getSittingHappyTimePictureBefore(
            @MiaoHttpParam("u_id") String u_id,
            @MiaoHttpParam("udid") String udid,
            @MiaoHttpParam("type") String typeClass,        //请求类型1为错误瞬间 2为愉悦瞬间
            @MiaoHttpParam("offsetId") String offsetId,     //指定id位置
            @MiaoHttpParam("dateString") String dateString, //日报日期字符串 如“2021-01-12”
            Type type);
    @MiaoHttpPost("v3/mirror/report/img/beforeafter")
    MiaoHttpEntity<SittingSitPictureListModel> getSittingHappyTimePictureBeforeAfter(
            @MiaoHttpParam("u_id") String u_id,
            @MiaoHttpParam("udid") String udid,
            @MiaoHttpParam("type") String typeClass,        //请求类型1为错误瞬间 2为愉悦瞬间
            @MiaoHttpParam("offsetId") String offsetId,     //指定id位置
            @MiaoHttpParam("dateString") String dateString, //日报日期字符串 如“2021-01-12”
            Type type);

    //删除错误图片
    @MiaoHttpPost("v3/mirror/report/img/delete")
    void deleteSittingHappyTimePicture(
            @MiaoHttpParam("u_id") String u_id,
            @MiaoHttpParam("udid") String udid,
            @MiaoHttpParam("dataIds") String dataIds,   //删除图片id,多个使用逗号分隔
            Class<String> clazz,
            MiaoHttpManager.Callback<String> callback);

    // 提交坐姿图片信息接口
    @MiaoHttpPost("v3/mirror/addImgData")
    void submitSittingHappyPicture(
            @MiaoHttpParam("userId") String userId,
            @MiaoHttpParam("udid") String udid,     // 设备id
            @MiaoHttpParam("imgDatas") String dataIds,   // list,坐姿图片信息
            Class<String> clazz,
            MiaoHttpManager.Callback<String> callback);


        @MiaoHttpGet("v3/agora/records")
    void getAuraRecordMovies(
            @MiaoHttpParam("userId") String uId,
            @MiaoHttpParam("page") String page,
                           Class<AuraRecordModel> clazz,
                           MiaoHttpManager.Callback<AuraRecordModel> callback);

        //是否支持高清视频
    @MiaoHttpGet("v3/agora/hd")
    void isSupportAuraHD(
            @MiaoHttpParam("sn") String sn,
            Class<String> clazz,
            MiaoHttpManager.Callback<String> callback);

    //是否支持高清视频
    @MiaoHttpGet("v3/agora/timeout")
    void isAuraHDMore180Days(
            @MiaoHttpParam("sn") String sn,
            Class<String> clazz,
            MiaoHttpManager.Callback<String> callback);

}
