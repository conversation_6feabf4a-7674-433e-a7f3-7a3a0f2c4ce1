package com.czur.cloud.netty.bean;

/**
 * Created by <PERSON>z on 2018/10/29.
 * Email：<EMAIL>
 */
public class ReceivedMsgBodyBean {
    /**
     * requestid : df15a5bd-dd1c-45c5-8ed1-70eff9e1214c
     * type : BIZ
     * body : {"action":"COMMAND","userid_from":"2855","udid_from":"CZURAURAMATE000","device_uuid":"CZURAURAMATE000","group_push":0,"data":{"device_status":{"firmware_current_version":"CZUR v1.0.0","firmware_need_update":"0","firmware_update_version":"nb:8.8.8","has_calibrated_sp":"0","light_level":"0","light_mode":"1","light_switch":"Off","sp_reminder_sensitivity_level":"Medium","sp_reminder_sensitivity_volume":15,"sp_reminder_switch":"Off","wifi_ssid":"CZUR_5G"},"message_name":"BindSuccess","uuid":"bde36497-d687-4af8-8281-79643f5defb2"}}
     */
    private String requestid;
    private String type;
    private BodyBean body;
    private long timestamp;

    public String getRequestid() {
        return requestid;
    }

    public void setRequestid(String requestid) {
        this.requestid = requestid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BodyBean getBody() {
        return body;
    }

    public void setBody(BodyBean body) {
        this.body = body;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public static class BodyBean {
        /**
         * action : COMMAND
         * userid_from : 2855
         * udid_from : CZURAURAMATE000
         * device_uuid : CZURAURAMATE000
         * group_push : 0
         * data : {"device_status":{"firmware_current_version":"CZUR v1.0.0","firmware_need_update":"0","firmware_update_version":"nb:8.8.8","has_calibrated_sp":"0","light_level":"0","light_mode":"1","light_switch":"Off","sp_reminder_sensitivity_level":"Medium","sp_reminder_sensitivity_volume":15,"sp_reminder_switch":"Off","wifi_ssid":"CZUR_5G"},"message_name":"BindSuccess","uuid":"bde36497-d687-4af8-8281-79643f5defb2"}

       ====》Starry
         "action": "CALL_VIDEO",
         "userid_from": "15840618316",
         "udid_from": "90c3d7cd26304193ac15e3e7d80bfef0",
         "nickname_from": "Jason",
         "data": {
         "headImage": null
         },
         "module": "Starry",
         "room": "7299",
         "room_name": "多人会议"
         */

        private String action;
        private String device_uuid;
        private int group_push;
        private DataBean data;

        private String userid_from;
        private String udid_from;
        private String call_id;
        private String method;

        // Starry Meeting
        private String nickname_from;
        private String module;
        private String room;
        private String room_name;

        private ReplyBean reply;

        public String getCall_id() {
            return call_id;
        }

        public void setCall_id(String call_id) {
            this.call_id = call_id;
        }

        public String getNickname_from() {            return nickname_from;        }
        public void setNickname_from(String nickname_from) {            this.nickname_from = nickname_from;        }

        public String getModule() {            return module;        }
        public void setModule(String module) {            this.module = module;        }

        public String getRoom() {            return room;        }
        public void setRoom(String room) {            this.room = room;        }

        public String getRoom_name() {            return room_name;        }
        public void setRoom_name(String room_name) {            this.room_name = room_name;        }

        public String getMethod() {
            return method;
        }

        public void setMethod(String method) {
            this.method = method;
        }

        public String getAction() {
            return action;
        }

        public void setAction(String action) {
            this.action = action;
        }

        public String getUserid_from() {
            return userid_from;
        }

        public void setUserid_from(String userid_from) {
            this.userid_from = userid_from;
        }

        public String getUdid_from() {
            return udid_from;
        }

        public void setUdid_from(String udid_from) {
            this.udid_from = udid_from;
        }

        public String getDevice_uuid() {
            return device_uuid;
        }

        public void setDevice_uuid(String device_uuid) {
            this.device_uuid = device_uuid;
        }

        public int getGroup_push() {
            return group_push;
        }

        public void setGroup_push(int group_push) {
            this.group_push = group_push;
        }

        public DataBean getData() {
            return data;
        }

        public void setData(DataBean data) {
            this.data = data;
        }


        public static class DataBean {
            /**
             * device_status : {"firmware_current_version":"CZUR v1.0.0","firmware_need_update":"0","firmware_update_version":"nb:8.8.8","has_calibrated_sp":"0","light_level":"0","light_mode":"1","light_switch":"Off","sp_reminder_sensitivity_level":"Medium","sp_reminder_sensitivity_volume":15,"sp_reminder_switch":"Off","wifi_ssid":"CZUR_5G"}
             * message_name : BindSuccess
             * uuid : bde36497-d687-4af8-8281-79643f5defb2
             */

            private Boolean switchResult;
            private DeviceStatusBean device_status;
            private String message_name;
            private String uuid;

            private String is_ready_for_video;
            private String is_ready_for_calibrate;
            private String video_chat_room_no;
            private String video_camera;
            private String sp_calibrate_image_oss_key;
            private String sp_calibrate_result;
            private String processing_status;
            private String oss_bucket;
            private String oss_key;
            private int update_fw_result;
            private int is_request_active;
            private int save_status;

            private String headImage;   //Starry，callin user photo
            private Boolean otherMeeting;   //用来标识自己是否在其他会议中

            public Boolean getSwitchResult() {
                if (switchResult == null){
                    return false;
                }
                return switchResult;
            }

            public void setSwitchResult(Boolean switchResult) {
                this.switchResult = switchResult;
            }

            public int getIs_request_active() {
                return is_request_active;
            }

            public void setIs_request_active(int is_request_active) {
                this.is_request_active = is_request_active;
            }

            public String getHeadImage() {                return headImage;            }
            public void setHeadImage(String headImage) {                this.headImage = headImage;            }

            public Boolean getOtherMeeting() {                return otherMeeting;            }
            public void setOtherMeeting(Boolean otherMeeting) {                this.otherMeeting = otherMeeting; }

            public DeviceStatusBean getDevice_status() {
                return device_status;
            }

            public void setDevice_status(DeviceStatusBean device_status) {
                this.device_status = device_status;
            }

            public String getMessage_name() {
                return message_name;
            }

            public void setMessage_name(String message_name) {
                this.message_name = message_name;
            }

            public String getUuid() {
                return uuid;
            }

            public void setUuid(String uuid) {
                this.uuid = uuid;
            }

            public String getIs_ready_for_video() {
                return is_ready_for_video;
            }

            public void setIs_ready_for_video(String is_ready_for_video) {
                this.is_ready_for_video = is_ready_for_video;
            }

            public String getVideo_chat_room_no() {
                return video_chat_room_no;
            }

            public void setVideo_chat_room_no(String video_chat_room_no) {
                this.video_chat_room_no = video_chat_room_no;
            }


            public String getVideo_camera() {
                return video_camera;
            }

            public void setVideo_camera(String video_camera) {
                this.video_camera = video_camera;
            }


            public String getSp_calibrate_image_oss_key() {
                return sp_calibrate_image_oss_key;
            }

            public void setSp_calibrate_image_oss_key(String sp_calibrate_image_oss_key) {
                this.sp_calibrate_image_oss_key = sp_calibrate_image_oss_key;
            }

            public String getSp_calibrate_result() {
                return sp_calibrate_result;
            }

            public void setSp_calibrate_result(String sp_calibrate_result) {
                this.sp_calibrate_result = sp_calibrate_result;
            }

            public int getUpdate_fw_result() {
                return update_fw_result;
            }

            public void setUpdate_fw_result(int update_fw_result) {
                this.update_fw_result = update_fw_result;
            }

            public String getIs_ready_for_calibrate() {
                return is_ready_for_calibrate;
            }

            public void setIs_ready_for_calibrate(String is_ready_for_calibrate) {
                this.is_ready_for_calibrate = is_ready_for_calibrate;
            }

            public String getProcessing_status() {
                return processing_status;
            }

            public void setProcessing_status(String processing_status) {
                this.processing_status = processing_status;
            }

            public String getOss_bucket() {
                return oss_bucket;
            }

            public void setOss_bucket(String oss_bucket) {
                this.oss_bucket = oss_bucket;
            }

            public String getOss_key() {
                return oss_key;
            }

            public void setOss_key(String oss_key) {
                this.oss_key = oss_key;
            }

            public int getSave_status() {
                return save_status;
            }

            public void setSave_status(int save_status) {
                this.save_status = save_status;
            }

            public static class DeviceStatusBean {
                /**
                 * firmware_current_version : CZUR v1.0.0
                 * firmware_need_update : 0
                 * firmware_update_version : nb:8.8.8
                 * has_calibrated_sp : 0
                 * light_level : 0
                 * light_mode : 1
                 * light_switch : Off
                 * sp_reminder_sensitivity_level : Medium
                 * sp_reminder_sensitivity_volume : 15
                 * sp_reminder_switch : Off
                 * wifi_ssid : CZUR_5G
                 */

                private String firmware_current_version;
                private String firmware_need_update;
                private String firmware_update_version;
                private int has_calibrated_sp;
                private String light_level;
                private String light_mode;
                private String light_switch;
                private String sp_reminder_sensitivity_level;
                private int sp_reminder_sensitivity_volume;
                private String sp_reminder_switch;
                private String wifi_ssid;
                private String system_language;
                private String smart_power_saving_switch;
                private String sedentary_reminder_switch;
                private int sedentary_reminder_duration;
                private int message_api_version;
                private String device_mode;

                public String getFirmware_current_version() {
                    return firmware_current_version;
                }

                public void setFirmware_current_version(String firmware_current_version) {
                    this.firmware_current_version = firmware_current_version;
                }

                public String getFirmware_need_update() {
                    return firmware_need_update;
                }

                public void setFirmware_need_update(String firmware_need_update) {
                    this.firmware_need_update = firmware_need_update;
                }

                public String getFirmware_update_version() {
                    return firmware_update_version;
                }

                public void setFirmware_update_version(String firmware_update_version) {
                    this.firmware_update_version = firmware_update_version;
                }

                public int getHas_calibrated_sp() {
                    return has_calibrated_sp;
                }

                public void setHas_calibrated_sp(int has_calibrated_sp) {
                    this.has_calibrated_sp = has_calibrated_sp;
                }

                public String getLight_level() {
                    return light_level;
                }

                public void setLight_level(String light_level) {
                    this.light_level = light_level;
                }

                public String getLight_mode() {
                    return light_mode;
                }

                public void setLight_mode(String light_mode) {
                    this.light_mode = light_mode;
                }

                public String getLight_switch() {
                    return light_switch;
                }

                public void setLight_switch(String light_switch) {
                    this.light_switch = light_switch;
                }

                public String getSp_reminder_sensitivity_level() {
                    return sp_reminder_sensitivity_level;
                }

                public void setSp_reminder_sensitivity_level(String sp_reminder_sensitivity_level) {
                    this.sp_reminder_sensitivity_level = sp_reminder_sensitivity_level;
                }

                public int getSp_reminder_sensitivity_volume() {
                    return sp_reminder_sensitivity_volume;
                }

                public void setSp_reminder_sensitivity_volume(int sp_reminder_sensitivity_volume) {
                    this.sp_reminder_sensitivity_volume = sp_reminder_sensitivity_volume;
                }

                public String getSp_reminder_switch() {
                    return sp_reminder_switch;
                }

                public void setSp_reminder_switch(String sp_reminder_switch) {
                    this.sp_reminder_switch = sp_reminder_switch;
                }

                public String getWifi_ssid() {
                    return wifi_ssid;
                }

                public void setWifi_ssid(String wifi_ssid) {
                    this.wifi_ssid = wifi_ssid;
                }

                public String getSystem_language() {
                    return system_language;
                }

                public void setSystem_language(String system_language) {
                    this.system_language = system_language;
                }


                public String getSmart_power_saving_switch() {
                    return smart_power_saving_switch;
                }

                public void setSmart_power_saving_switch(String smart_power_saving_switch) {
                    this.smart_power_saving_switch = smart_power_saving_switch;
                }

                public String getSedentary_reminder_switch() {
                    return sedentary_reminder_switch;
                }

                public void setSedentary_reminder_switch(String sedentary_reminder_switch) {
                    this.sedentary_reminder_switch = sedentary_reminder_switch;
                }

                public int getSedentary_reminder_duration() {
                    return sedentary_reminder_duration;
                }

                public void setSedentary_reminder_duration(int sedentary_reminder_duration) {
                    this.sedentary_reminder_duration = sedentary_reminder_duration;
                }

                public int getMessage_api_version() {
                    return message_api_version;
                }

                public void setMessage_api_version(int message_api_version) {
                    this.message_api_version = message_api_version;
                }

                public String getDevice_mode() {
                    return device_mode;
                }

                public void setDevice_mode(String device_mode) {
                    this.device_mode = device_mode;
                }
            }
        }

        public ReplyBean getReply() {
            return reply;
        }

        public void setReply(ReplyBean reply) {
            this.reply = reply;
        }

        public static class ReplyBean {
            /**
             * status : 1
             * "name": "OFFLINE",
             * "device": "CET15P1907000003",
             */

            private String status;
            private String name;
            private String device;


            public String getStatus() {
                return status;
            }

            public void setStatus(String status) {
                this.status = status;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getDevice() {
                return device;
            }

            public void setDevice(String device) {
                this.device = device;
            }
        }


    }
}
