package com.czur.cloud.util

import android.content.Context
import android.os.Build
import androidx.annotation.RequiresApi
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logE

/**
 * Android 16 适配工具类
 * 处理Android 16新特性和行为变更
 */
object Android16Utils {
    private const val TAG = "Android16Utils"

    /**
     * 检查当前是否运行在Android 16或更高版本
     */
    fun isAndroid16OrHigher(): Boolean {
        return Build.VERSION.SDK_INT >= 36 // Android 16 API Level 36
    }

    /**
     * 检查是否支持Android 16的新版本判断API
     */
    fun isSupportNewVersionApi(): Boolean {
        return isAndroid16OrHigher()
    }

    /**
     * 获取精确的SDK版本信息（Android 16新增）
     */
    @RequiresApi(36)
    fun getFullSdkVersion(): Int {
        return try {
            if (isAndroid16OrHigher()) {
                // 使用Android 16新增的SDK_INT_FULL API
                val sdkIntFullField = Build.VERSION::class.java.getDeclaredField("SDK_INT_FULL")
                sdkIntFullField.isAccessible = true
                sdkIntFullField.getInt(null)
            } else {
                Build.VERSION.SDK_INT
            }
        } catch (e: Exception) {
            logE("$TAG.getFullSdkVersion error: ${e.message}")
            Build.VERSION.SDK_INT
        }
    }

    /**
     * 获取Minor Release版本号（Android 16新增）
     */
    @RequiresApi(36)
    fun getMinorSdkVersion(): Int {
        return try {
            if (isAndroid16OrHigher()) {
                // 使用Android 16新增的getMinorSdkVersion API
                val getMinorSdkVersionMethod = Build::class.java.getDeclaredMethod("getMinorSdkVersion", Int::class.java)
                getMinorSdkVersionMethod.isAccessible = true
                getMinorSdkVersionMethod.invoke(null, 36) as Int
            } else {
                0
            }
        } catch (e: Exception) {
            logE("$TAG.getMinorSdkVersion error: ${e.message}")
            0
        }
    }

    /**
     * 检查设备是否启用了高级保护模式（Android 16新增）
     */
    fun isAdvancedProtectionEnabled(context: Context): Boolean {
        return try {
            if (isAndroid16OrHigher()) {
                // 使用反射调用AdvancedProtectionManager
                val advancedProtectionManagerClass = Class.forName("android.app.AdvancedProtectionManager")
                val getSystemServiceMethod = Context::class.java.getMethod("getSystemService", String::class.java)
                val manager = getSystemServiceMethod.invoke(context, "advanced_protection")
                
                if (manager != null) {
                    val isEnabledMethod = advancedProtectionManagerClass.getMethod("isAdvancedProtectionEnabled")
                    isEnabledMethod.invoke(manager) as Boolean
                } else {
                    false
                }
            } else {
                false
            }
        } catch (e: Exception) {
            logE("$TAG.isAdvancedProtectionEnabled error: ${e.message}")
            false
        }
    }

    /**
     * 检查设备是否支持动态刷新率（Android 16新增）
     */
    fun hasAdaptiveRefreshRateSupport(context: Context): Boolean {
        return try {
            if (isAndroid16OrHigher()) {
                val windowManager = context.getSystemService(Context.WINDOW_SERVICE)
                val display = windowManager?.javaClass?.getMethod("getDefaultDisplay")?.invoke(windowManager)
                
                if (display != null) {
                    val hasArrSupportMethod = display.javaClass.getMethod("hasArrSupport")
                    hasArrSupportMethod.invoke(display) as Boolean
                } else {
                    false
                }
            } else {
                false
            }
        } catch (e: Exception) {
            logE("$TAG.hasAdaptiveRefreshRateSupport error: ${e.message}")
            false
        }
    }

    /**
     * 获取建议的刷新率（Android 16新增）
     */
    fun getSuggestedFrameRate(context: Context, category: Int): Float {
        return try {
            if (isAndroid16OrHigher()) {
                val windowManager = context.getSystemService(Context.WINDOW_SERVICE)
                val display = windowManager?.javaClass?.getMethod("getDefaultDisplay")?.invoke(windowManager)
                
                if (display != null) {
                    val getSuggestedFrameRateMethod = display.javaClass.getMethod("getSuggestedFrameRate", Int::class.java)
                    getSuggestedFrameRateMethod.invoke(display, category) as Float
                } else {
                    60.0f
                }
            } else {
                60.0f
            }
        } catch (e: Exception) {
            logE("$TAG.getSuggestedFrameRate error: ${e.message}")
            60.0f
        }
    }

    /**
     * 检查是否需要16KB页大小适配
     */
    fun shouldAdapt16KBPageSize(): Boolean {
        return isAndroid16OrHigher()
    }

    /**
     * 创建进度样式通知（Android 16新增）
     */
    @RequiresApi(36)
    fun createProgressStyleNotification(
        context: Context,
        channelId: String,
        title: String,
        progress: Int,
        maxProgress: Int = 1000,
        segments: List<Pair<Int, Int>>? = null, // length, color
        points: List<Pair<Int, Int>>? = null, // position, color
        trackerIconRes: Int? = null
    ): Notification? {
        return try {
            if (!isAndroid16OrHigher()) return null

            val notificationClass = Class.forName("android.app.Notification")
            val progressStyleClass = Class.forName("android.app.Notification\$ProgressStyle")

            // 创建ProgressStyle实例
            val progressStyleConstructor = progressStyleClass.getDeclaredConstructor()
            val progressStyle = progressStyleConstructor.newInstance()

            // 设置基本进度
            val setProgressMethod = progressStyleClass.getMethod("setProgress", Int::class.java)
            setProgressMethod.invoke(progressStyle, progress)

            val setStyledByProgressMethod = progressStyleClass.getMethod("setStyledByProgress", Boolean::class.java)
            setStyledByProgressMethod.invoke(progressStyle, false)

            // 设置追踪图标
            trackerIconRes?.let { iconRes ->
                val iconClass = Class.forName("android.graphics.drawable.Icon")
                val createWithResourceMethod = iconClass.getMethod("createWithResource", Context::class.java, Int::class.java)
                val icon = createWithResourceMethod.invoke(null, context, iconRes)

                val setProgressTrackerIconMethod = progressStyleClass.getMethod("setProgressTrackerIcon", iconClass)
                setProgressTrackerIconMethod.invoke(progressStyle, icon)
            }

            // 设置分段
            segments?.let { segmentList ->
                val segmentClass = Class.forName("android.app.Notification\$ProgressStyle\$Segment")
                val segmentConstructor = segmentClass.getDeclaredConstructor(Int::class.java)
                val setColorMethod = segmentClass.getMethod("setColor", Int::class.java)

                val segmentObjects = segmentList.map { (length, color) ->
                    val segment = segmentConstructor.newInstance(length)
                    setColorMethod.invoke(segment, color)
                    segment
                }

                val setProgressSegmentsMethod = progressStyleClass.getMethod("setProgressSegments", List::class.java)
                setProgressSegmentsMethod.invoke(progressStyle, segmentObjects)
            }

            // 设置点
            points?.let { pointList ->
                val pointClass = Class.forName("android.app.Notification\$ProgressStyle\$Point")
                val pointConstructor = pointClass.getDeclaredConstructor(Int::class.java)
                val setColorMethod = pointClass.getMethod("setColor", Int::class.java)

                val pointObjects = pointList.map { (position, color) ->
                    val point = pointConstructor.newInstance(position)
                    setColorMethod.invoke(point, color)
                    point
                }

                val setProgressPointsMethod = progressStyleClass.getMethod("setProgressPoints", List::class.java)
                setProgressPointsMethod.invoke(progressStyle, pointObjects)
            }

            // 创建通知
            val builder = Notification.Builder(context, channelId)
                .setContentTitle(title)
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setProgress(maxProgress, progress, false)

            // 设置ProgressStyle
            val setStyleMethod = Notification.Builder::class.java.getMethod("setStyle", Notification.Style::class.java)
            setStyleMethod.invoke(builder, progressStyle)

            builder.build()
        } catch (e: Exception) {
            logE("$TAG.createProgressStyleNotification error: ${e.message}")
            null
        }
    }

    /**
     * 检查JobScheduler任务失败的所有原因（Android 16新增）
     */
    @RequiresApi(36)
    fun getPendingJobReasons(context: Context, jobId: Int): IntArray {
        return try {
            if (!isAndroid16OrHigher()) return intArrayOf()

            val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE)
            if (jobScheduler != null) {
                val getPendingJobReasonsMethod = jobScheduler.javaClass.getMethod("getPendingJobReasons", Int::class.java)
                getPendingJobReasonsMethod.invoke(jobScheduler, jobId) as IntArray
            } else {
                intArrayOf()
            }
        } catch (e: Exception) {
            logE("$TAG.getPendingJobReasons error: ${e.message}")
            intArrayOf()
        }
    }

    /**
     * 检查是否需要适配自适应布局
     */
    fun shouldAdaptAdaptiveLayout(): Boolean {
        return isAndroid16OrHigher()
    }

    /**
     * 检查是否需要适配预测性返回手势
     */
    fun shouldAdaptPredictiveBack(): Boolean {
        return isAndroid16OrHigher()
    }

    /**
     * 记录Android 16适配信息
     */
    fun logAndroid16Info(context: Context) {
        if (isAndroid16OrHigher()) {
            logI("$TAG.Android16Info:")
            logI("$TAG.SDK_INT: ${Build.VERSION.SDK_INT}")
            logI("$TAG.FullSdkVersion: ${getFullSdkVersion()}")
            logI("$TAG.MinorSdkVersion: ${getMinorSdkVersion()}")
            logI("$TAG.AdvancedProtectionEnabled: ${isAdvancedProtectionEnabled(context)}")
            logI("$TAG.AdaptiveRefreshRateSupport: ${hasAdaptiveRefreshRateSupport(context)}")
            logI("$TAG.SuggestedFrameRate: ${getSuggestedFrameRate(context, 0)}")
            logI("$TAG.ShouldAdapt16KBPageSize: ${shouldAdapt16KBPageSize()}")
            logI("$TAG.ShouldAdaptAdaptiveLayout: ${shouldAdaptAdaptiveLayout()}")
            logI("$TAG.ShouldAdaptPredictiveBack: ${shouldAdaptPredictiveBack()}")
        } else {
            logI("$TAG.Current Android version: ${Build.VERSION.SDK_INT}, Android 16 features not available")
        }
    }
}
