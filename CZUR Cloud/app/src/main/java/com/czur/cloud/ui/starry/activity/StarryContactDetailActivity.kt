package com.czur.cloud.ui.starry.activity

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.PopupWindow
import androidx.activity.OnBackPressedCallback
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryCommonEvent
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.component.popup.CloudCommonPopup
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants
import com.czur.cloud.ui.mirror.comm.FastBleToolUtils
import com.czur.cloud.ui.starry.base.StarryNewBaseActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.meeting.MeetingMainActivity
import com.czur.cloud.ui.starry.meeting.baselib.utils.addNoSpaceFilter
import com.czur.cloud.ui.starry.meeting.baselib.utils.dp2px
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.model.CommonEnterprise
import com.czur.cloud.ui.starry.model.ContactDetail
import com.czur.cloud.ui.starry.model.MeetingMember
import com.czur.cloud.ui.starry.model.StarryAddressBookModel
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.ui.starry.viewmodel.MeetingDetailViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryContactViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import com.czur.czurutils.log.logI
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_add_to_btn
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_detail_company_change_btn
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_detail_company_icon_iv
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_detail_company_name
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_detail_company_name_old
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_detail_company_name_tv
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_detail_company_name_value_tv
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_detail_company_out_ll
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_detail_company_title_tv
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_detail_company_title_value_tv
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_mobile
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_mobile_line
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_mobile_title
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_new_call_btn
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_nickname
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_nickname_rl
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_nickname_title
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_user_name
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_user_name_ed
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_user_name_edit_btn
import kotlinx.android.synthetic.main.starry_activity_contact_detail.contact_user_name_rl
import kotlinx.android.synthetic.main.starry_activity_contact_detail.content_ll
import kotlinx.android.synthetic.main.starry_activity_contact_detail.not_get_content_ll
import kotlinx.android.synthetic.main.starry_contact_detail_change_company_item.view.select_iv
import kotlinx.android.synthetic.main.starry_contact_detail_change_company_item.view.starry_contact_detail_company_change_cl
import kotlinx.android.synthetic.main.starry_contact_detail_change_company_item.view.title_tv
import kotlinx.android.synthetic.main.starry_layout_top_bar.user_back_btn
import kotlinx.android.synthetic.main.starry_layout_top_bar.user_delete_btn
import kotlinx.android.synthetic.main.starry_layout_top_bar.user_title
import kotlinx.coroutines.delay
import org.greenrobot.eventbus.EventBus

/**
 */
class StarryContactDetailActivity : StarryNewBaseActivity() {

    companion object{
        const val DEL_ACCOUNT_PRE = "DELETE#"
        const val DEL_ACCOUNT_000 = "000"
        const val TITLE_WIDTH = 80f
    }

    private val starryViewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryViewModel::class.java)
    }
    private val viewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryContactViewModel::class.java)
    }

    private val detailViewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(MeetingDetailViewModel::class.java)
    }

    private lateinit var currentAddressBookModel: StarryAddressBookModel
    private var contactId = ""
    private var isInAddressbook = false

    private var datasList: ArrayList<CommonEnterprise> = arrayListOf()
    private var popup: PopupWindow? = null
    private var listView: RecyclerView? = null

    private var isEditFlag = false
    private var contactName: String = ""
    private var contactNickName: String = ""
    private var contactNameOld:String = ""
    private lateinit var userFromType: String
    private var meetingFrom: String=""
    private var fromId :String = ""
    private var meetingNo = ""

//    private var isSelectedCompanyExpired = false    // 下拉选择的企业是否过期了？

    private var isPopWin = false

    private val onBackPressedCallback = object : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {
            ActivityUtils.finishActivity(this@StarryContactDetailActivity)
            viewModel.contactDetail.removeObservers(this@StarryContactDetailActivity)
        }
    }

    override fun getLayout(): Int = R.layout.starry_activity_contact_detail

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)

        val model = ContactDetail()
        viewModel.contactDetail.postValue(model)

        fromId = intent.getStringExtra(StarryConstants.INTENT_FROM_ID).toString()
        currentAddressBookModel =
            (intent.getSerializableExtra(StarryConstants.STARRY_USER_MODEL) as StarryAddressBookModel?)!!

        logI("currentAddressBookModel=${currentAddressBookModel}")

        contactName = currentAddressBookModel.name ?: ""
        contactNameOld = contactName
        contactNickName = contactName

        contact_user_name?.visibility = View.VISIBLE
        contact_user_name_ed?.visibility = View.GONE

        contactId = currentAddressBookModel.id.toString() ?: ""
        isInAddressbook = currentAddressBookModel.isInAddressbook == true ?: false

        user_delete_btn?.visibility = View.GONE
        contact_add_to_btn?.visibility = View.GONE
        contact_new_call_btn?.visibility = View.GONE
        contact_user_name_edit_btn?.visibility = View.GONE

        meetingFrom = intent.getStringExtra(StarryConstants.STARRY_MEETING_FROM) ?: ""

        userFromType = intent.getStringExtra(StarryConstants.STARRY_USER_TYPE) ?: StarryConstants.STARRY_USER_TYPE_CONTACT
        when (userFromType){

            StarryConstants.STARRY_USER_TYPE_CONTACT -> {
                meetingNo = currentAddressBookModel.meetingNo
                user_delete_btn?.visibility = View.VISIBLE
                contact_add_to_btn?.visibility = View.GONE
            }

            StarryConstants.STARRY_USER_TYPE_COMPANY -> {
                meetingNo = currentAddressBookModel.meetingAccout
                contact_new_call_btn?.visibility = View.VISIBLE
                user_delete_btn?.visibility = View.GONE

                if (!isInAddressbook){
                    contact_add_to_btn?.visibility = View.VISIBLE
                }
            }

            StarryConstants.STARRY_USER_TYPE_CC -> {
                meetingNo = currentAddressBookModel.meetingNo
                user_delete_btn?.visibility = View.GONE

                if (!isInAddressbook){
                    contact_add_to_btn?.visibility = View.VISIBLE
                }
            }
       }

        // 判断一下是否为注销账号
        if (meetingNo.startsWith(DEL_ACCOUNT_PRE) || meetingNo.startsWith(DEL_ACCOUNT_000)){
            meetingNo = getString(R.string.starry_account_delete)
        }
        contact_mobile?.text = meetingNo

        // title
        user_title.text = getString(R.string.starry_title_detail_contact)
        // back btn
        user_back_btn?.singleClick {
            ActivityUtils.finishActivity(this)
            viewModel.contactDetail.removeObservers(this@StarryContactDetailActivity)
//            val model = ContactDetail()
//            viewModel.contactDetail.postValue(model)
        }


        // delete
        user_delete_btn?.singleClick {
            showConfirmDeleteDialog()
        }

        // add
        contact_add_to_btn?.singleClick {
            addToAddressBookChangeUI()
        }

        // 编辑备注按钮
        contact_user_name_edit_btn?.singleClick {
            addOrEditRemarkName()
        }
        // 备注输入框监听
        contact_user_name_ed?.addTextChangedListener(object : TextWatcher {
            private var temp: CharSequence? = null
            private var selectionStart = 0
            private var selectionEnd = 0
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                temp = s
            }

            override fun afterTextChanged(s: Editable) {
                selectionStart = contact_user_name_ed?.selectionStart ?: 0
                selectionEnd = contact_user_name_ed?.selectionEnd ?: 0
                val str = temp.toString()
                if (!TextUtils.isEmpty(str)) {
                    val l = Tools.getTextLength(str)
                    if (l > StarryConstants.MAX_DEVICE_NAME_LENGTH) {
                        try{
                            s.delete(selectionStart - 1, selectionEnd)
                            val tempSelection = selectionEnd
                            contact_user_name_ed?.text = s
                            contact_user_name_ed?.setSelection(tempSelection)
                        }catch (e: Exception){
                        }
                    }
                    contactName = s.toString()
//                    Tools.setViewButtonEnable(contact_user_name_edit_btn, true)
                } else {
                    contactName = ""
//                    Tools.setViewButtonEnable(contact_user_name_edit_btn, false)
                }
            }
        })

        // 添加禁止输入空格的过滤器
        contact_user_name_ed?.addNoSpaceFilter()

        contact_detail_company_change_btn?.singleClick {
            changeCompanyBtn(it)
        }

        // 关联企业先spsce隐藏
        contact_detail_company_out_ll?.visibility = View.GONE

        // 发起会议
        contact_new_call_btn?.singleClick {

            if (!NetworkUtils.isConnected()) {
                ToastUtils.showLong(R.string.starry_network_error_msg)
                return@singleClick
            }

            onCheckPCEnter()

        }

        showProgressDialog()
        content_ll.visibility = View.GONE
        viewModel.getContactDetailV2(currentAddressBookModel.id){
            hideProgressDialog()
            observeDtail()
        }

        LiveDataBus.get()
            .with(StarryConstants.STARRY_CONTACT_ADDTO_MEETING, Boolean::class.java)
            .observe(this) {
                launch {
                    showProgressDialog()
                    contact_user_name_edit_btn?.visibility = View.VISIBLE
                    // 从添加联系人界面过来的，应该是已添加了联系人，先隐藏添加到联系人按钮，获取详情
                    contact_add_to_btn?.visibility = View.GONE
                    viewModel.getContactDetailV2(currentAddressBookModel.id){
                        hideProgressDialog()
                        observeDtail()
                    }
                }
            }

        if (BuildConfig.IS_OVERSEAS){
            contact_nickname_title?.width = dp2px(this, TITLE_WIDTH)
            contact_mobile_title?.width = dp2px(this, TITLE_WIDTH)
            contact_detail_company_name_tv?.width = dp2px(this, TITLE_WIDTH)
            contact_detail_company_title_tv?.width = dp2px(this, TITLE_WIDTH)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.contactDetail.removeObservers(this@StarryContactDetailActivity)
    }

    // 同一账号如果pc端已在会议中
    private fun onCheckPCEnter(){
        if (MeetingModel.isPCEnter){
            StarryCommonPopup.Builder(this)
                .setTitle(getString(R.string.starry_popupwindow_title))
                .setMessage(getString(R.string.starry_callout_dialog_msg))
                .setPositiveTitle(getString(R.string.starry_callout_dialog_goon))
                .setNegativeTitle(getString(R.string.starry_callout_dialog_cancel))
                .setOnPositiveListener { dialog, _ ->
                    launch {
                        newStartCallMeeting()
                        MeetingModel.isPCEnter = false
                    }
                    dialog?.dismiss()
                }
                .setOnNegativeListener { dialog, _ ->
                    dialog.dismiss()
                }
                .create()
                .show()
        }else{
            launch {
                newStartCallMeeting()
            }
        }
    }

    // 发起会议
    private suspend fun newStartCallMeeting(){

        // 判断自己是否在企业中（）
        // Bug #20567【联系人】与联系人有一个共同企业，自己被移除企业，点击发起会议，提示：创建会议室失败
        val accountId = StarryPreferences.getInstance().accountNo
        val name = StarryPreferences.getInstance().name
        val isInCompany = starryViewModel.isSelfInEnterprise(accountId, name)
        logI("newStartCallMeeting.isInCompany=${isInCompany}")
        if (!isInCompany) {
            ToastUtils.showLong(
                String.format(
                    getString(R.string.starry_contact_startmeeting_error_msg),
                    StarryPreferences.getInstance().name
                )
            )
            Tools.setViewButtonEnable(contact_new_call_btn, false)
            hideProgressDialog()
            return
        }

        // 判断Ta是否在企业中（）
        val nameTa = contactName ?: ""
        val recId = currentAddressBookModel.id
        val isTaInCompany = viewModel.isTaInCompany(recId)
        logI("newStartCallMeeting.nameTa=${nameTa},recId=${recId},isTaInCompany=${isTaInCompany}")
        if (!isTaInCompany) {
            ToastUtils.showLong(
                String.format(
                    getString(R.string.starry_contact_startmeeting_error_msg),
                    nameTa
                )
            )
            Tools.setViewButtonEnable(contact_new_call_btn, false)
            hideProgressDialog()
            return
        }

        // 判断是否修改手机号了
        meetingNo = viewModel.contactDetail.value?.meetingNo.toString()

        // 呼叫中，不能发起会议
        if (!MeetingModel.checkIsMeetingCall()){
            return
        }
        // 标记开始呼叫
        MeetingModel.isCallingOutMeeting = true

        // 企业是否过期
        val notExpiredCount = viewModel.commonEnterprise.value?.count {
            !it.expired
        } ?: 0
        if (notExpiredCount < 1){
            ToastUtils.showLong(getString(R.string.meeting_detail_startmeeting_company_expired))
            MeetingModel.isCallingOutMeeting = false
            return
        }

        // 从meetingdetail来的，判断企业是否过期？
        if (meetingFrom == StarryConstants.STARRY_MEETING_FROM_DETAIL) {
            if (!currentAddressBookModel.isInEnterprise){
                ToastUtils.showLong(R.string.starry_meeting_no_enterprise)
                MeetingModel.isCallingOutMeeting = false
                return
            }
        }

        if (isEditFlag) {
            val intent = Intent(this, StarryActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(intent)
        }

        val members = mutableListOf<MeetingMember>()
        val item = MeetingMember(
            meetingNo,
            "")
        members.add(item)

        val jsonParam = starryViewModel.startNewMeeting(members)

        val i = Intent(this, MeetingMainActivity::class.java)
        i.putExtra(MeetingMainActivity.KEY_BOOT_DATA, jsonParam)
        i.putExtra(MeetingMainActivity.KEY_BOOT_TYPE, MeetingMainActivity.BOOT_TYPE_START)
        startActivity(i)
    }


    // 切换企业按钮
    private fun changeCompanyBtn(view: View) {
        popupWindow()
    }

    // 修改备注名
    private fun addOrEditRemarkName(){
        isEditFlag = !isEditFlag

        if (isEditFlag) {
            contactName = formatContactName(contactName)
            contact_user_name_ed?.setText(contactName)
            contact_user_name?.visibility = View.GONE
            contact_user_name_ed?.visibility = View.VISIBLE
            contact_user_name_edit_btn?.setImageResource(R.mipmap.starry_contact_done_btn)

            contact_user_name_ed.postDelayed({
                contact_user_name_ed.setSelection(contactName.length)
                //将光标移至文字末尾
                contact_user_name_ed.isFocusable = true
                contact_user_name_ed.isFocusableInTouchMode = true
                contact_user_name_ed.requestFocus()
                contact_user_name_ed.findFocus()
                showSoftInputFromWindow(contact_user_name_ed)
            },150)


        }else{
            if (!NetworkUtils.isConnected()) {
                ToastUtils.showLong(R.string.starry_network_error_msg)
                return
            }

            editAddressBookName()
        }
    }

    private fun showSoftInputFromWindow(editText: EditText) {
        editText.isFocusable = true
        editText.isFocusableInTouchMode = true
        editText.requestFocus()
        val imm: InputMethodManager = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        if (imm != null) {
            imm.showSoftInput(editText, 0)
        }
    }

    private fun hideSoftInputFromWindow(editText: EditText) {
        editText.isFocusable = false
        editText.isFocusableInTouchMode = false
        val imm: InputMethodManager = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        if (imm != null) {
            imm.hideSoftInputFromWindow(window.decorView.windowToken, 0)
        }
    }

    private fun editAddressBookName() {
        if ( (viewModel.commonEnterprise.value?.isEmpty() == true) && (contactName == "") ){
            ToastUtils.showLong(getString(R.string.starry_contact_no_remark))
            return
        }

        // 判断名称长度
        val tmp_len = Tools.getTextLength(contactName)
        if (tmp_len > StarryConstants.MAX_DEVICE_NAME_LENGTH){
            ToastUtils.showLong(getString(R.string.starry_add_contact_name_long))
            return
        }

        // 特殊字符检测
        if (Tools.containsEmoji(contactName)){
            ToastUtils.showLong(getString(R.string.starry_add_contact_judge))
            return
        }

        launch {
            var name = contactName
            if (name.isEmpty()){
                name = contactNickName
            }
            showProgressDialog()
            val flag = viewModel.updateAddressBook( contactId, formatContactName(name) )
            if (flag) {
                contact_user_name?.text = contactName
                contact_user_name_ed?.visibility = View.GONE
                contact_user_name?.visibility = View.VISIBLE
                contact_user_name_edit_btn?.setImageResource(R.mipmap.starry_contact_edit)
                hideSoftInputFromWindow(contact_user_name_ed)
                contact_user_name_rl?.requestFocus()

                EventBus.getDefault().post(StarryCommonEvent(EventType.STARRY_CONTACT_EDIT, ""))
                // 重新获取detail
                val user_mobile = contact_mobile?.text?.toString() ?: ""
//                viewModel.getContactDetail(user_mobile)

                viewModel.getContactDetailV2(currentAddressBookModel.id){
                    hideProgressDialog()
                }

            }else{
                hideProgressDialog()
                ToastUtils.showLong(R.string.starry_edit_contact_fail)
            }
        }
        return
    }


    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)



        viewModel.commonEnterprise.observe(this){
            if (it.isNotEmpty()){
                contact_detail_company_out_ll?.visibility = View.VISIBLE

                contact_detail_company_change_btn?.visibility = View.GONE
                if (it.size > 1){
                    contact_detail_company_change_btn?.visibility = View.VISIBLE
                }
                datasList.clear()
                datasList.addAll(it)
                viewModel.getCurrentCompany(starryViewModel.currentCompanyModel.value)

            }else{
                contact_detail_company_out_ll?.visibility = View.GONE
            }
        }

        viewModel.currentSelectCompany.observe(this){
            contact_detail_company_name?.text = it.enterpriseName
            contact_detail_company_name_value_tv?.text = it.memberName
            var pos = getString(R.string.starry_home_none)
            if (it.position != null && it.position.isNotEmpty()){
                pos = it.position
            }
            contact_detail_company_title_value_tv?.text = pos
            if (it.expired){
                contact_detail_company_name_old?.visibility = View.VISIBLE
            }else{
                contact_detail_company_name_old?.visibility = View.GONE
            }
//            isSelectedCompanyExpired = it.expired
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == StarryConstants.RESULT_SUCCESS_CODE && resultCode == RESULT_OK){
            val contactEditType = data?.getIntExtra(StarryConstants.STARRY_CONTACT_EDIT_TYPE, StarryConstants.STARRY_CONTACT_EDIT_TYPE_NAME)
            val value = data?.getStringExtra(StarryConstants.STARRY_CONTACT_VALUE)

//            logI("onActivityResult.contactEditType=$contactEditType;value=$value")
            if (contactEditType == StarryConstants.STARRY_CONTACT_EDIT_TYPE_NAME){
                contact_user_name?.text = value
            }else{
                contact_mobile?.text = value
            }
        }
    }

    private fun formatContactName(name: String) :String{
        var newName = ""
        if (!TextUtils.isEmpty(name)) {//保留14个字符以内的名字
            val l = FastBleToolUtils.getTextLength(name)
            if (l > StarryConstants.MAX_DEVICE_NAME_LENGTH) {
                name.forEach {
                    newName += it.toString()
                    if (FastBleToolUtils.getTextLength(newName.toString()) > StarryConstants.MAX_DEVICE_NAME_LENGTH){
                        newName = newName.substring(0,newName.length-1)
                        return@forEach
                    }else if (FastBleToolUtils.getTextLength(newName.toString()) == StarryConstants.MAX_DEVICE_NAME_LENGTH){
                        return@forEach
                    }
                }
                return newName
            }
            return name
        }else{
            return ""
        }
    }

    private fun addToAddressBookChangeUI(){
        contact_add_to_btn?.visibility = View.GONE
        if (starryViewModel.clickNoNetwork()){
            return
        }
        val userType = starryViewModel.getCurrentUserType()
        intent = Intent(applicationContext, StarryContactAddToActivity::class.java)
        intent.putExtra(StarryConstants.STARRY_USER_MODEL, currentAddressBookModel)
        intent.putExtra( StarryConstants.STARRY_USER_TYPE, userType )
        ActivityUtils.startActivity(intent)
    }

    private fun deleteAddressBook(){
        val id = currentAddressBookModel.id.toString() ?: ""
        if (!NetworkUtils.isConnected()) {
            return
        }

        launch {
            showProgressDialog()
            val flag = viewModel.deleteAddressBook(id)
            if (flag) {
                EventBus.getDefault().post(StarryCommonEvent(EventType.STARRY_CONTACT_DEL, ""))
                LiveDataBus.get().with(StarryConstants.STARRY_CONTACT_DELETE).value = id
                delay(500)
                hideProgressDialog()
                ActivityUtils.finishActivity(StarryContactDetailActivity::class.java)
            } else {
                hideProgressDialog()
                ToastUtils.showLong(getString(R.string.starry_del_contact_fail))
            }
        }
    }

    private fun showConfirmDeleteDialog() {
        CloudCommonPopup.Builder(
            this,
            CloudCommonPopupConstants.COMMON_TWO_BUTTON_YES_NO
        )
            .setTitle(resources.getString(R.string.prompt))
            .setMessage(resources.getString(R.string.starry_contact_remove_confirm))
            .setOnPositiveListener { dialog, _ ->
                dialog?.dismiss()
                deleteAddressBook()
            }
            .setOnNegativeListener { dialog, _ -> dialog.dismiss() }
            .create()
            .show()
    }


    /**
     * 显示窗口
     */
    private fun popupWindow() {
        if (datasList.isEmpty()){
            return
        }

        isPopWin = true

        popup = PopupWindow(this)
        popup?.apply {
            width = ConstraintLayout.LayoutParams.WRAP_CONTENT  // 650  //ConstraintLayout.LayoutParams.MATCH_PARENT // contact_detail_company_change_btn?.width!!
            height = ConstraintLayout.LayoutParams.WRAP_CONTENT
            setBackgroundDrawable(getDrawable(R.color.transparent))
            val view = LayoutInflater.from(this@StarryContactDetailActivity).inflate(R.layout.starry_contact_detail_change_company_popup_layout,null,false)
            listView = view.findViewById(R.id.rvList)
            listView?.apply {
                isVerticalScrollBarEnabled = false //不显示滑动条
                val lm = LinearLayoutManager(context)
                lm.orientation = LinearLayoutManager.VERTICAL
                layoutManager = lm
                adapter = MyAdapter()
            }
            contentView = view
            isOutsideTouchable = true //点击PopupWindow以外的区域自动关闭该窗口
            isTouchable = true
            isFocusable = true      // pop显示时候，不让外部view相应点击事件
            showAsDropDown(contact_detail_company_change_btn, 0, 0, Gravity.END) //显示在edit控件的下面0,0代表偏移量
        }
    }

    //适配器
    inner class MyAdapter : RecyclerView.Adapter<MyAdapter.InnerHodler>() {

        inner class InnerHodler(itemView: View): RecyclerView.ViewHolder(itemView) {
        }

        override fun getItemId(position: Int): Long {
            return position.toLong()
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): InnerHodler {
            val itemView = LayoutInflater.from(parent.context).inflate(R.layout.starry_contact_detail_change_company_item, parent, false)
            return InnerHodler(itemView)
        }

        override fun getItemCount(): Int {
            return datasList.size
        }

        override fun onBindViewHolder(holder: InnerHodler, position: Int) {
            val companyModel = datasList[position]
            holder.itemView.title_tv?.text = companyModel.enterpriseName
            if (viewModel.currentSelectCompanyIndex == position) {
                holder.itemView.select_iv?.visibility = View.VISIBLE
            }else{
                holder.itemView.select_iv?.visibility = View.INVISIBLE
            }
            //为listView的每一个子条目设置监听,以区分每个删除按钮
            holder.itemView.starry_contact_detail_company_change_cl?.setOnClickListener(View.OnClickListener {
//                <EMAIL>() //更新ListView的数据
                viewModel.changeCurrentCompany(position)
                popup?.dismiss()
                popup = null
                isPopWin = false
            })

        }
    }

    private fun observeDtail(){
        viewModel.contactDetail.observe(this){

            contactId = it.contactId ?: ""
            // 首先判断it.meetingNo是否为空,空则显示:未获取到该账号详情
            if (it.meetingNo.isBlank()){
                content_ll?.visibility = View.GONE
                not_get_content_ll?.visibility = View.VISIBLE
                return@observe
            }

            // 从联系人列表查看联系人详情：联系人详情里显示备注（如果没有备注则仅显示昵称，效果同微信）、昵称、账号；
            val remark = it.remark ?: ""
            val nickName = it.nickName ?: ""
            val currentCompanyContactName = viewModel.getCurrentCompanyInfo(starryViewModel.currentCompanyModel.value)?.memberName ?: ""

            if (remark.isEmpty()){
                contactName = nickName
                contact_nickname_rl?.visibility = View.GONE
            }else if (nickName.isEmpty()) {
                contact_nickname_rl?.visibility = View.GONE
                contactName = remark
            }else{
                contact_nickname_rl?.visibility = View.VISIBLE
                if (remark == nickName){
                    contact_nickname_rl?.visibility = View.GONE
                }
                contactName = remark
            }

            // 企业联系人详细资料第一行显示优先级：备注＞昵称＞企业联系人姓名
            if (contactName.isBlank() && currentCompanyContactName.isNotBlank()){
                contact_nickname_rl?.visibility = View.GONE
                contactName = currentCompanyContactName
            }

            // 防止contact_user_name显示空
            if (contactName.isBlank()){
                contact_user_name_rl?.visibility = View.GONE
                contact_mobile_line?.visibility = View.GONE
            }else {
                contact_user_name_rl?.visibility = View.VISIBLE
                contact_mobile_line?.visibility = View.VISIBLE
                contact_user_name?.text = contactName
            }
            contact_nickname?.text = nickName
            contactNameOld = contactName
            contactNickName = nickName

            contact_mobile?.text = it.meetingNo

            // isMyContact 标记是否为收藏的联系人
            // 显示隐藏：编辑，删除，添加联系人,发起会议
            if (it.isMyContact){
                if (userFromType == StarryConstants.STARRY_USER_TYPE_CONTACT) {
                    user_delete_btn?.visibility = View.VISIBLE
                }else {
                    user_delete_btn?.visibility = View.GONE
                }
                contact_user_name_edit_btn?.visibility = View.VISIBLE
                contact_add_to_btn?.visibility = View.GONE
            }else{
                user_delete_btn?.visibility = View.GONE
                contact_user_name_edit_btn?.visibility = View.GONE
                contact_add_to_btn?.visibility = View.VISIBLE
            }
            contact_new_call_btn?.visibility = View.VISIBLE

            // 如果是自己,隐藏按钮
            if (it.meetingNo == StarryPreferences.getInstance().accountNo){
                contact_add_to_btn?.visibility = View.GONE
                contact_new_call_btn?.visibility = View.GONE
            }

            // 如果不是企业的成员，不能发起会议
            if (it.commonEnterprise.isEmpty()){
                contact_new_call_btn?.visibility = View.GONE
            }

            // 如果是已注销账号
            if (it.isDelete){
                val alpha = 0.3f

//                2.0账号注销处理UI调整
//                        当联系人/企业联系人账号注销时
//                1.联系人详情页：没有发起会议按钮
                contact_new_call_btn?.visibility = View.GONE
                contact_add_to_btn?.visibility = View.GONE

//                2.账号后的手机号替换为：已注销
                contact_mobile?.text = getString(R.string.starry_account_delete)
                contact_mobile?.setTextColor(getColor(R.color.starry_delete_red))
                contact_mobile?.alpha = 0.7f

//                if (contactName.isBlank()){
//                    contact_user_name_rl?.visibility = View.GONE
//                }else{
//                    contact_user_name_rl?.visibility = View.VISIBLE
//                }

//                3.备注、昵称、企业、企业姓名、职位，文字调整为灰色
//                contact_user_name?.setTextColor(getColor(R.color.starry_text_color_gray))
                contact_nickname?.setTextColor(getColor(R.color.starry_text_color_gray))
                contact_detail_company_name?.setTextColor(getColor(R.color.starry_text_color_gray))
                contact_detail_company_name_value_tv?.setTextColor(getColor(R.color.starry_text_color_gray))
                contact_detail_company_title_value_tv?.setTextColor(getColor(R.color.starry_text_color_gray))
                contact_detail_company_icon_iv?.setImageResource(R.mipmap.starry_home_comapny_icon_gray)

//                4.修改备注按钮、切换企业按钮为不可点击状态。
                if (contact_user_name_edit_btn?.visibility == View.VISIBLE) {
                    Tools.setViewButtonEnable(contact_user_name_edit_btn, false, alpha)
                }
                if (contact_detail_company_change_btn?.visibility == View.VISIBLE) {
                    Tools.setViewButtonEnable(contact_detail_company_change_btn, false, alpha)
                }
            }

            if (it.meetingNo == "" && it.id == ""){
                contact_new_call_btn?.visibility = View.GONE
                contact_add_to_btn?.visibility = View.GONE
                user_delete_btn?.visibility = View.GONE
            }
            content_ll.visibility = View.VISIBLE
        }
    }

}

