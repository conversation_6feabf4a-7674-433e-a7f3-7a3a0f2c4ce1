package com.czur.cloud.ui.component.segmentview;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.ColorStateList;
import android.util.AttributeSet;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.ui.mirror.comm.FastBleConstants;

import java.util.Calendar;

public class SegmentViewSitting extends LinearLayout{
    private final int SEGMENUCOUNT = 3;
    private TextView leftTextView;
    private TextView rightTextView;
    private TextView centerTextView;
    private onSegmentViewClickListener segmentListener;
    private TextView bgTextView;
    private long lastTime = 0;

    // 这是代码加载ui必须重写的方法
    public SegmentViewSitting(Context context) {
        super(context);
        initView();
    }

    public interface OnSegmentControlClickListener{
        void onSegmentControlClick(int index);
    }

    // 这是在xml布局使用必须重写的方法
    public SegmentViewSitting(Context context, AttributeSet attrs) {
        super(context, attrs);
        initView();
    }

    private void initView() {
        leftTextView = new TextView(getContext());
        rightTextView = new TextView(getContext());
        centerTextView = new TextView(getContext());
        bgTextView = new TextView(getContext());

        // 设置textview的布局宽高并设置为weight属性都为1
        int widthPixels = 260;//(750 / 120 = 6.25)
        WindowManager windowManager = (WindowManager) getContext().getSystemService(Context.WINDOW_SERVICE);
        if (windowManager != null) {
            DisplayMetrics outMetrics = new DisplayMetrics();
            windowManager.getDefaultDisplay().getMetrics(outMetrics);
            widthPixels = outMetrics.widthPixels/5;
        }
        leftTextView.setLayoutParams(new LayoutParams(widthPixels, LayoutParams.WRAP_CONTENT, 1));
        rightTextView.setLayoutParams(new LayoutParams(widthPixels, LayoutParams.WRAP_CONTENT, 1));
        centerTextView.setLayoutParams(new LayoutParams(widthPixels, LayoutParams.WRAP_CONTENT, 1));
        bgTextView.setLayoutParams(new LayoutParams(widthPixels*SEGMENUCOUNT, LayoutParams.WRAP_CONTENT, 1));

        // 初始化的默认文字  全部 日 周 月
        leftTextView.setText(R.string.segment_day);
        centerTextView.setText(R.string.segment_week);
        rightTextView.setText(R.string.segment_month);
        bgTextView.setText(R.string.segment_all);

        // 实现不同的按钮状态，不同的颜色
        @SuppressLint("ResourceType") ColorStateList csl = getResources().getColorStateList(R.drawable.segment_sitting_text_color_selector);
        leftTextView.setTextColor(csl);
        centerTextView.setTextColor(csl);
        rightTextView.setTextColor(csl);

        // 设置textview的内容位置居中
        leftTextView.setGravity(Gravity.CENTER);
        centerTextView.setGravity(Gravity.CENTER);
        rightTextView.setGravity(Gravity.CENTER);
        bgTextView.setGravity(Gravity.CENTER);

        // 设置textview的内边距
        int left=15;
        int top = 4;
        int right = 15;
        int bottom = 5;

        leftTextView.setPadding(left, top, right, bottom);
        centerTextView.setPadding(left, top, right, bottom);
        rightTextView.setPadding(left, top, right, bottom);
        bgTextView.setPadding(left, top, right, bottom);

        // 设置文字大小
        setSegmentTextSize(CZURConstants.SEGMENTTITLESIZE);

        // 设置背景资源
        leftTextView.setBackgroundResource(R.drawable.segment_sitting_left_background);
        centerTextView.setBackgroundResource(R.drawable.segment_sitting_center_background);
        rightTextView.setBackgroundResource(R.drawable.segment_sitting_right_background);

        bgTextView.setBackgroundResource(R.drawable.segment_sitting_bg_background);
        bgTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, CZURConstants.SEGMENTTITLESIZE+1);

        // 默认左侧textview为选中状态
        leftTextView.setSelected(true);

        // 加入textview
        LinearLayout layout = new LinearLayout(getContext());
        layout.setOrientation(LinearLayout.HORIZONTAL);
        LayoutParams lLayoutlayoutParams = new LayoutParams(
                widthPixels*SEGMENUCOUNT, ViewGroup.LayoutParams.WRAP_CONTENT);
        lLayoutlayoutParams.gravity=Gravity.CENTER;
        layout.setLayoutParams(lLayoutlayoutParams);
        layout.setPadding(2,2,2,2);
//        layout.setGravity(Gravity.CENTER);
        layout.addView(leftTextView);
        layout.addView(centerTextView);
        layout.addView(rightTextView);

        //new layout
        LinearLayout bglayout = new LinearLayout(getContext());
        LayoutParams bglLayoutlayoutParams = new LayoutParams(
                widthPixels*SEGMENUCOUNT, ViewGroup.LayoutParams.WRAP_CONTENT);
        bglayout.setLayoutParams(bglLayoutlayoutParams);
        bglayout.addView(bgTextView);

        RelativeLayout relativeLayout = new RelativeLayout(getContext());
        relativeLayout.setLayoutParams(new RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT));

        relativeLayout.addView(bglayout);
        relativeLayout.addView(layout);

        this.removeAllViews();
        this.addView(relativeLayout);

        this.invalidate();//重新draw()

        leftTextView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (leftTextView.isSelected()) {
                    return;
                }
                long cTime = Calendar.getInstance().getTimeInMillis();
                if (cTime - lastTime < FastBleConstants.MIN_CLICK_TIME){
                    return;
                }
                lastTime = cTime;

                leftTextView.setSelected(true);
                centerTextView.setSelected(false);
                rightTextView.setSelected(false);
                if (segmentListener != null) {
                    segmentListener.onSegmentViewClick(leftTextView, 0);
                }
            }
        });

        centerTextView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (centerTextView.isSelected()) {
                    return;
                }
                long cTime = Calendar.getInstance().getTimeInMillis();
                if (cTime - lastTime < FastBleConstants.MIN_CLICK_TIME){
                    return;
                }
                lastTime = cTime;

                leftTextView.setSelected(false);
                centerTextView.setSelected(true);
                rightTextView.setSelected(false);
                if (segmentListener != null) {
                    segmentListener.onSegmentViewClick(centerTextView, 1);
                }
            }
        });

        rightTextView.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (rightTextView.isSelected()) {
                    return;
                }
                long cTime = Calendar.getInstance().getTimeInMillis();
                if (cTime - lastTime < FastBleConstants.MIN_CLICK_TIME){
                    return;
                }
                lastTime = cTime;

                rightTextView.setSelected(true);
                centerTextView.setSelected(false);
                leftTextView.setSelected(false);
                if (segmentListener != null) {
                    segmentListener.onSegmentViewClick(rightTextView, 2);
                }
            }
        });

    }

    /**
     * 设置字体大小
     *
     * @param dp
     */
    private void setSegmentTextSize(int dp) {
        leftTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, dp);
        centerTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, dp);
        rightTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, dp);
    }

    /**
     * 手动设置选中的状态
     *
     * @param i
     */
    public void setSelect(int i) {
        if (i == 0) {
            leftTextView.setSelected(true);
            centerTextView.setSelected(false);
            rightTextView.setSelected(false);
        }else if (i == 1) {
            leftTextView.setSelected(false);
            centerTextView.setSelected(true);
            rightTextView.setSelected(false);
        }else {
            leftTextView.setSelected(false);
            centerTextView.setSelected(false);
            rightTextView.setSelected(true);
        }
    }

    /**
     * 设置控件显示的文字
     *
     * @param text
     * @param position
     */
    public void setSegmentText(CharSequence text, int position) {
        if (position == 0) {
            leftTextView.setText(text);
        }
        if (position == 1) {
            rightTextView.setText(text);
        }
    }

    // 定义一个接口接收点击事件
    public interface onSegmentViewClickListener {
        public void onSegmentViewClick(View view, int postion);
    }

    public void setOnSegmentViewClickListener(onSegmentViewClickListener segmentListener) {
        this.segmentListener = segmentListener;
    }
}