package com.czur.cloud.adapter;

import android.app.Activity;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.R;
import com.czur.cloud.model.UserShareModel;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.request.ImageRequest;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class DeleteUserAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_NORMA = 0;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<UserShareModel> datas;
    //是否进入选择

    private LayoutInflater mInflater;
    private LinkedHashMap<String, Boolean> isCheckedMap = new LinkedHashMap<>();

    /**
     * 构造方法
     */
    public DeleteUserAdapter(Activity activity, List<UserShareModel> datas) {
        this.mActivity = activity;
        cutDatas(datas);
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<UserShareModel> datas) {
        cutDatas(datas);
        this.datas = datas;
        notifyDataSetChanged();

    }

    private void cutDatas(List<UserShareModel> datas) {
        if (datas.size() >= 1) {
            for (int i = 0; i < datas.size(); i++) {
                if (i == 0) {
                    datas.remove(i);
                }
            }
        }
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new NormalViewHolder(mInflater.inflate(R.layout.item_et_delete, parent, false));
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {

        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);
            mHolder.etUserHeadName.setText(mHolder.mItem.getName());
            Uri uri;
            if (null==mHolder.mItem.getPhoto()){
                uri=null;
            }else {
                uri  = Uri.parse(mHolder.mItem.getPhoto());
            }
            DraweeController controller = Fresco.newDraweeControllerBuilder()
                    .setImageRequest(ImageRequest.fromUri(uri))
                    .setOldController(mHolder.etUserHeadImg.getController())
                    .build();
            mHolder.etUserHeadImg.setController(controller);

            mHolder.checkBox.setTag(mHolder.mItem.getUserId());
            mHolder.checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        if (!isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //选中时添加
                            isCheckedMap.put(mHolder.mItem.getUserId(), true);
                        }
                    } else {
                        if (isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //没选中时移除
                            isCheckedMap.remove(mHolder.mItem.getUserId());
                        }
                    }
                    if (onItemCheckListener != null) {
                        onItemCheckListener.onItemCheck(position, mHolder.mItem, isChecked,isCheckedMap, datas.size());
                    }
                }
            });


            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(position, mHolder.mItem, mHolder.checkBox);
                    }

                }
            });

        }
    }


    @Override
    public int getItemViewType(int position) {

        return ITEM_TYPE_NORMA;

    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }


    private class NormalViewHolder extends ViewHolder {
        public final View mView;
        UserShareModel mItem;
        SimpleDraweeView etUserHeadImg;
        TextView etUserHeadName;
        CheckBox checkBox;

        NormalViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            etUserHeadImg = (SimpleDraweeView) itemView.findViewById(R.id.et_user_head_img);
            etUserHeadName = (TextView) itemView.findViewById(R.id.et_user_head_name);
            checkBox = (CheckBox) itemView.findViewById(R.id.check);

        }


    }


    private onItemClickListener onItemClickListener;

    public void setOnItemClickListener(onItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface onItemClickListener {
        void onItemClick(int position, UserShareModel UserShareModel, CheckBox checkBox);
    }


    private OnItemCheckListener onItemCheckListener;

    public void setOnItemCheckListener(OnItemCheckListener onItemCheckListener) {
        this.onItemCheckListener = onItemCheckListener;
    }

    public interface OnItemCheckListener {
        void onItemCheck(int position, UserShareModel userShareModel, boolean isCheck,LinkedHashMap<String, Boolean> isCheckedMap, int totalSize);

    }

}
