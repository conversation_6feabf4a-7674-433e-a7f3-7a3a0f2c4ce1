package com.czur.cloud.ui.books;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.BookAdapter;
import com.czur.cloud.adapter.TagAdapter;
import com.czur.cloud.entity.realm.BookEntity;
import com.czur.cloud.entity.realm.PageEntity;
import com.czur.cloud.entity.realm.TagEntity;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.model.HandwritingCountModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.FirstPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.ControlScrollGridLayoutManager;
import com.czur.cloud.ui.component.popup.AddTagPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.UUID;

import io.realm.Realm;
import io.realm.RealmChangeListener;
import io.realm.RealmResults;
import io.realm.Sort;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class BookShelfActivity extends BaseActivity implements View.OnClickListener {

    private RelativeLayout bookshelfInsdieTopBar;
    private ImageView bookshelfBackBtn;
    private TextView bookshelfSelectAllBtn;
    private TextView bookshelfTitle;
    private TextView bookshelfCancelBtn;
    private RelativeLayout bookshelfUnselectedTopBarRl;
    private RelativeLayout bookshelfMoreBtn;
    private RelativeLayout bookshelfMultiSelectBtn;
    private RecyclerView bookshelfRecyclerView;
    private LinearLayout bookshelfBottomLl;
    private RelativeLayout bookshelfRenameRl;
    private RelativeLayout bookshelfDeleteRl;
    private BookAdapter bookAdapter;
    private int itemHeight = 0;
    private int statusBarHeight;
    private int screenHeight;
    private int rows;
    private int rowsMod;
    private boolean canScroll = false;
    private ControlScrollGridLayoutManager controlScrollGridLayoutManager;
    private List<BookEntity> bookEntities;
    private LinkedHashMap<String, Boolean> isCheckedMap;
    private EditText dialogEdt;
    private Realm realm;
    private UserPreferences userPreferences;
    private HttpManager httpManager;
    private SimpleDateFormat formatter;
    private boolean isOperate = false;
    private TextView bookshelfGuideTv;
    private FirstPreferences firstPreferences;
    private ImageView bookshelfGuideImg;
    private ImageView bookshelfFolderGuideImg;
    private TextView bookshelfFolderGuideTv;
    private TextView bookshelfFolderGuideHideTv;

    private ImageView bookshelfRenameImg;
    private ImageView bookshelfDeleteImg;
    private TextView bookshelfRenameTv;
    private TextView bookshelfDeleteTv;
    private ImageView bookTabImg;
    private TextView bookTabTv;
    private ImageView tagTabImg;
    private TextView tagTabTv;

    private LinearLayout bookTabLl;
    private RelativeLayout tagTabLl;
    private RecyclerView tagRecyclerView;
    private RealmResults<TagEntity> tagEntities;
    private TagAdapter tagAdapter;
    private RelativeLayout bookshelfTopBar;
    private RelativeLayout tagTopBar;
    private TextView editBtn;
    private boolean isSelected = false;
    private boolean isTag = false;
    private EditText dialogEdt1;
    private ImageView backBtn;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_bookshelf);
        initComponent();
        initRecyclerView();
        registerEvent();
        checkNotice();
    }


    private void initComponent() {
        EventBus.getDefault().register(this);
        userPreferences = UserPreferences.getInstance(this);
        firstPreferences = FirstPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        realm = Realm.getDefaultInstance();

        backBtn = (ImageView) findViewById(R.id.back_btn);
        bookshelfGuideImg = (ImageView) findViewById(R.id.bookshelf_guide_img);
        bookshelfGuideTv = (TextView) findViewById(R.id.bookshelf_guide_tv);
        bookshelfInsdieTopBar = (RelativeLayout) findViewById(R.id.bookshelf_inside_top_bar);
        bookshelfBackBtn = (ImageView) findViewById(R.id.bookshelf_back_btn);
        bookshelfSelectAllBtn = (TextView) findViewById(R.id.bookshelf_select_all_btn);
        bookshelfTitle = (TextView) findViewById(R.id.bookshelf_title);
        bookshelfCancelBtn = (TextView) findViewById(R.id.bookshelf_cancel_btn);
        bookshelfUnselectedTopBarRl = (RelativeLayout) findViewById(R.id.bookshelf_unselected_top_bar_rl);
        bookshelfMoreBtn = (RelativeLayout) findViewById(R.id.bookshelf_more_btn);
        bookshelfMultiSelectBtn = (RelativeLayout) findViewById(R.id.bookshelf_multi_select_btn);
        bookshelfRecyclerView = (RecyclerView) findViewById(R.id.bookshelf_recyclerView);
        bookshelfBottomLl = (LinearLayout) findViewById(R.id.bookshelf_bottom_ll);
        bookshelfRenameRl = (RelativeLayout) findViewById(R.id.bookshelf_rename_rl);
        bookshelfDeleteRl = (RelativeLayout) findViewById(R.id.bookshelf_delete_rl);


        bookshelfFolderGuideImg = (ImageView) findViewById(R.id.bookshelf_folder_guide_img);
        bookshelfFolderGuideTv = (TextView) findViewById(R.id.bookshelf_folder_guide_tv);
        bookshelfFolderGuideHideTv = (TextView) findViewById(R.id.bookshelf_folder_guide_hide_tv);
        bookshelfRenameImg = (ImageView) findViewById(R.id.bookshelf_rename_img);
        bookshelfDeleteImg = (ImageView) findViewById(R.id.bookshelf_delete_img);
        bookshelfRenameTv = (TextView) findViewById(R.id.bookshelf_rename_tv);
        bookshelfDeleteTv = (TextView) findViewById(R.id.bookshelf_delete_tv);

        bookTabImg = (ImageView) findViewById(R.id.book_tab_img);
        bookTabTv = (TextView) findViewById(R.id.book_tab_tv);
        tagTabImg = (ImageView) findViewById(R.id.tag_tab_img);
        tagTabTv = (TextView) findViewById(R.id.tag_tab_tv);
        bookTabLl = (LinearLayout) findViewById(R.id.book_tab_ll);
        tagTabLl = (RelativeLayout) findViewById(R.id.tag_tab_ll);

        bookshelfTopBar = (RelativeLayout) findViewById(R.id.bookshelf_top_bar);
        tagTopBar = (RelativeLayout) findViewById(R.id.tag_top_bar);
        editBtn = (TextView) findViewById(R.id.edit);
        tagRecyclerView = (RecyclerView) findViewById(R.id.tag_recyclerView);

        bookshelfTitle.setText(R.string.books);
        checkPromptToShow();


        tagEntities = realm.where(TagEntity.class).equalTo("isDelete", 0).distinct("tagName").sort("createTime", Sort.ASCENDING).findAll();
        tagAdapter = new TagAdapter(this, tagEntities, false, realm);
        tagAdapter.setAddTagListener(addTagListener);
        tagAdapter.setOnDeleteClickListener(onDeleteClickListener);
        tagAdapter.setOnTagItemClickListener(onTagItemClickListener);
        tagRecyclerView.setAdapter(tagAdapter);
        tagRecyclerView.setHasFixedSize(true);
        tagRecyclerView.setLayoutManager(new GridLayoutManager(this, 2));
        changeTabIcon(0);


    }

    private TagAdapter.AddTagListener addTagListener = new TagAdapter.AddTagListener() {
        @Override
        public void onAddTagClickListener(int position) {
            showAddTagDialog(true, null);
        }
    };

    private TagAdapter.OnDeleteClickListener onDeleteClickListener = new TagAdapter.OnDeleteClickListener() {
        @Override
        public void onTagDeleteClick(final TagEntity tagEntity, int position) {
            realm.executeTransaction(new Realm.Transaction() {
                @Override
                public void execute(Realm realm) {
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                    String curDate = formatter.format(new Date(System.currentTimeMillis()));
                    tagEntity.setUpdateTime(curDate);
                    tagEntity.setIsDirty(1);
                    tagEntity.setIsDelete(1);

                    RealmResults<PageEntity> pageEntities = realm.where(PageEntity.class).equalTo("tagId", tagEntity.getTagId()).equalTo("isDelete", 0).findAll();
                    for (PageEntity pageEntity : pageEntities) {
                        pageEntity.setUpdateTime(curDate);
                        pageEntity.setIsDirty(pageEntity.getIsDirty() == 1 ? 1 : 2);
                        pageEntity.setTagName("");
                        pageEntity.setTagId("");
                    }
                    tagAdapter.refreshData(true);
                    startAutoSync();
                }
            });

        }
    };

    private TagAdapter.OnTagItemClickListener onTagItemClickListener = new TagAdapter.OnTagItemClickListener() {
        @Override
        public void onTagEntityClick(TagEntity tagEntity, int position) {
            if (isSelected) {
                showAddTagDialog(false, tagEntity);
            } else {
                Intent intent = new Intent(BookShelfActivity.this, BookPageActivity.class);
                intent.putExtra("isTag", true);
                intent.putExtra("tagName", tagEntity.getTagName());
                intent.putExtra("tagId", tagEntity.getTagId());
                ActivityUtils.startActivity(intent);
            }

        }
    };

    /**
     * @des: 显示添加Dialog
     * @params:
     * @return:
     */

    private void showAddTagDialog(final boolean isAdd, final TagEntity tagEntity) {
        AddTagPopup.Builder builder = new AddTagPopup.Builder(BookShelfActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        if (isAdd) {
            builder.setTitle(getString(R.string.add_tag));
        } else {
            builder.setTitle(getString(R.string.rename_tag));
        }
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                realm.executeTransaction(new Realm.Transaction() {
                    @Override
                    public void execute(Realm realm) {
                        if (!EtUtils.containsEmoji(dialogEdt1.getText().toString())) {
                            if (Validator.isNotEmpty(dialogEdt1.getText().toString())) {
                                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                                String curDate = formatter.format(new Date(System.currentTimeMillis()));
                                if (isAdd) {
                                    TagEntity tagEntity = new TagEntity();
                                    tagEntity.setIsSelf(1);
                                    tagEntity.setTagId(UUID.randomUUID().toString());
                                    tagEntity.setTagName(dialogEdt1.getText().toString());
                                    tagEntity.setIsDirty(1);
                                    tagEntity.setCreateTime(curDate);
                                    tagEntity.setUpdateTime(curDate);
                                    realm.copyToRealmOrUpdate(tagEntity);
                                    tagAdapter.refreshData(false);
                                } else {
                                    //重命名tag
                                    tagEntity.setIsDirty(1);
                                    tagEntity.setUpdateTime(curDate);
                                    tagEntity.setTagName(dialogEdt1.getText().toString());
                                    RealmResults<PageEntity> pageEntities = realm.where(PageEntity.class).equalTo("tagId", tagEntity.getTagId()).findAll();
                                    for (PageEntity pageEntity : pageEntities) {
                                        pageEntity.setTagName(tagEntity.getTagName());
                                        pageEntity.setUpdateTime(curDate);
                                        pageEntity.setIsDirty(1);
                                    }
                                    tagAdapter.refreshData(true);
                                }
                                startAutoSync();
                            } else {
                                showMessage(R.string.tag_should_not_be_empty);
                            }
                        } else {
                            showMessage(R.string.nickname_toast_symbol);
                        }


                    }
                });

                dialog.dismiss();

            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });

        AddTagPopup commonPopup = builder.create();
        dialogEdt1 = (EditText) commonPopup.getWindow().findViewById(R.id.edt);
        commonPopup.show();
    }

    private void checkPromptToShow() {
        if (firstPreferences.isFirstBookGuide()) {
            bookshelfGuideTv.setVisibility(View.VISIBLE);
            bookshelfGuideImg.setVisibility(View.VISIBLE);
            bookshelfFolderGuideImg.setVisibility(View.GONE);
            bookshelfFolderGuideHideTv.setVisibility(View.GONE);
            bookshelfFolderGuideTv.setVisibility(View.GONE);
        } else {
            bookshelfGuideTv.setVisibility(View.GONE);
            bookshelfGuideImg.setVisibility(View.GONE);
            if (firstPreferences.isFirstBookFolderGuide()) {
                bookshelfFolderGuideImg.setVisibility(View.VISIBLE);
                bookshelfFolderGuideHideTv.setVisibility(View.VISIBLE);
                bookshelfFolderGuideTv.setVisibility(View.VISIBLE);
            } else {
                bookshelfFolderGuideImg.setVisibility(View.GONE);
                bookshelfFolderGuideHideTv.setVisibility(View.GONE);
                bookshelfFolderGuideTv.setVisibility(View.GONE);
            }
        }
    }

    private void changeTabIcon(int index) {
        switch (index) {
            case 0:
                bookTabImg.setSelected(true);
                tagTabImg.setSelected(false);
                bookTabTv.setSelected(true);
                tagTabTv.setSelected(false);
                bookshelfRecyclerView.setVisibility(View.VISIBLE);
                bookshelfTopBar.setVisibility(View.VISIBLE);
                tagRecyclerView.setVisibility(View.GONE);
                tagTopBar.setVisibility(View.GONE);
                checkPromptToShow();
                isSelected = false;
                isTag = false;
                editBtn.setText(R.string.edit);
                tagAdapter.refreshData(isSelected);

                break;

            case 1:
                isTag = true;
                bookTabImg.setSelected(false);
                tagTabImg.setSelected(true);
                bookTabTv.setSelected(false);
                tagTabTv.setSelected(true);
                bookshelfTopBar.setVisibility(View.GONE);
                bookshelfRecyclerView.setVisibility(View.GONE);
                tagRecyclerView.setVisibility(View.VISIBLE);
                tagTopBar.setVisibility(View.VISIBLE);


                bookshelfGuideTv.setVisibility(View.GONE);
                bookshelfGuideImg.setVisibility(View.GONE);
                bookshelfFolderGuideImg.setVisibility(View.GONE);
                bookshelfFolderGuideHideTv.setVisibility(View.GONE);
                bookshelfFolderGuideTv.setVisibility(View.GONE);
                break;

            default:
                break;
        }
    }

    /**
     * @des:数据库改变监听
     * @params:
     * @return:
     */

    private RealmChangeListener realmChangeListener = new RealmChangeListener() {
        @Override
        public void onChange(Object element) {
            if (!isTag) {
                checkPromptToShow();
                resetAndRefresh();
            }


        }
    };

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {

            case ADD_TAGS:
            case ADD_TAG:
            case CHANGE_TAG:
            case DELETE_TAG:
            case DELETE_TAGS:
            case DELETE_TAG_IN_PREVIEW:
                if (!isTag) {
                    checkPromptToShow();
                }
                tagEntities = realm.where(TagEntity.class).equalTo("isDelete", 0).distinct("tagName").sort("createTime", Sort.ASCENDING).findAll();
                tagAdapter.refreshData(tagEntities);
                break;
            default:
                break;
        }
    }

    /**
     * @des: 重置选中状态并且刷新
     * @params:
     * @return:
     */
    private void resetAndRefresh() {

        RealmResults<BookEntity> deleteEntities = realm.where(BookEntity.class).equalTo("isDelete", 1).findAll();
        for (BookEntity deleteEntity : deleteEntities) {
            if (isCheckedMap.containsKey(deleteEntity.getBookId())) {
                //没选中时移除
                isCheckedMap.remove(deleteEntity.getBookId());
            }
        }
        if (isCheckedMap.size() == 1) {
            bookshelfTitle.setText(R.string.select_one_book);
            showDelete();
            showRename();
        } else if (isCheckedMap.size() > 1) {
            bookshelfTitle.setText(String.format(getString(R.string.select_num_book), isCheckedMap.size() + ""));
            darkRename();
            showDelete();
        } else {
            darkRename();
            darkDelete();
            if (bookEntities.size() <= 0) {
                resetStatus();
                int finalRows = calculateRows(bookEntities);
                canListScroll();
                bookAdapter.refreshData(bookEntities, finalRows, false, isCheckedMap);
                return;
            }
            if (isMultiSelect) {
                bookshelfTitle.setText(String.format(getString(R.string.select_num_book), isCheckedMap.size() + ""));

            }
        }

        checkSize(isCheckedMap, bookAdapter.getTotalSize());
        bookEntities = realm.where(BookEntity.class)
                .equalTo("isDelete", 0)
                .distinct("bookName")
                .findAll()
                .sort("createTime", Sort.DESCENDING);

        int finalRows = calculateRows(bookEntities);
        canListScroll();
        bookAdapter.refreshData(bookEntities, finalRows, isMultiSelect, isCheckedMap);
    }


    /**
     * @des: 显示选择TopBar
     * @params:
     * @return:
     */

    private void showSelectTopBar() {
        darkDelete();
        darkRename();
        bookshelfGuideTv.setVisibility(View.GONE);
        bookshelfGuideImg.setVisibility(View.GONE);
        bookshelfFolderGuideImg.setVisibility(View.GONE);
        bookshelfFolderGuideHideTv.setVisibility(View.GONE);
        bookshelfFolderGuideTv.setVisibility(View.GONE);
        bookshelfBottomLl.setVisibility(View.VISIBLE);

        isOperate = false;
        bookshelfUnselectedTopBarRl.setVisibility(View.GONE);
        bookshelfBackBtn.setVisibility(View.GONE);
        bookshelfCancelBtn.setVisibility(View.VISIBLE);
        bookshelfSelectAllBtn.setVisibility(View.VISIBLE);
        bookshelfTitle.setText(String.format(getString(R.string.select_num_book), isCheckedMap.size() + ""));
        bookshelfCancelBtn.setText(R.string.cancel);
        bookshelfSelectAllBtn.setText(R.string.select_all);
    }

    /**
     * @des: 隐藏选择TopBar
     * @params:
     * @return:
     */

    private void hideSelectTopBar() {
        checkPromptToShow();
        isOperate = true;
        bookshelfBottomLl.setVisibility(View.GONE);
        bookshelfUnselectedTopBarRl.setVisibility(View.VISIBLE);
        bookshelfBackBtn.setVisibility(View.VISIBLE);
        bookshelfCancelBtn.setVisibility(View.GONE);
        bookshelfSelectAllBtn.setVisibility(View.GONE);
        bookshelfTitle.setText(R.string.books);
    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initRecyclerView() {
        bookEntities = new ArrayList<>();
        isCheckedMap = new LinkedHashMap<>();
        bookEntities = realm.where(BookEntity.class)
                .equalTo("isDelete", 0)
                .distinct("bookName")
                .findAll()
                .sort("createTime", Sort.DESCENDING);
        int finalRows = calculateRows(bookEntities);
        bookAdapter = new BookAdapter(this, bookEntities, false, finalRows);
        bookAdapter.setOnBookItemClickListener(onItemClickListener);
        bookAdapter.setOnItemCheckListener(onItemCheckListener);
        bookAdapter.setOnBookItemLongClickListener(onBookItemLongClickListener);
        bookAdapter.setAddBookClickListener(addBookClickListener);
        bookshelfRecyclerView.setAdapter(bookAdapter);
        bookshelfRecyclerView.setHasFixedSize(true);
        canListScroll();


    }

    private void registerEvent() {
        realm.addChangeListener(realmChangeListener);
        editBtn.setOnClickListener(this);
        bookTabLl.setOnClickListener(this);
        tagTabLl.setOnClickListener(this);
        bookshelfFolderGuideHideTv.setOnClickListener(this);
        bookshelfGuideTv.setOnClickListener(this);
        bookshelfBackBtn.setOnClickListener(this);
        bookshelfSelectAllBtn.setOnClickListener(this);
        bookshelfCancelBtn.setOnClickListener(this);
        bookshelfMoreBtn.setOnClickListener(this);
        bookshelfMultiSelectBtn.setOnClickListener(this);
        bookshelfRenameRl.setOnClickListener(this);
        bookshelfDeleteRl.setOnClickListener(this);
        backBtn.setOnClickListener(this);

    }

    /**
     * @des: 控制列表可不可以滚动
     * @params:
     * @return:
     */

    private void canListScroll() {
        controlScrollGridLayoutManager = new ControlScrollGridLayoutManager(this, 3);
        controlScrollGridLayoutManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
            @Override
            public int getSpanSize(int position) {
                return bookAdapter.isFooter(position) ? controlScrollGridLayoutManager.getSpanCount() : 1;
            }
        });
        if (canScroll) {
            controlScrollGridLayoutManager.setScrollEnabled(true);
        } else {
            controlScrollGridLayoutManager.setScrollEnabled(false);
        }
        bookshelfRecyclerView.setLayoutManager(controlScrollGridLayoutManager);
    }

    /**
     * @des: 计算实际需要补多少空白Item
     * @params:
     * @return:
     */

    private int calculateRows(List<BookEntity> bookEntities) {
        screenHeight = ScreenUtils.getScreenHeight();
        statusBarHeight = BarUtils.getStatusBarHeight() + SizeUtils.dp2px(30);
        itemHeight = SizeUtils.dp2px(150);
        rows = (screenHeight - statusBarHeight) / itemHeight;
        rowsMod = (screenHeight - statusBarHeight) % itemHeight;
        int realCount = rows * 3;
        if (rowsMod > 0) {
            if (bookEntities.size() >= realCount) {
                canScroll = true;
            } else {
                canScroll = false;
            }
            return rows + 1;

        } else {
            if (bookEntities.size() > realCount) {
                canScroll = true;
            } else {
                canScroll = false;
            }
            return rows;
        }


    }

    /**
     * @des: 长按监听
     * @params:
     * @return:
     */

    private BookAdapter.OnBookItemLongClickListener onBookItemLongClickListener = new BookAdapter.OnBookItemLongClickListener() {
        @Override
        public void onBookEntityLongClick(int position, BookEntity bookEntity, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize) {

            multiSelect();
            checkSize(isCheckedMap, totalSize);
            bookshelfTitle.setText(R.string.select_one_book);
            bookshelfBottomLl.setVisibility(View.VISIBLE);
            showRename();
            bookAdapter.refreshData(true);

        }
    };

    /**
     * @des: 判断检查状态
     * @params:
     * @return:
     */

    private void checkSize(LinkedHashMap<String, Boolean> isCheckedMap, int totalSize) {
        if (isCheckedMap.size() < totalSize) {
            bookshelfSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        } else {
            bookshelfSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;
        }
    }

    /**
     * @des: Item选中监听
     * @params:
     * @return:
     */

    private BookAdapter.OnItemCheckListener onItemCheckListener = new BookAdapter.OnItemCheckListener() {
        @Override
        public void onItemCheck(int position, BookEntity item, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize) {
            BookShelfActivity.this.isCheckedMap = isCheckedMap;

            //如果选中一个 文案变为已选中1个
            if (isCheckedMap.size() == 1) {
                bookshelfTitle.setText(R.string.select_one_book);
                showRename();
                showDelete();
            } else if (isCheckedMap.size() > 1) {
                bookshelfTitle.setText(String.format(getString(R.string.select_num_book), isCheckedMap.size() + ""));
                showDelete();
                darkRename();
            } else {
                darkRename();
                darkDelete();
                if (isMultiSelect) {
                    bookshelfTitle.setText(String.format(getString(R.string.select_num_book), isCheckedMap.size() + ""));

                }

            }
            //如果选择不是全部Item  text变为取消全选
            checkSize(isCheckedMap, totalSize);

        }

    };

    private void showRename() {
        bookshelfRenameRl.setClickable(true);
        bookshelfRenameRl.setEnabled(true);

        bookshelfRenameImg.setSelected(true);
        bookshelfRenameTv.setTextColor(getResources().getColor(R.color.white));
    }

    private void showDelete() {
        bookshelfDeleteRl.setClickable(true);
        bookshelfDeleteRl.setEnabled(true);

        bookshelfDeleteImg.setSelected(true);
        bookshelfDeleteTv.setTextColor(getResources().getColor(R.color.white));
    }

    private void darkDelete() {
        bookshelfDeleteRl.setClickable(false);
        bookshelfDeleteRl.setEnabled(false);

        bookshelfDeleteImg.setSelected(false);
        bookshelfDeleteTv.setTextColor(getResources().getColor(R.color.dark_text));
    }

    private void darkRename() {
        bookshelfRenameRl.setClickable(false);
        bookshelfRenameRl.setEnabled(false);
        bookshelfRenameImg.setSelected(false);
        bookshelfRenameTv.setTextColor(getResources().getColor(R.color.dark_text));
    }


    /**
     * @des: Item点击监听
     * @params:
     * @return:
     */

    private BookAdapter.OnBookItemClickListener onItemClickListener = new BookAdapter.OnBookItemClickListener() {


        @Override
        public void onBookEntityClick(BookEntity bookEntity, int position, CheckBox checkBox) {
            if (isMultiSelect) {
                checkBox.setChecked(!checkBox.isChecked());
            } else {
                Intent intent = new Intent(BookShelfActivity.this, BookPageActivity.class);
                intent.putExtra("noteName", bookEntity.getBookName());
                intent.putExtra("bookId", bookEntity.getBookId());
                ActivityUtils.startActivity(intent);
            }


        }
    };
    /**
     * @des: 添加Book点击监听
     * @params:
     * @return:
     */

    private BookAdapter.AddBookClickListener addBookClickListener = new BookAdapter.AddBookClickListener() {
        @Override
        public void onAddBookClick(int position) {
            ActivityUtils.startActivity(AddBookActivity.class);

        }
    };

    private boolean isMultiSelect = false;
    private boolean isSelectAll = false;

    /**
     * @des: 多选
     * @params:
     * @return:
     */

    private void multiSelect() {
        if (Validator.isNotEmpty(bookEntities)) {
            isMultiSelect = !isMultiSelect;
            bookAdapter.refreshData(isMultiSelect);
            if (isMultiSelect) {
                showSelectTopBar();
            } else {
                hideSelectTopBar();
            }
        }
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.bookshelf_folder_guide_hide_tv:
                firstPreferences.setIsFirstBookFolderGuide(false);
                bookshelfFolderGuideImg.setVisibility(View.GONE);
                bookshelfFolderGuideHideTv.setVisibility(View.GONE);
                bookshelfFolderGuideTv.setVisibility(View.GONE);
                break;
            case R.id.back_btn:
            case R.id.bookshelf_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.bookshelf_cancel_btn:
                cancelEvent();
                break;
            case R.id.bookshelf_select_all_btn:
                selectAll();
                break;
            case R.id.bookshelf_guide_tv:
            case R.id.bookshelf_more_btn:
                firstPreferences.setIsFirstBookGuide(false);
                bookshelfGuideTv.setVisibility(View.GONE);
                bookshelfGuideImg.setVisibility(View.GONE);
                ActivityUtils.startActivity(BookMenuActivity.class);
                break;
            case R.id.bookshelf_multi_select_btn:
                multiSelect();
                break;

            case R.id.bookshelf_rename_rl:
                renameBooks();
                break;
            case R.id.bookshelf_delete_rl:
                confirmDeleteDialog();
                break;
            case R.id.tag_tab_ll:
                changeTabIcon(1);
                break;
            case R.id.book_tab_ll:
                changeTabIcon(0);
                break;
            case R.id.edit:
                checkEditBtn();
                tagAdapter.refreshData(isSelected);
                break;
            default:
                break;
        }
    }

    private void checkEditBtn() {
        isSelected = !isSelected;
        if (isSelected) {
            editBtn.setText(R.string.finish);
        } else {
            editBtn.setText(R.string.edit);
        }
    }


    /**
     * @des: 显示删除Dialog
     * @params:
     * @return:
     */

    private void confirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(BookShelfActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.confirm_delete));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                deleteBooks();
                dialog.dismiss();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    /**
     * @des: 重命名Books
     * @params:
     * @return:
     */

    private void renameBooks() {
        final CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(BookShelfActivity.this, CloudCommonPopupConstants.EDT_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.input_file_name));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (Validator.isNotEmpty(dialogEdt.getText().toString())) {
                    //不能含有表情
                    if (EtUtils.containsEmoji(dialogEdt.getText().toString())) {
                        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(BookShelfActivity.this, CloudCommonPopupConstants.COMMON_ONE_BUTTON);
                        builder.setTitle(getResources().getString(R.string.prompt));
                        builder.setMessage(getResources().getString(R.string.nickname_toast_symbol));
                        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                dialog.dismiss();
                            }
                        });
                        CloudCommonPopup commonPopup = builder.create();
                        commonPopup.show();
                    } else {
                        //文件名字重复添加括号
                        int i = 0;
                        String bookName = dialogEdt.getText().toString();
                        String finalName = bookName;
                        RealmResults<BookEntity> sameBook = realm.where(BookEntity.class)
                                .equalTo("isDelete", 0)
                                .equalTo("bookName", dialogEdt.getText().toString())
                                .findAll();
                        while (Validator.isNotEmpty(sameBook)) {
                            i++;
                            finalName = bookName + String.format(getString(R.string.repeat_name_format), i + "");
                            sameBook = realm.where(BookEntity.class)
                                    .equalTo("isDelete", 0)
                                    .equalTo("bookName", finalName)
                                    .findAll();
                        }
                        final String finalName1 = finalName;
                        final String finalRemainId = (String) isCheckedMap.keySet().toArray()[0];
                        realm.executeTransaction(new Realm.Transaction() {
                            @Override
                            public void execute(Realm realms) {
                                //先查找后得到BookEntity对象
                                BookEntity bookEntity = realm.where(BookEntity.class)
                                        .equalTo("isDelete", 0)
                                        .equalTo("bookId", finalRemainId)
                                        .findFirst();
                                String curDate = formatter.format(new Date(System.currentTimeMillis()));

                                RealmResults<PageEntity> pageEntities = realm.where(PageEntity.class)
                                        .equalTo("noteName", bookEntity.getBookName())
                                        .equalTo("isDelete", 0)
                                        .findAll();
                                for (PageEntity pageEntity : pageEntities) {
                                    pageEntity.setNoteName(finalName1);
                                    pageEntity.setUpdateTime(curDate);
                                    pageEntity.setIsDirty(2);
                                }
                                RealmResults<BookEntity> bookEntities = realm.where(BookEntity.class)
                                        .equalTo("isDelete", 0)
                                        .equalTo("bookName", bookEntity.getBookName())
                                        .findAll();
                                for (BookEntity entity : bookEntities) {
                                    entity.setBookName(finalName1);
                                    entity.setUpdateTime(curDate);
                                    entity.setIsDirty(1);
                                }

                                startAutoSync();

                            }
                        });

                        resetStatus();
                        int finalRows = calculateRows(bookEntities);
                        canListScroll();
                        bookAdapter.refreshData(bookEntities, finalRows, false, isCheckedMap);
                        dialog.dismiss();
                    }


                } else {
                    showMessage(R.string.rename_empty);
                }
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        dialogEdt = (EditText) commonPopup.getWindow().findViewById(R.id.edt);
        commonPopup.show();
    }

    private void resetStatus() {
        isMultiSelect = false;
        isSelectAll = false;
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        hideSelectTopBar();
    }

    /**
     * @des: 选中所有
     * @params:
     * @return:
     */

    private void selectAll() {
        if (!isSelectAll) {
            for (int i = 0; i < bookEntities.size(); i++) {

                if (!isCheckedMap.containsKey((bookEntities.get(i).getBookId()))) {
                    isCheckedMap.put(bookEntities.get(i).getBookId(), true);
                }
            }
            if (isCheckedMap.size() == 1) {
                showRename();
            } else {
                darkRename();
            }
            bookshelfSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;

        } else {

            showRename();
            isCheckedMap.clear();
            isCheckedMap = new LinkedHashMap<>();
            bookshelfSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        }
        bookshelfTitle.setText(String.format(getString(R.string.select_num_book), isCheckedMap.size() + ""));
        bookAdapter.refreshData(bookEntities, -1, true, isCheckedMap);
    }

    /**
     * @des: 取消事件
     * @params:
     * @return:
     */

    private void cancelEvent() {
        resetStatus();
        bookAdapter.refreshData(bookEntities, -1, false, isCheckedMap);
    }

    /**
     * @des: 删除books
     * @params:
     * @return:
     */


    private void deleteBooks() {

        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realms) {
                //先查找后得到BookEntity对象
                for (String finalId : isCheckedMap.keySet()) {

                    BookEntity sameNameBook = realm.where(BookEntity.class)
                            .equalTo("bookId", finalId)
                            .equalTo("isDelete", 0)
                            .findFirst();

                    RealmResults<PageEntity> deletePageEntities = realm.where(PageEntity.class)
                            .equalTo("noteName", sameNameBook.getBookName())
                            .equalTo("isDelete", 0)
                            .findAllAsync();
                    String curDate = formatter.format(new Date(System.currentTimeMillis()));
                    for (PageEntity deletePageEntity : deletePageEntities) {
                        deletePageEntity.setIsDelete(1);
                        deletePageEntity.setIsDirty(1);
                        deletePageEntity.setUpdateTime(curDate);
                    }

                    RealmResults<BookEntity> sameBooks = realm.where(BookEntity.class)
                            .equalTo("bookName", sameNameBook.getBookName())
                            .equalTo("isDelete", 0)
                            .findAllAsync();
                    for (BookEntity sameBook : sameBooks) {
                        sameBook.setIsDelete(1);
                        sameBook.setIsDirty(1);
                        sameBook.setUpdateTime(curDate);
                    }


                }
                resetStatus();
                startAutoSync();
            }
        });


    }

    /**
     * @des: 提示获得手写识别次数
     * @params:[]
     * @return:void
     */
    private void checkNotice() {

        httpManager.request().checkNoticeHandwriting(
                userPreferences.getUserId(), HandwritingCountModel.class, new MiaoHttpManager.CallbackNetwork<HandwritingCountModel>() {
                    @Override
                    public void onNoNetwork() {
                        hideProgressDialog();
                    }

                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<HandwritingCountModel> entity) {
                        hideProgressDialog();

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<HandwritingCountModel> entity) {
                        hideProgressDialog();
                        if (entity.getCode() == MiaoHttpManager.STATUS_BOOKS_HANDWRITING_NOT_NOTICED) {
                            CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(BookShelfActivity.this, CloudCommonPopupConstants.COMMON_ONE_BUTTON);
                            builder.setTitle(getResources().getString(R.string.congratulation));
                            builder.setMessage(getResources().getString(R.string.first_time_handwriting_notice));
                            builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialog, int which) {
                                    dialog.dismiss();
                                }
                            });
                            CloudCommonPopup commonPopup = builder.create();
                            commonPopup.show();
                        } else if (entity.getCode() == MiaoHttpManager.STATUS_BOOKS_HANDWRITING_NOTICED) {

                        } else if (entity.getCode() == MiaoHttpManager.STATUS_BOOK_INVALID) {

                        } else if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            showMessage(R.string.toast_internal_error);
                        } else {
                            showMessage(R.string.request_failed_alert);
                        }

                    }

                    @Override
                    public void onError(Exception e) {

                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
//        removeStickyEvent();
        realm.removeChangeListener(realmChangeListener);
        realm.close();
    }
}
