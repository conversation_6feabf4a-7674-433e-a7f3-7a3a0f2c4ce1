package com.czur.cloud.ui.books;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.czur.cloud.R;
import com.czur.cloud.adapter.BookPdfAdapter;
import com.czur.cloud.entity.realm.BookPdfEntity;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.util.PermissionUtil;
import com.czur.cloud.util.validator.Validator;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmChangeListener;
import io.realm.Sort;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class BookPdfActivity extends BaseActivity implements View.OnClickListener {

    private RecyclerView gridFileList;
    private List<BookPdfEntity> datas;
    private ImageView myPdfBackBtn;
    private TextView myPdfTopSelectAllBtn;
    private TextView myPdfTitle;
    private TextView myPdfCancelBtn;
    private RelativeLayout myPdfDeleteRl;
    private RelativeLayout pdfEmptyRl;
    private RelativeLayout myPdfMultiSelectBtn;
    private RecyclerView myPdfRecyclerView;
    private BookPdfAdapter bookPdfAdapter;
    private List<BookPdfEntity> pdfEntities;
    private LinearLayout myPdfBottomLl;
    private LinkedHashMap<String, Boolean> isCheckedMap;
    private Realm realm;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_my_pdf);
        initComponent();
        initRecyclerView();
        registerEvent();
    }

    private void initComponent() {
        pdfEmptyRl = (RelativeLayout) findViewById(R.id.pdf_empty_rl);
        myPdfDeleteRl = (RelativeLayout) findViewById(R.id.my_pdf_delete_rl);
        myPdfBottomLl = (LinearLayout) findViewById(R.id.my_pdf_bottom_ll);
        myPdfBackBtn = (ImageView) findViewById(R.id.my_pdf_back_btn);
        myPdfTopSelectAllBtn = (TextView) findViewById(R.id.my_pdf_top_select_all_btn);
        myPdfTitle = (TextView) findViewById(R.id.my_pdf_title);
        myPdfCancelBtn = (TextView) findViewById(R.id.my_pdf_cancel_btn);
        myPdfMultiSelectBtn = (RelativeLayout) findViewById(R.id.my_pdf_multi_select_btn);
        myPdfRecyclerView = (RecyclerView) findViewById(R.id.my_pdf_recyclerView);
        myPdfTitle.setText(R.string.my_pdf);
        realm = Realm.getDefaultInstance();

    }

    private void registerEvent() {
        realm.addChangeListener(realmChangeListener);
        myPdfTopSelectAllBtn.setOnClickListener(this);
        myPdfCancelBtn.setOnClickListener(this);
        myPdfMultiSelectBtn.setOnClickListener(this);
        myPdfBackBtn.setOnClickListener(this);
        myPdfDeleteRl.setOnClickListener(this);

    }

    /**
     * @des: 是否显示空文件夹提示区域
     * @params:
     * @return:
     */

    private void isShowEmptyPrompt() {
        if (Validator.isEmpty(pdfEntities) || pdfEntities.size() <= 0) {
            myPdfRecyclerView.setVisibility(View.GONE);
            pdfEmptyRl.setVisibility(View.VISIBLE);
        } else {
            myPdfRecyclerView.setVisibility(View.VISIBLE);
            pdfEmptyRl.setVisibility(View.GONE);
        }
    }

    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initRecyclerView() {
        pdfEntities = new ArrayList<>();
        isCheckedMap = new LinkedHashMap<>();
        pdfEntities = realm.where(BookPdfEntity.class).equalTo("isDelete", 0).sort("createTime", Sort.DESCENDING).findAll();


//        scanSdPdfIsExist();

        bookPdfAdapter = new BookPdfAdapter(this, pdfEntities, false);
        bookPdfAdapter.setOnItemCheckListener(onItemCheckListener);
        bookPdfAdapter.setOnItemClickListener(onItemClickListener);
        bookPdfAdapter.setOnItemLongClickListener(onItemLongClickListener);
        myPdfRecyclerView.setAdapter(bookPdfAdapter);
        myPdfRecyclerView.setHasFixedSize(true);
        myPdfRecyclerView.setLayoutManager(new LinearLayoutManager(this));

        isShowEmptyPrompt();
    }

    /**
     * @des: 扫描本地PDF是否存在，不存在删除数据库
     * @params:
     * @return:
     */

    private void scanSdPdfIsExist() {
        final List<String> pdfExistPaths = new ArrayList<>();
        for (int i = 0; i < pdfEntities.size(); i++) {
            final String pdfPath = pdfEntities.get(i).getPdfPath();

            ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
                @Override
                public Void doInBackground() {


                    if (!FileUtils.isFileExists(pdfPath)) {
                        pdfExistPaths.add(pdfPath);
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                realm.executeTransaction(new Realm.Transaction() {
                                    @Override
                                    public void execute(Realm realms) {
                                        //先查找后得到PageEntity对象
                                        realm.where(BookPdfEntity.class).equalTo("pdfPath", pdfPath).findAll().deleteFirstFromRealm();
                                    }
                                });
                            }
                        });
                    } else {
                    }
                    return null;
                }

                @Override
                public void onSuccess(Void result) {

                }
            });
        }


    }

    /**
     * @des:数据库改变监听
     * @params:
     * @return:
     */

    private RealmChangeListener realmChangeListener = new RealmChangeListener() {
        @Override
        public void onChange(Object element) {
//            isMultiSelect = false;
//            isSelectAll = false;
//            myPdfTitle.setText(R.string.my_pdf);
//            isCheckedMap.clear();
//            isCheckedMap = new LinkedHashMap<>();
//            hideSelectTopBar();
            bookPdfAdapter.refreshData(pdfEntities, isMultiSelect, isCheckedMap);
            isShowEmptyPrompt();
        }
    };

    /**
     * @des: 请求权限
     * @params:[]
     * @return:void
     */
    private void requestPermission(final BookPdfEntity BookPdfEntity) {

        PermissionUtils.permission(PermissionUtil.getStoragePermission())
                .rationale(new PermissionUtils.OnRationaleListener() {
                    @Override
                    public void rationale(UtilsTransActivity activity, ShouldRequest shouldRequest) {
                        showMessage(R.string.denied_PDF_preview);
                        shouldRequest.again(true);
                    }
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(List<String> permissionsGranted) {
                        Intent intent = new Intent(BookPdfActivity.this, PdfPreviewActivity.class);
                        intent.putExtra("pdfPath", BookPdfEntity.getPdfPath());
                        intent.putExtra("pdfId", BookPdfEntity.getPdfId());
                        intent.putExtra("pdfName", BookPdfEntity.getPdfName());
                        ActivityUtils.startActivity(intent);
                    }

                    @Override
                    public void onDenied(List<String> permissionsDeniedForever,
                                         List<String> permissionsDenied) {
                        showMessage(R.string.denied_PDF_preview);
                    }
                })
                .request();
    }

    /**
     * @des: 长按监听
     * @params:
     * @return:
     */

    private BookPdfAdapter.OnItemLongClickListener onItemLongClickListener = new BookPdfAdapter.OnItemLongClickListener() {
        @Override
        public void onBookPdfEntityLongClick(int position, BookPdfEntity BookPdfEntity, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize) {
            multiSelect();
            checkSize(isCheckedMap, totalSize);
            myPdfTitle.setText(R.string.select_one_pdf);
            myPdfBottomLl.setVisibility(View.VISIBLE);
            bookPdfAdapter.refreshData(true);
        }
    };
    /**
     * @des: item点击监听
     * @params:
     * @return:
     */
    private BookPdfAdapter.OnItemClickListener onItemClickListener = new BookPdfAdapter.OnItemClickListener() {
        @Override
        public void onBookPdfEntityClick(BookPdfEntity BookPdfEntity, int position, CheckBox checkBox) {
            if (isMultiSelect) {
                checkBox.setChecked(!checkBox.isChecked());
            } else {
                requestPermission(BookPdfEntity);
            }
        }
    };
    /**
     * @des: item选择监听
     * @params:
     * @return:
     */
    private BookPdfAdapter.OnItemCheckListener onItemCheckListener = new BookPdfAdapter.OnItemCheckListener() {
        @Override
        public void onItemCheck(int position, BookPdfEntity BookPdfEntity, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize) {
            BookPdfActivity.this.isCheckedMap = isCheckedMap;
            //如果选中一个 文案变为已选中1个
            if (isCheckedMap.size() == 1) {
                myPdfTitle.setText(R.string.select_one_pdf);
                myPdfBottomLl.setVisibility(View.VISIBLE);
            } else if (isCheckedMap.size() > 1) {
                myPdfTitle.setText(String.format(getString(R.string.select_num_pdf), isCheckedMap.size() + ""));
                myPdfBottomLl.setVisibility(View.VISIBLE);
            } else {
                if (isMultiSelect) {
                    myPdfTitle.setText(String.format(getString(R.string.select_num_pdf), isCheckedMap.size() + ""));
                    myPdfBottomLl.setVisibility(View.GONE);
                }
            }
            //如果选择不是全部Item  text变为取消全选
            checkSize(isCheckedMap, totalSize);
        }
    };

    private void checkSize(LinkedHashMap<String, Boolean> isCheckedMap, int totalSize) {
        if (isCheckedMap.size() < totalSize) {
            myPdfTopSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        } else {
            myPdfTopSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;
        }
    }

    private boolean isMultiSelect = false;
    private boolean isSelectAll = false;

    /**
     * @des: 多选
     * @params:
     * @return:
     */

    private void multiSelect() {
        if (Validator.isNotEmpty(pdfEntities)) {
            isMultiSelect = !isMultiSelect;
            bookPdfAdapter.refreshData(isMultiSelect);
            if (isMultiSelect) {
                showSelectTopBar();
            } else {
                hideSelectTopBar();
            }
        }
    }

    /**
     * @des: 选中所有
     * @params:
     * @return:
     */

    private void selectAll() {
        if (!isSelectAll) {
            for (int i = 0; i < pdfEntities.size(); i++) {

                if (!isCheckedMap.containsKey((pdfEntities.get(i).getPdfId()))) {
                    isCheckedMap.put(pdfEntities.get(i).getPdfId(), true);
                }
            }
            myPdfBottomLl.setVisibility(View.VISIBLE);
            myPdfTopSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;

        } else {

            myPdfBottomLl.setVisibility(View.GONE);
            isCheckedMap.clear();
            isCheckedMap = new LinkedHashMap<>();
            myPdfTopSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        }
        myPdfTitle.setText(String.format(getString(R.string.select_num_pdf), isCheckedMap.size() + ""));
        bookPdfAdapter.refreshData(pdfEntities, true, isCheckedMap);
    }

    /**
     * @des: 取消事件
     * @params:
     * @return:
     */

    private void cancelEvent() {
        isMultiSelect = false;
        isSelectAll = false;
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        bookPdfAdapter.refreshData(pdfEntities, false, isCheckedMap);
        hideSelectTopBar();
    }

    /**
     * @des: 显示选择TopBar
     * @params:
     * @return:
     */

    private void showSelectTopBar() {
        myPdfMultiSelectBtn.setVisibility(View.GONE);
        myPdfBackBtn.setVisibility(View.GONE);
        myPdfCancelBtn.setVisibility(View.VISIBLE);
        myPdfTopSelectAllBtn.setVisibility(View.VISIBLE);
        myPdfCancelBtn.setText(R.string.cancel);
        myPdfTopSelectAllBtn.setText(R.string.select_all);
        myPdfTitle.setText(String.format(getString(R.string.select_num_pdf), isCheckedMap.size() + ""));
    }

    /**
     * @des: 隐藏选择TopBar
     * @params:
     * @return:
     */

    private void hideSelectTopBar() {
        myPdfBottomLl.setVisibility(View.GONE);
        myPdfMultiSelectBtn.setVisibility(View.VISIBLE);
        myPdfBackBtn.setVisibility(View.VISIBLE);
        myPdfCancelBtn.setVisibility(View.GONE);
        myPdfTopSelectAllBtn.setVisibility(View.GONE);
        myPdfTitle.setText(R.string.my_pdf);
    }

    /**
     * @des: 删除PDF
     * @params:
     * @return:
     */

    private void deletePdf() {

        final List<String> needDeletePaths = new ArrayList<>();
        for (final String pdfId : isCheckedMap.keySet()) {
            //得到待删除pdf paths
            BookPdfEntity bookPdfEntity = realm.where(BookPdfEntity.class).equalTo("pdfId", pdfId).findFirst();
            if (bookPdfEntity != null) {
                needDeletePaths.add(bookPdfEntity.getPdfPath());
            }
        }
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                //删除sd卡上的pdf
                for (String needDeletePath : needDeletePaths) {
                    FileUtils.delete(needDeletePath);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });
        String curDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(new Date(System.currentTimeMillis()));
        //删除数据库中的pdf
        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realms) {
                //先查找后得到PageEntity对象
                for (String pdfId : isCheckedMap.keySet()) {
                    //先查找后得到PageEntity对象
                    BookPdfEntity bookPdfEntity = realm.where(BookPdfEntity.class).equalTo("pdfId", pdfId).findFirst();
                    if (bookPdfEntity != null) {
                        bookPdfEntity.setIsDelete(1);
                        bookPdfEntity.setIsDirty(1);
                        bookPdfEntity.setUpdateTime(curDate);
                    }

                }
            }
        });
        isMultiSelect = false;
        isSelectAll = false;
        myPdfTitle.setText(R.string.my_pdf);
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        hideSelectTopBar();
        bookPdfAdapter.refreshData(pdfEntities, false, isCheckedMap);
        isShowEmptyPrompt();

        startAutoSync();

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.my_pdf_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.my_pdf_cancel_btn:
                cancelEvent();
                break;
            case R.id.my_pdf_top_select_all_btn:
                selectAll();
                break;
            case R.id.my_pdf_multi_select_btn:
                multiSelect();
                break;
            case R.id.my_pdf_delete_rl:
                confirmDeleteDialog();
                break;
            default:
                break;
        }
    }

    /**
     * @des: 显示删除Dialog
     * @params:
     * @return:
     */

    private void confirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(BookPdfActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.confirm_delete));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {

                dialog.dismiss();
                deletePdf();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        realm.removeChangeListener(realmChangeListener);
        realm.close();
    }
}
