package com.czur.cloud.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.TextView;

import com.czur.cloud.R;

public class AuraMateUpdatingPopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;

    private AuraMateUpdatingPopup(Context context, int theme) {
        super(context, theme);
    }

    public static class Builder {
        private Context context;
        private AuraMateUpdatingPopup.Builder.OnBtnClickListener onBtnClickListener;

        public interface OnBtnClickListener {
            void onClick();
        }

        public Builder(Context context) {
            this.context = context;
        }

        public AuraMateUpdatingPopup.Builder setOnPositiveListener(AuraMateUpdatingPopup.Builder.OnBtnClickListener onBtnClickListener) {
            this.onBtnClickListener = onBtnClickListener;
            return this;
        }

        public AuraMateUpdatingPopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            final AuraMateUpdatingPopup dialog = new AuraMateUpdatingPopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);
            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;
            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final AuraMateUpdatingPopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.6f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            View layout = inflater.inflate(R.layout.dialog_aura_mate_updating, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);
            TextView btnIKonw = (TextView) layout.findViewById(R.id.btn_i_konw);
            btnIKonw.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                    if (onBtnClickListener != null) {
                        onBtnClickListener.onClick();
                    }
                }
            });
            return layout;
        }
    }
}

