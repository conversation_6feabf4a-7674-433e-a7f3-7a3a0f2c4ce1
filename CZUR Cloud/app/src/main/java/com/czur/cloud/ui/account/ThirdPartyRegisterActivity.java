package com.czur.cloud.ui.account;

import static com.czur.cloud.common.CZURConstants.PWD_MIN_LENGTH;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.CleanUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.StringUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.Utils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.common.MD5Utils;
import com.czur.cloud.entity.realm.BookEntity;
import com.czur.cloud.entity.realm.BookPdfEntity;
import com.czur.cloud.entity.realm.DownloadEntity;
import com.czur.cloud.entity.realm.HomeCacheEntity;
import com.czur.cloud.entity.realm.OcrEntity;
import com.czur.cloud.entity.realm.PageEntity;
import com.czur.cloud.entity.realm.PdfDownloadEntity;
import com.czur.cloud.entity.realm.PdfEntity;
import com.czur.cloud.entity.realm.SPReportEntity;
import com.czur.cloud.entity.realm.SPReportEntitySub;
import com.czur.cloud.entity.realm.SPReportSittingEntity;
import com.czur.cloud.entity.realm.SyncBookEntity;
import com.czur.cloud.entity.realm.SyncPageEntity;
import com.czur.cloud.entity.realm.SyncPdfEntity;
import com.czur.cloud.entity.realm.SyncTagEntity;
import com.czur.cloud.entity.realm.TagEntity;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.LoginEvent;
import com.czur.cloud.model.ChannelModel;
import com.czur.cloud.model.RegisterModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.FirstPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.NoHintEditText;
import com.czur.cloud.ui.component.ProgressButton;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.home.IndexActivity;
import com.czur.cloud.util.StringToolsUtils;
import com.facebook.drawee.backends.pipeline.Fresco;

import org.greenrobot.eventbus.EventBus;

import java.io.File;

import io.realm.Realm;

/**
 * Created by Yz on 2018/3/7.
 * Email：<EMAIL>
 */

public class ThirdPartyRegisterActivity extends BaseActivity implements View.OnClickListener {

    private ImageView accountBackBtn;
    private TextView accountTitle;
    private NoHintEditText firstSetPasswordEdt;
    private ProgressButton confirmBtn;


    private boolean hasFirstPassword = false;
    private HttpManager httpManager;
    private String account;
    private String code;
    private UserPreferences userPreferences;
    private String userId;
    private String platName;
    private String thirdPartyToken;
    private String thirdPartyOpenId;
    private String thirdPartyPlatName;
    private String thirdPartyRefreshToken;
    private CloudCommonPopup commonPopup;
    private FirstPreferences firstPreferences;
    private Realm realm;
    private long currentTime;
    private MiaoHttpEntity<RegisterModel> registerEntity;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_bind_third_party_set_password);
        initComponent();
        registerEvent();
    }

    private void initComponent() {
        realm = Realm.getDefaultInstance();
        firstPreferences = FirstPreferences.getInstance(this);


        platName = getIntent().getStringExtra("platName");
        thirdPartyToken = getIntent().getStringExtra("thirdPartyToken");
        thirdPartyOpenId = getIntent().getStringExtra("thirdPartyOpenId");
        thirdPartyPlatName = getIntent().getStringExtra("thirdPartyPlatName");
        thirdPartyRefreshToken = getIntent().getStringExtra("thirdPartyRefreshToken");
        account = getIntent().getStringExtra("account");
        code = getIntent().getStringExtra("code");
        userId = getIntent().getStringExtra("userId");
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);

        accountBackBtn = (ImageView) findViewById(R.id.account_back_btn);
        accountTitle = (TextView) findViewById(R.id.account_title);
        firstSetPasswordEdt = (NoHintEditText) findViewById(R.id.first_set_password_edt);
        confirmBtn = (ProgressButton) findViewById(R.id.confirm_btn);


        //设置标题
        accountTitle.setText(R.string.third_party_set_password);

    }

    private void registerEvent() {
        accountBackBtn.setOnClickListener(this);
        confirmBtn.setOnClickListener(this);
        confirmBtn.setOnProgressFinishListener(onProgressFinish);
        confirmBtn.setSelected(false);
        confirmBtn.setClickable(false);
        firstSetPasswordEdt.addTextChangedListener(firstPswTextWatcher);

    }
    private ProgressButton.OnProgressFinish onProgressFinish = new ProgressButton.OnProgressFinish() {
        @Override
        public void onFinish() {
            confirmToClearLastUserData(registerEntity);
        }
    };
    /**
     * @des: 第三方账号注册
     * @params:[]
     * @return:void
     */
    private void thirdPartyRegister(final boolean hasChannel) {
        final String password = MD5Utils.md5(firstSetPasswordEdt.getText().toString());
        HttpManager.getInstance().requestPassport().thirdPartyRegister(userPreferences.getChannel(), userPreferences.getIMEI(), CZURConstants.CLOUD_ANDROID, code, password,
                account, userId, RegisterModel.class, new MiaoHttpManager.Callback<RegisterModel>() {
                    @Override
                    public void onStart() {
                        if (hasChannel) {
                            confirmBtn.startLoading();
                        }
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<RegisterModel> entity) {
                        registerEntity = entity;
                        registerSuccessDelay(entity);


                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<RegisterModel> entity) {
                        switch (entity.getCode()) {

                            case MiaoHttpManager.STATUS_NOT_USER:
                                registerFailedDelay(R.string.toast_user_no_exist);
                                break;
                            case MiaoHttpManager.STATUS_USER_EXISTS:
                                registerFailedDelay(R.string.toast_user_existing);
                                break;
                            case MiaoHttpManager.STATUS_INVALID_CODE:
                                registerFailedDelay(R.string.toast_code_error);
                                break;
                            case MiaoHttpManager.STATUS_ERROR:
                                registerFailedDelay(R.string.toast_internal_error);
                                break;
                            default:
                                registerFailedDelay(R.string.request_failed_alert);
                                break;
                        }
                    }

                    @Override
                    public void onError(Exception e) {

                        registerFailedDelay(R.string.request_failed_alert);
                    }
                });


    }

    private void registerFailedDelay(final int failedText) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showMessage(failedText);
                            confirmBtn.stopLoading();
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }


    private void registerSuccessDelay(final MiaoHttpEntity<RegisterModel> entity) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            confirmBtn.stopLoadingSuccess();

                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }

    /**
     * @des: 确认删除用户信息
     * @params:
     * @return:
     */

    private void confirmToClearLastUserData(final MiaoHttpEntity<RegisterModel> entity) {
        String currentUserId = entity.getBody().getId();
        if ((!StringUtils.equals(userId, currentUserId)) && userPreferences.isValidUser()) {

            CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(ThirdPartyRegisterActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON_YES_NO);
            builder.setTitle(getResources().getString(R.string.prompt));
            String title = String.format(getString(R.string.confirm_to_clear_account), userPreferences.getUserName());
            builder.setMessage(title);
            builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    if (commonPopup != null) {
                        commonPopup.dismiss();
                    }
                    clearLastUserDataAndSetCurrentData(entity);
                }
            });
            builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.dismiss();
                    ActivityUtils.finishActivity(ThirdPartyRegisterActivity.this);
                }
            });
            commonPopup = builder.create();
            commonPopup.show();

        } else {
            setCurrentUserData(entity);
        }

    }

    /**
     * @des: 如果和上次userId不一样 就清除sp  data/file并且设置用户信息sp
     * @params:
     * @return:
     */

    private void clearLastUserDataAndSetCurrentData(final MiaoHttpEntity<RegisterModel> entity) {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                String filePath = getFilesDir() + File.separator + userPreferences.getUserId();
                FileUtils.deleteAllInDir(new File(filePath));
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        //清空sp
                        userPreferences.resetUser();
                        firstPreferences.resetFirstPreference();
                        //清空数据库
                        realm.executeTransaction(new Realm.Transaction() {
                            @Override
                            public void execute(Realm realms) {
                                realm.where(SPReportEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SPReportEntitySub.class).findAll().deleteAllFromRealm();
                                realm.where(SPReportSittingEntity.class).findAll().deleteAllFromRealm();

                                realm.where(PageEntity.class).findAll().deleteAllFromRealm();
                                realm.where(BookEntity.class).findAll().deleteAllFromRealm();
                                realm.where(PdfEntity.class).findAll().deleteAllFromRealm();
                                realm.where(BookPdfEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SyncPdfEntity.class).findAll().deleteAllFromRealm();
                                realm.where(TagEntity.class).findAll().deleteAllFromRealm();
                                realm.where(OcrEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SyncTagEntity.class).findAll().deleteAllFromRealm();
                                realm.where(HomeCacheEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SyncPageEntity.class).findAll().deleteAllFromRealm();
                                realm.where(SyncBookEntity.class).findAll().deleteAllFromRealm();
                                realm.where(DownloadEntity.class).findAll().deleteAllFromRealm();
                                realm.where(PdfDownloadEntity.class).findAll().deleteAllFromRealm();

                            }
                        });
                        Fresco.getImagePipeline().clearCaches();
                        CleanUtils.cleanCustomDir(Utils.getApp().getFilesDir() + File.separator + CZURConstants.PDF_PATH);
                        setCurrentUserData(entity);
                    }
                });
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });
    }

    /**
     * @des: 显示登录成功并且跳转到主页
     * @params:
     * @return:
     */
    private void showLoginSuccessAndGoIndex() {
        showMessage(R.string.register_success);
        EventBus.getDefault().post(new LoginEvent(EventType.THIRD_PARTY_REGISTER_SUCCESS));
        Intent intent = new Intent(ThirdPartyRegisterActivity.this, IndexActivity.class);
        intent.putExtra("needSync", true);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
        ActivityUtils.startActivity(intent);
        ;
    }

    /**
     * @des: 存储当前用户信息到SP
     * @params:
     * @return:
     */
    private void setCurrentUserData(MiaoHttpEntity<RegisterModel> entity) {
        userPreferences.setUser(entity.getBody());
        userPreferences.setIsUserLogin(true);

        userPreferences.setIsThirdParty(true);
        userPreferences.setThirdPartyOpenid(thirdPartyOpenId);
        userPreferences.setThirdPartyToken(thirdPartyToken);
        userPreferences.setThirdPartyRefreshToken(thirdPartyRefreshToken);
        userPreferences.setThirdPartyPlatName(thirdPartyPlatName);
        userPreferences.setServicePlatName(platName);

        showLoginSuccessAndGoIndex();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.confirm_btn:
                confirmPassword();
                break;
            case R.id.account_back_btn:
                Intent intent = new Intent(ThirdPartyRegisterActivity.this, LoginActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                ActivityUtils.startActivity(intent);
                ActivityUtils.finishActivity(this);
                break;
            default:
                break;
        }
    }

    /**
     * @des: 确认新密码
     * @params:[]
     * @return:void
     */
    private void confirmPassword() {
        String firstPassword = firstSetPasswordEdt.getText().toString();

        if (firstPassword.length() <= PWD_MIN_LENGTH ||
                !StringToolsUtils.isLetterDigit(firstPassword)) {
            showMessage(R.string.toast_pwd_length);
        } else {
            currentTime = System.currentTimeMillis();
            KeyboardUtils.hideSoftInput(this);
            if (TextUtils.isEmpty(userPreferences.getChannel())) {
                getChannel();
            } else {
                thirdPartyRegister(true);
            }

        }
    }

    /**
     * @des:获取channel
     * @params:[userId, token, platformName, isThirdParty, mobileMail, pwd]
     * @return:void
     */
    private void getChannel() {


        HttpManager.getInstance().request().channel(ChannelModel.class, new MiaoHttpManager.Callback<ChannelModel>() {
            @Override
            public void onStart() {
                confirmBtn.startLoading();
            }

            @Override
            public void onResponse(MiaoHttpEntity<ChannelModel> entity) {
                userPreferences.setChannel(entity.getBody().getChannel());
                userPreferences.setEndpoint(entity.getBody().getEndPoint());
                thirdPartyRegister(false);
            }

            @Override
            public void onFailure(MiaoHttpEntity<ChannelModel> entity) {
                registerFailedDelay(R.string.request_failed_alert);
            }

            @Override
            public void onError(Exception e) {
                registerFailedDelay(R.string.request_failed_alert);
            }
        });
    }


    private TextWatcher firstPswTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            if (s.length() > 0) {
                hasFirstPassword = true;
            } else {
                hasFirstPassword = false;
            }

            checkNextStepButtonToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            if (s.length() > 0) {
                hasFirstPassword = true;
            } else {
                hasFirstPassword = false;
            }
            checkNextStepButtonToClick();
        }
    };

    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private void checkNextStepButtonToClick() {

        if (hasFirstPassword) {
            confirmBtn.setSelected(true);
            confirmBtn.setClickable(true);
        } else {
            confirmBtn.setSelected(false);
            confirmBtn.setClickable(false);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (realm!=null){
            realm.close();
        }
    }

    @Override
    public void onBackPressed() {
        Intent intent = new Intent(ThirdPartyRegisterActivity.this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
        ActivityUtils.startActivity(intent);
        ActivityUtils.finishActivity(this);
    }
}
