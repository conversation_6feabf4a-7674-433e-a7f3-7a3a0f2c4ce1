<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/tag_add_item"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    tools:ignore="MissingPrefix">
    <RelativeLayout
        android:layout_centerInParent="true"
        android:layout_marginBottom="13dp"
        android:layout_marginRight="13.5dp"
        android:layout_marginLeft="13.5dp"
        android:id="@+id/tag_inner_item"
        android:background="@drawable/btn_rect_6_bg_gray_ec"
        android:layout_gravity="center"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="40dp">

        <ImageView
            android:id="@+id/tag_item_name"
            android:layout_centerInParent="true"
            android:layout_width="10dp"
            android:src="@mipmap/edit_tag_add_icon"
            android:layout_height="10dp" />



    </RelativeLayout>




</RelativeLayout>