package com.czur.cloud.ui.component.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.R;

public class VersionEditDialog extends Dialog {
    private TextView tv_current_version;
    private EditText et_version;
    private Button btn_ensure;
    private SharedPreferences sharedPreferences;

    public VersionEditDialog(@NonNull Context context) {
        super(context);
    }

    public VersionEditDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected VersionEditDialog(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_version_edit);
        tv_current_version = findViewById(R.id.tv_current_version);
        et_version = findViewById(R.id.et_version);
        btn_ensure = findViewById(R.id.btn_ensure);

        sharedPreferences = getContext().getSharedPreferences("version_sp", Context.MODE_PRIVATE);
        tv_current_version.setText("当前版本" + sharedPreferences.getInt("version", 1));

        btn_ensure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (TextUtils.isEmpty(et_version.getText().toString())) {
                    ToastUtils.showShort("输入版本号");
                } else {
                    sharedPreferences.edit().putInt("version", Integer.parseInt(et_version.getText().toString())).apply();
                    dismiss();
                }
            }
        });


    }
}
