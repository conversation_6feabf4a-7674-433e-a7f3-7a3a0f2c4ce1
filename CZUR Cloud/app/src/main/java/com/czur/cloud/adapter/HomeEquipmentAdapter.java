package com.czur.cloud.adapter;

/**
 * Created by czur_app001 on 2018/1/19.
 * Email：<EMAIL>
 * (ง •̀_•́)ง
 */

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.model.IndexEquipmentModel;
import com.czur.cloud.ui.base.BaseActivity;

import java.util.List;


public class HomeEquipmentAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private static final int TYPE_NORMAL = 0;
    private static final int TYPE_ADD_EQUIPMENT = 1;
    private List<IndexEquipmentModel> datas;
    private BaseActivity context;


    public HomeEquipmentAdapter(BaseActivity context, List<IndexEquipmentModel> datas) {
        this.datas = datas;
        this.context = context;
    }

    public void setData(List<IndexEquipmentModel> datas) {
        this.datas = datas;
    }

    public void refreshData(List<IndexEquipmentModel> datas) {
        this.datas = datas;
        notifyDataSetChanged();
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {

        if (viewType == TYPE_NORMAL) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_home_equipment_layout, parent, false);
            return new NormalViewHolder(view);
        } else if (viewType == TYPE_ADD_EQUIPMENT) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_home_equipment_add_layout, parent, false);
            return new AddEquipmentViewHolder(view);
        } else {
            return null;
        }
    }

    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder holder, final int position) {
        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);

            int screenWidth = ScreenUtils.getScreenWidth();
            int shadowWidth = screenWidth - SizeUtils.dp2px(15);
            int itemWidth = screenWidth - SizeUtils.dp2px(35);

            final String equipmentName = mHolder.mItem.getKey();
            String equipmentAlias = mHolder.mItem.getAlias();
            if (equipmentName.equals("Aura")) {
                mHolder.equipmentImg.setImageResource(R.mipmap.maura);
                equipmentAlias = context.getString(R.string.aura_1);
            }
            else if (equipmentName.equals(context.getString(R.string.ET))) {
                mHolder.equipmentImg.setImageResource(R.mipmap.met);

            }
            else if (equipmentName.equals(context.getString(R.string.AURA_HOME))) {
                equipmentAlias = context.getString(R.string.AURA_MATE);
                mHolder.equipmentImg.setImageResource(R.mipmap.mauramate);

            }
            else if (equipmentName.equals(context.getString(R.string.ETS))) {
                mHolder.equipmentImg.setImageResource(R.mipmap.mets);
            }
            // 智能笔记本
            else if (equipmentName.equals(context.getString(R.string.notebookCN))) {
                mHolder.equipmentImg.setImageResource(R.mipmap.mbook);
                equipmentAlias = context.getString(R.string.notebook);
            }
            //坐姿仪
            else if (equipmentName.equals(context.getString(R.string.Mirror))) {
                equipmentAlias = context.getString(R.string.smart_sitting);
                mHolder.equipmentImg.setImageResource(R.mipmap.msitting);
            }
            //投影仪
            else if (equipmentName.equals(context.getString(R.string.Starry))) {
                equipmentAlias = context.getString(R.string.smart_starry2);
                mHolder.equipmentImg.setImageResource(R.mipmap.mstarry);
            }
            //无线投屏
            else if (equipmentName.equals(context.getString(R.string.EShare))) {
                equipmentAlias = context.getString(R.string.smart_eshare);
                mHolder.equipmentImg.setImageResource(R.mipmap.meshare);
            }

            if (!mHolder.mItem.isIsActive()) {
                ViewGroup.LayoutParams itemViewLayoutParams = mHolder.itemView.getLayoutParams();
                itemViewLayoutParams.height = 0;
                mHolder.itemView.setLayoutParams(itemViewLayoutParams);
            } else {
                ViewGroup.LayoutParams itemViewLayoutParams = mHolder.itemView.getLayoutParams();
                itemViewLayoutParams.height = screenWidth * 155 / 375;
                mHolder.itemView.setLayoutParams(itemViewLayoutParams);
            }
            RelativeLayout.LayoutParams shadowLayoutParams = (RelativeLayout.LayoutParams) mHolder.homeItemShadowImg.getLayoutParams();
            shadowLayoutParams.height = shadowWidth * 155 / 360;
            mHolder.homeItemShadowImg.setLayoutParams(shadowLayoutParams);

            RelativeLayout.LayoutParams homeItemRlLayoutParams = (RelativeLayout.LayoutParams) mHolder.homeItemRl.getLayoutParams();
            homeItemRlLayoutParams.height = itemWidth * 135 / 340;
            mHolder.homeItemRl.setLayoutParams(homeItemRlLayoutParams);

            RelativeLayout.LayoutParams equipmentImgLayoutParams = (RelativeLayout.LayoutParams) mHolder.equipmentImg.getLayoutParams();
            equipmentImgLayoutParams.height = homeItemRlLayoutParams.height * 226 / 135;
            mHolder.equipmentImg.setLayoutParams(equipmentImgLayoutParams);

            mHolder.equipmentNameTv.setText(equipmentAlias);
            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(position, mHolder.itemView, mHolder.mItem);
                    }
                }
            });

            mHolder.itemView.setOnLongClickListener(new View.OnLongClickListener() {
                @Override
                public boolean onLongClick(View v) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemLongClick(position, mHolder.mItem);
                        return true;
                    }
                    return false;
                }
            });
        } else if (holder instanceof AddEquipmentViewHolder) {
            AddEquipmentViewHolder mHolder = (AddEquipmentViewHolder) holder;

        }


    }

    @Override
    public int getItemViewType(int position) {
        if (position >= 0 && position < datas.size()) {
            return TYPE_NORMAL;
        } else {
            return TYPE_ADD_EQUIPMENT;
        }
    }

    @Override
    public int getItemCount() {
        return datas.size() + 1;
    }


    public static class NormalViewHolder extends RecyclerView.ViewHolder {
        public final View mView;
        TextView equipmentNameTv;
        ImageView equipmentImg;
        IndexEquipmentModel mItem;
        ImageView homeItemShadowImg;
        RelativeLayout homeItemRl;
        RelativeLayout itemView;

        public NormalViewHolder(View view) {
            super(view);
            mView = view;
            homeItemShadowImg = (ImageView) view.findViewById(R.id.home_item_shadow_img);
            homeItemRl = (RelativeLayout) view.findViewById(R.id.home_item_rl);
            itemView = (RelativeLayout) view.findViewById(R.id.equipment_rl);
            equipmentImg = (ImageView) view.findViewById(R.id.equipment_name_img);
            equipmentNameTv = (TextView) view.findViewById(R.id.equipment_name_tv);
        }

    }

    public static class AddEquipmentViewHolder extends RecyclerView.ViewHolder {
        public final View mView;
        LinearLayout itemView;

        AddEquipmentViewHolder(View view) {
            super(view);
            mView = view;
            itemView = (LinearLayout) view.findViewById(R.id.add_equipment_ll);
        }

    }

    public OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(int position,View view, IndexEquipmentModel indexEquipmentModel);

        void onItemLongClick(int position, IndexEquipmentModel indexEquipmentModel);
    }
}