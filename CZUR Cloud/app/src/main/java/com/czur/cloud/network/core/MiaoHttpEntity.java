package com.czur.cloud.network.core;

import java.util.List;

public class MiaoHttpEntity<T> {
    private int code;
    private String msg;
    private T body;
    private List<T> bodyList;

    private T data;
    private List<T> dataList;


    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getBody() {
        return body;
    }

    public void setBody(T body) {
        this.body = body;
    }

    public List<T> getBodyList() {
        return bodyList;
    }

    public void setBodyList(List<T> bodyList) {
        this.bodyList = bodyList;
    }

    // Starry
    public T getData() {        return data;    }
    public void setData(T data) {        this.data = data;    }

    public List<T> getDataList() {        return dataList;    }

    public void setDataList(List<T> dataList) {        this.dataList = dataList;    }
}
