package com.czur.cloud.ui.home;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.ui.base.BaseActivity;

/**
 * Created by Yz on 2018/2/26.
 * Email：<EMAIL>
 */

public class AddEquipmentFailedActivity extends BaseActivity implements View.OnClickListener {


    private TextView addFailedBtn;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this,true);
        setContentView(R.layout.activity_add_equipment_failed);
        initComponent();
        registerEvent();
    }


    private void initComponent() {
        addFailedBtn = (TextView) findViewById(R.id.add_equipment_failed_btn);
    }

    private void registerEvent() {
        addFailedBtn.setOnClickListener(this);

    }


    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.add_equipment_failed_btn:
                ActivityUtils.startActivity(IndexActivity.class);
                break;

            default:
                break;
        }
    }


}
