package com.czur.cloud.ui.starry.activity

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.R
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryCommonEvent
import com.czur.cloud.ui.starry.base.StarryBaseActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup
import com.czur.cloud.ui.starry.meeting.network.core.common.ResCode.RESULT_CODE_NO_INTERNET
import com.czur.cloud.ui.starry.meeting.network.core.common.ResCode.RESULT_CODE_NO_NET_CONNECT
import com.czur.cloud.ui.starry.model.StarryEnterpriseModel
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import kotlinx.android.synthetic.main.starry_activity_company_detail.*
import kotlinx.android.synthetic.main.starry_layout_top_bar.*
import org.greenrobot.eventbus.EventBus

/**
 *
 */
class StarryCompanyDetailActivity : StarryBaseActivity(), View.OnClickListener {

    private lateinit var currentCompanyModel: StarryEnterpriseModel
    private var companyFromType:String=""
    private var status = StarryConstants.STARRY_COMPANY_STATUS_REJECT

    private val viewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryViewModel::class.java)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.white)
        BarUtils.setStatusBarLightMode(this, true)
        setContentView(R.layout.starry_activity_company_detail)
        initComponent()
        registerEvent()
    }

    @SuppressLint("SetTextI18n")
    private fun initComponent() {
        user_title?.setText(R.string.starry_title_detail)

        companyFromType = intent.getStringExtra(StarryConstants.STARRY_USER_TYPE).toString()

        currentCompanyModel =
            (intent.getSerializableExtra(StarryConstants.STARRY_USER_MODEL) as StarryEnterpriseModel?)!!

        if (currentCompanyModel != null) {
            company_name?.text = currentCompanyModel.enterpriseName
            if (currentCompanyModel.expired) {
                company_name_old?.visibility = View.VISIBLE
            }else{
                company_name_old?.visibility = View.GONE
            }
            val memberList = currentCompanyModel.membersList
            company_count?.text = memberList.size.toString() + getString(R.string.starry_company_count_unit)

            // 获取管理员账号
            if (memberList != null) {
                for (model in memberList){
                    if (model.isAdmin){
                        company_admin_account?.text = model.meetingAccout
                        company_admin_name?.text = model.name
                        break
                    }
                }
            }
        }

        //未加入
        if (companyFromType == StarryConstants.STARRY_USER_TYPE_COMP_NO){
            company_exit_btn_ll?.visibility = View.GONE
            company_jion_btn_ll?.visibility = View.VISIBLE
        }else{
            company_exit_btn_ll?.visibility = View.VISIBLE
            company_jion_btn_ll?.visibility = View.GONE
        }

    }

    private fun registerEvent() {
        user_back_btn?.singleClick(this)
        company_exit_btn?.singleClick(this)
        company_join_btn?.singleClick(this)
        company_reject_btn?.singleClick(this)
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.user_back_btn -> ActivityUtils.finishActivity(this)
            R.id.company_exit_btn -> {
                showConfirmDeleteDialog(StarryConstants.STARRY_COMPANY_STATUS_EXIT)
            }
            R.id.company_join_btn -> {
                updateJoinStatus(StarryConstants.STARRY_COMPANY_STATUS_JOIN)
            }
            R.id.company_reject_btn -> {
                updateJoinStatus(StarryConstants.STARRY_COMPANY_STATUS_REJECT)
            }
            else -> {
            }
        }
    }

    private fun showConfirmDeleteDialog(status: Int) {
        StarryCommonPopup.Builder(this)
            .setTitle(getString(R.string.starry_popupwindow_title))
            .setMessage(getString(R.string.starry_company_exit_confirm))
            .setPositiveTitle(getString(R.string.starry_common_dialog_yes))
            .setNegativeTitle(getString(R.string.starry_common_dialog_not))
            .setOnPositiveListener { dialog, _ ->
                updateJoinStatus(status)
                dialog?.dismiss()
                LiveDataBus.get().with(StarryConstants.STARRY_COMPANY_EXIT).value = ""
            }
            .setOnNegativeListener { dialog, _ ->
                dialog.dismiss()
            }
            .create()
            .show()
    }

    // 更新企业联系人加入状态
    private fun updateJoinStatus(status1: Int){
        if (!NetworkUtils.isConnected()) {
            ToastUtils.showShort(R.string.starry_network_error_msg)
            return
        }

        launch {
            val enterpriseId = currentCompanyModel.enterpriseId
            status = status1
            val responseData = viewModel.updateJoinStatus(enterpriseId, status1)

            if (responseData.code == RESULT_CODE_NO_INTERNET || responseData.code == RESULT_CODE_NO_NET_CONNECT) {
                // 网络错误
                ToastUtils.showShort(R.string.starry_network_error_msg)
                return@launch
            }

            if (responseData.code == 2042){//邀请消息已经过期或web端删除了邀请
                ToastUtils.showShort(getString(R.string.starry_invite_msg_gone));
                EventBus.getDefault()
                    .post(StarryCommonEvent(EventType.STARRY_COMPANY_INVENT_GONE, ""))
                ActivityUtils.finishActivity(this@StarryCompanyDetailActivity)
                return@launch
            }

            if (responseData.enterpriseId == "1"){
                if (status == StarryConstants.STARRY_COMPANY_STATUS_REJECT){
                    EventBus.getDefault()
                        .post(StarryCommonEvent(EventType.STARRY_COMPANY_REJECT, ""))
                }else {
                    EventBus.getDefault()
                        .post(StarryCommonEvent(EventType.STARRY_COMPANY_JOIN, ""))
                }
                val typeId = "2"
                viewModel.readByEnterpriseId(enterpriseId, typeId)
                viewModel.hasUnReadNotices()

                ActivityUtils.finishActivity(StarryCompanyDetailActivity::class.java)
            }
        }

    }

}