package com.czur.cloud.ui.auramate;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.ReportAdapter;
import com.czur.cloud.entity.realm.SPReportEntity;
import com.czur.cloud.model.AuraMateReportModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.reflect.TypeToken;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

import io.realm.Realm;
import io.realm.RealmResults;
import io.realm.Sort;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class AuraMateReportActivity extends AuramateBaseActivity implements View.OnClickListener{
    private ImageView userBackBtn;
    private TextView userTitle;
    private RecyclerView reportRecyclerView;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private ReportAdapter reportAdapter;
    private Realm realm;
    private RealmResults<SPReportEntity> datas;
    private RelativeLayout emptyRl;
    private SmartRefreshLayout swipeRefreshLayout;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_mate_report);
        initComponent();
        registerEvent();
    }

    @Override
    public boolean PCNeedFinish() {
        return false;
    }


    private void initComponent() {
        realm = Realm.getDefaultInstance();
        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        userBackBtn = (ImageView) findViewById(R.id.user_back_btn);

        userBackBtn = (ImageView) findViewById(R.id.user_back_btn);
        userTitle = (TextView) findViewById(R.id.user_title);
        emptyRl = (RelativeLayout) findViewById(R.id.empty_rl);

        userTitle = (TextView) findViewById(R.id.user_title);
        userTitle.setText(R.string.aura_mate_report);

        swipeRefreshLayout = (SmartRefreshLayout) findViewById(R.id.refresh_layout);
        swipeRefreshLayout.setOnRefreshListener(onRefreshListener);
        reportRecyclerView = (RecyclerView) findViewById(R.id.report_recyclerView);

        initRecyclerView();
        showProgressDialog();
        getReportList();
    }

    private void showEmpty() {
        if (datas.size() > 0) {
            emptyRl.setVisibility(View.GONE);
        } else {
            emptyRl.setVisibility(View.VISIBLE);
        }
    }

    private void initRecyclerView() {
        reportAdapter = new ReportAdapter(this, new ArrayList<>());
        reportRecyclerView.setHasFixedSize(true);
        reportRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        reportRecyclerView.setAdapter(reportAdapter);
    }

    /**
     * @des: 刷新接口
     * @params:
     * @return:
     */

    private OnRefreshListener onRefreshListener = refreshLayout -> getReportList();

    private List<AuraMateReportModel> getReportList(String equipmentId) {
        try {
//            final MiaoHttpEntity<AuraMateReportModel> reportEntity = httpManager.request().getUserReportSync(
//            userPreferences.getUserId(),
//            equipmentId,
//            userPreferences.getReportTime(),
//            new TypeToken<List<AuraMateReportModel>>() {
//            }.getType());
            final MiaoHttpEntity<AuraMateReportModel> reportEntity = httpManager.request().
                    getAllUseReport(
                            userPreferences.getUserId(),
                            equipmentId,
                            userPreferences.getReportTime(),
                            new TypeToken<List<AuraMateReportModel>>() {}.getType());

            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return reportEntity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private String getServerTimeSync() {
        try {
            MiaoHttpEntity<String> serverTimeEntity = httpManager.request().getServerTime(
                    userPreferences.getUserId(), String.class);
            if (serverTimeEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                String serverTime = serverTimeEntity.getBody();
                return serverTime;
            } else {
                return null;
            }

        } catch (Exception e) {
            logE(e.toString());
            e.printStackTrace();
        }

        return null;

    }

    /**
     * @des: 刷新列表
     * @params:
     * @return:
     */
    public void getReportList() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                try (Realm realm = Realm.getDefaultInstance()) {
                    String time = getServerTimeSync();
                    List<AuraMateReportModel> reportList = getReportList(equipmentId);
                    if (Validator.isNotEmpty(reportList)) {
                        realm.executeTransaction(new Realm.Transaction() {
                            @Override
                            public void execute(Realm realm) {
                                for (AuraMateReportModel auraMateReportModel : reportList) {
                                    SPReportEntity sameEntity = realm.where(SPReportEntity.class).equalTo("id", auraMateReportModel.getId()).findFirst();
                                    if (sameEntity == null) {
                                        SPReportEntity object = realm.createObject(SPReportEntity.class, auraMateReportModel.getId());
                                        object.setBeginTime(auraMateReportModel.getBeginTime());
                                        object.setCreateTime(auraMateReportModel.getCreateTime());
                                        object.setPushTime(auraMateReportModel.getPushTime());
                                        object.setEndTime(auraMateReportModel.getEndTime());
                                        object.setEquipmentUuid(auraMateReportModel.getEquipmentUuid());
                                        object.setErrorDuration(auraMateReportModel.getErrorDuration());
                                        object.setUsingDuration(auraMateReportModel.getUsingDuration());
                                        object.setProportion(auraMateReportModel.getProportion());
                                        object.setHaveRead(0);

                                        object.setRightDuration(auraMateReportModel.getRightDuration());
                                        object.setSeriousErrorDuration(auraMateReportModel.getSeriousErrorDuration());
                                        object.setMildErrorDuration(auraMateReportModel.getMildErrorDuration());
                                        object.setModerateErrorDuration(auraMateReportModel.getModerateErrorDuration());
                                        object.setRightProportion(auraMateReportModel.getRightProportion());
                                        object.setSeriousProportion(auraMateReportModel.getSeriousProportion());
                                        object.setMildProportion(auraMateReportModel.getMildProportion());
                                        object.setModerateProportion(auraMateReportModel.getModerateProportion());
                                    }
                                }
                            }
                        });
                    }
                    userPreferences.setReportTime(time);
                }

                return null;
            }

            @Override
            public void onSuccess(Void result) {
                swipeRefreshLayout.finishRefresh();
                refreshFiles();
                showEmpty();
                hideProgressDialog();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                swipeRefreshLayout.finishRefresh(false);
                hideProgressDialog();
            }
        });
    }

    private void refreshFiles() {
        datas = realm.where(SPReportEntity.class).equalTo("equipmentUuid", equipmentId).sort("createTime", Sort.DESCENDING).findAll();

        reportAdapter.refreshData(datas);
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.user_back_btn:
                finish();
                break;
            default:
                break;
        }
    }

    private void registerEvent() {
        userBackBtn.setOnClickListener(this);
        setNetListener();
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realm) {
                RealmResults<SPReportEntity> entities = realm.where(SPReportEntity.class).sort("createTime", Sort.DESCENDING).findAll();
                for (SPReportEntity spReportEntity : entities) {
                    spReportEntity.setHaveRead(1);
                }
            }
        });
        realm.close();
    }
}
