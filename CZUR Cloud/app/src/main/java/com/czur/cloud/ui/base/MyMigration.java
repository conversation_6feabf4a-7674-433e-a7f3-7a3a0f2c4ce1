package com.czur.cloud.ui.base;

import java.util.Objects;

import io.realm.DynamicRealm;
import io.realm.DynamicRealmObject;
import io.realm.FieldAttribute;
import io.realm.RealmMigration;
import io.realm.RealmObjectSchema;
import io.realm.RealmSchema;

public class MyMigration implements RealmMigration {
    @Override
    public void migrate(DynamicRealm realm, long oldVersion, long newVersion) {
        RealmSchema schema = realm.getSchema();
        if (oldVersion == 1) {
            schema.create("OcrModeEntity")
                    .addField("fileId", String.class, FieldAttribute.PRIMARY_KEY)
                    .addField("viewId", int.class);
            oldVersion++;
        }
        if (oldVersion == 2) {
            Objects.requireNonNull(schema.get("PageEntity"))
                    .addField("tagName", String.class)
                    .addField("tagId", String.class)
                    .addField("noteName", String.class)
                    .addField("isNewAdd", int.class);

            Objects.requireNonNull(schema.get("SyncPageEntity"))
                    .addField("tagName", String.class)
                    .addField("tagId", String.class)
                    .addField("noteName", String.class)
                    .addField("isNewAdd", int.class);

            schema.create("TagEntity")
                    .addField("tagId", String.class, FieldAttribute.PRIMARY_KEY)
                    .addField("tagName", String.class)
                    .addField("createTime", String.class)
                    .addField("updateTime", String.class)
                    .addField("isDelete", int.class)
                    .addField("isDirty", int.class)
                    .addField("isSelf", int.class);

            schema.create("SyncTagEntity")
                    .addField("tagId", String.class)
                    .addField("tagName", String.class)
                    .addField("createTime", String.class)
                    .addField("updateTime", String.class)
                    .addField("isDelete", int.class)
                    .addField("isDirty", int.class)
                    .addField("syncTime", String.class);
            oldVersion++;
        }
        if (oldVersion == 3) {
            schema.create("OcrEntity")
                    .addField("pageId", String.class, FieldAttribute.PRIMARY_KEY)
                    .addField("ocrContent", String.class);
            oldVersion++;
        }
        if (oldVersion == 4) {
            schema.create("BookPdfEntity")
                    .addField("pdfId", String.class, FieldAttribute.PRIMARY_KEY)
                    .addField("pdfName", String.class)
                    .addField("pdfPath", String.class)
                    .addField("createTime", String.class)
                    .addField("updateTime", String.class)
                    .addField("pdfSize", String.class)
                    .addField("isNewAdd", int.class)
                    .addField("isDelete", int.class)
                    .addField("syncTime", String.class)
                    .addField("isDirty", int.class);


            schema.create("SyncPdfEntity")
                    .addField("pdfId", String.class)
                    .addField("pdfName", String.class)
                    .addField("pdfPath", String.class)
                    .addField("createTime", String.class)
                    .addField("updateTime", String.class)
                    .addField("hasUploadPdf", boolean.class)
                    .addField("pdfSize", String.class)
                    .addField("isNewAdd", int.class)
                    .addField("isDirty", int.class)
                    .addField("syncTime", String.class)
                    .addField("isDelete", int.class);

            schema.create("PdfDownloadEntity")
                    .addField("pdfID", String.class)
                    .addField("hasDownloadPdf", boolean.class);
            oldVersion++;
        }
        if (oldVersion == 5) {
            schema.create("MessageEntity")
                    .addField("requestId", String.class)
                    .addField("uuid", String.class)
                    .addField("createTimeString", String.class)
                    .addField("createTime", long.class)
                    .addField("type", int.class);

            schema.create("SPReportEntity")
                    .addField("equipmentUuid", String.class)
                    .addField("proportion", String.class)
                    .addField("id", int.class)
                    .addField("errorDuration", int.class)
                    .addField("usingDuration", int.class)
                    .addField("haveRead", int.class)
                    .addField("beginTime", long.class)
                    .addField("endTime", long.class)
                    .addField("createTime", long.class)
                    .addField("rightDuration", int.class)
                    .addField("seriousErrorDuration", int.class)
                    .addField("mildErrorDuration", int.class)
                    .addField("moderateErrorDuration", int.class)
                    .addField("rightProportion", String.class)
                    .addField("seriousProportion", String.class)
                    .addField("mildProportion", String.class)
                    .addField("moderateProportion", String.class);

            schema.create("MissedCallEntity")
                    .addField("userId", String.class)
                    .addField("udid", String.class)
                    .addField("deviceName", String.class)
                    .addField("direction", String.class)
                    .addField("callId", String.class)
                    .addField("equipmentUuid", String.class)
                    .addField("id", int.class)
                    .addField("ownerType", int.class)
                    .addField("status", int.class)
                    .addField("haveRead", int.class)
                    .addField("createTime", long.class);

            schema.create("AuraMateDeviceModel")
                    .addField("releationId", String.class)
                    .addField("id", int.class)
                    .addField("equipmentUid", String.class)
                    .addField("ownerId", int.class)
                    .addField("memberId", int.class)
                    .addField("deviceName", String.class)
                    .addField("createOn", long.class)
                    .addField("isSelect", boolean.class)
                    .addField("lastFileTimestamp", String.class);

            schema.create("AuraMateNewFileRemind")
                    .addField("releationId", String.class)
                    .addField("timeStamp", String.class)
                    .addField("haveRead", boolean.class);

            schema.create("WifiHistoryEntity")
                    .addField("ssid", String.class)
                    .addField("password", String.class)
                    .addField("createTime", long.class);

            oldVersion++;
        }
        if (oldVersion == 6) {
            Objects.requireNonNull(schema.get("MessageEntity"))
                    .removeField("createTimeString")
                    .transform(new RealmObjectSchema.Function() {
                        @Override
                        public void apply(DynamicRealmObject obj) {
                            obj.setLong("serverTimestamp", Long.parseLong(obj.getString("serverTimestamp")));
                        }
                    });
        }


    }
}