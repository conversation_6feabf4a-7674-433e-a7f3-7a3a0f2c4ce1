package com.czur.cloud.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.ComponentName
import android.os.Build
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import androidx.activity.OnBackPressedCallback
import androidx.activity.ComponentActivity
import androidx.annotation.RequiresApi
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logE

/**
 * Android 16 行为变更适配工具类
 * 处理edge-to-edge、预测性返回手势、自适应布局等行为变更
 */
object Android16BehaviorUtils {
    private const val TAG = "Android16BehaviorUtils"

    /**
     * 适配Edge-to-Edge全面屏（Android 16强制启用）
     */
    fun adaptEdgeToEdge(activity: Activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            try {
                // Android 16中edge-to-edge无法关闭，必须适配
                val window = activity.window
                
                // 设置状态栏和导航栏透明
                window.statusBarColor = android.graphics.Color.TRANSPARENT
                window.navigationBarColor = android.graphics.Color.TRANSPARENT
                
                // 设置系统栏行为
                val controller = window.insetsController
                controller?.let {
                    // 设置状态栏内容为深色（适用于浅色背景）
                    it.setSystemBarsAppearance(
                        WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS,
                        WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
                    )
                    
                    // 设置导航栏内容为深色（适用于浅色背景）
                    it.setSystemBarsAppearance(
                        WindowInsetsController.APPEARANCE_LIGHT_NAVIGATION_BARS,
                        WindowInsetsController.APPEARANCE_LIGHT_NAVIGATION_BARS
                    )
                }
                
                // 设置内容延伸到系统栏
                window.setDecorFitsSystemWindows(false)
                
                logI("$TAG.adaptEdgeToEdge: Edge-to-edge adapted successfully")
            } catch (e: Exception) {
                logE("$TAG.adaptEdgeToEdge error: ${e.message}")
            }
        }
    }

    /**
     * 为View设置系统栏内边距
     */
    fun applySystemBarInsets(view: View, applyTop: Boolean = true, applyBottom: Boolean = true) {
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            
            val paddingLeft = v.paddingLeft
            val paddingRight = v.paddingRight
            val paddingTop = if (applyTop) systemBars.top else v.paddingTop
            val paddingBottom = if (applyBottom) systemBars.bottom else v.paddingBottom
            
            v.setPadding(paddingLeft, paddingTop, paddingRight, paddingBottom)
            insets
        }
    }

    /**
     * 适配预测性返回手势（Android 16默认启用）
     */
    fun adaptPredictiveBack(activity: ComponentActivity, onBackAction: (() -> Unit)? = null) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            try {
                val callback = object : OnBackPressedCallback(true) {
                    override fun handleOnBackPressed() {
                        // 执行自定义返回逻辑
                        onBackAction?.invoke() ?: run {
                            // 默认返回行为
                            if (activity.supportFragmentManager.backStackEntryCount > 0) {
                                activity.supportFragmentManager.popBackStack()
                            } else {
                                activity.finish()
                            }
                        }
                    }
                }
                
                activity.onBackPressedDispatcher.addCallback(activity, callback)
                logI("$TAG.adaptPredictiveBack: Predictive back gesture adapted")
            } catch (e: Exception) {
                logE("$TAG.adaptPredictiveBack error: ${e.message}")
            }
        }
    }

    /**
     * 检查Intent是否符合Android 16的安全要求
     */
    fun isIntentSecure(intent: Intent): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= 36) {
                // Android 16要求显式Intent必须有action
                if (intent.component != null && intent.action == null) {
                    logE("$TAG.isIntentSecure: Explicit intent without action is not allowed in Android 16")
                    return false
                }
                
                // 检查Intent Filter匹配
                if (intent.component != null) {
                    // 对于显式Intent，建议添加action以符合安全要求
                    if (intent.action == null) {
                        logI("$TAG.isIntentSecure: Adding default action for explicit intent")
                        intent.action = Intent.ACTION_DEFAULT
                    }
                }
            }
            true
        } catch (e: Exception) {
            logE("$TAG.isIntentSecure error: ${e.message}")
            false
        }
    }

    /**
     * 创建安全的显式Intent（符合Android 16要求）
     */
    fun createSecureExplicitIntent(context: Context, targetClass: Class<*>, action: String = Intent.ACTION_DEFAULT): Intent {
        val intent = Intent(context, targetClass)
        
        if (Build.VERSION.SDK_INT >= 36) {
            // Android 16要求显式Intent必须有action
            intent.action = action
        }
        
        return intent
    }

    /**
     * 创建安全的组件Intent（符合Android 16要求）
     */
    fun createSecureComponentIntent(packageName: String, className: String, action: String): Intent {
        val intent = Intent()
        intent.component = ComponentName(packageName, className)
        
        if (Build.VERSION.SDK_INT >= 36) {
            // Android 16要求显式Intent必须有action
            intent.action = action
        }
        
        return intent
    }

    /**
     * 适配自适应布局（Android 16大屏设备）
     */
    fun adaptAdaptiveLayout(activity: Activity) {
        if (Build.VERSION.SDK_INT >= 36) {
            try {
                // 移除屏幕方向限制，让系统自动适配
                activity.requestedOrientation = android.content.pm.ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
                
                logI("$TAG.adaptAdaptiveLayout: Adaptive layout adapted for large screen devices")
            } catch (e: Exception) {
                logE("$TAG.adaptAdaptiveLayout error: ${e.message}")
            }
        }
    }

    /**
     * 检查是否需要16KB页大小兼容模式
     */
    fun shouldUse16KBPageSizeCompat(): Boolean {
        return Build.VERSION.SDK_INT >= 36
    }

    /**
     * 全面适配Android 16行为变更
     */
    fun adaptAllBehaviorChanges(activity: ComponentActivity, onBackAction: (() -> Unit)? = null) {
        if (Build.VERSION.SDK_INT >= 36) {
            logI("$TAG.adaptAllBehaviorChanges: Starting Android 16 behavior adaptation")
            
            // 1. 适配Edge-to-Edge
            adaptEdgeToEdge(activity)
            
            // 2. 适配预测性返回手势
            adaptPredictiveBack(activity, onBackAction)
            
            // 3. 适配自适应布局
            adaptAdaptiveLayout(activity)
            
            logI("$TAG.adaptAllBehaviorChanges: Android 16 behavior adaptation completed")
        }
    }

    /**
     * 检查应用是否已正确适配Android 16
     */
    fun checkAndroid16Compatibility(activity: Activity): Boolean {
        if (Build.VERSION.SDK_INT < 36) return true
        
        var isCompatible = true
        val issues = mutableListOf<String>()
        
        try {
            // 检查Edge-to-Edge适配
            val window = activity.window
            if (window.statusBarColor != android.graphics.Color.TRANSPARENT) {
                issues.add("Status bar should be transparent for edge-to-edge")
                isCompatible = false
            }
            
            // 检查预测性返回手势适配
            if (activity is ComponentActivity) {
                // 这里可以添加更多检查逻辑
            }
            
            if (issues.isNotEmpty()) {
                logE("$TAG.checkAndroid16Compatibility: Compatibility issues found:")
                issues.forEach { issue ->
                    logE("$TAG.checkAndroid16Compatibility: - $issue")
                }
            } else {
                logI("$TAG.checkAndroid16Compatibility: App is compatible with Android 16")
            }
            
        } catch (e: Exception) {
            logE("$TAG.checkAndroid16Compatibility error: ${e.message}")
            isCompatible = false
        }
        
        return isCompatible
    }
}
