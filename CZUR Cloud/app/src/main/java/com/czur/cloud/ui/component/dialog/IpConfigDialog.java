package com.czur.cloud.ui.component.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.R;
import com.czur.cloud.event.AuraMateOperateEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.netty.Config;
import com.czur.cloud.netty.core.CZURTcpClient;

import org.greenrobot.eventbus.EventBus;

public class IpConfigDialog extends Dialog {
    private TextView tv_current_ip, tv_current_port;
    private EditText et_ip, et_port;
    private Button btn_ensure, btn_reset;
    private SharedPreferences sharedPreferences;

    public IpConfigDialog(@NonNull Context context) {
        super(context);
    }

    public IpConfigDialog(@NonNull Context context, int themeResId) {
        super(context, themeResId);
    }

    protected IpConfigDialog(@NonNull Context context, boolean cancelable, @Nullable OnCancelListener cancelListener) {
        super(context, cancelable, cancelListener);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_ip_edit);
        tv_current_ip = findViewById(R.id.tv_current_ip);
        et_ip = findViewById(R.id.et_ip);

        tv_current_port = findViewById(R.id.tv_current_port);
        et_port = findViewById(R.id.et_port);

        btn_ensure = findViewById(R.id.btn_ensure);
        btn_reset = findViewById(R.id.btn_reset);
        sharedPreferences = getContext().getSharedPreferences("ip_port_sp", Context.MODE_PRIVATE);

        tv_current_ip.setText("当前IP:  " + sharedPreferences.getString("ip", Config.ADDRESS));
        tv_current_port.setText("当前PORT:  " + sharedPreferences.getInt("port", Config.PORT));

        btn_ensure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!TextUtils.isEmpty(et_ip.getText().toString())) {
                    sharedPreferences.edit().putString("ip", et_ip.getText().toString()).apply();
                }
                if (!TextUtils.isEmpty(et_port.getText().toString())) {
                    sharedPreferences.edit().putInt("port", Integer.parseInt(et_port.getText().toString())).apply();
                }
                dismiss();
                ToastUtils.showLong("正在重连...");
                CZURTcpClient.getInstance().closeChannel();
                CZURTcpClient.getInstance().connect();
                EventBus.getDefault().post(new AuraMateOperateEvent(EventType.AURA_MATE_CHANGED, ""));
            }
        });

        btn_reset.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                tv_current_ip.setText("当前IP:  " + Config.ADDRESS);
                tv_current_port.setText("当前PORT:  " + Config.PORT);
                sharedPreferences.edit().clear().apply();
                ToastUtils.showLong("正在重连...");
                CZURTcpClient.getInstance().closeChannel();
                CZURTcpClient.getInstance().connect();
                btn_reset.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        dismiss();
                        EventBus.getDefault().post(new AuraMateOperateEvent(EventType.AURA_MATE_CHANGED, ""));
                    }
                }, 2000);
            }
        });

    }
}
