package com.czur.cloud.ui.starry.meeting.bean

import com.google.gson.annotations.SerializedName

data class Token(
    @SerializedName("rtc_token")
    val rtcToken: String,  // 音视频token
    @SerializedName("rtm_token")
    val rtmToken: String,   // 聊天token
    @SerializedName("channel")
    val room: Int,           // 会议室号
)

/**
 * 分享用Token
 */
data class ShareToken(
//    @SerializedName("rtc_token")
//    val rtcToken: String,  // 音视频token
    val shareId:Long,
    val rtc_token: String,
    val rtm_token: String
)

/**
 * 分享用Token 包装类, 为了加入报错码
 */
data class ShareTokenWrapper(
//    @SerializedName("rtc_token")
//    val rtcToken: String,  // 音视频token
    val shareToken:ShareToken?,
    var errorCode: Int = 0
)

data class StartToken(
    val channel: Int,
    val rtc_token: String,
    val rtm_token: String,
    val meetingCode: String,
    val uid: Int
)