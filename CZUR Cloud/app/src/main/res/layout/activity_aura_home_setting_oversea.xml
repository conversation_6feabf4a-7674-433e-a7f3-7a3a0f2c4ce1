<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:background="@color/white">

        <ImageView
            android:id="@+id/normal_back_btn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="10dp"
            android:padding="10dp"
            android:src="@mipmap/login_back_icon" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/normal_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/account_title"
            android:textSize="18sp" />

    </RelativeLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fadingEdge="none"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:gravity="center_vertical"
                android:paddingLeft="18.5dp"
                android:text="@string/aura_home_device_name"
                android:textColor="@color/black_22"
                android:textSize="16sp" />

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="true"
                android:focusableInTouchMode="true">

                <EditText
                    android:id="@+id/et_add_user_edt"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:layout_toStartOf="@+id/edit_aura_home_name_finish"
                    android:background="@null"
                    android:hint="@string/input_aura_home_name"
                    android:maxLength="20"
                    android:maxLines="1"
                    android:paddingStart="18.5dp"
                    android:singleLine="true"
                    android:layout_marginEnd="10dp"
                    android:textColor="@color/black_22"
                    android:textColorHint="@color/gray_bb"
                    android:textSize="15sp" />

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/edit_aura_home_name_finish"
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="12dp"
                    android:gravity="center"
                    android:text="@string/change"
                    android:textColor="@color/blue_29b0d7"
                    android:textSize="15sp" />
            </RelativeLayout>


            <View
                android:layout_width="match_parent"
                android:layout_height="14dp"
                android:background="@color/gary_f9"
                android:gravity="center_vertical" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_marginStart="18.5dp"
                android:layout_marginTop="14dp"
                android:gravity="center_vertical"
                android:text="@string/aura_home_device_is_state"
                android:textColor="@color/black_22"
                android:textSize="16sp" />


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="45dp">

                <LinearLayout
                    android:id="@+id/offline_text_ll"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="18.5dp"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/device_status_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/offline"
                        android:textColor="@color/black_22"
                        android:textSize="16sp" />

                    <ImageView
                        android:id="@+id/device_status_img"
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="5dp"
                        android:src="@mipmap/offline_icon" />
                </LinearLayout>

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/status_info"
                    android:layout_toEndOf="@+id/offline_text_ll"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="18.5dp"
                    android:text="@string/online_info"
                    app:autoSizeTextType="uniform"
                    app:autoSizeMinTextSize="10sp"
                    app:autoSizeMaxTextSize="14sp"
                    app:autoSizeStepGranularity="1sp"
                    android:gravity="end"
                    android:maxLines="2"
                    android:layout_marginStart="30dp"
                    android:textColor="@color/gray_bb"
                    android:textSize="14sp" />


            </RelativeLayout>


            <RelativeLayout
                android:id="@+id/aura_home_reconnect_rl"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/gary_ff">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="17dp"
                    android:background="@color/gray_e5" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="17.5dp"
                    android:text="@string/aura_home_reconnect"
                    android:textColor="@color/normal_blue"
                    android:textSize="15sp" />


                <ImageView
                    android:layout_width="6dp"
                    android:layout_height="10.5dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="17dp"
                    android:background="@mipmap/user_right_gray_arrow"
                    android:padding="10dp" />

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/aura_home_invite_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="14dp"
                    android:background="@color/gary_f9"
                    android:gravity="center_vertical" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/gary_ff">


                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="17.5dp"
                        app:autoSizeMaxTextSize="15sp"
                        app:autoSizeMinTextSize="11sp"
                        app:autoSizeStepGranularity="1sp"
                        app:autoSizeTextType="uniform"
                        android:maxLines="1"
                        android:layout_marginEnd="20dp"
                        android:text="@string/invite_family_tip"
                        android:textColor="@color/normal_blue"
                        android:textSize="15sp" />


                    <ImageView
                        android:layout_width="6dp"
                        android:layout_height="10.5dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="17dp"
                        android:background="@mipmap/user_right_gray_arrow"
                        android:padding="10dp" />


                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/aura_home_trans_rl"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/gary_ff"
                    android:visibility="gone">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginStart="17dp"
                        android:background="@color/gray_e5" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="17.5dp"
                        android:text="@string/transform_to_family"
                        android:textColor="@color/normal_blue"
                        android:textSize="15sp" />


                    <ImageView
                        android:layout_width="6dp"
                        android:layout_height="10.5dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="17dp"
                        android:background="@mipmap/user_right_gray_arrow"
                        android:padding="10dp" />

                </RelativeLayout>

            </LinearLayout>

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/manage_device_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/gary_f9"
                android:gravity="center_vertical"
                android:paddingLeft="17.5dp"
                android:paddingTop="14dp"
                android:paddingBottom="10dp"
                android:text="@string/share_devices_title"
                android:textColor="@color/gary_c4"
                android:textSize="12sp"
                android:visibility="gone" />

            <RelativeLayout
                android:id="@+id/setting_recyclerView_rl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:descendantFocusability="blocksDescendants"
                android:visibility="gone">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/setting_recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    android:overScrollMode="never"
                    android:scrollbars="none" />
            </RelativeLayout>


            <LinearLayout
                android:id="@+id/aura_home_language_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="14dp"
                    android:background="@color/gary_f9"
                    android:gravity="center_vertical" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/gary_ff">


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="17.5dp"
                        android:text="@string/set_aura_mate_language"
                        android:textColor="@color/normal_blue"
                        android:textSize="15sp" />

                    <ImageView
                        android:layout_width="6dp"
                        android:layout_height="10.5dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="17dp"
                        android:background="@mipmap/user_right_gray_arrow"
                        android:padding="10dp" />


                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/aura_home_errorsit_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="14dp"
                    android:background="@color/gary_f9"
                    android:gravity="center_vertical" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/gary_ff">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="17.5dp"
                            android:text="@string/aura_home_errorsit_name"
                            android:textColor="@color/normal_blue"
                            android:textSize="15sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="17.5dp"
                            android:layout_marginTop="2dp"
                            android:text="@string/aura_home_errorsit_sub_name"
                            android:textColor="@color/gray_bb"
                            android:textSize="10sp" />

                    </LinearLayout>


                    <com.github.iielse.switchbutton.SwitchView
                        android:id="@+id/switch_errorsit"
                        android:layout_width="50dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="17.5dp"
                        app:isOpened="true"
                        app:primaryColor="@color/blue_33c5e4"
                        app:primaryColorDark="@color/blue_33c5e4"
                        app:ratioAspect="0.6"
                        android:layout_height="30dp"/>

                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/aura_home_notify_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="14dp"
                    android:background="@color/gary_f9"
                    android:gravity="center_vertical" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/gary_ff">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="17.5dp"
                            android:text="@string/notify_all"
                            android:textColor="@color/normal_blue"
                            android:textSize="15sp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="17.5dp"
                            android:layout_marginTop="2dp"
                            android:text="@string/aura_setting_notify_detail"
                            android:textColor="@color/gray_bb"
                            android:textSize="10sp" />

                    </LinearLayout>


                    <com.github.iielse.switchbutton.SwitchView
                        android:id="@+id/switch_notify"
                        android:layout_width="50dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="17.5dp"
                        app:isOpened="false"
                        app:primaryColor="@color/blue_33c5e4"
                        app:primaryColorDark="@color/blue_33c5e4"
                        app:ratioAspect="0.6"
                        android:layout_height="30dp"/>

                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/aura_mate_notify_oversea_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="14dp"
                    android:background="@color/gary_f9"
                    android:gravity="center_vertical" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/gary_ff">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="17.5dp"
                            android:text="@string/notify_use"
                            android:textColor="@color/normal_blue"
                            android:textSize="15sp" />

                    </LinearLayout>


                    <com.github.iielse.switchbutton.SwitchView
                        android:id="@+id/switch_notify_use"
                        android:layout_width="50dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="17.5dp"
                        app:isOpened="false"
                        app:primaryColor="@color/blue_33c5e4"
                        app:primaryColorDark="@color/blue_33c5e4"
                        app:ratioAspect="0.6"
                        android:layout_height="30dp"/>

                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="17dp"
                    android:background="@color/gray_e5" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/gary_ff">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="17.5dp"
                            android:text="@string/notify_file"
                            android:textColor="@color/normal_blue"
                            android:textSize="15sp" />

                    </LinearLayout>


                    <com.github.iielse.switchbutton.SwitchView
                        android:id="@+id/switch_notify_file"
                        android:layout_width="50dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="17.5dp"
                        app:isOpened="true"
                        app:primaryColor="@color/blue_33c5e4"
                        app:primaryColorDark="@color/blue_33c5e4"
                        app:ratioAspect="0.6"
                        android:layout_height="30dp"/>

                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginStart="17dp"
                    android:background="@color/gray_e5" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/gary_ff">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="17.5dp"
                            android:text="@string/notify_offline"
                            android:textColor="@color/normal_blue"
                            android:textSize="15sp" />

                    </LinearLayout>


                    <com.github.iielse.switchbutton.SwitchView
                        android:id="@+id/switch_notify_offline"
                        android:layout_width="50dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="17.5dp"
                        app:isOpened="true"
                        app:primaryColor="@color/blue_33c5e4"
                        app:primaryColorDark="@color/blue_33c5e4"
                        app:ratioAspect="0.6"
                        android:layout_height="30dp"/>

                </RelativeLayout>

            </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="14dp"
                    android:background="@color/gary_f9"
                    android:gravity="center_vertical" />

                <RelativeLayout
                    android:id="@+id/rl_light_detail"
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/gary_ff">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="17.5dp"
                        android:text="@string/aura_setting_light_detail"
                        android:textColor="@color/normal_blue"
                        android:textSize="15sp" />

                    <ImageView
                        android:layout_width="6dp"
                        android:layout_height="10.5dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="17dp"
                        android:background="@mipmap/user_right_gray_arrow"
                        android:padding="10dp" />


                </RelativeLayout>
            </LinearLayout>




            <LinearLayout
                android:id="@+id/aura_home_update_rl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="14dp"
                    android:background="@color/gary_f9"
                    android:gravity="center_vertical" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="45dp"
                    android:background="@color/gary_ff">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentStart="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="10dp"
                        android:layout_marginStart="18.5dp">

                        <TextView
                            android:id="@+id/current_version_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/device_version"
                            android:maxLines="2"
                            android:layout_gravity="center_vertical"
                            android:textColor="@color/normal_blue"
                            android:textSize="15sp" />

                        <ImageView
                            android:id="@+id/need_update_img"
                            android:layout_width="5dp"
                            android:layout_height="5dp"
                            android:visibility="gone"
                            android:src="@drawable/circle_red" />
                    </LinearLayout>

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/need_update_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="28dp"
                        android:text="@string/device_new_version"
                        android:textColor="@color/blue_29b0d7"
                        android:textSize="12sp"
                        android:visibility="gone" />

                    <com.czur.cloud.ui.component.MediumBoldTextView
                        android:id="@+id/device_latest_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="28dp"
                        android:text="@string/device_latest"
                        android:visibility="gone"
                        android:textColor="@color/gray_bb"
                        android:textSize="12sp" />

                    <LinearLayout
                        android:id="@+id/offline_update_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="1.5dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="18dp"
                        android:orientation="horizontal">

                        <View
                            android:layout_width="15dp"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="2dp"
                            android:background="@color/black_22" />

                        <View
                            android:layout_width="15dp"
                            android:layout_height="match_parent"
                            android:background="@color/black_22" />
                    </LinearLayout>


                    <ImageView
                        android:id="@+id/update_arrow"
                        android:layout_width="6dp"
                        android:layout_height="10.5dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="17dp"
                        android:visibility="gone"
                        android:background="@mipmap/blue_arrow"
                        android:padding="10dp" />

                </RelativeLayout>
            </LinearLayout>


            <View
                android:layout_width="match_parent"
                android:layout_height="14dp"
                android:background="@color/gary_f9"
                android:gravity="center_vertical" />


            <RelativeLayout
                android:id="@+id/aura_home_unbind_rl"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/gary_ff">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="@string/unbind"
                    android:textColor="@color/red_de4d4d"
                    android:textSize="15sp" />
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@color/gary_f9"
                android:gravity="center_vertical" />
        </LinearLayout>
    </ScrollView>


</LinearLayout>

