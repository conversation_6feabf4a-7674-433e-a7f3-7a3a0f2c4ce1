package com.czur.cloud.entity;

/**
 * Created by Yz on 2018/6/11.
 * Email：<EMAIL>
 */
public class AuraMateWrongTagModel {


    /**
     * id : 8
     * tagName : 数学
     * ownerId : 2771
     * equipmentUid : 1234567890
     * createUserId : 87
     * deleted : null
     * createTime : 2019-07-11 17:35:54.000
     * updateTime : null
     */

    private int id;
    private String tagName;
    private String ownerId;
    private String equipmentUid;
    private int createUserId;
    private Object deleted;
    private String createTime;
    private Object updateTime;

    private boolean isSelect;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTagName() {
        return tagName;
    }
    public boolean isSelect() {
        return isSelect;
    }

    public void setSelect(boolean select) {
        isSelect = select;
    }
    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getEquipmentUid() {
        return equipmentUid;
    }

    public void setEquipmentUid(String equipmentUid) {
        this.equipmentUid = equipmentUid;
    }

    public int getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(int createUserId) {
        this.createUserId = createUserId;
    }

    public Object getDeleted() {
        return deleted;
    }

    public void setDeleted(Object deleted) {
        this.deleted = deleted;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public Object getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Object updateTime) {
        this.updateTime = updateTime;
    }
}
