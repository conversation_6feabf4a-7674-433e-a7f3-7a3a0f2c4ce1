package com.czur.cloud.ui.component.popup;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowInsets;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.R;
import com.czur.cloud.util.ClipboardUtils;
import com.czur.cloud.util.validator.StringUtils;

/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class CopyPopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;

    private boolean isChecked;

    public CopyPopup(Context context) {
        super(context);
    }

    public CopyPopup(Context context, int theme) {
        super(context, theme);
    }

    public boolean isChecked() {
        return this.isChecked;
    }

    public static class Builder implements View.OnClickListener {
        private Context context;

        private String message;
        private String title;
        private View contentsView;
        private ImageView backBtn;
        private Class<? extends Activity> clz;


        private OnClickListener positiveListener;
        private OnClickListener onNegativeListener;
        private OnDismissListener onDismissListener;
        private RelativeLayout handwritingResultCopyRl;
        private TextView textView;
        private View empty;
        private RelativeLayout copyTopRl;
        private ImageView dismissBtn;

        public Builder(Context context) {
            this.context = context;
        }

        public Builder setMessage(String message) {
            this.message = message;
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public Builder setContentsView(View contentsView) {
            this.contentsView = contentsView;
            return this;
        }

        public Builder setContentsView(int resource) {
            this.contentsView = LayoutInflater.from(context).inflate(resource, null);
            return this;
        }

        public Builder setOnClickListener(OnBottomClickListener onBottomClickListener) {
            this.onBottomClickListener = onBottomClickListener;
            return this;
        }

        public Builder setFinishToActivity(Class<? extends Activity> clz) {
            this.clz = clz;
            return this;
        }

        /**
         * 点击事件接口
         **/
        public interface OnBottomClickListener {
            /**
             * @param viewId
             */
            void onClick(int viewId);
        }

        private OnBottomClickListener onBottomClickListener;

        private void setOnBottomClickListener(OnBottomClickListener onBottomClickListener) {
            this.onBottomClickListener = onBottomClickListener;

        }


        @Override
        public void onClick(View v) {
            switch (v.getId()) {
                case R.id.back_btn:
                    ActivityUtils.finishToActivity(clz, false);
                    break;
                case R.id.handwriting_result_copy_rl:
                    ClipboardUtils.copyText(textView.getText().toString());
                    ToastUtils.showShort(R.string.copy_success);
                    break;
                case R.id.dialog_dismiss_btn:
                case R.id.copy_empty:
                    if (onBottomClickListener != null) {
                        onBottomClickListener.onClick(v.getId());
                    }
                    break;
                default:
                    break;
            }

        }

        public CopyPopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            final CopyPopup dialog;
            View layout;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                dialog = new CopyPopup(context, R.style.Dialog_Fullscreen);
                layout = commonCustomPopLayout(inflater, dialog);

                dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
                dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
                dialog.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
                dialog.getWindow().setStatusBarColor(Color.TRANSPARENT);
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                dialog = new CopyPopup(context, R.style.Dialog_Fullscreen);
                layout = commonCustomPopLayout(inflater, dialog);

                dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            } else {
                dialog = new CopyPopup(context, R.style.Dialog_Fullscreen_Low);
                layout = commonCustomPopLayout(inflater, dialog);

                dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
                dialog.getWindow().addFlags(
                        WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);

            }

            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            layout.setOnApplyWindowInsetsListener(new View.OnApplyWindowInsetsListener() {
                @NonNull
                @Override
                public WindowInsets onApplyWindowInsets(@NonNull View v, @NonNull WindowInsets insets) {
                    if (Build.VERSION.SDK_INT > Build.VERSION_CODES.Q) {
                        v.setPadding(v.getPaddingLeft(),v.getPaddingTop(),v.getPaddingRight(),insets.getSystemWindowInsets().bottom);
                    }
                    return insets;
                }
            });
            dialog.getWindow().setAttributes(params);
            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final CopyPopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 1.0f;
            dialog.getWindow().setAttributes(lp);
            View layout = inflater.inflate(R.layout.copy_popup, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);
            empty = layout.findViewById(R.id.copy_empty);

            dismissBtn = (ImageView) layout.findViewById(R.id.dialog_dismiss_btn);
            copyTopRl = (RelativeLayout) layout.findViewById(R.id.copy_top_rl);
            textView = (TextView) layout.findViewById(R.id.handwriting_result_text);
            handwritingResultCopyRl = (RelativeLayout) layout.findViewById(R.id.handwriting_result_copy_rl);
            backBtn = (ImageView) layout.findViewById(R.id.back_btn);

            RelativeLayout.LayoutParams layoutParams = (RelativeLayout.LayoutParams) copyTopRl.getLayoutParams();
            layoutParams.height += BarUtils.getStatusBarHeight();
            copyTopRl.setLayoutParams(layoutParams);

            handwritingResultCopyRl.setOnClickListener(this);
            backBtn.setOnClickListener(this);
            empty.setOnClickListener(this);
            dismissBtn.setOnClickListener(this);

            if (contentsView == null) {
                if (StringUtils.isNotEmpty(message)) {
                    textView.setText(message + StringUtils.EMPTY);
                }

            } else {
                textView.setVisibility(View.GONE);
            }


            if (positiveListener != null) {
                handwritingResultCopyRl.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        positiveListener.onClick(dialog, R.id.handwriting_result_copy_rl);
                    }
                });
            }

            if (onNegativeListener != null) {
                backBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        onNegativeListener.onClick(dialog, R.id.back_btn);
                    }
                });
            }

            if (onDismissListener != null) {
                dialog.setOnDismissListener(onDismissListener);
            }

            return layout;
        }
    }
}
