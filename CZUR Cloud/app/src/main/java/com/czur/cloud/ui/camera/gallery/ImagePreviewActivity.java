package com.czur.cloud.ui.camera.gallery;

import static androidx.annotation.Dimension.DP;
import static com.czur.cloud.ui.books.sync.SyncService.JSON;
import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Bitmap;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.SpannableStringBuilder;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.EncodeUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.SpanUtils;
import com.blankj.utilcode.util.StringUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.common.HandwritingRecognitionTask;
import com.czur.cloud.entity.HandwritingEntity;
import com.czur.cloud.entity.realm.PageEntity;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.HandWritingCountEvent;
import com.czur.cloud.model.BaiduTokenModel;
import com.czur.cloud.model.BaiduWordModel;
import com.czur.cloud.model.HandwritingCountModel;
import com.czur.cloud.model.OcrServerModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.callback.ProgressHelper;
import com.czur.cloud.network.callback.UIProgressRequestListener;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.FirstPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.books.EditTagActivity;
import com.czur.cloud.ui.books.SelectLanguageActivity;
import com.czur.cloud.ui.camera.CameraActivity;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.component.popup.CopyPopup;
import com.czur.cloud.ui.component.popup.WhiteRightPopup;
import com.czur.cloud.util.BitmapUtils;
import com.czur.cloud.util.PermissionUtil;
import com.czur.cloud.util.share.FileUtil;
import com.czur.cloud.util.share.ShareContentType;
import com.czur.cloud.util.share.ShareUtils;
import com.czur.cloud.util.validator.Validator;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import io.realm.Realm;
import io.realm.Sort;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;


/**
 * Created by Yz on 2018/4/3
 * Email：<EMAIL>
 */

public class ImagePreviewActivity extends BaseActivity implements View.OnClickListener {
    private ImageView backBtn;
    private GalleryViewPager viewPager;
    private TextView countTv;
    private RelativeLayout cameraPreviewSaveAlbumRl;
    private RelativeLayout cameraPreviewHandwritingRl;
    private RelativeLayout cameraPreviewStarRl;
    private RelativeLayout cameraPreviewShareRl;
    private RelativeLayout cameraPreviewDeleteRl;
    private ImageView cameraPreviewStarImg;
    private TextView cameraPreviewStarTv;
    private static final int SHARE_SUCCESS_CODE = 666;
    private static final int SELECT_LANGUAGE_CODE = 667;
    private HashMap<Integer, ZoomImageView> viewMap = new HashMap<>();
    private List<PageEntity> mDataList;
    private ImagePagerAdapter mAdapter;
    private int count = 0;
    private int currentItem = 0;
    private Realm realm;
    private String currentPageId;
    private WhiteRightPopup whiteRightPopup;
    private int isItemStar;
    private SimpleDateFormat formatter;
    private UserPreferences userPreferences;
    private HttpManager httpManager;
    private WeakHandler handler;
    private String sdPath;
    private FirstPreferences firstPreferences;

    private CopyPopup copyPopup;
    private CopyPopup.Builder builder;
    private RelativeLayout addTagRl;
    private TextView previewAddTagTv;
    private ImageView previewAddTagImg;
    private int isItemTag;
    private OkHttpClient okHttpClient;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.black_2a);
        BarUtils.setNavBarColor(this, getColor(R.color.black_2a));
        BarUtils.setStatusBarLightMode(this, false);
        setContentView(R.layout.activity_preview);
        initView();
        initData();
        initEvents();
    }

    private void initView() {
        EventBus.getDefault().register(this);
        okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.MINUTES)
                .readTimeout(1, TimeUnit.MINUTES)
                .build();
        firstPreferences = FirstPreferences.getInstance(this);
        backBtn = findViewById(R.id.preview_camera_back_btn);
        viewPager = findViewById(R.id.content);
        countTv = findViewById(R.id.preview_camera_title);
        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        handler = new WeakHandler();

//        sdPath = Environment.getExternalStorageDirectory() + CZURConstants.SD_PATH;
        sdPath = CZURConstants.SD_PATH;
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        cameraPreviewSaveAlbumRl = findViewById(R.id.camera_preview_save_album_rl);
        cameraPreviewHandwritingRl = findViewById(R.id.camera_preview_handwriting_rl);
        cameraPreviewStarRl = findViewById(R.id.camera_preview_star_rl);
        cameraPreviewShareRl = findViewById(R.id.camera_preview_share_rl);
        cameraPreviewDeleteRl = findViewById(R.id.camera_preview_delete_rl);
        cameraPreviewStarImg = findViewById(R.id.camera_preview_star_img);
        cameraPreviewStarTv = findViewById(R.id.camera_preview_star_tv);

        WhiteRightPopup.Builder builder = new WhiteRightPopup.Builder(ImagePreviewActivity.this);
        builder.setTitle(getString(R.string.saved_album));
        whiteRightPopup = builder.create();
        addTagRl = findViewById(R.id.add_tag_rl);
        previewAddTagTv = findViewById(R.id.preview_add_tag_tv);
        previewAddTagImg = findViewById(R.id.preview_add_tag_img);

    }

    private void initData() {
        Fresco.getImagePipeline().clearCaches();
        isItemTag = getIntent().getIntExtra("isItemTag", 0);
        currentItem = getIntent().getIntExtra("index", 0);
        currentPageId = getIntent().getStringExtra("currentPageId");
        isItemStar = getIntent().getIntExtra("isItemStar", 0);
        realm = Realm.getDefaultInstance();

        mDataList = realm.where(PageEntity.class)
                .equalTo("isTemp", 1)
                .equalTo("isDelete", 0)
                .findAll()
                .sort("takePhotoTime", Sort.ASCENDING);

        if (mDataList == null || mDataList.size() == 0) {
            finish();
            return;
        }
        count = mDataList.size();
        mAdapter = new ImagePagerAdapter();
        viewPager.setPageMargin(10 * DP);
        viewPager.setAdapter(mAdapter);
        viewPager.addOnPageChangeListener(viewPageListener);
        viewPager.setOffscreenPageLimit(1);
        viewPager.setCurrentItem(currentItem, false);
        countTv.setText(String.format("%1$d / %2$d", (currentItem + 1), count));
        showIsStar();
        showIsTag();
    }

    private void initEvents() {
        backBtn.setOnClickListener(this);
        cameraPreviewDeleteRl.setOnClickListener(this);
        cameraPreviewSaveAlbumRl.setOnClickListener(this);
        cameraPreviewHandwritingRl.setOnClickListener(this);
        cameraPreviewStarRl.setOnClickListener(this);
        addTagRl.setOnClickListener(this);
        cameraPreviewShareRl.setOnClickListener(this);
    }

    private void createCopyDialog(String msg) {
        CopyPopup.Builder builder = new CopyPopup.Builder(this);
        builder.setMessage(msg)
                .setFinishToActivity(CameraActivity.class)
                .setOnClickListener(new CopyPopup.Builder.OnBottomClickListener() {
                    @Override
                    public void onClick(int viewId) {
                        copyPopup.dismiss();
                    }
                });
        copyPopup = builder.create();
//        final TextView textView = copyPopup.findViewById(R.id.handwriting_result_text);
        copyPopup.show();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.preview_camera_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.camera_preview_delete_rl:
                confirmDeleteDialog();
                break;
            case R.id.camera_preview_star_rl:
                starPage();
                break;
            case R.id.camera_preview_share_rl:
                requestCopyToSdPermission(true);
                break;
            case R.id.camera_preview_save_album_rl:
                requestCopyToSdPermission(false);
                break;
            case R.id.camera_preview_handwriting_rl:
                if (firstPreferences.isHandwritingGuide()) {
                    startActivityForResult(new Intent(ImagePreviewActivity.this, SelectLanguageActivity.class), SELECT_LANGUAGE_CODE);
                } else {
                    goHandwriting();
                }
                break;
            case R.id.add_tag_rl:
                Intent intent = new Intent(ImagePreviewActivity.this, EditTagActivity.class);
                intent.putExtra("isPreview", true);
                intent.putExtra("pageId", mDataList.get(viewPager.getCurrentItem()).getPageId());
                ActivityUtils.startActivity(intent);
                break;

            default:
                break;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        Realm realm = Realm.getDefaultInstance();
        switch (event.getEventType()) {

            case ADD_TAG:
                PageEntity pageEntity1 = realm.where(PageEntity.class)
                        .equalTo("pageId", mDataList.get(viewPager.getCurrentItem()).getPageId()).findFirst();

                mDataList.get(viewPager.getCurrentItem()).setTagName(pageEntity1.getTagName());
                mDataList.get(viewPager.getCurrentItem()).setTagId(pageEntity1.getTagId());


                previewAddTagTv.setText(pageEntity1.getTagName());
                previewAddTagTv.setTextColor(getResources().getColor(R.color.white));
                previewAddTagImg.setImageResource(R.mipmap.white_tag_preview_icon);
                addTagRl.setBackground(getResources().getDrawable(R.drawable.btn_rec_5_bg_with_red_fa292f));
                break;
            case DELETE_TAG:

                mDataList.get(viewPager.getCurrentItem()).setTagName("");
                mDataList.get(viewPager.getCurrentItem()).setTagId("");
                setNormalTag();
                break;

            default:
                break;
        }
    }

    private void checkUpdateList(final String filePath) {
        String clearCacheStr = "?" + UUID.randomUUID().toString();
        Request checkRequest = new Request.Builder().url(BuildConfig.CHECK_OCR_URL + clearCacheStr).get().build();
        Call checkCall = new OkHttpClient().newCall(checkRequest);
        checkCall.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {

            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                OcrServerModel ocrServerModel = new Gson().fromJson(response.body().string(), OcrServerModel.class);

                if (ocrServerModel.getHandwritingOCRServer().equals(getString(R.string.baidu))) {
                    requestCloudOcrToken(ImageUtils.getBitmap(filePath));
                } else {
                    handwritingRecognition(true, filePath);
                }
            }
        });

    }

    public void goHandwriting() {
        //如果没有手写体识别内容就去识别
        String handwritingContent = mDataList.get(viewPager.getCurrentItem()).getOcrContent();
        if (handwritingContent == null) {
            getHandwritingCount();
        } else {
//            mobClickEvent(ImagePreviewActivity.this, BuildConfig.PHASE.getOcrCount());
            createCopyDialog(handwritingContent);
        }
    }

    private void requestCloudOcrToken(Bitmap bitmap) {
        currentTimeMillis = System.currentTimeMillis();
        showProgressDialog(true);
        FormBody formBody = new FormBody.Builder()
                .add("grant_type", CZURConstants.BAIDU_GRANT_TYPE)
                .add("client_id", CZURConstants.BAIDU_CLIENT_ID)
                .add("client_secret", CZURConstants.BAIDU_CLIENT_SECRET)
                .build();
        Request request = new Request.Builder().url(CZURConstants.BAIDU_TOKEN_URL)
                .post(formBody).build();
        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.code() == 200) {
                    BaiduTokenModel json = new Gson().fromJson(response.body().string(), BaiduTokenModel.class);
                    String urlEncode = EncodeUtils.base64Encode2String(ImageUtils.bitmap2Bytes(bitmap, Bitmap.CompressFormat.JPEG, 90));
                    cloudOcr(json.getAccess_token(), urlEncode);
                } else {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            hideProgressDialog();
                            showMessage(R.string.request_failed_alert);
                        }
                    });

                }
            }
        });


    }

    private long currentTimeMillis = 0;

    /**
     * @des: 如果不到100毫秒就休眠到100毫秒
     * @params:
     * @return:
     */
    private long threadSleepTo1000Ms(long algTime) {
        if (algTime > 0 && algTime < 1000) {
            return 1000L - algTime;
        } else {
            return 0;
        }
    }

    private void cloudOcr(String access_token, String image) {
        FormBody formBody = new FormBody.Builder()
                .add("access_token", access_token)
                .add("image", image)
                .build();
        Request request = new Request.Builder().url(CZURConstants.BAIDU_OCR_HAND_URL)
                .post(formBody).build();

        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.code() == 200) {
                    String body = response.body().string();

                    BaiduWordModel baiduWordModel = new Gson().fromJson(body, BaiduWordModel.class);
                    SpanUtils stringSpan = new SpanUtils();
                    List<BaiduWordModel.WordsResultBean> words_result = baiduWordModel.getWords_result();
                    if (words_result != null) {
                        for (BaiduWordModel.WordsResultBean wordsResultBean : words_result) {
                            stringSpan.appendLine(wordsResultBean.getWords());
                        }
                        Looper.prepare();
                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                hideProgressDialog();
                                String string = stringSpan.create().toString();
                                handwritingCharge(1, null, string);
                            }
                        }, threadSleepTo1000Ms(System.currentTimeMillis() - currentTimeMillis));
                        Looper.loop();

                    } else {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                hideProgressDialog();
                                showMessage(R.string.recognition_defeat);
                            }
                        });

                    }


                } else {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            hideProgressDialog();
                            showMessage(R.string.request_failed_alert);
                        }
                    });

                }
            }
        });

    }

    /**
     * @des: 手写识别dialog
     * @params:
     * @return:
     */

    private void confirmDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(this, CloudCommonPopupConstants.OK_ONE_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.writing_prompt));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                firstPreferences.setIsHandwritingGuide(false);
                goHandwriting();
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.setCanceledOnTouchOutside(false);
        commonPopup.setCancelable(false);
        commonPopup.show();
    }

    /**
     * @des: 是否星标
     * @params:
     * @return:
     */

    private void showIsStar() {
        if (isItemStar == 0) {
            cameraPreviewStarImg.setSelected(false);
            cameraPreviewStarTv.setText(R.string.star_page);
            cameraPreviewStarTv.setTextColor(this.getResources().getColor(R.color.white));
        } else {
            cameraPreviewStarImg.setSelected(true);
            cameraPreviewStarTv.setText(R.string.cancel_page);
            cameraPreviewStarTv.setTextColor(this.getResources().getColor(R.color.yellow_star));
        }
    }


    /**
     * @des: 显示删除Dialog
     * @params:
     * @return:
     */

    private void confirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(ImagePreviewActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.confirm_delete));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {

                dialog.dismiss();
                deleteItem();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    /**
     * @des: 删除
     * @params:
     * @return:
     */
    private void deleteItem() {
        if (count == 1) {
            ActivityUtils.finishActivity(ImagePreviewActivity.this);
        }
        PageEntity deletePageEntity = realm.where(PageEntity.class)
                .equalTo("pageId", mDataList.get(viewPager.getCurrentItem()).getPageId())
                .equalTo("isDelete", 0)
                .findFirst();
        //删除本地文件
        final List<String> needDeletePaths = new ArrayList<>();
        needDeletePaths.add(deletePageEntity.getPicUrl());
        needDeletePaths.add(deletePageEntity.getSmallPicUrl());
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                //删除sd卡上book page
                for (String needDeletePath : needDeletePaths) {
                    FileUtils.delete(needDeletePath);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {

            }
        });

        realm.executeTransaction(new Realm.Transaction() {
            @Override
            public void execute(Realm realms) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                String curDate = formatter.format(new Date(System.currentTimeMillis()));

                PageEntity deletePageEntity = realm.where(PageEntity.class)
                        .equalTo("pageId", mDataList.get(viewPager.getCurrentItem()).getPageId())
                        .equalTo("isDelete", 0).findFirst();
                deletePageEntity.setIsDelete(1);
                deletePageEntity.setIsDirty(1);
                deletePageEntity.setUpdateTime(curDate);

                startAutoSync();
            }
        });

        count = mDataList.size();
        if (count <= 0) {
            ActivityUtils.finishActivity(ImagePreviewActivity.this);
            countTv.setText("");
        } else {
            countTv.setText(String.format("%1$d / %2$d", (currentItem + 1), count));
        }

        if (count >= 1) {
            viewPager.setCurrentItem(currentItem, false);
            int checkCurrentItem = 0;
            if (currentItem == 0) {
                checkCurrentItem = 0;
            } else if (currentItem == count) {
                checkCurrentItem = count - 1;
            } else {
                checkCurrentItem = currentItem;
            }

            isItemTag = Validator.isNotEmpty(mDataList.get(checkCurrentItem).getTagName()) ? 1 : 0;
            showIsTagFromDelete(checkCurrentItem);

        }

        viewMap.remove(currentItem);
        mAdapter.notifyDataSetChanged();
    }

    private void showIsTagFromDelete(int checkCurrentItem) {
        if (isItemTag == 0) {
            setNormalTag();
        } else {
            previewAddTagTv.setText(mDataList.get(checkCurrentItem).getTagName());
            previewAddTagTv.setTextColor(getResources().getColor(R.color.white));
            previewAddTagImg.setImageResource(R.mipmap.white_tag_preview_icon);
            addTagRl.setBackground(getResources().getDrawable(R.drawable.btn_rec_5_bg_with_red_fa292f));
        }


    }

    /**
     * @des: 星标或者取消星标页
     * @params:
     * @return:
     */

    private void starPage() {
        isItemStar = 1 - isItemStar;
        PageEntity pageEntity = realm.where(PageEntity.class)
                .equalTo("pageId", mDataList.get(viewPager.getCurrentItem()).getPageId())
                .findFirst();
        realm.beginTransaction();
        pageEntity.setIsStar(isItemStar);

        String curDate = formatter.format(new Date(System.currentTimeMillis()));
        pageEntity.setUpdateTime(curDate);
        pageEntity.setIsDirty(1);
        realm.commitTransaction();
        showIsStar();
    }

    private ViewPager.OnPageChangeListener viewPageListener = new ViewPager.OnPageChangeListener() {
        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

        }

        @Override
        public void onPageSelected(int position) {
            if (viewMap.get(currentItem) != null) {
                viewMap.get(currentItem).reset();
            }
            isItemTag = Validator.isNotEmpty(mDataList.get(position).getTagName()) ? 1 : 0;
            currentPageId = mDataList.get(position).getPageId();
            isItemStar = mDataList.get(position).getIsStar();
            currentItem = position;
            showIsStar();
            showIsTag();
            countTv.setText(String.format("%1$d / %2$d", (currentItem + 1), count));
        }

        @Override
        public void onPageScrollStateChanged(int state) {

        }
    };

    private void showIsTag() {
        if (isItemTag == 0) {
            setNormalTag();
        } else {
            previewAddTagTv.setText(mDataList.get(viewPager.getCurrentItem()).getTagName());
            previewAddTagTv.setTextColor(getResources().getColor(R.color.white));
            previewAddTagImg.setImageResource(R.mipmap.white_tag_preview_icon);
            addTagRl.setBackground(getResources().getDrawable(R.drawable.btn_rec_5_bg_with_red_fa292f));
        }


    }

    private void setNormalTag() {
        previewAddTagTv.setText(getString(R.string.add_tag));
        previewAddTagTv.setTextColor(getResources().getColor(R.color.black_22));
        previewAddTagImg.setImageResource(R.mipmap.red_tag_preview_icon);
        addTagRl.setBackground(getResources().getDrawable(R.drawable.btn_rec_5_bg_with_white));
    }

    public class ImagePagerAdapter extends PagerAdapter {

        @Override
        public int getCount() {
            return mDataList.size();
        }

        @Override
        public void setPrimaryItem(ViewGroup container, int position, Object object) {
            super.setPrimaryItem(container, position, object);
            ZoomImageView zoomImage = viewMap.get(position);
            ((GalleryViewPager) container).setZoomView(zoomImage);
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            ZoomImageView zoomImage = viewMap.get(position);
            if (zoomImage == null) {
                zoomImage = setImageToIndex(position);
            }
            container.addView(zoomImage);
            return zoomImage;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            Log.i("czur", "remove: " + position, null);
            container.removeView((View) object);
            ZoomImageView zoomImage = viewMap.get(position);
            if (zoomImage != null) {
                zoomImage.setImageBitmap(null);
                viewMap.remove(position);
            }
        }


        @Override
        public boolean isViewFromObject(View view, Object object) {
            return view == object;
        }

        @Override
        public int getItemPosition(Object object) {
            return POSITION_NONE;
        }
    }

    private ZoomImageView setImageToIndex(int index) {
        ZoomImageView zoomImage = new ZoomImageView(this);
        String path = mDataList.get(index).getPicUrl();
        zoomImage.setImageBitmap(ImageUtils.getBitmap(path));
        viewMap.put(index, zoomImage);
        return zoomImage;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        realm.close();

    }

    /**
     * @des: 保存至相册
     * @params:
     * @return:
     */

    private void copyToSd() {
//        final String sdPicPath = sdPath + UUID.randomUUID() + CZURConstants.JPG;
        final String sdPicPath = CZURConstants.PICTURE_PATH + UUID.randomUUID() + CZURConstants.JPG;
        final String sourcePicPath = mDataList.get(viewPager.getCurrentItem()).getPicUrl();
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {

                FileUtils.copy(sourcePicPath, sdPicPath, new FileUtils.OnReplaceListener() {
                    @Override
                    public boolean onReplace(File srcFile, File destFile) {
                        return true;
                    }
                });
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        whiteRightPopup.show();
                        noticeAlbumUpdate(sdPicPath);
                    }
                });
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        whiteRightPopup.dismiss();
                    }
                }, 900);
            }

            @Override
            public void onFail(Throwable t) {
                logE(t.toString());
                super.onFail(t);
            }
        });


    }

    private void noticeAlbumUpdate(String sdPicPath) {
        //通知系统相册更新
        File sdPicFile = FileUtils.getFileByPath(sdPicPath);
        Uri contentUri = Uri.fromFile(sdPicFile);
        MediaScannerConnection.scanFile(ImagePreviewActivity.this,
                new String[]{sdPicFile.getAbsolutePath()}, new String[]{"image/jpeg"},
                new MediaScannerConnection.OnScanCompletedListener() {
                    @Override
                    public void onScanCompleted(String path, Uri uri) {
                        logI("file " + path + ", scanned seccessfully: " + uri);
                    }

                });
    }


    /**
     * @des: 保存至相册
     * @params:
     * @return:
     */

    private void copyToSdPrePare() {
//        final String sdPicPath = sdPath + mDataList.get(viewPager.getCurrentItem()).getPageId() + CZURConstants.JPG;
        final String sdPicPath = CZURConstants.PICTURE_PATH + mDataList.get(viewPager.getCurrentItem()).getPageId() + CZURConstants.JPG;
        final String sourcePicPath = mDataList.get(viewPager.getCurrentItem()).getPicUrl();
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                FileUtils.copy(sourcePicPath, sdPicPath, new FileUtils.OnReplaceListener() {
                    @Override
                    public boolean onReplace(File srcFile, File destFile) {
                        return false;
                    }
                });
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        noticeAlbumUpdate(sdPicPath);
                        share(sdPicPath);
                    }
                });
            }
        });


    }


    /**
     * @des: 分享
     * @params:
     * @return:
     */
    private void share(String sdPicPath) {
        new ShareUtils.Builder(ImagePreviewActivity.this)
                .setOnActivityResult(SHARE_SUCCESS_CODE)
                // 指定分享的文件类型
                .setContentType(ShareContentType.IMAGE)
                // 设置要分享的文件 Uri
                .setShareFileUri(FileUtil.getFileUri(ImagePreviewActivity.this, ShareContentType.FILE, new File(sdPicPath)))
                // 设置分享选择器的标题
                .setTitle(getString(R.string.share_to))
                .build()
                // 发起分享
                .shareBySystem();
    }

    /**
     * @des: 申请权限
     * @params:
     * @return:
     */

    private void requestCopyToSdPermission(final boolean isPrepared) {
        PermissionUtils.permission(PermissionUtil.getStoragePermission())
                .rationale(new PermissionUtils.OnRationaleListener() {
                    @Override
                    public void rationale(UtilsTransActivity activity, ShouldRequest shouldRequest) {
                        showMessage(R.string.denied_sdcard);
                        shouldRequest.again(true);
                    }
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(List<String> permissionsGranted) {
                        if (isPrepared) {
                            copyToSdPrePare();
                        } else {
                            copyToSd();
                        }
                    }

                    @Override
                    public void onDenied(List<String> permissionsDeniedForever,
                                         List<String> permissionsDenied) {
                        showMessage(R.string.denied_sdcard);
                    }
                })
                .theme(new PermissionUtils.ThemeCallback() {
                    @Override
                    public void onActivityCreate(Activity activity) {
                        com.blankj.utilcode.util.ScreenUtils.setFullScreen(activity);
                    }
                })
                .request();
    }

    /**
     * @des: 获取手写识别次数
     * @params:[path]
     * @return:void
     */
    private void getHandwritingCount() {

        httpManager.request().getHandwritingCount(
                userPreferences.getUserId(), HandwritingCountModel.class, new MiaoHttpManager.Callback<HandwritingCountModel>() {
                    @Override
                    public void onStart() {
                        showProgressDialog(true);
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<HandwritingCountModel> entity) {
                        String ocrNumStr = entity.getBody().getOcrNum();
                        userPreferences.setHandwritingCount(ocrNumStr);
                        if (!StringUtils.isEmpty(ocrNumStr)) {
                            int ocrNumber = Integer.parseInt(ocrNumStr);
                            if (ocrNumber <= 0) {
                                hideProgressDialog();
                                showDialog();
                            } else {
                                if (userPreferences.getOcrLanguageId() == CZURConstants.LANGUAGE.length - 1 || !BuildConfig.IS_OVERSEAS) {
                                    checkUpdateList(mDataList.get(viewPager.getCurrentItem()).getPicUrl());
                                } else {
                                    handwritingRecognition(false, mDataList.get(viewPager.getCurrentItem()).getPicUrl());
                                }
                            }
                        }

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<HandwritingCountModel> entity) {
                        hideProgressDialog();
                        if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            showMessage(R.string.toast_internal_error);
                        } else {
                            showMessage(R.string.request_failed_alert);
                        }

                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
    }

    /**
     * @des: 手写识别
     * @params:
     * @return:
     */

    /**
     * @des: 手写识别
     * @params:
     * @return:
     */

    public void handwritingRecognition(boolean isCHN, String filePath) {
        if (isCHN) {
            ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
                @Override
                public Void doInBackground() {
                    upload(new File(filePath));
                    return null;
                }

                @Override
                public void onSuccess(Void result) {

                }
            });
        } else {
            HandwritingRecognitionTask.handwritingRecognition(this, filePath, userPreferences.getOcrLanguageId(), new HandwritingRecognitionTask.HandwritingRecognitionCallback() {
                @Override
                public void failedToast() {
                    FailedToast();
                }

                @Override
                public void hideProgressDialog1() {
                    hideProgressDialog();
                }

                @Override
                public void success(String json) {
                    runOnUiThread(() -> {
//                        mobClickEvent(ImagePreviewActivity.this, BuildConfig.PHASE.getOcrCount());
                        handwritingCharge(0, null, json);
                    });
                }
            });
        }
    }

    /**
     * @des: 上传手写体识别image
     * @params:
     * @return:
     */

    private void upload(File file) {
        logI("path:" + file.getAbsolutePath());
        try {
            //这个是ui线程回调，可直接操作UI
            final UIProgressRequestListener uiProgressRequestListener = new UIProgressRequestListener() {
                @Override
                public void onUIRequestProgress(long bytesWrite, long contentLength, boolean done) {
                    Log.i("czurxx", "bytesWrite:" + bytesWrite);
                    Log.i("czurxx", "contentLength" + contentLength);
                    Log.i("czurxx", (100 * bytesWrite) / contentLength + " % done ");
                    Log.i("czurxx", "done:" + done);
                    Log.i("czurxx", "================================");

                }
            };

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("base64", BitmapUtils.imageToBase64(file.getPath()));
            String syncJson = jsonObject.toString();
            RequestBody requestBody = RequestBody.create(JSON, syncJson);

            Request request = new Request.Builder()
                    .header("Content-Type", "application/json")
                    .header("T-ID", userPreferences.getToken())
                    .header("U-ID", userPreferences.getUserId())
                    .url(CZURConstants.HANDWRITING_URL)
                    .post(ProgressHelper.addProgressRequestListener(requestBody, uiProgressRequestListener))
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(request).execute();
            String jsonString = response.body().string();
            HandwritingEntity handwritingEntity = new Gson().fromJson(jsonString, HandwritingEntity.class);
            // 请求成功
            if (response.isSuccessful()) {
                int code = handwritingEntity.getCode();
                // 上传成功
                if (code == 1000) {
                    Gson gson = new Gson();
                    HandwritingEntity.DataBean dataBean = new Gson().fromJson(handwritingEntity.getData(), HandwritingEntity.DataBean.class);
                    handwritingEntity.setDatabean(dataBean);
                    afterUpload(handwritingEntity, jsonString);
                } else if (code == -9011) {
                    FailedRecognizedToast();
                } else {
                    FailedToast();
                }
            }
            // 请求失败
            else {
                FailedToast();
            }
        } catch (IOException | JSONException e) {
            logE(e.toString());
            FailedToast();
        } catch (Exception e) {
            logE(e.toString());
            FailedToast();
        }
    }

    private void afterUpload(final HandwritingEntity handwritingEntity, final String jsonString) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
//                mobClickEvent(ImagePreviewActivity.this, BuildConfig.PHASE.getOcrCount());
                handwritingCharge(2, handwritingEntity, jsonString);
            }
        });
    }

    private void FailedRecognizedToast() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                hideProgressDialog();
                showMessage(R.string.recognize_failed_alert);
            }
        });
    }

    private void FailedToast() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                hideProgressDialog();
                showMessage(R.string.request_failed_alert);
            }
        });
    }

    /**
     * @des: 手写识别扣除
     * @params:[]
     * @return:void
     */
    private void handwritingCharge(int type, final HandwritingEntity handwritingEntity, final String result) {

        httpManager.request().handwritingCharge(
                userPreferences.getUserId(), HandwritingCountModel.class, new MiaoHttpManager.Callback<HandwritingCountModel>() {
                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<HandwritingCountModel> entity) {
                        SpannableStringBuilder resultString = null;
                        hideProgressDialog();
                        if (type == 2) {

                            SpanUtils stringSpan = new SpanUtils();
                            List<HandwritingEntity.TextDetectionsBean> resultItems = handwritingEntity.getDatabean().getTextDetections();
                            for (int i = 0; i < resultItems.size(); i++) {
                                stringSpan.appendLine(resultItems.get(i).getDetectedText());
                            }
                            resultString = stringSpan.create();
                        }

                        SpannableStringBuilder finalResultString = resultString;

                        realm.executeTransaction(new Realm.Transaction() {
                            @Override
                            public void execute(Realm realms) {
                                //先查找后得到BookEntity对象
                                PageEntity pageEntity = realm.where(PageEntity.class).equalTo("pageId", mDataList.get(viewPager.getCurrentItem()).getPageId()).findFirst();
                                pageEntity.setOcrContent(type == 2 ? finalResultString.toString() : result);
                                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                                String curDate = formatter.format(new Date(System.currentTimeMillis()));
                                pageEntity.setUpdateTime(curDate);
                                pageEntity.setIsDirty(1);
                                startAutoSync();
                            }
                        });
                        createCopyDialog(type == 2 ? finalResultString.toString() : result);
                        EventBus.getDefault().post(new HandWritingCountEvent(EventType.HANDWRITING_COUNT_REDUCE));
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<HandwritingCountModel> entity) {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
    }

    /**
     * @des: 识别失败提示框
     * @params:
     * @return:
     */

    private void showDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(this, CloudCommonPopupConstants.COMMON_ONE_BUTTON);
        builder.setTitle(getResources().getString(R.string.recognition_defeat));
        builder.setMessage(getResources().getString(R.string.has_no_recognition_count));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == SELECT_LANGUAGE_CODE) {
            confirmDialog();
        }
    }
}