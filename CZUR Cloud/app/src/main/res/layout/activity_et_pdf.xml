<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">


    <RelativeLayout
        android:id="@+id/bookshelf_inside_top_bar"
        android:layout_width="match_parent"
        android:minHeight="45dp"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/my_pdf_back_btn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginStart="9dp"
            android:padding="10dp"
            android:src="@mipmap/login_back_icon" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/my_pdf_top_select_all_btn"
            android:layout_width="110dp"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:paddingStart="18.5dp"
            android:textColor="@color/black_22"
            android:textSize="16sp"
            android:visibility="gone" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/my_pdf_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/account_title"
            android:textSize="18sp" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/my_pdf_cancel_btn"
            android:layout_width="110dp"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:gravity="center_vertical|end"
            android:layout_centerVertical="true"
            android:paddingEnd="18.5dp"
            android:textColor="@color/black_22"
            android:textSize="16sp"
            android:visibility="gone" />

        <RelativeLayout
            android:id="@+id/my_pdf_multi_select_btn"
            android:layout_width="62dp"
            android:layout_centerVertical="true"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true">

            <ImageView
                android:layout_width="22.5dp"
                android:layout_height="19.5dp"
                android:layout_centerInParent="true"
                android:background="@mipmap/multi_select_icon" />

        </RelativeLayout>


    </RelativeLayout>
    <com.baoyz.widget.PullRefreshLayout
        android:layout_below="@+id/bookshelf_inside_top_bar"
        android:id="@+id/swipeRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gary_f9">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/my_pdf_recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never"
            android:scrollbars="none">

        </androidx.recyclerview.widget.RecyclerView>

    </com.baoyz.widget.PullRefreshLayout>



    <RelativeLayout
        android:id="@+id/pdf_empty_rl"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/bookshelf_inside_top_bar"
        android:background="@color/gary_f9"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="vertical">

            <ImageView
                android:layout_width="120dp"
                android:adjustViewBounds="true"
                android:scaleType="fitXY"
                android:layout_gravity="center_horizontal"
                android:layout_height="wrap_content"
                android:src="@mipmap/book_page_no_page_icon" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="15.5dp"
                android:text="@string/none_file_book_page"
                android:textColor="@color/gary_c4"
                android:textSize="15sp" />
        </LinearLayout>


    </RelativeLayout>

    <LinearLayout
        android:id="@+id/my_pdf_bottom_ll"
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_alignParentBottom="true"
        android:visibility="gone">

        <RelativeLayout
            android:id="@+id/my_pdf_delete_rl"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/black">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true">

                <ImageView
                    android:id="@+id/book_page_delete_img"
                    android:layout_width="22.5dp"
                    android:layout_height="22.5dp"
                    android:layout_centerHorizontal="true"
                    android:background="@mipmap/book_delete" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/book_page_delete_img"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="6.5dp"
                    android:text="@string/delete"
                    android:textColor="@color/white"
                    android:textSize="9sp"
                    android:textStyle="bold" />
            </RelativeLayout>

        </RelativeLayout>


    </LinearLayout>
</RelativeLayout>

