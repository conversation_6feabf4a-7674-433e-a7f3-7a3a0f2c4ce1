<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#222222"
    android:fitsSystemWindows="true"
    android:keepScreenOn="true">

    <FrameLayout
        android:id="@+id/remote_video_view_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@color/black_22"
        app:layout_constraintDimensionRatio="w,4:3" />
    <FrameLayout
        android:id="@+id/local_video_view_container"
        android:layout_width="150dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/remote_video_view_container"
        app:layout_constraintDimensionRatio="w,4:3"
        app:layout_constraintRight_toRightOf="parent" />

    <ImageView
        android:id="@+id/remote_video_back_btn"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="20dp"
        android:src="@mipmap/video_back_icon"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="invisible"/>

    <ImageView
        android:id="@+id/change_camera_bg"
        android:layout_width="90dp"
        android:layout_height="25dp"
        android:layout_marginRight="20dp"
        android:background="@drawable/btn_rec_50_bg_with_code_white"
        app:layout_constraintBottom_toBottomOf="@+id/remote_video_back_btn"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/remote_video_back_btn" />

    <ImageView
        android:id="@+id/change_camera_icon"
        android:layout_width="14dp"
        android:layout_height="12dp"
        android:layout_marginLeft="10dp"
        android:src="@mipmap/change_camera_icon"
        app:layout_constraintBottom_toBottomOf="@+id/change_camera_bg"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintLeft_toLeftOf="@+id/change_camera_bg"
        app:layout_constraintRight_toRightOf="@+id/change_camera_bg"
        app:layout_constraintTop_toTopOf="@+id/change_camera_bg" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/change_camera_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/scan_camera"
        android:textColor="@color/black_22"
        android:textSize="10sp"
        app:layout_constraintBottom_toBottomOf="@+id/change_camera_bg"
        app:layout_constraintHorizontal_bias="0.3"
        app:layout_constraintLeft_toRightOf="@+id/change_camera_icon"
        app:layout_constraintRight_toRightOf="@+id/change_camera_bg"
        app:layout_constraintTop_toTopOf="@+id/change_camera_bg" />

    <RelativeLayout
        android:visibility="gone"
        android:id="@+id/video_loading_rl"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@color/black_22"
        app:layout_constraintDimensionRatio="w,4:3">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/loading_img"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center_horizontal"
                android:src="@mipmap/video_loading" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="25dp"
                android:text="@string/calling"
                android:textColor="@color/white"
                android:textSize="14sp" />


        </LinearLayout>


    </RelativeLayout>
    <ImageView
        android:id="@+id/dialog_out_btn"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:src="@mipmap/dialog_out"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/remote_video_view_container" />

    <View
        android:id="@+id/call_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:drawingCacheQuality="low"
        android:layerType="software"
        android:background="@drawable/call_bg" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/call_text_1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        android:text="@string/aura_home"
        android:textColor="@color/white"
        android:textSize="24sp"
        app:layout_constraintBottom_toTopOf="@+id/call_text_2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/call_text_2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/aura_home_request_video"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintBottom_toTopOf="@+id/call_out_btn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/call_text_1" />


    <ImageView
        android:id="@+id/call_out_btn"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginRight="100dp"
        android:layout_marginBottom="70dp"
        android:src="@mipmap/call_out_icon"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/call_in_btn" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:text="@string/call_out"
        android:textColor="@color/white"
        android:textSize="15sp"
        app:layout_constraintLeft_toLeftOf="@+id/call_out_btn"
        app:layout_constraintRight_toRightOf="@+id/call_out_btn"
        app:layout_constraintTop_toBottomOf="@+id/call_out_btn" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:text="@string/call_in"
        android:textColor="@color/white"
        android:textSize="15sp"
        app:layout_constraintLeft_toLeftOf="@+id/call_in_btn"
        app:layout_constraintRight_toRightOf="@+id/call_in_btn"
        app:layout_constraintTop_toBottomOf="@+id/call_in_btn" />

    <ImageView
        android:id="@+id/call_in_btn"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:src="@mipmap/call_in_icon"
        app:layout_constraintBottom_toBottomOf="@+id/call_out_btn"
        app:layout_constraintLeft_toRightOf="@+id/call_out_btn"
        app:layout_constraintRight_toRightOf="parent" />





    <androidx.constraintlayout.widget.Group
        android:id="@+id/call_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="call_out_btn,call_in_btn,call_bg,call_text_1,call_text_2" />

    <ImageView
        android:id="@+id/call_transfer_btn"
        android:layout_width="125dp"
        android:layout_height="40dp"
        android:layout_marginBottom="70dp"
        android:src="@mipmap/call_trans_bg"
        app:layout_constraintBottom_toTopOf="@+id/call_in_btn"
        app:layout_constraintRight_toRightOf="parent" />

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/call_transfer_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="5dp"
        android:layout_marginBottom="5dp"
        android:maxWidth="85dp"
        android:maxLines="2"
        android:text="@string/transfer_family"
        android:textColor="@color/white"
        android:textSize="15sp"
        app:layout_constraintBottom_toBottomOf="@+id/call_transfer_btn"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/call_transfer_btn" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/call_transfer_group"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="call_transfer_btn,call_transfer_tv" />

</androidx.constraintlayout.widget.ConstraintLayout>
