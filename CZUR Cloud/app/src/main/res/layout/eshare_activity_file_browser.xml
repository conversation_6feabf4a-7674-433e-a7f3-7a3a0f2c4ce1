<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/logo_background"
    android:fitsSystemWindows="true"
    android:orientation="vertical">


    <include
        android:id="@+id/eshare_layout_top_bar_rl"
        layout="@layout/eshare_layout_top_bar" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/local_file_cl"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="20dp">

        <ImageView
            android:id="@+id/icon_iv"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:src="@mipmap/ic_local_files"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/file_name_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="35dp"
            android:text="@string/local_files"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/icon_iv"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="8dp"
            android:layout_height="14dp"
            android:rotation="180"
            android:src="@mipmap/eshare_top_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/download_file_cl"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="20dp">

        <ImageView
            android:id="@+id/download_file_iv"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:src="@mipmap/ic_download_files"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/download_file_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="35dp"
            android:text="@string/download_files"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/download_file_iv"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="8dp"
            android:layout_height="14dp"
            android:rotation="180"
            android:src="@mipmap/eshare_top_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/czur_wma_cl"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="20dp">

        <ImageView
            android:id="@+id/czur_wma_iv"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:background="@mipmap/ic_czur_wma"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/czur_wma_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="35dp"
            android:text="@string/eshare_title"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/czur_wma_iv"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="8dp"
            android:layout_height="14dp"
            android:rotation="180"
            android:src="@mipmap/eshare_top_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

<View
    android:layout_marginBottom="5dp"
    android:layout_marginTop="5dp"
    android:background="@color/white"
    android:layout_width="match_parent"
    android:layout_height="0.5dp"
    android:layout_marginStart="20dp"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/meeting_record_cl"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="20dp">

        <ImageView
            android:id="@+id/meeting_record_iv"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:src="@mipmap/ic_metting_record_files"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/meeting_record_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="35dp"
            android:text="@string/meeting_record"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/meeting_record_iv"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="8dp"
            android:layout_height="14dp"
            android:rotation="180"
            android:src="@mipmap/eshare_top_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/photo_cl"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:layout_marginStart="40dp"
        android:layout_marginEnd="20dp">

        <ImageView
            android:id="@+id/photo_iv"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:src="@mipmap/ic_photo_files"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/photo_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="35dp"
            android:text="@string/photo"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/photo_iv"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="8dp"
            android:layout_height="14dp"
            android:rotation="180"
            android:src="@mipmap/eshare_top_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</LinearLayout>