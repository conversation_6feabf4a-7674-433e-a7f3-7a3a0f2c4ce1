package com.czur.cloud.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.czur.cloud.R;
import com.czur.cloud.adapter.AuraMatePopupAdapter;
import com.czur.cloud.model.AuraMateShareUserModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class AuraMateButtonPopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;

    public AuraMateButtonPopup(Context context) {
        super(context);
    }

    public AuraMateButtonPopup(Context context, int theme) {
        super(context, theme);
    }


    public static class Builder {
        private Context context;

        private String title;
        private View contentsView;

        private DialogInterface.OnClickListener positiveListener;
        private OnClickListener onNegativeListener;
        private OnDismissListener onDismissListener;

        private ImageView img;
        private TextView confirmBtn;
        private RecyclerView auraHomeRecyclerView;
        private AuraMatePopupAdapter adapter;
        private List<AuraMateShareUserModel> datas;

        public Builder(Context context) {
            this.context = context;

        }

        public Builder(Context context, List<AuraMateShareUserModel> datas) {
            datas = new ArrayList<>();
            this.context = context;
            this.datas = datas;
        }

        public Builder setDatas(List<AuraMateShareUserModel> datas) {
            this.datas = new ArrayList<>();
            this.datas = datas;
            return this;
        }

        //        public Builder setOnClickListener(OnItemClickListener onItemClickListener) {
//            this.onItemClickListener = onItemClickListener;
//            return this;
//        }
        public Builder setOnPositiveListener(DialogInterface.OnClickListener positiveListener) {
            this.positiveListener = positiveListener;
            return this;
        }

        public Builder setTitle(String title) {
            this.title = title;
            return this;
        }

        public void refreshDatas(List<AuraMateShareUserModel> datas) {
            this.datas = datas;
            adapter.notifyDataSetChanged();
        }

        public AuraMateButtonPopup create() {
            LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            AuraMateButtonPopup dialog = new AuraMateButtonPopup(context, R.style.TransparentProgressDialog);
            View layout = commonCustomPopLayout(inflater, dialog);

            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;

            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final AuraMateButtonPopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
            dialog.setCancelable(false);
            dialog.setCanceledOnTouchOutside(false);
            View layout = inflater.inflate(R.layout.aura_home_share_user_dialog, null, false);
            ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
            dialog.addContentView(layout, params);


            confirmBtn = (TextView) layout.findViewById(R.id.confirm_btn);

            img = (ImageView) layout.findViewById(R.id.img);

            confirmBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    positiveListener.onClick(dialog,R.string.confirm1);
                }
            });
            confirmBtn.setEnabled(false);
            confirmBtn.setClickable(false);
            img.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dialog.dismiss();
                }
            });

            return layout;
        }


    }

}
