package com.czur.cloud.ui.auramate.reportfragment;

import static com.blankj.utilcode.util.PermissionUtils.isGranted;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;

import androidx.core.app.ActivityCompat;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.model.AuraMateReportModelSub;
import com.czur.cloud.ui.mirror.model.SittingReportModelSub;
import com.czur.cloud.util.PermissionUtil;
import com.czur.cloud.util.validator.Validator;

import org.jetbrains.annotations.NotNull;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class ReportUtil {
    private static final String TAG = "ReportUtil";

    private  static int REQUEST_EXTERNAL_STORAGE = 1;



    /**
         * 比较两个日期的大小，日期格式为yyyy-MM-dd
         *
         * @param str1 the first date
         * @param str2 the second date
         * @return true <br/>false
         */
    public static boolean isDateOneBigger(String str1, String str2) {
        boolean isBigger = false;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd");
        Date dt1 = null;
        Date dt2 = null;
        try {
            dt1 = sdf.parse(str1);
            dt2 = sdf.parse(str2);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (dt1.getTime() > dt2.getTime()) {
            isBigger = true;
        }
        return isBigger;
    }

    /**
     * 返回当前日期
     */
    public static String getNowDay(String formater){
        String retStr = "";
        String oldDate = "";

        try {
            SimpleDateFormat sdf = new SimpleDateFormat(formater);
            oldDate = sdf.format(new Date());
            Date dt = sdf.parse(oldDate);
            retStr = sdf.format(dt);

        }catch (Exception e){
            e.printStackTrace();
        }

        return retStr;
    }

    /**
     * 返回给定日期的前n天日
     */
    public static String getBeforeDay(String oldDate, int n, String formater){
        String retStr = "";

        try {
            SimpleDateFormat sdf = new SimpleDateFormat(formater);
            if (oldDate.length()<2) {
                oldDate = sdf.format(new Date());
            }
            Date dt = sdf.parse(oldDate);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            rightNow.add(Calendar.DAY_OF_YEAR, -n);//日期-7天
            Date dt1 = rightNow.getTime();

            retStr = sdf.format(dt1);

        }catch (Exception e){
            e.printStackTrace();
        }

        return retStr;
    }

    public static String getFormateReportTitleEn(String sDateTime,String type) {
        //"title": "2020-12-16日报",  ==> 日报(2020.12.16)
        //"title": "2020/12/14-2020/12/20周报",==>周报(2020.12.14-12.20)
        //"title": "2020.12月报",     ==>月报(2020.12)
        String formater="yyyy.MM.dd";
        String sTtitle = "";

        String sDateNew = "";
        if (type.equals("3")){
            sTtitle = "Monthly";
            sDateNew = sDateTime.substring(0,7);
        }else if (type.equals("2")){
            sTtitle = "Weekly";
            String s3 = sDateTime.substring(0,21);
            int a = s3.indexOf("-");
            String s31 = s3.substring(0,a);
            s31 = ReportUtil.foramtDateTime(s31, formater);
            String s32 = s3.substring(a+1);
            s32 = ReportUtil.foramtDateTime(s32, formater);
            sDateNew = s31 + "-" + s32.substring(5);
        }else{
            sTtitle = "Daily";
            String sDateOld = sDateTime.substring(0,10);
            sDateNew = ReportUtil.foramtDateTime(sDateOld, formater);
        }

        String txtTitle = sTtitle + "(" + sDateNew + ")";

        return txtTitle;
    }

    public static String getFormateReportTitle(String sDateTime,String type) {
        return getFormateReportTitle(sDateTime, type, 0);
    }
        // iType = 0 日报；iType=1 日报告
    public static String getFormateReportTitle(String sDateTime,String type, int iType){
        //"title": "2020-12-16日报",  ==> 日报(2020.12.16)
        //"title": "2020/12/14-2020/12/20周报",==>周报(2020.12.14-12.20)
        //"title": "2020.12月报",     ==>月报(2020.12)
        String formater="yyyy.MM.dd";
        String sTtitle = sDateTime.substring(sDateTime.length()-2);

        String sDateOld = sDateTime.substring(0,sDateTime.length()-2);
        String sDateNew = "";
        if (type.equals("3")){
            sDateNew = sDateOld;
        }else if (type.equals("2")){
            String s3 = sDateOld.substring(0,sDateTime.length()-2);
            int a = s3.indexOf("-");
            String s31 = s3.substring(0,a);
            s31 = ReportUtil.foramtDateTime(s31, formater);
            String s32 = s3.substring(a+1);
            s32 = ReportUtil.foramtDateTime(s32, formater);
            sDateNew = s31 + "-" + s32.substring(5);
        }else{
            sDateNew = ReportUtil.foramtDateTime(sDateOld, formater);
        }

        String txtTitle = sTtitle + "(" + sDateNew + ")";
        if (iType == 1){
            txtTitle = sTtitle + "告(" + sDateNew + ")";
        }

        return txtTitle;
    }

    public  static void verifyStoragePermissions(Activity activity) {
        // Check if we have write permission
        String[] permissions = PermissionUtil.getStoragePermission();

        boolean flag = isGranted(permissions);
        if (!flag) {
            // We don't have permission so prompt the user
            ActivityCompat.requestPermissions(activity, PermissionUtil.getStoragePermission(),
                    REQUEST_EXTERNAL_STORAGE);
        }
    }

    /**
     * 申请权限
     */
    public static void requestCopyToSdPermission() {
        PermissionUtils.permission(PermissionUtil.getStoragePermission())
                .rationale((activity, shouldRequest) -> {
                    ToastUtils.showShort(R.string.denied_sdcard);
                    shouldRequest.again(true);
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NotNull List<String> permissionsGranted) {
                    }

                    @Override
                    public void onDenied(@NotNull List<String> permissionsDeniedForever,
                                         @NotNull List<String> permissionsDenied) {
                        ToastUtils.showShort(R.string.denied_sdcard);
                    }
                })
                .theme(ScreenUtils::setFullScreen)
                .request();
    }

    //打印列表数据的首位数据；
    public  static void printDatasLog(List<AuraMateReportModelSub> modelSub){
        if (Validator.isEmpty(modelSub)){
            return;
        }
        int count = modelSub.size();
        logI("=====printDatasLog",
                "count="+count,
                "No.0",
                "id="+modelSub.get(0).getId(),
                "FromEnd="+modelSub.get(0).getFromEnd(),
                "getTitle="+modelSub.get(0).getTitle(),
                "No."+(count-1),
                "id="+modelSub.get(count-1).getId(),
                "FromEnd="+modelSub.get(count-1).getFromEnd(),
                "getTitle="+modelSub.get(count-1).getTitle());
    }

    //打印列表数据的首位数据；
    public  static void printSittingDatasLog(List<SittingReportModelSub> modelSub){
        if (Validator.isEmpty(modelSub)){
            return;
        }
        int count = modelSub.size();
        logI("=====printDatasLog",
                "count="+count,
                "No.0",
                "id="+modelSub.get(0).getId(),
                "FromEnd="+modelSub.get(0).getFromEnd(),
                "getTitle="+modelSub.get(0).getTitle(),
                "No."+(count-1),
                "id="+modelSub.get(count-1).getId(),
                "FromEnd="+modelSub.get(count-1).getFromEnd(),
                "getTitle="+modelSub.get(count-1).getTitle());
    }

    //给定起止日期，连续生成有效的日期数据；根据类型区分；
    //日：11.26 ==> [11.20,11.21,11.22,11.23,11.24,11.25,11.26]
    //周：11.16-11.22 ==> [10.5-10.11,10.12-10.18,11.19-10.25,10.26-11.1,11.2-11.8,11.9-11.15,11.16-11.22]
    //月：2020.11 ==> [2020.05,2020.06,2020.07,2020.08,2020.09,2020.10,2020.11]
    public static ArrayList<String> insertDateList(String startDate, String endDate, int type) {
        final int countSize = 30;
        ArrayList<String> retList = new ArrayList<>();

        retList.add(startDate);
        //日
        if (type == 1){
            for (int i=1;i<countSize;i++){
                String ret = getBeforeDay(startDate, -i);
                if (ret.equals(endDate))
                    break;

                retList.add(ret);
            }
        }

        //周
        if (type == 2){
            for (int i=1;i<countSize;i++){
                String ret = getBeforeWeek(startDate, -i);
                if (ret.equals(endDate))
                    break;

                retList.add(ret);
            }
        }

        //月
        if (type == 3){
            for (int i=1;i<countSize;i++){
                String ret = getBeforeMonth(startDate, -i);
                if (ret.equals(endDate))
                    break;

                retList.add(ret);
            }
        }
//        retList.add(endDate);

        return retList;
    }

    public static String getBeforeDate(String oldDate, int n, int type) {
        String retString = "";
        //日
        if (type == 1){
            retString = getBeforeDay(oldDate, n);
        }

        //周
        if (type == 2){
            retString = getBeforeWeek(oldDate, n);
        }

        //月
        if (type == 3){
            retString = getBeforeMonth(oldDate, n);
        }

        return retString;
    }

    /**
     * 获取本周的第一天--最后一天
     * @return String
     * **/
    public static String getWeekStartEnd(){
        Calendar cal=Calendar.getInstance();
        cal.add(Calendar.WEEK_OF_MONTH, 0);
        cal.set(Calendar.DAY_OF_WEEK, 2);
        Date time=cal.getTime();
        String start = new SimpleDateFormat("MM.dd").format(time);
        cal.set(Calendar.DAY_OF_WEEK, cal.getActualMaximum(Calendar.DAY_OF_WEEK));
        cal.add(Calendar.DAY_OF_WEEK, 1);
        Date time2=cal.getTime();
        String end = new SimpleDateFormat("MM.dd").format(time2);

        return start + "-" + end;
    }

    /**
     * 返回给定月份的前n月
     * 2020.11 == 前3月 ==》 2020.8
     */
    public static String getBeforeMonth(String oldDate, int n){
        String retStr = "";

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM");
            Date dt = sdf.parse(oldDate);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            rightNow.add(Calendar.MONTH, -n);//-n月
            Date dt1 = rightNow.getTime();

            retStr = sdf.format(dt1);

        }catch (Exception e){
            e.printStackTrace();
        }
        return retStr;
    }

    /**
     * 返回给定星期的前n周
     * 11.16-11.22 == 前1周 ==》 11.9-11.15
     */
    public static String getBeforeWeek(String oldDate, int n){
        String retStr = "";
        String[] strArray = oldDate.split("-");

        if (strArray.length < 1){
            return "";
        }

        String strMon = strArray[0];
        String strSun = strArray[1];

        String strFrom = getBeforeDay(strMon,n*7);
        String strEnd = getBeforeDay(strSun,n*7);

        retStr = strFrom+"-"+strEnd;

        return retStr;
    }

    /**
     * 返回给定日期的前n天日
     */
    public static String getBeforeDay(String oldDate, int n){
        String retStr = "";

        try {
//            SimpleDateFormat sdf = new SimpleDateFormat("yyyy.M.d");
            SimpleDateFormat sdf = new SimpleDateFormat("MM.dd");
            Date dt = sdf.parse(oldDate);
            Calendar rightNow = Calendar.getInstance();
            rightNow.setTime(dt);
            //rightNow.add(Calendar.YEAR, -1);//日期减1年
            //rightNow.add(Calendar.MONTH, 3);//日期加3个月
            rightNow.add(Calendar.DAY_OF_YEAR, -n);//日期-7天
            Date dt1 = rightNow.getTime();

            retStr = sdf.format(dt1);

        }catch (Exception e){
            e.printStackTrace();
        }

        return retStr;
    }

        // 格式化：日期时间分隔符：.
//    "fromEnd": "2020-11-21",==>11.23
//    "fromEnd": "2020/11/16-2020/11/22",==>11.16-11.22
//    "fromEnd": "2020.11", ==>2020.11
    public static String foramtDateTime1(String oldDate){
        String retData = "";
        if (oldDate.length()<6){
            SimpleDateFormat sdf = new SimpleDateFormat("MM.dd");
            retData = sdf.format(new Date());
        }else if (oldDate.length() > 10){
            String s1 = oldDate.replace("/",".");
            int a = s1.indexOf("-");
            // 2020.11.16-2020.11.22 ==> 2020.11.16-11.22
            String s2 = s1.substring(5,a+1);
            String s3 = s1.substring(a+6);
            retData = s2 + s3;
        }else if (oldDate.length() > 7 ){
            retData = oldDate.replace("-",".").substring(5);
        }else{
            retData = oldDate.replace("-",".");
        }

        return retData;
    }

    //按照 日周月分别格式化日期字符串
    //日：12-01 ==> 12.1
    //    2020-12-08 ==》 12.8
    //周：2020/11/23-2020/11/29 ==> 11.23-11.29
    //月：2020-12 ==》 2020.12
    public static String foramtDateTime(String oldDate, int type){
        String retData = "";
//        System.out.println("foramtDateTime.oldDate===" + oldDate);

        // 12-01 ==> 12.1
        if (type == 1){
            retData = foramtDayDateTime(oldDate);
        }
        // 2020/11/23-2020/11/29 ==> 11.23-11.29
        else if (type == 2){
            String[] strArray = oldDate.split("-");

            if (strArray.length < 1){
                return "";
            }
            String strMon = strArray[0];
            String strSun = strArray[1];

            String strFrom = foramtDayDateTime(strMon);
            String strEnd = foramtDayDateTime(strSun);

            retData = strFrom+"-"+strEnd;
        }
        // 2020-12 ==> 2020.12
        else if (type == 3){
            retData = foramtMonthDateTime(oldDate);
        }
        else{
            retData = "";
        }

        return retData;
    }

    // 仅针对 周 的日期格式进行折行显示
    public static String foramtDateTimeSplit(String oldDate){
        String retData = "";

        // 2020/11/23-2020/11/29 ==> 11.23\r\n-11.29
        String[] strArray = oldDate.split("-");

        if (strArray.length < 1){
            return "";
        }
        String strMon = strArray[0];
        String strSun = strArray[1];

        String strFrom = foramtDayDateTime(strMon);
        String strEnd = foramtDayDateTime(strSun);

        retData = strFrom+"\r\n-"+strEnd;

        return retData;
    }


    // 格式化日字符串为：MM.dd
    public static String foramtDayDateTime(String oldDate){
        String retData = "";
        oldDate = oldDate.replace("/",".").replace("-",".");
        if (oldDate.length() > 5)
            oldDate = oldDate.substring(5);

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("MM.dd");
            Date dt = sdf.parse(oldDate);
            retData = sdf.format(dt);
        }catch (Exception e){
            e.printStackTrace();
        }
        return retData;
    }

    //格式化月字符串为：yyyy.M
    public static String foramtMonthDateTime(String oldDate){
        String retData = "";
        oldDate = oldDate.replace("/",".").replace("-",".");
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM");
            Date dt = sdf.parse(oldDate);
            retData = sdf.format(dt);
        }catch (Exception e){
            e.printStackTrace();
        }
        return retData;
    }

    // formater: "yyyy.MM.dd"
    public static String foramtDateTime(String oldDate, String formater){
        String retData = "";
        oldDate = oldDate.replace("/",".").replace("-",".");
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(formater);
            Date dt = sdf.parse(oldDate);
            retData = sdf.format(dt);
        }catch (Exception e){
            e.printStackTrace();
        }
        return retData;
    }

    public static String getTodayFormatDate(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd");
        String retData = sdf.format(new Date());
        return retData;
    }

    public static String foramtCurrentDateTime(String type){
        String retData = "";
        if (type.equals("1")){
            SimpleDateFormat sdf = new SimpleDateFormat("MM.dd");
            retData = sdf.format(new Date());
        }

        if (type.equals("2")){
            retData = getWeekStartEnd();
        }

        if (type.equals("3") ){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM");
            retData = sdf.format(new Date());
        }

        return retData;
    }


    // 格式化：分钟时间--小时分钟
    public static String getDayUsingAllTime(int t){
        String h_name = "小时";
        String h_names = "小时";
        String m_name = "分钟";
        if (BuildConfig.IS_OVERSEAS){
            h_name = " Hour ";
            h_names = " Hours ";
            m_name = " Mins";
        }
        String retName = "";
        int hours = t / 60; //since both are ints, you get an int
        int minutes = t % 60;
        String sHour="";
        if ( hours > 0) {
            if (hours>1){
                h_name = h_names;
            }
            sHour = hours + h_name;

        }
        retName =  sHour + minutes + m_name;
        return retName;
    }

    //获取 本日坐姿评级
    //a. 5个等级:极好，优良，中等，较差，极差，
    //b. 按照正确率的百分比，需要在后台可以任意修改（可以加入严重错误的百分比），
    // 90%以及上，75-90%（不包含90%），60%-75%（不包含75%），40%-60%（不包含60%），40%（不包含40%）以下
    public static String getRightName(int right){
        String retName = "一般";
        if (right >= 90) {
            retName = "极好";
        } else if(right >= 75) {
            retName = "优良";
        } else if(right >= 60) {
            retName = "中等";
        } else if(right >= 40) {
            retName = "较差";
        } else {
            retName = "极差";
        }

        return retName;
    }

    public static String addCommas(float num) {
        NumberFormat formatter = new DecimalFormat("###,###");
        return formatter.format(num);
    }

    /**
     * 判断是否安装了微博
     * @param context
     * @return
     */
    public static boolean isWeiboInstalled(Context context) {
        final PackageManager packageManager = context.getPackageManager();
        List<PackageInfo> pinfo = packageManager.getInstalledPackages(0);
        if (pinfo != null) {
            for (int i = 0; i < pinfo.size(); i++) {
                String pn = pinfo.get(i).packageName.toLowerCase(Locale.ENGLISH);
                if (pn.equals("com.sina.weibo")) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断是否安装了微信
     * @param context
     * @return
     */
    public static boolean isWeixinInstalled(Context context) {
        final PackageManager packageManager = context.getPackageManager();
        // 获取所有已安装程序的包信息
        List<PackageInfo> pinfo = packageManager.getInstalledPackages(0);
        if (pinfo != null) {
            for (int i = 0; i < pinfo.size(); i++) {
                String pn = pinfo.get(i).packageName.toLowerCase(Locale.ENGLISH);
                if (pn.equals("com.tencent.mm")) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断是否安装了QQ
     * @param context
     * @return
     */
    public static boolean isQQClientInstalled(Context context) {
        final PackageManager packageManager = context.getPackageManager();
        List<PackageInfo> pinfo = packageManager.getInstalledPackages(0);
        if (pinfo != null) {
            for (int i = 0; i < pinfo.size(); i++) {
                String pn = pinfo.get(i).packageName.toLowerCase(Locale.ENGLISH);
                if (pn.equals("com.tencent.mobileqq")) {
                    return true;
                }
            }
        }
        return false;
    }
}
