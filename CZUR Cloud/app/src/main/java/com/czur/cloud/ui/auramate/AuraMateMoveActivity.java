package com.czur.cloud.ui.auramate;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;

import android.content.DialogInterface;
import android.os.Bundle;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.Group;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.AuraMoveAdapter;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.MoveSuccessEvent;
import com.czur.cloud.model.AuraMateFilesModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class AuraMateMoveActivity extends AuramateBaseActivity implements View.OnClickListener {
    private ImageView etMoveBackBtn;
    private TextView etMoveCreateBtn;
    private RecyclerView etMoveRecyclerView;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private Group etEmptyGroup;
    private AuraMoveAdapter moveAdapter;
    private List<AuraMateFilesModel> foldersBeans;
    private boolean isRoot;
    private String dirId;
    private String files;
    private String folderId;
    private EditText dialogEdt;
    private String ownerId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_et_move);
        initComponent();
        registerEvent();
        initRecyclerView();
        getFolderListView();
    }

    @Override
    public boolean PCNeedFinish() {
        return false;
    }


    private void initComponent() {
        isRoot = getIntent().getBooleanExtra("isRoot", false);
        files = getIntent().getStringExtra("files");
        folderId = getIntent().getStringExtra("folderId");
        ownerId = getIntent().getStringExtra("ownerId");
        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        etMoveBackBtn = (ImageView) findViewById(R.id.et_move_back_btn);
        etMoveCreateBtn = (TextView) findViewById(R.id.et_move_create_btn);
        etMoveRecyclerView = (RecyclerView) findViewById(R.id.et_move_recyclerView);
        etEmptyGroup = (Group) findViewById(R.id.et_empty_group);
        etEmptyGroup.setVisibility(View.GONE);

    }

    private void registerEvent() {
        etMoveBackBtn.setOnClickListener(this);
        etMoveCreateBtn.setOnClickListener(this);
        setNetListener();
    }

    private void initRecyclerView() {
        foldersBeans = new ArrayList<>();
        moveAdapter = new AuraMoveAdapter(this, foldersBeans);
        moveAdapter.setOnItemClickListener(onItemClickListener);
        etMoveRecyclerView.setHasFixedSize(true);
        etMoveRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        etMoveRecyclerView.setAdapter(moveAdapter);
    }

    private AuraMoveAdapter.onItemClickListener onItemClickListener = new AuraMoveAdapter.onItemClickListener() {


        @Override
        public void onItemClick(int position, AuraMateFilesModel foldersBean) {
            if (position == 0) {
                if (isRoot) {
                    dirId = foldersBean.getId();
                } else {
                    dirId = "";
                }

            } else {
                dirId = foldersBean.getId();
            }
            moveFile();
        }
    };

    private void getFolderListView() {
        httpManager.request().getAuraMateAllDir(userPreferences.getUserId(), ownerId, equipmentId,  new TypeToken<List<AuraMateFilesModel>>() {
        }.getType(), new MiaoHttpManager.CallbackNetwork<AuraMateFilesModel>() {
            @Override
            public void onNoNetwork() {
                showEmpty();
            }

            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<AuraMateFilesModel> entity) {
                hideProgressDialog();
                hideEmpty();
                foldersBeans = entity.getBodyList();
                if (foldersBeans.size() == 0) {
                    showEmpty();
                    return;
                } else {
                    if (!isRoot) {
                        for (int i = 0; i < foldersBeans.size(); i++) {
                            if (foldersBeans.get(i).getId().equals(folderId)) {
                                foldersBeans.remove(foldersBeans.get(i));
                            }
                        }
                        AuraMateFilesModel foldersBean = new AuraMateFilesModel();
                        foldersBean.setName(getString(R.string.root_folder));
                        foldersBean.setId("root");
                        foldersBeans.add(0, foldersBean);

                    }
                }
                moveAdapter.refreshData(foldersBeans);
            }

            @Override
            public void onFailure(MiaoHttpEntity<AuraMateFilesModel> entity) {
                hideProgressDialog();
                showEmpty();
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showEmpty();
                logE(e.toString());
            }
        });

    }

    private void moveFile() {
        HttpManager.getInstance().request().moveAuraFolder(userPreferences.getUserId(), files, dirId,ownerId, String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                EventBus.getDefault().post(new MoveSuccessEvent(EventType.AURA_MOVE_SUCCESS));
                finish();
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                showMessage(R.string.move_failed);
                finish();
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showMessage(R.string.move_failed);
                finish();
            }
        });
    }

    private void showEmpty() {
        etEmptyGroup.setVisibility(View.VISIBLE);
        etMoveRecyclerView.setVisibility(View.GONE);
    }

    private void hideEmpty() {
        etEmptyGroup.setVisibility(View.GONE);
        etMoveRecyclerView.setVisibility(View.VISIBLE);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.et_move_back_btn:
                finish();
                break;
            case R.id.et_move_create_btn:
                createFolderDialog();
                break;
            default:
                break;
        }
    }

    /**
     * @des: 创建文件夹
     * @params:
     * @return:
     */

    private void createFolderDialog() {
        final CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMateMoveActivity.this, CloudCommonPopupConstants.EDT_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.input_folder_name));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (Validator.isNotEmpty(dialogEdt.getText().toString())) {
                    //不能含有表情
                    if (EtUtils.containsEmoji(dialogEdt.getText().toString())) {
                        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMateMoveActivity.this, CloudCommonPopupConstants.COMMON_ONE_BUTTON);
                        builder.setTitle(getResources().getString(R.string.prompt));
                        builder.setMessage(getResources().getString(R.string.nickname_toast_symbol));
                        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                dialog.dismiss();
                            }
                        });
                        CloudCommonPopup commonPopup = builder.create();
                        commonPopup.show();
                    } else {
                        createFolder();
                        dialog.dismiss();
                    }


                } else {
                    showMessage(R.string.tip_file_rename_length_toast);
                }
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        dialogEdt = (EditText) commonPopup.getWindow().findViewById(R.id.edt);
        commonPopup.show();
    }

    /**
     * @des: 创建文件夹
     * @params:
     * @return:
     */
    private void createFolder() {

        httpManager.request().createAuraFolder(userPreferences.getUserId(), equipmentId, dialogEdt.getText().toString(), ownerId, String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {

                hideProgressDialog();
                getFolderListView();
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                if (entity.getCode()==MiaoHttpManager.STATUS_NAME_IS_SAMED){
                    showMessage(R.string.had_same_name_folder);
                }
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
            }
        });
    }


}
