package com.czur.cloud.util

import android.Manifest
import android.content.Context
import android.content.DialogInterface
import android.os.Build
import android.view.Gravity
import android.view.View
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.PermissionUtils.FullCallback
import com.blankj.utilcode.util.PermissionUtils.permission
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup
import com.czur.cloud.ui.starry.utils.RomUtils.PermissionPageManagement.goToSetting
import com.czur.czurutils.log.logTagD

/**
 * 定义回调接口
 */
interface PermissionCallBack {
    fun execute()
}

object PermissionUtil {

    @JvmStatic
    fun getPhonePermission(): Array<String?> {
        val requestPermissions: Array<String?>
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestPermissions = arrayOfNulls(1)
            requestPermissions[0] = Manifest.permission.READ_PHONE_STATE
        } else {
            requestPermissions = arrayOfNulls(1)
            requestPermissions[0] = Manifest.permission.READ_PHONE_STATE
        }
        return requestPermissions
    }
    @JvmStatic
    fun getStoragePermission(): Array<String?> {
        val requestPermissions: Array<String?>
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestPermissions = arrayOfNulls(2)
            requestPermissions[0] = Manifest.permission.READ_MEDIA_IMAGES
            requestPermissions[1] = Manifest.permission.READ_MEDIA_VIDEO
        } else {
            requestPermissions = arrayOfNulls(2)
            requestPermissions[0] = Manifest.permission.WRITE_EXTERNAL_STORAGE
            requestPermissions[1] = Manifest.permission.READ_EXTERNAL_STORAGE
        }
        return requestPermissions
    }
    @JvmStatic
    fun getStoragePermission(vararg addPermissions: String?): Array<String?> {
        var requestPermissions: Array<String?>  = arrayOf()
        requestPermissions = requestPermissions.plus(getStoragePermission()).plus(addPermissions)
        return requestPermissions
    }

    private var checkPermissionDialog: StarryCommonPopup? = null

    // 权限设置友好提示对话框
    @JvmStatic
    fun checkPermissionWithDialog(
        context: Context,
        title: String = "",
        message: String = "",
        positiveTitle: String = "",
        negativeTitle: String = "",
        clickListener: View.OnClickListener) {
        if (checkPermissionDialog != null && checkPermissionDialog?.isShowing() == true) {
            return
        }
        checkPermissionDialog = StarryCommonPopup.Builder(
            ActivityUtils.getTopActivity(),
            CloudCommonPopupConstants.COMMON_TWO_BUTTON
        )
            .setTitle(title)
            .setMessage(message)
            .setPositiveTitle(positiveTitle)
            .setNegativeTitle(negativeTitle)
            .setOnPositiveListener { dialog: DialogInterface, _: Int ->
                clickListener.onClick(View(context))
                dialog.dismiss()
            }
            .setOnNegativeListener { dialog: DialogInterface, _: Int ->
                clickListener.onClick(null)
                dialog.dismiss()
            }
            .setTextContentGravity(Gravity.START or Gravity.CENTER_VERTICAL)
            .create()
        checkPermissionDialog?.show()
    }

    // 权限设置返回处理
    // permissions：需要判断的权限
    // callback： onGranted中的回调处理函数
    // callbackDenied： onDenied中的回调处理函数
    @JvmStatic
    fun useToolsRequestPermission(permissions: Array<String>,
                                  callback: PermissionCallBack,
                                  callbackDenied: PermissionCallBack) {
        val requestPermissionClickTime = System.currentTimeMillis()
        val isRefuseSecondPermission = booleanArrayOf(false) //当时点击了永久拒绝
        permission(*permissions)
            .rationale { _, shouldRequest ->
                shouldRequest.again(true)
                isRefuseSecondPermission[0] = true
            }
            .callback(object : FullCallback {
                override fun onGranted(granted: List<String>) {
                    logTagD("song","grantedList ${granted}")
                    //权限通过
                    if (checkPermissionDialog != null && checkPermissionDialog?.isShowing == true) {
                        checkPermissionDialog?.dismiss()
                    }
                    if (granted.size == permissions.size){
                        callback.execute()
                    }else{

                    }
                }

                override fun onDenied(deniedForever: List<String>, denied: List<String>) {
                    //权限拒绝
                    if (deniedForever.isNotEmpty()) { //永久拒绝
                        if (isRefuseSecondPermission[0]) {
                            //当时点击永久拒绝的时候不做处理
                        } else {
                            if (System.currentTimeMillis() - requestPermissionClickTime < 500) { //500ms之内 主观认为是系统返回,而非用户点击
                                goToSetting(ActivityUtils.getTopActivity())
                            }
                        }
                    }

                    callbackDenied.execute()
                }
            })
            .explain { _, _, shouldRequest ->
                shouldRequest.start(true) //第一次拒绝过了, 现在进行第二次权限弹窗,提示你需要弹出解释窗了
            }
            .request()
    }

    // 权限设置返回处理
    // permissions：需要判断的权限
    // callback： onGranted中的回调处理函数
    @JvmStatic
    fun useToolsRequestPermission(permissions: Array<String>, callback: PermissionCallBack) {
        val requestPermissionClickTime = System.currentTimeMillis()
        val isRefuseSecondPermission = booleanArrayOf(false) //当时点击了永久拒绝
        permission(*permissions)
            .rationale { _, shouldRequest ->
                shouldRequest.again(true)
                isRefuseSecondPermission[0] = true
            }
            .callback(object : FullCallback {
                override fun onGranted(granted: List<String>) {
                    //权限通过
                    if (checkPermissionDialog != null && checkPermissionDialog?.isShowing == true) {
                        checkPermissionDialog?.dismiss()
                    }
                    if (granted.size == permissions.size){
                        callback.execute()
                    }
                }

                override fun onDenied(deniedForever: List<String>, denied: List<String>) {
                    //权限拒绝
                    if (deniedForever.isNotEmpty()) { //永久拒绝
                        if (isRefuseSecondPermission[0]) {
                            //当时点击永久拒绝的时候不做处理
                        } else {
                            if (System.currentTimeMillis() - requestPermissionClickTime < 500) { //500ms之内 主观认为是系统返回,而非用户点击
                                goToSetting(ActivityUtils.getTopActivity())
                            }
                        }
                    } else if (denied.isNotEmpty()) {
                        //第一次拒绝
                    }
                }
            })
            .explain { _, _, shouldRequest ->
                shouldRequest.start(true) //第一次拒绝过了, 现在进行第二次权限弹窗,提示你需要弹出解释窗了
            }
            .request()
    }

    // 适配Android12，申请新的蓝牙权限
    @JvmStatic
    fun getBlueToothPermissions(): Array<String> {
        var permissions = arrayOf<String>()
        // Android 版本大于等于 12 时，申请新的蓝牙权限
        permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            arrayOf(
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_ADVERTISE,
                Manifest.permission.BLUETOOTH_CONNECT
            )
        } else {
            //根据实际需要申请定位权限
            arrayOf(
                Manifest.permission.ACCESS_COARSE_LOCATION,
                Manifest.permission.ACCESS_FINE_LOCATION
            )
        }
        return permissions
    }

}