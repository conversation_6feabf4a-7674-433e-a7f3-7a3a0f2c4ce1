<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="40dp">

    <com.czur.cloud.ui.component.MediumBoldTextView
        android:id="@+id/tv_wifi_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="15sp"
        android:singleLine="true"
        android:layout_toStartOf="@+id/img_lock"
        android:layout_marginEnd="15dp"
        android:layout_centerVertical="true"
        android:layout_marginStart="14dp"
        android:ellipsize="end"
        android:textColor="@color/black_22" />
    <ImageView
        android:id="@+id/img_lock"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_marginEnd="14dp"
        android:visibility="gone"
        android:layout_centerVertical="true"
        android:layout_toStartOf="@+id/image"
        android:src="@mipmap/wifi_locked"/>
    <ImageView
        android:id="@+id/image"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginEnd="14dp"
        android:layout_centerVertical="true"
        android:layout_alignParentEnd="true"
        android:src="@mipmap/wifi_small_icon"/>

</RelativeLayout>