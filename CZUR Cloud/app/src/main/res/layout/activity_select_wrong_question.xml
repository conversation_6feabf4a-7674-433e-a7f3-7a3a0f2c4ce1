<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/black">

    <RelativeLayout
        android:id="@+id/layout_title"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_alignParentTop="true"
        android:background="@color/black_2a">


        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/img_back"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:paddingEnd="17dp"
            android:gravity="center_vertical"
            android:paddingStart="17dp"
            android:text="@string/cancel"
            android:textColor="@color/white"
            android:textSize="17sp" />


    </RelativeLayout>

    <!-- Image Cropper fill the remaining available height -->
    <com.czur.cloud.ui.component.cropper.CropImageView
        android:id="@+id/cropImageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/operate_bar"
        android:layout_below="@+id/layout_title"
        android:background="@color/black"
        app:cropBorderCornerColor="@color/transparent"
        app:cropBorderLineColor="@color/red_f07574"
        app:cropBorderLineThickness="1dp"
        app:cropGuidelines="on"
        app:cropShowTag="true"
        app:isCrop="false" />

    <RelativeLayout
        android:id="@+id/operate_bar"
        android:layout_width="255dp"
        android:layout_height="95dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="23dp">

        <LinearLayout
            android:layout_width="250dp"
            android:layout_height="90dp"
            android:layout_centerInParent="true"
            android:orientation="vertical">

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/crop_btn"
                android:layout_width="250dp"
                android:layout_height="40dp"
                android:background="@drawable/btn_rec_5_bg_with_red_d4"
                android:gravity="center"
                android:text="@string/add_to_wrong_question"
                android:textColor="@color/white"
                android:textSize="15sp" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/finish_btn"
                android:layout_width="250dp"
                android:layout_height="40dp"
                android:layout_marginTop="10dp"
                android:background="@drawable/btn_rec_5_bg_with_blue_aura_home"
                android:gravity="center"
                android:text="@string/finish"
                android:textColor="@color/white"
                android:textSize="15sp" />

        </LinearLayout>


    </RelativeLayout>


</RelativeLayout>