package com.czur.cloud.ui.auramate;

import static io.agora.rtc2.Constants.CHANNEL_PROFILE_LIVE_BROADCASTING;

import android.Manifest;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.SurfaceView;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.OSS;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.callback.OSSProgressCallback;
import com.alibaba.sdk.android.oss.common.OSSLog;
import com.alibaba.sdk.android.oss.model.GetObjectRequest;
import com.alibaba.sdk.android.oss.model.GetObjectResult;
import com.alibaba.sdk.android.oss.model.ObjectMetadata;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.OSSInstance;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.FirstCalibrateEvent;
import com.czur.cloud.event.aurahome.CalibrateEvent;
import com.czur.cloud.event.aurahome.VideoEvent;
import com.czur.cloud.model.VideoTokenModel;
import com.czur.cloud.netty.CZURMessageConstants;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.util.validator.Validator;
import com.facebook.drawee.view.SimpleDraweeView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.concurrent.atomic.AtomicBoolean;

import io.agora.rtc2.ChannelMediaOptions;
import io.agora.rtc2.Constants;
import io.agora.rtc2.IRtcEngineEventHandler;
import io.agora.rtc2.RtcEngine;
import io.agora.rtc2.video.BeautyOptions;
import io.agora.rtc2.video.VideoCanvas;
import io.agora.rtc2.video.VideoEncoderConfiguration;

public class AuraMateRecordActivity extends AuramateBaseActivity implements View.OnClickListener {

    public static final int STREAM_FALLBACK_OPTION_AUDIO_ONLY = 2;

    private static final String LOG_TAG = AuraMateRecordActivity.class.getSimpleName();
    private static final int PERMISSION_REQ_ID_RECORD_AUDIO = 22;
    private static final int PERMISSION_REQ_ID_CAMERA = PERMISSION_REQ_ID_RECORD_AUDIO + 1;
    private ImageView positionRecordBackBtn;
    private TextView recordTv;
    private LinearLayout recordAgainBtn;
    private TextView recordBtn;
    private RelativeLayout videoLoadingRl;
    private ImageView loadingImg;
    private SimpleDraweeView recordImg;
    private String channel;
    private RelativeLayout failedToast;
    private RelativeLayout successToast;
    private TextView finishBtn;
    private OSS ossClient;
    private TextView recordAgain1Btn;
    private UserPreferences userPreferences;
    private String token;
    private boolean isFirst;
    private boolean isSuccess;
    private AtomicBoolean isInVideo, isInChannel;

    private CountDownTimer timer;
    private int meCount, otherCount, unKnownCount;
    private TextView tvLoading, tvNetToast;
    private RelativeLayout rlVideo;
    private FrameLayout container;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.black_22);
        BarUtils.setNavBarColor(this, getColor(R.color.black_22));
        BarUtils.setStatusBarLightMode(this, false);
        setContentView(R.layout.activity_sitting_position_record);
        initComponent();
        registerEvent();

    }

    @Override
    public boolean PCNeedFinish() {
        return !TextUtils.isEmpty(equipmentId);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case SITTING_POSITION_VIDEO:
                VideoEvent videoEvent = (VideoEvent) event;
                String isReadyForCalibrate = videoEvent.getIsReadyForCalibrate();
                countDown();
                if (isReadyForCalibrate.equals("NO")) {
                    showMessage(R.string.msg_busy);
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            ActivityUtils.finishActivity(AuraMateRecordActivity.class);
                        }
                    }, 2000);
                }

                HttpManager.getInstance().request().getVideoToken(userPreferences.getUserId(), channel, VideoTokenModel.class, new MiaoHttpManager.CallbackNetwork<VideoTokenModel>() {
                    @Override
                    public void onNoNetwork() {

                    }

                    @Override
                    public void onStart() {

                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<VideoTokenModel> entity) {
                        token = entity.getBody().getRtc_token();
                        initAgoraEngineAndJoinChannel();
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<VideoTokenModel> entity) {

                    }

                    @Override
                    public void onError(Exception e) {

                    }
                });
                break;
            case SITTING_POSITION_VIDEO_TIME_OUT:
            case VIDEO_CANCEL:
                if (hasShowPcPopup) {
                    return;
                }
                VideoEvent videoEvent1 = (VideoEvent) event;
                if (videoEvent1.getDeviceUdid().equals(equipmentId)) {
                    ActivityUtils.finishActivity(this);
                }
                break;
            case SITTING_POSITION_CALIBRATE:
                isRun = true;
                CalibrateEvent calibrateEvent = (CalibrateEvent) event;
                String osskey = calibrateEvent.getOsskey();
                String result = calibrateEvent.getResult();
                if (result.equals(CZURMessageConstants.CalibrateResult.FAILD.getValue())) {
                    recordTv.setText(R.string.sit_correct);
                    recordAgainBtn.setVisibility(View.GONE);
                    recordAgain1Btn.setVisibility(View.VISIBLE);
                    recordBtn.setVisibility(View.GONE);
                    finishBtn.setVisibility(View.GONE);
                    failedToast.setVisibility(View.VISIBLE);
                    successToast.setVisibility(View.GONE);
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            failedToast.setVisibility(View.GONE);
                        }
                    }, 1000);
                } else {
                    recordTv.setText(R.string.record_prompt);
                    recordAgainBtn.setVisibility(View.VISIBLE);
                    finishBtn.setVisibility(View.VISIBLE);
                    recordBtn.setVisibility(View.GONE);
                    recordAgain1Btn.setVisibility(View.GONE);
                    isSuccess = true;
                    successToast.setVisibility(View.VISIBLE);
                    failedToast.setVisibility(View.GONE);
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            successToast.setVisibility(View.GONE);
                        }
                    }, 1000);
                }
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        showCalibrateImage(osskey);
                    }
                }).start();
                break;
            case SITTING_POSITION_CALIBRATE_TIME_OUT:
                showMessage(R.string.scan_text);
                break;
            default:
                break;
        }
    }

    private int i;
    private boolean isRun = false;

    private void checkVideoTimeOut() {
        i = 0;
        new Thread(new Runnable() {
            @Override
            public void run() {
                while (i <= 29) {
                    try {
                        if (isRun) {
                            i = 0;
                            break;
                        }
                        i++;
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (!isRun) {
                            ActivityUtils.finishActivity(AuraMateRecordActivity.this);
                        }

                    }
                });
            }

        }).start();
    }

    private void countDown() {
        timer = new CountDownTimer(6000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                if(millisUntilFinished<=1000){
                    tvLoading.setText(String.format(getString(R.string.calling_second), (1 + "")));
                }else{
                    tvLoading.setText(String.format(getString(R.string.calling_second), ((millisUntilFinished ) / 1000) + ""));
                }

            }

            @Override
            public void onFinish() {
                videoLoadingRl.setVisibility(View.GONE);
                rlVideo.setVisibility(View.VISIBLE);

            }
        }.start();

    }


    private void showCalibrateImage(String ossKey) {
        GetObjectRequest get = new GetObjectRequest(BuildConfig.OSS_BUCKET, ossKey);
//设置下载进度回调
        get.setProgressListener(new OSSProgressCallback<GetObjectRequest>() {
            @Override
            public void onProgress(GetObjectRequest request, long currentSize, long totalSize) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        tvLoading.setText(getString(R.string.getting));
                        videoLoadingRl.setVisibility(View.VISIBLE);
                    }
                });

                OSSLog.logDebug("getobj_progress: " + currentSize + "  total_size: " + totalSize, false);
            }
        });

        try {
            // 同步执行下载请求，返回结果
            GetObjectResult getResult = OSSInstance.Companion.getInstance().oss().getObject(get);

            Log.d("Content-Length", "" + getResult.getContentLength());

            // 获取文件输入流
            InputStream inputStream = getResult.getObjectContent();
            ByteArrayOutputStream byteArrayOutputStream = ConvertUtils.input2OutputStream(inputStream);
            if (byteArrayOutputStream.toByteArray() != null) {
                Bitmap bitmap = ConvertUtils.bytes2Bitmap(byteArrayOutputStream.toByteArray());
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        videoLoadingRl.setVisibility(View.GONE);
                        recordImg.setVisibility(View.VISIBLE);
                        recordImg.setImageBitmap(ImageUtils.compressByScale(bitmap, 2000, bitmap.getHeight() * 2000 / bitmap.getWidth()));
                    }
                });

                // 下载后可以查看文件元信息
                ObjectMetadata metadata = getResult.getMetadata();
                Log.d("ContentType", metadata.getContentType());
            }


        } catch (ClientException e) {
            // 本地异常如网络异常等
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    videoLoadingRl.setVisibility(View.GONE);
                }
            });
            e.printStackTrace();
        } catch (ServiceException e) {
            // 服务异常
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    videoLoadingRl.setVisibility(View.GONE);
                }
            });
            Log.e("RequestId", e.getRequestId());
            Log.e("ErrorCode", e.getErrorCode());
            Log.e("HostId", e.getHostId());
            Log.e("RawMessage", e.getRawMessage());
        }

    }

    private void initComponent() {
        isInVideo = new AtomicBoolean(false);
        isInChannel = new AtomicBoolean(false);
        userPreferences = UserPreferences.getInstance(this);
        isFirst = getIntent().getBooleanExtra("isFirst", false);
        channel = getIntent().getStringExtra("channel");
        equipmentId = getIntent().getStringExtra("equipmentId");

        ossClient = OSSInstance.Companion.getInstance().oss();
        container = (FrameLayout) findViewById(R.id.remote_video_view_container);
        recordImg = (SimpleDraweeView) findViewById(R.id.record_img);
        positionRecordBackBtn = (ImageView) findViewById(R.id.position_record_back_btn);
        videoLoadingRl = (RelativeLayout) findViewById(R.id.video_loading_rl);
        loadingImg = (ImageView) findViewById(R.id.loading_img);
        tvLoading = findViewById(R.id.tv_loading);
        tvLoading.setText(getResources().getString(R.string.calling));
        tvNetToast = findViewById(R.id.tv_net_toast);
        recordTv = (TextView) findViewById(R.id.record_tv);
        recordAgainBtn = (LinearLayout) findViewById(R.id.record_again_btn);
        recordAgain1Btn = (TextView) findViewById(R.id.record_again_btn1);
        recordBtn = (TextView) findViewById(R.id.record_btn);
        finishBtn = (TextView) findViewById(R.id.finish_btn);

        Animation imgAnim = AnimationUtils.loadAnimation(
                this, R.anim.dialog_anim);
        loadingImg.startAnimation(imgAnim);
        failedToast = (RelativeLayout) findViewById(R.id.failed_toast);
        successToast = (RelativeLayout) findViewById(R.id.success_toast);
        videoLoadingRl.setVisibility(View.VISIBLE);

        rlVideo = findViewById(R.id.rl_video);

        ViewGroup.LayoutParams layoutParams = container.getLayoutParams();
        layoutParams.width = ScreenUtils.getScreenWidth();
        layoutParams.height = (int) (layoutParams.width * 0.75);
        container.setLayoutParams(layoutParams);

        ViewGroup.LayoutParams imgLayoutParams = recordImg.getLayoutParams();
        imgLayoutParams.width = ScreenUtils.getScreenWidth();
        imgLayoutParams.height = (int) (imgLayoutParams.width * 0.75);
        recordImg.setLayoutParams(layoutParams);
    }

    private void registerEvent() {
        finishBtn.setOnClickListener(this);
        positionRecordBackBtn.setOnClickListener(this);
        recordBtn.setOnClickListener(this);
        recordAgainBtn.setOnClickListener(this);
        recordAgain1Btn.setOnClickListener(this);
    }

    private void initAgoraEngineAndJoinChannel() {
        initializeAgoraEngine();     // Tutorial Step 1
        setupVideoProfile();         // Tutorial Step 2
        joinChannel();
        if (mRtcEngine != null) {
            mRtcEngine.muteLocalAudioStream(true);
        }
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.record_again_btn1:
            case R.id.record_again_btn:
                recordImg.setVisibility(View.GONE);
                recordTv.setText(R.string.sit_correct);
                recordAgainBtn.setVisibility(View.GONE);
                recordAgain1Btn.setVisibility(View.GONE);
                recordBtn.setVisibility(View.VISIBLE);
                finishBtn.setVisibility(View.GONE);
                break;
            case R.id.record_btn:
                tvLoading.setText(getResources().getString(R.string.recording));
                videoLoadingRl.setVisibility(View.VISIBLE);
                CZURTcpClient.getInstance().sittingPositionPhoto(AuraMateRecordActivity.this, equipmentId);
                recordBtn.setVisibility(View.GONE);
                checkVideoTimeOut();
                break;
            case R.id.finish_btn:
            case R.id.position_record_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            default:
                break;
        }
    }

    private RtcEngine mRtcEngine;// Tutorial Step 1
    private final IRtcEngineEventHandler mRtcEventHandler = new IRtcEngineEventHandler() { // Tutorial Step 1


        @Override
        public void onFirstRemoteVideoDecoded(final int uid, int width, int height, int elapsed) { // Tutorial Step 5
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    setupRemoteVideo(uid);
                }
            });
        }

        @Override
        public void onUserOffline(int uid, int reason) { // Tutorial Step 7
            if (hasShowPcPopup) {
                return;
            }
            ActivityUtils.finishActivity(AuraMateRecordActivity.this);
        }

        @Override
        public void onLocalVideoStateChanged(Constants.VideoSourceType source, int state, int error) {
            super.onLocalVideoStateChanged(source, state, error);
        }

        @Override
        public void onUserMuteVideo(final int uid, final boolean muted) { // Tutorial Step 10
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    onRemoteUserVideoMuted(uid, muted);
                }
            });
        }

        @Override
        public void onJoinChannelSuccess(String channel, int uid, int elapsed) {
            super.onJoinChannelSuccess(channel, uid, elapsed);
            isInChannel.set(true);
        }

        @Override
        public void onUserJoined(int uid, int elapsed) {
            super.onUserJoined(uid, elapsed);
            Log.d("xxxx", "设备进入坐姿校准状态");
            isInVideo.set(true);
        }

        @Override
        public void onNetworkQuality(int uid, int txQuality, int rxQuality) {
            //tx 是上行； rx 下行
            super.onNetworkQuality(uid, txQuality, rxQuality);
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    showNetToast(uid, txQuality);
                }
            });
        }
    };

    private void showNetToast(int uid, int txQuality) {
        if (uid == 0) {
            if (txQuality >= 4 && txQuality <= 6) {
                meCount++;
            } else {
                meCount--;
                if (meCount < 0) {
                    meCount = 0;
                }
            }
        } else {
            if (txQuality >= 4 && txQuality <= 6) {
                otherCount++;
            } else {
                otherCount--;
                if (otherCount < 0) {
                    otherCount = 0;
                }
            }
        }

        //双方网络同时都差
        if (meCount == 2 && otherCount == 2) {
            meCount = 0;
            otherCount = 0;
            tvNetToast.setText(getResources().getString(R.string.network_bad));
            tvNetToast.setVisibility(View.VISIBLE);
            tvNetToast.postDelayed(new Runnable() {
                @Override
                public void run() {
                    tvNetToast.setVisibility(View.GONE);
                }
            }, 3000);
        } else if (meCount == 2) {
            //我的网络差
            meCount = 0;
            tvNetToast.setText(getResources().getString(R.string.me_network_bad));
            tvNetToast.setVisibility(View.VISIBLE);
            tvNetToast.postDelayed(new Runnable() {
                @Override
                public void run() {
                    tvNetToast.setVisibility(View.GONE);
                }
            }, 3000);
        } else if (otherCount == 2) {
            //对方网络差
            otherCount = 0;
            tvNetToast.setText(getResources().getString(R.string.other_network_bad));
            tvNetToast.setVisibility(View.VISIBLE);
            tvNetToast.postDelayed(new Runnable() {
                @Override
                public void run() {
                    tvNetToast.setVisibility(View.GONE);
                }
            }, 3000);
        }
        if (uid == 0) {
            if (txQuality == 0 || txQuality == 6) {
                unKnownCount++;
            } else {
                unKnownCount--;
                if (unKnownCount < 0) {
                    unKnownCount = 0;
                }
            }
            if (unKnownCount == 5) {
                unKnownCount = 0;
                ActivityUtils.finishActivity(this);
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        ActivityUtils.finishActivity(this);
    }

    @Override
    protected void onDestroy() {
        if (isFirst) {
            EventBus.getDefault().post(new FirstCalibrateEvent(EventType.SITTING_POSITION_FIRST_CALIBRATE, isSuccess));
        }
        if (Validator.isNotEmpty(channel)) {
            CZURTcpClient.getInstance().videoCancel(AuraMateRecordActivity.this, equipmentId, channel);
            leaveChannel();
            if (mRtcEngine != null) {
                RtcEngine.destroy();
            }
        }
        mRtcEngine = null;
        if (timer != null) {
            timer.cancel();
        }
        super.onDestroy();
    }


    // Tutorial Step 9
    public void onLocalAudioMuteClicked(View view) {
        ImageView iv = (ImageView) view;
        if (iv.isSelected()) {
            iv.setSelected(false);
            iv.clearColorFilter();
        } else {
            iv.setSelected(true);
        }
        mRtcEngine.muteLocalAudioStream(iv.isSelected());
    }


    // Tutorial Step 1
    private void initializeAgoraEngine() {
        try {
            mRtcEngine = RtcEngine.create(this, BuildConfig.AGORA_APP_ID, mRtcEventHandler);
        } catch (Exception e) {
            Log.e(LOG_TAG, Log.getStackTraceString(e));
            throw new RuntimeException("NEED TO check rtc sdk init fatal error\n" + Log.getStackTraceString(e));
        }
    }

    // Tutorial Step 2
    private void setupVideoProfile() {
        if (mRtcEngine != null) {
            mRtcEngine.setChannelProfile(CHANNEL_PROFILE_LIVE_BROADCASTING);
            mRtcEngine.setDefaultAudioRoutetoSpeakerphone(true);
            mRtcEngine.setClientRole(Constants.CLIENT_ROLE_BROADCASTER);
            mRtcEngine.enableVideo();
            mRtcEngine.setBeautyEffectOptions(true, new BeautyOptions(1, 1f, 1f, 1f, 0.3f));
            mRtcEngine.setVideoEncoderConfiguration(
                    new VideoEncoderConfiguration(
                            VideoEncoderConfiguration.VD_320x240,
                            VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_15,
                            VideoEncoderConfiguration.STANDARD_BITRATE,
                            VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_FIXED_LANDSCAPE
                    )
            );
            mRtcEngine.enableDualStreamMode(false);
            mRtcEngine.setLocalPublishFallbackOption(STREAM_FALLBACK_OPTION_AUDIO_ONLY);
            mRtcEngine.setRemoteSubscribeFallbackOption(STREAM_FALLBACK_OPTION_AUDIO_ONLY);
        }
    }


    // Tutorial Step 4
    private void joinChannel() {
        if (mRtcEngine != null) {
            ChannelMediaOptions option = new ChannelMediaOptions();
            option.autoSubscribeAudio = true;
            option.autoSubscribeVideo = true;
            option.publishCameraTrack = true;
            option.publishMicrophoneTrack = true;
            option.publishScreenCaptureVideo = false;
            mRtcEngine.joinChannel(
                    token,
                    channel,
                    Integer.parseInt(UserPreferences.getInstance().getUserId()),
                    option);
//            mRtcEngine.joinChannel(token, channel, "AuraMate", Integer.parseInt(userPreferences.getUserId())); // if you do not specify the uid, we will generate the uid for you
        }
    }

    // Tutorial Step 5
    private void setupRemoteVideo(int uid) {
        if (container.getChildCount() >0) {
            container.removeAllViews();
        }
        SurfaceView surfaceView = RtcEngine.CreateRendererView(getBaseContext());
        container.addView(surfaceView);
        if (mRtcEngine != null) {
            mRtcEngine.setupRemoteVideo(new VideoCanvas(surfaceView, VideoCanvas.RENDER_MODE_ADAPTIVE, uid));
            mRtcEngine.setRemoteVideoStreamType(uid,0);
        }
        surfaceView.setTag(uid); // for mark purpose

    }

    // Tutorial Step 6
    private void leaveChannel() {
        if (mRtcEngine != null && isInChannel.get()) {
            mRtcEngine.leaveChannel();
        }
    }

    // Tutorial Step 7
    private void onRemoteUserLeft() {
        container.removeAllViews();
    }

    // Tutorial Step 10
    private void onRemoteUserVideoMuted(int uid, boolean muted) {
        SurfaceView surfaceView = (SurfaceView) container.getChildAt(0);
        if(surfaceView != null) {
            Object tag = surfaceView.getTag();
            if (tag != null && (Integer) tag == uid) {
                surfaceView.setVisibility(muted ? View.GONE : View.VISIBLE);
            }
        }
    }

    public boolean checkSelfPermission(String permission, int requestCode) {
        Log.i(LOG_TAG, "checkSelfPermission " + permission + " " + requestCode);
        if (ContextCompat.checkSelfPermission(this,
                permission)
                != PackageManager.PERMISSION_GRANTED) {

            ActivityCompat.requestPermissions(this,
                    new String[]{permission},
                    requestCode);
            return false;
        }
        return true;
    }


    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String permissions[], @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        Log.i(LOG_TAG, "onRequestPermissionsResult " + grantResults[0] + " " + requestCode);

        switch (requestCode) {
            case PERMISSION_REQ_ID_RECORD_AUDIO: {
                if (grantResults.length > 0
                        && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    checkSelfPermission(Manifest.permission.CAMERA, PERMISSION_REQ_ID_CAMERA);
                } else {
                    showMessage("No permission for " + Manifest.permission.RECORD_AUDIO);
                    finish();
                }
                break;
            }
            case PERMISSION_REQ_ID_CAMERA: {
                if (grantResults.length > 0
                        && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    initAgoraEngineAndJoinChannel();
                } else {
                    showMessage("No permission for " + Manifest.permission.CAMERA);
                    finish();
                }
                break;
            }
        }
    }
}
