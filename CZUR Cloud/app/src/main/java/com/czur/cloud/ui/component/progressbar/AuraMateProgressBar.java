package com.czur.cloud.ui.component.progressbar;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;

/**
 * Created by Yz on 2018/3/12
 * Email：<EMAIL>
 */

public class AuraMateProgressBar extends View {

    private Paint mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private int progress = 1;
    private float lineWidth = 1;
    private Paint allArcPaint;
    private Paint bluePaint;
    private int barColor;
    private int progressColor;
    private float[] floats = new float[11];


    public AuraMateProgressBar(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        /*获取自定义参数的颜色值*/
        TypedArray a = context.getTheme().obtainStyledAttributes(attrs, R.styleable.AuraMateProgressBar, defStyle, 0);
        int n = a.getIndexCount();
        for (int i = 0; i < n; i++) {
            int attr = a.getIndex(i);
            switch (attr) {
                case R.styleable.AuraMateProgressBar_lineWidth:
                    lineWidth = a.getDimension(attr, SizeUtils.dp2px(5));
                    break;
                case R.styleable.AuraMateProgressBar_barColors:
                    progressColor = a.getColor(attr, Color.RED);
                    break;
                case R.styleable.AuraMateProgressBar_backColors:
                    barColor = a.getColor(attr, Color.RED);
                    break;
            }

        }
        a.recycle();

    }

    public AuraMateProgressBar(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AuraMateProgressBar(Context context) {
        this(context, null);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);

    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        //背景
        allArcPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        allArcPaint.setAntiAlias(true);
        allArcPaint.setStyle(Paint.Style.STROKE);
        allArcPaint.setStrokeWidth(lineWidth);
        allArcPaint.setColor(barColor);
        allArcPaint.setStrokeCap(Paint.Cap.ROUND);

        bluePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        bluePaint.setAntiAlias(true);
        bluePaint.setStyle(Paint.Style.STROKE);
        bluePaint.setStrokeWidth(lineWidth);
        bluePaint.setColor(progressColor);
        bluePaint.setStrokeCap(Paint.Cap.ROUND);


        for (int i = 0; i < 11; i++) {
            float v = this.getMeasuredWidth();
            if (i == 0) {
                floats[i] = lineWidth;
            } else {
                floats[i] = ((v - lineWidth) * i / 10) + lineWidth / 2;
            }


        }
        canvas.drawLine(lineWidth, this.getMeasuredHeight() / 2, this.getMeasuredWidth() - lineWidth, this.getMeasuredHeight() / 2, allArcPaint);

        for (int i = 0; i < progress; i++) {
            if (i == 0) {
                canvas.drawLine(floats[i], this.getMeasuredHeight() / 2, floats[i + 1] - lineWidth / 2, this.getMeasuredHeight() / 2, bluePaint);

            } else {
                canvas.drawLine(floats[i] + lineWidth / 2, this.getMeasuredHeight() / 2, floats[i + 1] - lineWidth / 2, this.getMeasuredHeight() / 2, bluePaint);

            }

        }


    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {


        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:  //第一个点按下
                float[] downXArray = new float[10];
                for (int i = 0; i < downXArray.length; i++) {
                    downXArray[i] = floats[i + 1];
                }
                for (int i = 1; i < floats.length; i++) {
                    if (event.getX() <= downXArray[i - 1]) {
                        progress = i;
                        onSlideListener.onSlide(progress,true);
                        invalidate();
                        break;
                    }
                }
                break;
            case MotionEvent.ACTION_MOVE:  //第一个点按下
                float[] xArray = new float[10];

                float v = this.getMeasuredWidth();
                float halfProgress = (v / 10) / 2;

                for (int i = 0; i < xArray.length; i++) {
                    if (i == 0) {
                        xArray[i] = lineWidth + halfProgress;
                    } else {
                        xArray[i] = floats[i] + halfProgress;
                    }


                }
                for (int i = 0; i < xArray.length + 1; i++) {
                    if (i == 10) {
                        if (event.getX() >= xArray[i - 1]) {
                            progress = i;
                            onSlideListener.onSlide(progress,true);
                            invalidate();
                            break;
                        }
                    } else {
                        if (event.getX() <= xArray[i]) {
                            if (i <= 0) {
                                i = 1;
                            }
                            progress = i;
                            onSlideListener.onSlide(progress,true);
                            invalidate();
                            break;
                        }
                    }


                }
                break;
                case MotionEvent.ACTION_UP:
                    onSlideListener.onSlide(progress,false);

                    break;
        }
        return true;
    }

    /*设置进度条进度, 外部调用*/
    public void setProgress(int progress) {
        if (progress > 10) {
            this.progress = 10;
        } else if (progress < 1) {
            this.progress = 1;
        } else {
            this.progress = progress;
        }
        postInvalidate();
    }

    public int getProgress() {
        return progress;
    }

    private OnSlideListener onSlideListener;

    public interface OnSlideListener {
        void onSlide(int progress,boolean onlyShow);

    }

    public void setOnSlideListener(OnSlideListener onSlideListener) {
        this.onSlideListener = onSlideListener;
    }

}