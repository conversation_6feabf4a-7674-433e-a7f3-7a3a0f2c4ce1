package com.czur.cloud.ui.component;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.graphics.Typeface;
import android.graphics.drawable.AnimationDrawable;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;

/**
 * Created by Yz on 2018/7/5.
 * Email：<EMAIL>
 */
public class ProgressButton extends RelativeLayout {

    private TypedArray typedArray;
    private String btnText;
    private int btnColor;
    private TextView textView;
    private ImageView circleImg;
    private ObjectAnimator hideTextAnim;
    private ObjectAnimator showCircleAnim;
    private ObjectAnimator loadingAnim;
    private ObjectAnimator showTextAnim;
    private ObjectAnimator hideCircleAnim;
    private long currentTimeMillis;
    private boolean isSuccess;
    private ImageView whiteImage;

    public ProgressButton(Context context) {
        super(context);
    }

    public ProgressButton(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public ProgressButton(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        typedArray = context.obtainStyledAttributes(attrs, R.styleable.ProgressButton);
        btnText = typedArray.getString(R.styleable.ProgressButton_progress_btn_tv);
        btnColor = typedArray.getColor(R.styleable.ProgressButton_progress_btn_color, Color.WHITE);
//        drawable = typedArray.getDrawable(R.styleable.ProgressButton_progress_btn_background);
//        setBackground(drawable);

        circleImg = new ImageView(context);
        circleImg.setImageResource(R.mipmap.loading_white);
        LayoutParams layoutParams = new LayoutParams(SizeUtils.dp2px(20), SizeUtils.dp2px(20));
        layoutParams.addRule(RelativeLayout.CENTER_IN_PARENT, RelativeLayout.TRUE);
        circleImg.setLayoutParams(layoutParams);
        addView(circleImg);
        circleImg.setVisibility(GONE);

        whiteImage = new ImageView(context);
        whiteImage.setImageResource(R.drawable.white_right_animlist);
        LayoutParams layoutParams2 = new LayoutParams(SizeUtils.dp2px(26), SizeUtils.dp2px(26));
        layoutParams2.addRule(RelativeLayout.CENTER_IN_PARENT, RelativeLayout.TRUE);
        whiteImage.setLayoutParams(layoutParams2);
        addView(whiteImage);
        whiteImage.setVisibility(GONE);


        textView = new TextView(context);
        textView.setText(btnText);
        textView.setTextColor(btnColor);
        textView.setTextSize(15);
        textView.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        LayoutParams layoutParams1 = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        layoutParams1.addRule(RelativeLayout.CENTER_IN_PARENT, RelativeLayout.TRUE);
        textView.setLayoutParams(layoutParams1);
        addView(textView);


        //隐藏文字动画
        hideTextAnim = ObjectAnimator.ofFloat(textView, "alpha", 1.0f, 0);
        hideTextAnim.setInterpolator(new AccelerateInterpolator());
        hideTextAnim.setDuration(250);
        hideTextAnim.addListener(hideTextAnimListener);

        //显示文字动画
        showTextAnim = ObjectAnimator.ofFloat(textView, "alpha", 0, 1.0f);
        showTextAnim.setInterpolator(new AccelerateInterpolator());
        showTextAnim.addListener(showTextAnimListener);
        showTextAnim.setDuration(250);


        //隐藏圆圈动画
        hideCircleAnim = ObjectAnimator.ofFloat(circleImg, "alpha", 1.0f, 0);
        hideCircleAnim.setInterpolator(new AccelerateInterpolator());
        hideCircleAnim.addListener(hideCircleAnimListener);
        hideCircleAnim.setDuration(250);

        //显示圆圈动画
        showCircleAnim = ObjectAnimator.ofFloat(circleImg, "alpha", 0, 1.0f);
        showCircleAnim.setInterpolator(new AccelerateInterpolator());
        showCircleAnim.addListener(showCircleAnimListener);
        showCircleAnim.setDuration(250);


        //旋转动画
        loadingAnim = ObjectAnimator.ofFloat(circleImg, "rotation", 0, 720);
        loadingAnim.setInterpolator(new LinearInterpolator());
        loadingAnim.setDuration(2800);
        loadingAnim.setRepeatCount(ObjectAnimator.INFINITE);


    }

    public void startLoading() {
        hideTextAnim.start();
        setClickable(false);

    }

    public void reset() {
        setClickable(true);
        whiteImage.setVisibility(GONE);
        textView.setAlpha(1F);
    }

    public void startDelayLoading(final Activity activity) {
        setClickable(false);
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    Thread.sleep(300);
                    activity.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            hideTextAnim.start();
                        }
                    });

                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }

    public void stopLoading() {
        isSuccess = false;
        hideCircleAnim.start();
    }

    public void stopLoadingSuccess() {
        isSuccess = true;
        hideCircleAnim.start();
    }

    private Animator.AnimatorListener hideTextAnimListener = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {
        }

        @Override
        public void onAnimationEnd(Animator animation) {
            setClickable(true);
            showCircleAnim.start();
            loadingAnim.start();
        }

        @Override
        public void onAnimationCancel(Animator animation) {

        }

        @Override
        public void onAnimationRepeat(Animator animation) {

        }
    };

    private Animator.AnimatorListener hideCircleAnimListener = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {

        }

        @Override
        public void onAnimationEnd(Animator animation) {

            if (isSuccess) {
                whiteImage.setVisibility(VISIBLE);
                AnimationDrawable animationDrawable = (AnimationDrawable) whiteImage.getDrawable();
                animationDrawable.start();
                Handler handler = new Handler();
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (onProgressFinishListener != null) {
                            onProgressFinishListener.onFinish();
                        }
                    }

                }, 540);
            } else {
                showTextAnim.start();
            }

        }

        @Override
        public void onAnimationCancel(Animator animation) {

        }

        @Override
        public void onAnimationRepeat(Animator animation) {

        }
    };

    private Animator.AnimatorListener showCircleAnimListener = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {
            circleImg.setVisibility(VISIBLE);
        }

        @Override
        public void onAnimationEnd(Animator animation) {

        }

        @Override
        public void onAnimationCancel(Animator animation) {

        }

        @Override
        public void onAnimationRepeat(Animator animation) {

        }
    };

    private Animator.AnimatorListener showTextAnimListener = new Animator.AnimatorListener() {
        @Override
        public void onAnimationStart(Animator animation) {

        }

        @Override
        public void onAnimationEnd(Animator animation) {
            circleImg.setVisibility(GONE);
            setClickable(true);
        }

        @Override
        public void onAnimationCancel(Animator animation) {

        }

        @Override
        public void onAnimationRepeat(Animator animation) {

        }
    };

    private OnProgressFinish onProgressFinishListener;

    public interface OnProgressFinish {
        void onFinish();
    }

    public void setOnProgressFinishListener(OnProgressFinish onProgressFinishListener) {
        this.onProgressFinishListener = onProgressFinishListener;
    }
}
