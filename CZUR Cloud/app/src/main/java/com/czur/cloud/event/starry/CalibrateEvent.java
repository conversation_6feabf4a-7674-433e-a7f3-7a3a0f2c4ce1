package com.czur.cloud.event.starry;


import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;

/**
 * 上线通知
 */
public class CalibrateEvent extends BaseEvent {
    private String result;
    private String osskey;

    public CalibrateEvent(EventType eventType, String result, String osskey) {
        super(eventType);
        this.result = result;
        this.osskey = osskey;
    }

    public String getOsskey() {
        return osskey;
    }

    public String getResult() {
        return result;
    }

    @Override
    public boolean match(Object obj) {
        return true;
    }
}
