<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/layout_title"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/select_language_cancel_btn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:layout_marginLeft="17dp"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="@color/black_2a"
            android:textSize="15sp" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_marginRight="10dp"
            android:text="@string/select_language"
            android:textColor="@color/black_2a"
            android:textSize="15sp" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/select_language_btn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:gravity="center"
            android:paddingStart="0dp"
            android:paddingLeft="0dp"
            android:paddingEnd="10dp"
            android:paddingRight="10dp"
            android:text="@string/finish"
            android:textColor="@color/blue_29b0d7"
            android:textSize="15sp" />
    </RelativeLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/language_recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:overScrollMode="never" />
</LinearLayout>

