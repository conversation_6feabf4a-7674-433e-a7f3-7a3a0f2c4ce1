package com.czur.cloud.ui.auramate;


import android.app.AppOpsManager;
import android.app.Dialog;
import android.app.NotificationManager;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.core.app.NotificationManagerCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.adapter.AuraMateEquipmentAdapter;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.entity.realm.MissedCallEntity;
import com.czur.cloud.entity.realm.SPReportEntity;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.aurahome.ATBindSuccessEvent;
import com.czur.cloud.event.aurahome.ATCheckDeviceIsOnlineEvent;
import com.czur.cloud.model.AuraDeviceModel;
import com.czur.cloud.model.AuraMateReportModel;
import com.czur.cloud.model.MissedCallModel;
import com.czur.cloud.netty.CZURMessageConstants;
import com.czur.cloud.netty.bean.ReceivedMsgBodyBean;
import com.czur.cloud.netty.core.CZURTcpClient;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.FirstPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.reportfragment.ShowMoreDialog;
import com.czur.cloud.ui.base.LazyLoadBaseFragment;
import com.czur.cloud.ui.component.popup.AuraMatePermissionDialog;
import com.czur.cloud.ui.component.popup.AuraMateUpdatePopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.util.validator.Validator;
import com.czur.czurutils.log.CZURLogUtilsKt;
import com.google.gson.reflect.TypeToken;
import com.lsjwzh.widget.recyclerviewpager.RecyclerViewPager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import io.realm.Realm;
import io.realm.RealmQuery;
import io.realm.RealmResults;


public class AuraMateFragment extends LazyLoadBaseFragment {

    private RecyclerViewPager recyclerView;
    private AuraMateEquipmentAdapter adapter;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private AuraMateActivity activity;
    private List<AuraDeviceModel> deviceModels;
    private RelativeLayout slideRl;
    private Realm realm;
    private ImageView missedCallGuideImg;
    private TextView missedCallGuideTv;
    private AuraMatePermissionDialog permissionDialog;
    private LinearLayoutManager linearLayoutManager;
    private TextView tvNoNetWork;
    private RelativeLayout rlNoNetWork;
    private View missedCallPoint;
    private FirstPreferences firstPreferences;
    private boolean hasTip = false;
    private SharedPreferences sharedPreferences;
    private int version;
    private RecyclerViewPager.OnPageChangedListener onPageChangedListener;
    private String initEquipmentId;
    private int currentPosition;
    private String bindDeviceId;
    private Handler handler;
    private Runnable task;
    private CloudCommonPopup openNotifyDialog;
    private Dialog moreTipDialog;

    public static AuraMateFragment newInstance(String device) {
        AuraMateFragment auraMateFragment = new AuraMateFragment();
        Bundle bundle = new Bundle();
        bundle.putString("device", device);
        auraMateFragment.setArguments(bundle);
        return auraMateFragment;
    }

    @Override
    public void onFragmentResume() {
        super.onFragmentResume();
        if (activity != null) {
            activity.getRedTip();
        }
    }


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if ((getArguments() != null ? getArguments().getString("device") : null) != null) {
            initEquipmentId = getArguments().getString("device");
        }
    }


    //显示首次的蒙版提示
    private void showMoreTipDialog(){
        if (getActivity() == null){
            return;
        }
        moreTipDialog = new ShowMoreDialog(getActivity());
        moreTipDialog.setCanceledOnTouchOutside(false);//加上这个，点击空白处不消失
        moreTipDialog.show();

    }
    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_aura_home;
    }

    private void initDialog() {
        if (userPreferences.isAuraMateDialog()) {
            AuraMatePermissionDialog.Builder builder = new AuraMatePermissionDialog.Builder(ActivityUtils.getTopActivity());
            builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialogInterface, int i) {
                    permissionDialog.dismiss();
                    userPreferences.setAuraMateDialog(false);
                    //6.0以后海外版本引导用户开启最上层权限
                    if (BuildConfig.IS_OVERSEAS && Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        //如果没有覆盖权限
                        if (!Settings.canDrawOverlays(getActivity())) {
                            //引导用户去开启权限
                            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + getActivity().getPackageName()));
                            startActivity(intent);
                        }
                    }
                }
            });
            permissionDialog = builder.create();
            permissionDialog.show();
        }
    }

    @Override
    protected void initView(View view) {
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        realm = Realm.getDefaultInstance();
        httpManager = HttpManager.getInstance();
        slideRl = (RelativeLayout) view.findViewById(R.id.slide_rl);
        recyclerView = (RecyclerViewPager) view.findViewById(R.id.recycler_view);
        recyclerView.setPadding(ScreenUtils.getScreenWidth() * 3 / 64, 0, ScreenUtils.getScreenWidth() * 3 / 64, 0);
        linearLayoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setNestedScrollingEnabled(true);
        rlNoNetWork = view.findViewById(R.id.rl_no_network);
        tvNoNetWork = view.findViewById(R.id.tv_click_refresh);
        tvNoNetWork.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                rlNoNetWork.setVisibility(View.GONE);
                showProgressDialog();
                //这块要等待socket断网后重连
                getAuraDevice();
            }
        });
        onPageChangedListener = new RecyclerViewPager.OnPageChangedListener() {
            @Override
            public void OnPageChanged(int before, int current) {
                currentPosition = current;
                if (!firstPreferences.isAuraMateUpdateFwPrompt()) {
                    return;
                }

                if (deviceModels != null && deviceModels.size() > 0) {
                    AuraDeviceModel deviceModel;
                    if (deviceModels.size() == 1) {
                        deviceModel = deviceModels.get(current);
                    } else {
                        //加号特殊处理
                        if (current == deviceModels.size()) {
                            deviceModel = null;
                        } else {
                            deviceModel = deviceModels.get(current);
                        }
                    }
                    //在线 不是分享用户是自己本人
                    if (deviceModel != null && !deviceModel.isOffline() && deviceModel.isReadyForCheckUpdate() && deviceModel.getBindUserId() == userPreferences.getLongUserId()) {
                        Set<String> set = sharedPreferences.getStringSet("update_equipment_uid", new HashSet<>());
                        set = new HashSet<>(set);
                        //已经提示过
                        if (set.contains(deviceModel.getEquipmentUID())) {
                            return;
                        }

                        version = getResources().getInteger(R.integer.message_api_version);

                        if (version > deviceModel.getMessage_api_version()) {
                            set.add(deviceModel.getEquipmentUID());
                            sharedPreferences.edit().putStringSet("update_equipment_uid", set).apply();
                            if (deviceModel.getFirmware_need_update() == null) {
                                showUpdateDialog(deviceModel.getEquipmentUID(), false);
                            } else {
                                showUpdateDialog(deviceModel.getEquipmentUID(), deviceModel.getFirmware_need_update().equals("1"));
                            }
                        }
                    }
                }
            }
        };
        recyclerView.addOnPageChangedListener(onPageChangedListener);
    }


    private void showUpdateDialog(String equipmentUID, boolean readyForOTAUpdate) {
        new AuraMateUpdatePopup.Builder(activity)
                .setOnPositiveListener(new AuraMateUpdatePopup.Builder.OnEnsureClickListener() {
                    @Override
                    public void onEnsureClick() {
                        Intent intent = new Intent(activity, AuraMateUpdateActivity.class);
                        intent.putExtra("equipmentId", equipmentUID);
                        intent.putExtra("readyForOTAUpdate", readyForOTAUpdate);
                        ActivityUtils.startActivity(intent);
                    }
                }).create().show();
        hasTip = true;
    }

    private void startCheckThread() {
        handler = new Handler();
        task = new Runnable() {
            @Override
            public void run() {
                if (deviceModels != null) {
                    for (AuraDeviceModel deviceModel : deviceModels) {
                        if (getContext() != null) {
                            CZURTcpClient.getInstance().deviceCheckIsOnline(getContext(), deviceModel.getEquipmentUID());
                        } else {
                            handler.removeCallbacksAndMessages(null);
                        }
                    }
                    handler.postDelayed(task, 30000);
                }
            }
        };
        handler.postDelayed(task, 30000);
    }

    public void getAuraDevice() {
        if (!NetworkUtils.isConnected()) {
            rlNoNetWork.setVisibility(View.VISIBLE);
            rlNoNetWork.postDelayed(new Runnable() {
                @Override
                public void run() {
                    hideProgressDialog();
                }
            }, 300);
            return;
        }
        HttpManager.getInstance().request().getAuraDevices(userPreferences.getUserId(), new TypeToken<List<AuraDeviceModel>>() {
        }.getType(), new MiaoHttpManager.Callback<AuraDeviceModel>() {

            @Override
            public void onStart() {
            }

            @Override
            public void onResponse(MiaoHttpEntity<AuraDeviceModel> entity) {
                hideProgressDialog();
                if (entity != null && entity.getBodyList() != null) {
                    deviceModels = entity.getBodyList();
                } else {
                    showMessage(R.string.request_failed_alert);
                    return;
                }
                //这块要特殊处理 防止页面两次刷新导致动画卡顿
                if (deviceModels.size() == 0) {
                    if (recyclerView.getAdapter() == null) {
                        adapter = new AuraMateEquipmentAdapter(activity, deviceModels, realm);
                        adapter.setOnItemAddClickListener(onItemAddClickListener);
                        recyclerView.setAdapter(adapter);
                        adapter.setOnItemScrollListener(new AuraMateEquipmentAdapter.OnItemScrollListener() {
                            @Override
                            public void onItemScroll(int position, int y) {
                                if (y > 50) {
//                                    if (slideRl.getVisibility() == View.VISIBLE) {
//                                        slideRl.setVisibility(View.GONE);
//                                        userPreferences.setAuraMateIsSlide(false);
//                                    }
                                }
                            }
                        });
                    } else {
                        adapter.refreshData(deviceModels);
                    }
                    if (!realm.isClosed()){
                        realm.executeTransaction(new Realm.Transaction() {
                            @Override
                            public void execute(Realm realm) {
                                realm.where(MissedCallEntity.class).findAll().deleteAllFromRealm();
                            }
                        });
                    }
                    userPreferences.setIsHasAuraHomeDevices(false);
//                    slideRl.setVisibility(View.GONE);
                } else {
                    userPreferences.setIsHasAuraHomeDevices(true);
                    //保存设备信息Jason
                    userPreferences.setAuraMateDevices(deviceModels);

                    checkAllDevicesOnline();
                    getReportAndSetup();
                    getCallAndSetup();
                }
            }

            @Override
            public void onFailure(MiaoHttpEntity<AuraDeviceModel> entity) {
                showMessage(R.string.request_failed_alert);
                if (getActivity() != null) {
                    hideProgressDialog();
                }
            }

            @Override
            public void onError(Exception e) {
                showMessage(R.string.request_failed_alert);
                if (getActivity() != null) {
                    hideProgressDialog();
                }
            }
        });
    }

    private void checkAllDevicesOnline() {
        if (deviceModels != null && getContext() != null) {
            for (AuraDeviceModel deviceModel : deviceModels) {
                CZURTcpClient.getInstance().deviceCheckIsOnline(getContext(), deviceModel.getEquipmentUID());
            }
        }
    }


    private void getReportAndSetup() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<String>() {
            @Override
            public String doInBackground() throws Throwable {
                String time = getServerTimeSync();
                try (Realm realm = Realm.getDefaultInstance()) {
                    realm.executeTransaction(new Realm.Transaction() {
                        @Override
                        public void execute(Realm realm) {
                            RealmQuery<MissedCallEntity> missCallQuery = realm.where(MissedCallEntity.class);
                            RealmQuery<SPReportEntity> reportQuery = realm.where(SPReportEntity.class);
                            for (AuraDeviceModel deviceModel : deviceModels) {
                                missCallQuery = missCallQuery.notEqualTo("equipmentUuid", deviceModel.getEquipmentUID());
                                reportQuery = reportQuery.notEqualTo("equipmentUuid", deviceModel.getEquipmentUID());
                                List<AuraMateReportModel> reportList = getReportList(deviceModel.getEquipmentUID());
                                if (Validator.isNotEmpty(reportList)) {
                                    for (AuraMateReportModel auraMateReportModel : reportList) {
                                        int newID = auraMateReportModel.getId();
                                        int newType = auraMateReportModel.getType();
                                        if (newType == 0){
                                            newID = newID + 10000;
                                        }
                                        SPReportEntity sameEntity = realm.where(SPReportEntity.class).equalTo("id", newID).findFirst();
                                        if (sameEntity == null) {
                                            SPReportEntity object = realm.createObject(SPReportEntity.class, newID);
                                            object.setBeginTime(auraMateReportModel.getBeginTime());
                                            object.setCreateTime(auraMateReportModel.getCreateTime());
                                            object.setPushTime(auraMateReportModel.getPushTime());
                                            object.setEndTime(auraMateReportModel.getEndTime());
                                            object.setEquipmentUuid(auraMateReportModel.getEquipmentUuid());
                                            object.setErrorDuration(auraMateReportModel.getErrorDuration());
                                            object.setUsingDuration(auraMateReportModel.getUsingDuration());
                                            object.setProportion(auraMateReportModel.getProportion());
                                            object.setHaveRead(0);

                                            object.setRightDuration(auraMateReportModel.getRightDuration());
                                            object.setSeriousErrorDuration(auraMateReportModel.getSeriousErrorDuration());
                                            object.setMildErrorDuration(auraMateReportModel.getMildErrorDuration());
                                            object.setModerateErrorDuration(auraMateReportModel.getModerateErrorDuration());
                                            object.setRightProportion(auraMateReportModel.getRightProportion());
                                            object.setSeriousProportion(auraMateReportModel.getSeriousProportion());
                                            object.setMildProportion(auraMateReportModel.getMildProportion());
                                            object.setModerateProportion(auraMateReportModel.getModerateProportion());

                                            object.setType(auraMateReportModel.getType());
                                            object.setReportId(auraMateReportModel.getId()+"");
                                            object.setTitle(auraMateReportModel.getTitle());
                                        }
                                    }
                                    RealmResults<SPReportEntity> entities = realm.where(SPReportEntity.class).equalTo("equipmentUuid", deviceModel.getEquipmentUID()).equalTo("haveRead", 0).findAll();
                                    deviceModel.setUnreadCount(entities.size());
                                }
                            }
                            RealmResults<MissedCallEntity> missCallResult = missCallQuery.findAll();
                            missCallResult.deleteAllFromRealm();
                            RealmResults<SPReportEntity> reportResult = reportQuery.findAll();
                            reportResult.deleteAllFromRealm();
                        }
                    });
                }
                return time;
            }

            @Override
            public void onSuccess(String result) {
                userPreferences.setReportTime(result);
                if (recyclerView.getAdapter() == null) {
                    adapter = new AuraMateEquipmentAdapter(activity, deviceModels, realm);
                    adapter.setOnItemAddClickListener(onItemAddClickListener);
                    adapter.setOnItemScrollListener(new AuraMateEquipmentAdapter.OnItemScrollListener() {
                        @Override
                        public void onItemScroll(int position, int y) {
                            if (y > 50) {
//                                if (slideRl.getVisibility() == View.VISIBLE) {
//                                    slideRl.setVisibility(View.GONE);
//                                    userPreferences.setAuraMateIsSlide(false);
//                                }
                            }
                        }
                    });
                    recyclerView.setAdapter(adapter);
                    //消息跳转到指定设备
                    if (!TextUtils.isEmpty(initEquipmentId)) {
                        for (int i = 0; i < deviceModels.size(); i++) {
                            if (initEquipmentId.equals(deviceModels.get(i).getEquipmentUID())) {
                                recyclerView.scrollToPosition(i);
                                currentPosition = i;
                                initEquipmentId = null;
                                break;
                            }
                        }
                    }
                    //新绑定的设备跳转
                    if (!TextUtils.isEmpty(bindDeviceId)) {
                        for (int i = 0; i < deviceModels.size(); i++) {
                            if (bindDeviceId.equals(deviceModels.get(i).getEquipmentUID())) {
                                recyclerView.scrollToPosition(i);
                                currentPosition = i;
                                bindDeviceId = null;
                                break;
                            }
                        }
                    }
                } else {
                    adapter.refreshData(deviceModels);
                }

//                RealmResults<SPReportEntity> entities = realm.where(SPReportEntity.class).equalTo("haveRead", 0).findAll();
//                if (userPreferences.isAuraMateSlide() && entities.size() > 0) {
//                    slideRl.setVisibility(View.VISIBLE);
//                } else {
//                    slideRl.setVisibility(View.GONE);
//                }

                hideProgressDialog();

                if (userPreferences.isAuraMateSlide()) {
                    showMoreTipDialog();
                    userPreferences.setAuraMateIsSlide(false);
                }

            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    private void getCallAndSetup() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<String>() {
            @Override
            public String doInBackground() throws Throwable {
                String time = getServerTimeSync();
                List<MissedCallModel> callList = getMissedCallList();
                if (Validator.isNotEmpty(callList)) {
                    try (Realm realm = Realm.getDefaultInstance()) {
                        realm.executeTransaction(new Realm.Transaction() {
                            @Override
                            public void execute(Realm realm) {
                                for (MissedCallModel callModel : callList) {
                                    MissedCallEntity sameEntity = realm.where(MissedCallEntity.class).equalTo("id", callModel.getId()).findFirst();
                                    if (sameEntity == null) {
                                        MissedCallEntity object = realm.createObject(MissedCallEntity.class, callModel.getId());
                                        object.setCallId(callModel.getCallId());
                                        object.setCreateTime(callModel.getCreateTime());
                                        object.setDirection(callModel.getDirection());
                                        object.setOwnerType(callModel.getOwnerType());
                                        object.setStatus(callModel.getStatus());
                                        object.setUdid(callModel.getUdid());
                                        object.setEquipmentUuid(callModel.getEquipmentUuid());
                                        object.setDeviceName(callModel.getDeviceName());
                                        object.setUserId(callModel.getUserId());
                                        object.setHaveRead(0);
                                    }
                                }
                            }
                        });
                    }
                }

                return time;
            }

            @Override
            public void onSuccess(String result) {
                WeakHandler weakHandler = new WeakHandler();
                weakHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        if (realm.isClosed()) {
                            realm = Realm.getDefaultInstance();
                        }
                        int size = realm.where(MissedCallEntity.class).equalTo("haveRead", 0).findAll().size();

                        if (size > 0) {
                            missedCallPoint = activity.findViewById(R.id.aura_mate_top_red_point);
                            missedCallPoint.setVisibility(View.VISIBLE);
                            if (userPreferences.isNoticedMissedCall()) {
                                missedCallGuideImg.setVisibility(View.VISIBLE);
                                missedCallGuideTv.setVisibility(View.VISIBLE);
                                missedCallGuideTv.setText(String.format(activity.getResources().getString(R.string.aura_mate_missed_call_size), size + ""));
                            } else {
                                missedCallGuideImg.setVisibility(View.GONE);
                                missedCallGuideTv.setVisibility(View.GONE);
                            }
                        } else {
                            missedCallPoint = activity.findViewById(R.id.aura_mate_top_red_point);
                            missedCallPoint.setVisibility(View.GONE);
                            missedCallGuideImg.setVisibility(View.GONE);
                            missedCallGuideTv.setVisibility(View.GONE);
                        }
                        userPreferences.setCallTime(result);
                    }
                },500);

            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    private List<MissedCallModel> getMissedCallList() {
        try {
            final MiaoHttpEntity<MissedCallModel> reportEntity = httpManager.request().getMissedCallSync(userPreferences.getUserId(), userPreferences.getCallTime(), new TypeToken<List<MissedCallModel>>() {
            }.getType());
            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return reportEntity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private List<AuraMateReportModel> getReportList(String equipmentUID) {
        try {
//            final MiaoHttpEntity<AuraMateReportModel> reportEntity = httpManager.request().getUserReportSync(
//            userPreferences.getUserId(),
//            equipmentUID,
//            userPreferences.getReportTime(),
//            new TypeToken<List<AuraMateReportModel>>() {
//            }.getType());

            String reportTime = userPreferences.getReportTime();
//            reportTime = "2020-12-1 10:40:17";
            final MiaoHttpEntity<AuraMateReportModel> reportEntity = httpManager.request().
                    getAllUseReport(
                            userPreferences.getUserId(),
                            equipmentUID,
                            reportTime,
                            new TypeToken<List<AuraMateReportModel>>() {}.getType());
            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return reportEntity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    private String getServerTimeSync() {
        try {
            MiaoHttpEntity<String> serverTimeEntity = httpManager.request().getServerTime(
                    userPreferences.getUserId(), String.class);
            if (serverTimeEntity != null && serverTimeEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return serverTimeEntity.getBody();
            } else {
                return null;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case AURA_MATE_ONLINE:
                checkAllDevicesOnline();
                break;
            case ADD_AURA_SHARE_USER:
            case AURA_MATE_UPDATE:
            case UNBIND_AURA_MATE:
            case AURA_MATE_OFFLINE:
            case TRANSFER_AURA_MATE:
            case DELETE_AURA_SHARE_USER:
            case EXIT_SHARE_USER:
            case AURA_RENAME_DEVICE:
            case AURA_BIND_SUCCESS:
            case AURA_MATE_CHANGED:
                if (event instanceof ATBindSuccessEvent) {
                    bindDeviceId = ((ATBindSuccessEvent) event).getDeviceUdid();
                }
                getAuraDevice();
                break;
            case REFRESH_MISSED_CALL:
                getCallAndSetup();
                break;
            case CHECK_DEVICE_IS_ONLINE:
            case MODE_CHANGE:
            case LIGHT_SWITCH:
            case LIGHT_LEVEL:
            case LIGHT_MODE:
            case SP_SWITCH:
            case SP_VOLUME:
            case SP_LEVEL:
            case SYSTEM_LANGUAGE:
            case SMART_POWER:
            case SEDENTARY_REMINDER_SWITCH:
            case SEDENTARY_REMINDER_DURATION:
                setRefreshData(event);
                break;
            case AURA_MATE_READY_UPDATE:
//                AuraMateReadyUpdateEvent auraMateReadyUpdateEvent = (AuraMateReadyUpdateEvent) event;
//                showUpdateDialog(auraMateReadyUpdateEvent.getDeviceUdid(), auraMateReadyUpdateEvent.getStatusBean().getFirmware_need_update().equals("1"));
                break;
            default:
                break;
        }
    }

    //页面刷新 使用局部刷新
    private void setRefreshData(BaseEvent event) {
        ATCheckDeviceIsOnlineEvent onlineEvent = (ATCheckDeviceIsOnlineEvent) event;
        ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean statusBean = onlineEvent.getStatusBean();
        String deviceUdid = onlineEvent.getDeviceUdid();
        String dataBegin = onlineEvent.getDataBegin();

        if (Validator.isNotEmpty(deviceModels)) {
            int devicePosition = checkRefreshPosition(deviceUdid, deviceModels);
            if (devicePosition == -1) {
                return;
            }
            deviceModels.get(devicePosition).setShowLoading(false);
            if (event.getEventType().equals(EventType.CHECK_DEVICE_IS_ONLINE)) {
                if (statusBean == null) {
                    deviceModels.get(devicePosition).setOffline(true);
                } else {
                    //全量刷新
                    String lightLevel = statusBean.getLight_level();
                    String mode = statusBean.getLight_mode();
                    String lightSwitch = statusBean.getLight_switch();
                    String sensitivityLevel = statusBean.getSp_reminder_sensitivity_level();
                    String sensitivitySwitch = statusBean.getSp_reminder_switch();
                    String wifi_ssid = statusBean.getWifi_ssid();
                    String firmware_current_version = statusBean.getFirmware_current_version();
                    String firmware_update_version = statusBean.getFirmware_update_version();
                    String firmware_need_update = statusBean.getFirmware_need_update();
                    String system_language = statusBean.getSystem_language();
                    int has_calibrated_sp = statusBean.getHas_calibrated_sp();
                    int sp_reminder_sensitivity_volume = statusBean.getSp_reminder_sensitivity_volume();
                    String smart_power_saving_switch = statusBean.getSmart_power_saving_switch();
                    String sedentary_reminder_switch = statusBean.getSedentary_reminder_switch();
                    int sedentary_reminder_duration = statusBean.getSedentary_reminder_duration();
                    int message_api_version = statusBean.getMessage_api_version();
                    String device_mode = statusBean.getDevice_mode();

                    deviceModels.get(devicePosition).setOffline(false);
                    deviceModels.get(devicePosition).setLight_level(switchLevel(lightLevel));
                    deviceModels.get(devicePosition).setLight_mode(mode);
                    deviceModels.get(devicePosition).setLight_is_online(true);
                    deviceModels.get(devicePosition).setLight_switch(switchLight(lightSwitch));
                    deviceModels.get(devicePosition).setSitting_position_switch(switchPosition(sensitivitySwitch));
                    deviceModels.get(devicePosition).setSitting_position_level(sensitivityLevel);
                    deviceModels.get(devicePosition).setWifi_ssid(wifi_ssid);
                    deviceModels.get(devicePosition).setFirmware_current_version(firmware_current_version);
                    deviceModels.get(devicePosition).setFirmware_update_version(firmware_update_version);
                    deviceModels.get(devicePosition).setFirmware_need_update(firmware_need_update);
                    deviceModels.get(devicePosition).setHas_calibrated_sp(has_calibrated_sp);
                    deviceModels.get(devicePosition).setSp_reminder_sensitivity_volume(sp_reminder_sensitivity_volume);
                    deviceModels.get(devicePosition).setSystem_language(system_language);
                    deviceModels.get(devicePosition).setSmart_power_saving_switch(smart_power_saving_switch);
                    deviceModels.get(devicePosition).setSedentary_reminder_switch(sedentary_reminder_switch);
                    deviceModels.get(devicePosition).setSedentary_reminder_duration(sedentary_reminder_duration);
                    deviceModels.get(devicePosition).setMessage_api_version(message_api_version);
                    deviceModels.get(devicePosition).setReadyForCheckUpdate(true);
                    deviceModels.get(devicePosition).setDevice_mode(device_mode);
                }
            } else if (event.getEventType().equals(EventType.LIGHT_SWITCH)) {
                if (statusBean == null) {
                    if (!TextUtils.isEmpty(dataBegin)) {
                        deviceModels.get(devicePosition).setLight_switch(switchLight(dataBegin));
                    }
                } else {
                    deviceModels.get(devicePosition).setLight_switch(switchLight(statusBean.getLight_switch()));
                }
            } else if (event.getEventType().equals(EventType.LIGHT_LEVEL)) {
                if (statusBean == null) {
                    if (!TextUtils.isEmpty(dataBegin)) {
                        deviceModels.get(devicePosition).setLight_level(switchLevel(dataBegin));
                    }
                } else {
                    deviceModels.get(devicePosition).setLight_level(switchLevel(statusBean.getLight_level()));
                }
            } else if (event.getEventType().equals(EventType.LIGHT_MODE)) {
                if (statusBean == null) {
                    if (!TextUtils.isEmpty(dataBegin)) {
                        deviceModels.get(devicePosition).setLight_mode(dataBegin);
                    }
                } else {
                    deviceModels.get(devicePosition).setLight_mode(statusBean.getLight_mode());
                }
            } else if (event.getEventType().equals(EventType.SP_VOLUME)) {
                if (statusBean == null) {
                    if (!TextUtils.isEmpty(dataBegin)) {
                        deviceModels.get(devicePosition).setSp_reminder_sensitivity_volume(Integer.parseInt(dataBegin));
                    }
                } else {
                    deviceModels.get(devicePosition).setSp_reminder_sensitivity_volume(statusBean.getSp_reminder_sensitivity_volume());
                }
            } else if (event.getEventType().equals(EventType.SP_SWITCH)) {
                if (statusBean == null) {
                    if (!TextUtils.isEmpty(dataBegin)) {
                        deviceModels.get(devicePosition).setSitting_position_switch(switchPosition(dataBegin));
                    }
                } else {
                    deviceModels.get(devicePosition).setSitting_position_switch(switchPosition(statusBean.getSp_reminder_switch()));
                }
            } else if (event.getEventType().equals(EventType.SP_LEVEL)) {
                if (statusBean == null) {
                    if (!TextUtils.isEmpty(dataBegin)) {
                        deviceModels.get(devicePosition).setSitting_position_level(dataBegin);
                    }
                } else {
                    deviceModels.get(devicePosition).setSitting_position_level(statusBean.getSp_reminder_sensitivity_level());
                }
            } else if (event.getEventType().equals(EventType.SYSTEM_LANGUAGE)) {
                if (statusBean == null) {
                    if (!TextUtils.isEmpty(dataBegin)) {
                        deviceModels.get(devicePosition).setSystem_language(dataBegin);
                    }
                } else {
                    deviceModels.get(devicePosition).setSystem_language(statusBean.getSystem_language());
                }
            } else if (event.getEventType().equals(EventType.SMART_POWER)) {
                if (statusBean == null) {
                    if (!TextUtils.isEmpty(dataBegin)) {
                        deviceModels.get(devicePosition).setSmart_power_saving_switch(dataBegin);
                    }
                } else {
                    deviceModels.get(devicePosition).setSmart_power_saving_switch(statusBean.getSmart_power_saving_switch());
                }
            } else if (event.getEventType().equals(EventType.SEDENTARY_REMINDER_SWITCH)) {
                if (statusBean == null) {
                    if (!TextUtils.isEmpty(dataBegin)) {
                        deviceModels.get(devicePosition).setSedentary_reminder_switch(dataBegin);
                    }
                } else {
                    deviceModels.get(devicePosition).setSedentary_reminder_switch(statusBean.getSedentary_reminder_switch());
                }
            } else if (event.getEventType().equals(EventType.SEDENTARY_REMINDER_DURATION)) {
                if (statusBean == null) {
                    if (!TextUtils.isEmpty(dataBegin)) {
                        deviceModels.get(devicePosition).setSedentary_reminder_duration(Integer.parseInt(dataBegin));
                    }
                } else {
                    deviceModels.get(devicePosition).setSedentary_reminder_duration(statusBean.getSedentary_reminder_duration());
                }
            } else if (event.getEventType().equals(EventType.MODE_CHANGE)) {
                if (statusBean == null) {
                    if (!TextUtils.isEmpty(dataBegin)) {
                        deviceModels.get(devicePosition).setDevice_mode(dataBegin);
                    }
                } else {
                    deviceModels.get(devicePosition).setDevice_mode(statusBean.getDevice_mode());
                }
            }
            if (recyclerView.getAdapter() != null) {
                AuraMateEquipmentAdapter adapter = (AuraMateEquipmentAdapter) recyclerView.getAdapter();
                adapter.refreshData(deviceModels);
            }
            //为了触发升级提示
            if (currentPosition == devicePosition) {
                recyclerView.scrollToPosition(currentPosition);
            }

        }
        //保存设备信息Jason
        userPreferences.setAuraMateDevices(deviceModels);
    }

    private int switchLevel(String lightLevel) {
        if (lightLevel.equals(CZURMessageConstants.LightLevel.LEVEL_1.getLevel())) {
            return 10;
        } else if (lightLevel.equals(CZURMessageConstants.LightLevel.LEVEL_2.getLevel())) {
            return 20;
        } else if (lightLevel.equals(CZURMessageConstants.LightLevel.LEVEL_3.getLevel())) {
            return 30;
        } else if (lightLevel.equals(CZURMessageConstants.LightLevel.LEVEL_4.getLevel())) {
            return 40;
        } else if (lightLevel.equals(CZURMessageConstants.LightLevel.LEVEL_5.getLevel())) {
            return 50;
        } else if (lightLevel.equals(CZURMessageConstants.LightLevel.LEVEL_6.getLevel())) {
            return 60;
        } else {
            return 10;
        }
    }

    private boolean switchLight(String switchLight) {
        return switchLight.equals(CZURMessageConstants.LightSwitch.LIGHT_SWITCH_ON.getLightSwitch());
    }

    private boolean switchPosition(String position) {
        return position.equals(CZURMessageConstants.SensitivitySwitch.SENSITIVITY_SWITCH_ON.getSensitivitySwitch());
    }


    private int checkRefreshPosition(String deviceUdid, List<AuraDeviceModel> deviceList) {
        for (int i = 0; i < deviceList.size(); i++) {
            if (deviceList.get(i).getEquipmentUID().equals(deviceUdid)) {
                return i;
            }
        }
        return -1;
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        activity = (AuraMateActivity) getActivity();
        userPreferences = UserPreferences.getInstance(activity);
        firstPreferences = FirstPreferences.getInstance(activity);
        sharedPreferences = activity.getSharedPreferences("update_equipment_uid_sp", Context.MODE_PRIVATE);
        missedCallGuideImg = activity.findViewById(R.id.missed_call_guide_img);
        missedCallGuideTv = activity.findViewById(R.id.missed_call_guide_tv);
        missedCallPoint = activity.findViewById(R.id.aura_mate_top_red_point);
        showProgressDialog();
        initDialog();
        getAuraDevice();
        startCheckThread();
        initNetListener();
        if (!isNotificationEnabled(activity)) {
            showOpenNotifyDialog();
        }
        activity.getRedTip();
    }

    private void initNetListener() {
        NetworkUtils.registerNetworkStatusChangedListener(new NetworkUtils.OnNetworkStatusChangedListener() {
            @Override
            public void onDisconnected() {
                rlNoNetWork.setVisibility(View.VISIBLE);
            }

            @Override
            public void onConnected(NetworkUtils.NetworkType networkType) {
                rlNoNetWork.setVisibility(View.GONE);
                getAuraDevice();
            }
        });
    }


    private AuraMateEquipmentAdapter.OnItemAddClickListener onItemAddClickListener = new AuraMateEquipmentAdapter.OnItemAddClickListener() {
        @Override
        public void onItemAddClick(int position) {
            //添加的时候需要key
            Intent intent = new Intent(activity, AuraMateWifiHistoryActivity.class);
            intent.putExtra("noNeedKey", false);
            ActivityUtils.startActivity(intent);
        }
    };


    @Override
    public void onDestroy() {
        super.onDestroy();
        if (permissionDialog != null && permissionDialog.isShowing()){
            permissionDialog.dismiss();
        }
        if (openNotifyDialog != null && openNotifyDialog.isShowing()){
            openNotifyDialog.dismiss();
        }
        if (moreTipDialog != null && moreTipDialog.isShowing()){
            moreTipDialog.dismiss();
        }


        if (hasTip) {
            firstPreferences.setIsAuraMateFwPrompt(false);
        }
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        userPreferences.setAuraMateDevices(deviceModels);
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        if (realm != null) {
            realm.close();
        }
    }


    private static final int SUCCESS_CODE = 666;

    private void showOpenNotifyDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(activity, CloudCommonPopupConstants.NOTIFY_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.no_notify_text));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                goNotifySetting();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });

        openNotifyDialog = builder.create();
        openNotifyDialog.setCanceledOnTouchOutside(false);
        openNotifyDialog.show();
    }

    private void goNotifySetting() {
        Intent localIntent = new Intent();
        //直接跳转到应用通知设置的代码：
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.O) {
            localIntent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
            localIntent.putExtra(Settings.EXTRA_APP_PACKAGE, activity.getPackageName());
        } else if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            localIntent.setAction("android.settings.APP_NOTIFICATION_SETTINGS");
            localIntent.putExtra("app_package", activity.getPackageName());
            localIntent.putExtra("app_uid", activity.getApplicationInfo().uid);
        } else if (android.os.Build.VERSION.SDK_INT == Build.VERSION_CODES.KITKAT) {
            localIntent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            localIntent.addCategory(Intent.CATEGORY_DEFAULT);
            localIntent.setData(Uri.parse("package:" + activity.getPackageName()));
        } else {
            //4.4以下没有从app跳转到应用通知设置页面的Action，可考虑跳转到应用详情页面,
            localIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            if (Build.VERSION.SDK_INT >= 9) {
                localIntent.setAction("android.settings.APPLICATION_DETAILS_SETTINGS");
                localIntent.setData(Uri.fromParts("package", activity.getPackageName(), null));
            } else if (Build.VERSION.SDK_INT <= 8) {
                localIntent.setAction(Intent.ACTION_VIEW);
                localIntent.setClassName("com.android.settings", "com.android.setting.InstalledAppDetails");
                localIntent.putExtra("com.android.settings.ApplicationPkgName", activity.getPackageName());
            }
        }
        startActivityForResult(localIntent, SUCCESS_CODE);
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static boolean isNotificationEnabled(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            NotificationManagerCompat notificationManagerCompat = NotificationManagerCompat.from(context);
            return notificationManagerCompat.areNotificationsEnabled();
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            //8.0手机以上
            if (((NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE)).getImportance() == NotificationManager.IMPORTANCE_NONE) {
                return false;
            }
        }
        String CHECK_OP_NO_THROW = "checkOpNoThrow";
        String OP_POST_NOTIFICATION = "OP_POST_NOTIFICATION";
        AppOpsManager mAppOps = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
        ApplicationInfo appInfo = context.getApplicationInfo();
        String pkg = CZURConstants.PACKAGE_NAME;
        int uid = appInfo.uid;
        Class appOpsClass = null;
        try {
            appOpsClass = Class.forName(AppOpsManager.class.getName());
            Method checkOpNoThrowMethod = appOpsClass.getMethod(CHECK_OP_NO_THROW, Integer.TYPE, Integer.TYPE,
                    String.class);
            Field opPostNotificationValue = appOpsClass.getDeclaredField(OP_POST_NOTIFICATION);
            int value = (Integer) opPostNotificationValue.get(Integer.class);
            return ((Integer) checkOpNoThrowMethod.invoke(mAppOps, value, uid, pkg) == AppOpsManager.MODE_ALLOWED);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


}