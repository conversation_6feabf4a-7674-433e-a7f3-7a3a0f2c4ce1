package com.czur.cloud.netty.bean

import com.czur.cloud.ui.starry.model.StarryDoingMeetingModel

data class StarryRecivedNoticeBean(
    val requestid: String,
    val type: String,
    val body: StarryRecivedNoticeBody,
    val timestamp: Long,
    val nodeId: Int
)

data class StarryRecivedNoticeBody(
    val action: String,
    val udid_to: String,
    val userid_to: String,
    val appid_to: String,
    val userid_from: String,
    val udid_from: String,
    val data: String?,
    val room: String?,
    val module: String
)

data class StarryRecivedNoticeData(
    //"{\"type\":\"remove\",\"enterpriseName\":\"成者科技有限公司\",\"enterpriseId\":\"1qa2wsx3edc4rfv4rfv5tg\"}"
    val type: String,
    val enterpriseName: String,
    val enterpriseId: String,
    val from_api: String
)
