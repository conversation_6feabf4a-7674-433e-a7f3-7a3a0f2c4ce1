package com.czur.cloud.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.R;
import com.czur.cloud.model.AuraMateShareUserModel;
import com.facebook.drawee.view.SimpleDraweeView;

import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class MemberPopupAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_NORMA = 0;
    private Context mActivity;
    //当前需要显示的所有的图片数据
    private List<AuraMateShareUserModel> datas;

    private LayoutInflater mInflater;

    /**
     * 构造方法
     */
    public MemberPopupAdapter(Context activity, List<AuraMateShareUserModel> datas) {

        this.mActivity = activity;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<AuraMateShareUserModel> datas) {
        this.datas = datas;
        notifyDataSetChanged();

    }


    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new NormalViewHolder(mInflater.inflate(R.layout.item_aura_mate_member, parent, false));
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {

        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);
            mHolder.etUserHeadName.setText(mHolder.mItem.getMemberName());
            if (TextUtils.isEmpty(mHolder.mItem.getMemberPhoto())) {
                mHolder.etUserHeadImg.setImageResource(R.mipmap.user_default_icon);
            } else {
                mHolder.etUserHeadImg.setImageURI(mHolder.mItem.getMemberPhoto());
            }

            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    notifyDataSetChanged();
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(position, mHolder.mItem);
                    }
                }
            });


        }
    }


    @Override
    public int getItemViewType(int position) {

        return ITEM_TYPE_NORMA;

    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }


    private class NormalViewHolder extends ViewHolder {
        public final View mView;
        RelativeLayout auraHomePopupRl;
        SimpleDraweeView etUserHeadImg;
        TextView etUserHeadName;
        AuraMateShareUserModel mItem;


        NormalViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            auraHomePopupRl = (RelativeLayout) itemView.findViewById(R.id.aura_home_popup_rl);
            etUserHeadImg = (SimpleDraweeView) itemView.findViewById(R.id.et_user_head_img);
            etUserHeadName = (TextView) itemView.findViewById(R.id.et_user_head_name);

        }


    }


    private onItemClickListener onItemClickListener;

    public void setOnItemClickListener(onItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface onItemClickListener {
        void onItemClick(int position, AuraMateShareUserModel foldersBean);
    }


}
