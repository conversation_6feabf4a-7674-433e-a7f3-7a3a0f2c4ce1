package com.czur.cloud.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.blankj.utilcode.util.ImageUtils;
import com.czur.cloud.R;
import com.czur.cloud.entity.realm.PageEntity;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class BookPageAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_NORMA = 0;
    private static final int ITEM_TYPE_EMPTY = 1;
    private static final int ITEM_TYPE_FOOTER = 2;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<PageEntity> datas;
    //是否进入选择
    private boolean isSelectItem;
    private int emptyItemCounts;

    private LinkedHashMap<String, Boolean> isCheckedMap = new LinkedHashMap<>();

    /**
     * 构造方法
     */
    public BookPageAdapter(Activity activity, List<PageEntity> datas, boolean isSelectItem) {
        this.mActivity = activity;
        this.isSelectItem = isSelectItem;
        this.datas = datas;
        emptyItemCounts = calculateEmptyRows(datas.size());
    }

    public void refreshData(List<PageEntity> books, boolean isSelectItem, LinkedHashMap<String, Boolean> isCheckedMap) {

        this.isSelectItem = isSelectItem;
        this.datas = books;
        this.isCheckedMap = isCheckedMap;
        emptyItemCounts = calculateEmptyRows(datas.size());
        notifyDataSetChanged();

    }

    public void refreshData(boolean isSelectItem) {
        this.isSelectItem = isSelectItem;
        notifyDataSetChanged();
    }


    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (viewType == ITEM_TYPE_NORMA) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_book_page_list, parent, false);
            return new PagesViewHolder(view);
        } else if (viewType == ITEM_TYPE_EMPTY) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_empty_book_page_list, parent, false);
            return new EmptyHolder(view);
        } else if (viewType == ITEM_TYPE_FOOTER) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_footer_book_page, parent, false);
            return new FooterHolder(view);
        } else {
            return null;
        }
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {

        if (holder instanceof PagesViewHolder) {
            final PagesViewHolder mHolder = (PagesViewHolder) holder;
            mHolder.mItem = datas.get(position);
            mHolder.itemNumTv.setText(mHolder.mItem.getPageNum()+"");
            mHolder.itemPageImg.setImageBitmap(ImageUtils.getBitmap(mHolder.mItem.getSmallPicUrl()));
            if (isSelectItem) {
                mHolder.bookPageStarImg.setVisibility(View.GONE);
                mHolder.checkBox.setVisibility(View.VISIBLE);
                mHolder.checkBox.setTag(mHolder.mItem.getPageId());
            } else {
                mHolder.bookPageStarImg.setVisibility(mHolder.mItem.getIsStar()==1?View.VISIBLE:View.GONE);
                mHolder.checkBox.setVisibility(View.GONE);
            }
            mHolder.checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        mHolder.shadowImg.setVisibility(View.VISIBLE);
                        if (!isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //选中时添加
                            isCheckedMap.put(mHolder.mItem.getPageId(), true);
                        }
                    } else {
                        mHolder.shadowImg.setVisibility(View.GONE);
                        if (isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //没选中时移除
                            isCheckedMap.remove(mHolder.mItem.getPageId());
                        }
                    }
                    if (onItemCheckListener != null) {
                        onItemCheckListener.onItemCheck(position, mHolder.mItem, isCheckedMap, datas.size());
                    }
                }
            });

            if (isCheckedMap != null) {
                mHolder.checkBox.setChecked(isCheckedMap.containsKey(mHolder.mItem.getPageId()) ? true : false);
            } else {
                mHolder.checkBox.setChecked(false);
            }

            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onPageEntityClick(mHolder.mItem, position,mHolder.checkBox);
                    }

                }
            });


//            mHolder.itemView.setOnLongClickListener(new View.OnLongClickListener() {
//                @Override
//                public boolean onLongClick(View v) {
//                    if (!isSelectItem) {
//                        mHolder.checkBox.setChecked(true);
//                        isCheckedMap.put(mHolder.mItem.getPageId(),true);
//                        if (onItemLongClickListener != null) {
//                            onItemLongClickListener.onPageEntityLongClick(position, mHolder.mItem, isCheckedMap, datas.size());
//                        }
//                        return false;
//                    }
//                    return true;
//
//                }
//            });




        } else if (holder instanceof EmptyHolder) {
            final EmptyHolder mHolder = (EmptyHolder) holder;
        } else if (holder instanceof FooterHolder) {
            final FooterHolder mHolder = (FooterHolder) holder;

        }

    }
    public int getTotalSize() {
        return datas.size();
    }
    /**
     * @des: 计算空白条目数量
     * @params:[]
     * @return:void
     */

    private int calculateEmptyRows(int dataSize) {
        switch (datas.size() % 3) {
            case 0:
                return 0;
            case 1:
                return 2;
            case 2:
                return 1;
            default:
                break;
        }
        return -1;
    }



    @Override
    public int getItemViewType(int position) {

        if (position >= 0 && position < datas.size()) {
            return ITEM_TYPE_NORMA;
        } else if (position >= datas.size() && position < datas.size() + emptyItemCounts) {
            return ITEM_TYPE_EMPTY;
        } else {
            return ITEM_TYPE_FOOTER;
        }

    }


    public boolean isFooter(int position) {
        return position == (datas.size() + emptyItemCounts);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size() + 1 + emptyItemCounts;
    }


    private class PagesViewHolder extends ViewHolder {
        public final View mView;
        PageEntity mItem;
        RelativeLayout bookPageItem;
        TextView itemNumTv;
        CheckBox checkBox;
        ImageView itemPageImg;
        ImageView bookPageStarImg;
        ImageView shadowImg;

        PagesViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            bookPageItem = (RelativeLayout) itemView.findViewById(R.id.book_page_item);
            itemNumTv = (TextView) itemView.findViewById(R.id.item_num_tv);
            shadowImg = (ImageView) itemView.findViewById(R.id.item_page_shadow);
            checkBox = (CheckBox) itemView.findViewById(R.id.check);
            itemPageImg = (ImageView) itemView.findViewById(R.id.item_page_img);
            bookPageStarImg = (ImageView) itemView.findViewById(R.id.book_page_star_img);

        }


    }


    private class EmptyHolder extends ViewHolder {

        public final View mView;


        public EmptyHolder(View view) {
            super(view);
            mView = view;


        }
    }

    private class FooterHolder extends ViewHolder {

        public final View mView;
        RelativeLayout footerPageItem;

        public FooterHolder(View view) {
            super(view);
            mView = view;
            footerPageItem = (RelativeLayout) itemView.findViewById(R.id.book_page_footer_item);

        }
    }





    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onPageEntityClick(PageEntity PageEntity, int position,CheckBox checkBox);
    }

    private OnItemLongClickListener onItemLongClickListener;

    public void setOnItemLongClickListener(OnItemLongClickListener onItemLongClickListener) {
        this.onItemLongClickListener = onItemLongClickListener;
    }

    public interface OnItemLongClickListener {
        void onPageEntityLongClick(int position, PageEntity PageEntity, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize);
    }


    private OnItemCheckListener onItemCheckListener;

    public void setOnItemCheckListener(OnItemCheckListener onItemCheckListener) {
        this.onItemCheckListener = onItemCheckListener;
    }

    public interface OnItemCheckListener {
        void onItemCheck(int position, PageEntity PageEntity, LinkedHashMap<String, Boolean> isCheckedMap, int totalSize);

    }

}
