package com.czur.cloud.ui.starry.meeting.agora

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.os.Build
import android.util.Log
import android.view.View
import androidx.lifecycle.LiveData
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.ui.starry.meeting.base.CZURAtyManager
import com.czur.cloud.ui.starry.meeting.bean.Token
import com.czur.cloud.ui.starry.meeting.common.StreamType
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import com.czur.cloud.ui.starry.meeting.screensharing.RefusedPermissionListener
import com.czur.czurutils.log.logI
import kotlinx.coroutines.flow.Flow

/**
 * Created by 陈丰尧 on 4/19/21
 */

private const val TAG = "AgoraManager"

class AgoraManager(onRemoteUserOnLine: ((uid: String) -> Unit)?,
                   onRemoteUserOffLine: ((uid: String) -> Unit)?) {

    companion object {
        const val JOIN_CHANNEL_SUCCESS = 0
        const val JOIN_CHANNEL_FAIL = 1
    }

    private val chatManager: ChatManager
    private val displayManager: DisplayManager

    //    private val context: Context = CzurCloudApplication.getApplication()
    private val context: Context = CZURAtyManager.appContext

    fun startScreenCapture(token: String,
                           channelName: String,
                           shareUid: Int) =
        displayManager.startScreenCapture( token,channelName,shareUid )

    fun stopScreenCaption() = displayManager.stopScreenCaption()

    fun setRefusedPermissionListener(_refusedPermissionListener: RefusedPermissionListener){
        displayManager.setRefusedPermissionListener(_refusedPermissionListener)
    }

    // 音频管理
    private val audioManager by lazy {
        context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    }

    private var afChangeListener: AudioManager.OnAudioFocusChangeListener =
        AudioManager.OnAudioFocusChangeListener {
            val logInfo = when (it) {
                AudioManager.AUDIOFOCUS_GAIN -> "AUDIOFOCUS_GAIN"
                AudioManager.AUDIOFOCUS_LOSS -> "AUDIOFOCUS_LOSS"
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> "AUDIOFOCUS_LOSS_TRANSIENT"
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> "AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK"
                else -> "其他:${it}"
            }

            logI("${TAG}.afChangeListener.音频焦点改变:${logInfo}")

        }


    init {
        // 聊天管理器
        chatManager = ChatManager(context)
        // 初始化 聊天Manager
        chatManager.init()

        // 视频会议
        displayManager = DisplayManager(context, onRemoteUserOnLine, onRemoteUserOffLine)
        // 初始化视频会议SDK
        displayManager.init()

    }

    /**
     * 获取本地的图像的surfaceView,需要在主线程调用
     * @param uid   要获取的UID, 默认为自己的czurID,表示获取自己的surfaceView
     * @return 如果为null 表示这个用户还没有接入声网ID
     */
    fun getSurface(uid: String = UserHandler.czurId.toString()): View? {
        return displayManager.getSurface(uid)
    }
    fun getNewSurface(uid: String = UserHandler.czurId.toString()): View? {
        return displayManager.getNewSurface(uid)
    }

    fun getTextTrueView(uid: String = UserHandler.czurId.toString()): View? {
        return displayManager.getTextTrue(uid)
    }

    // 重新设置视图显示
    fun setupViewForVideo(view: View, uid: String){
        displayManager.setupViewForVideo(view, uid)
    }

    fun getVolumeLiveData(uid: String): LiveData<Int> = displayManager.getVolumeLiveData(uid)

    // 获取网络质量
    fun getNetworkQualityLiveData(uid: String): LiveData<Int> = displayManager.getNetworkQualityLiveData(uid)
    fun getSelfNetworkTxQualityLiveData(uid: String): LiveData<Int> = displayManager.getSelfNetworkTxQualityLiveData(uid)
    fun getSelfNetworkRxQualityLiveData(uid: String): LiveData<Int> = displayManager.getSelfNetworkRxQualityLiveData(uid)
    fun getSelfNetQualityFlow(): Flow<Int> = displayManager.getSelfNetQualityFlow()
    // Jason
    // cam/mic 静音开关
    fun notMuteLocalAudioOrVideo(stream: StreamType, flag: Boolean){
        displayManager.notMuteVideoOrAudio(stream, flag)
//        if (stream == StreamType.VIDEO) {
//            displayManager.muteLocalVideo(flag)
//        }
//        if (stream == StreamType.AUDIO) {
//            displayManager.muteLocalAudio(flag)
//        }
    }

    // 切换摄像头
    fun changeLocalCarmea() {
        displayManager.changeLocalCarmea()
    }

    // 开启/关闭本地视频采集。
    fun enableLocalVideo(flag:Boolean = true) {
        displayManager.enableLocalVideo(flag)
    }
    // 开启/关闭本地音频采集。
    fun enableLocalAudio(flag:Boolean = true){
        displayManager.enableLocalAudio(flag)
    }

    /**
     * 加入Channel
     * @param token token信息包含 聊天 和 视频
     * @param room  会议室号
     * @param displayJoinResult 视频加入频道结果
     * @param chatJoinResult    聊天加入频道结果
     */
    fun joinChannel(
        token: Token,
        room: String,
        displayJoinResult: (Int) -> Unit,
        chatJoinResult: (Int) -> Unit
    ) {
        // 加入会议室先请求音频焦点
        requestAudioFocus()

//        val czurID = UserHandler.czurId
        val czurID = UserPreferences.getInstance().userId

        // 加入会议的channel
        displayManager.joinChannel(token.rtcToken, room, czurID.toInt(), displayJoinResult)

        // 加入聊天的channel
        chatManager.joinChannel(token.rtmToken, room, czurID.toString(), chatJoinResult)
    }

    fun joinChatChannel(
    token: Token,
    room: String,
    chatJoinResult: (Int) -> Unit){
        // 加入会议室先请求音频焦点
        requestAudioFocus()

        val czurID = UserPreferences.getInstance().userId
        chatManager.joinChannel(token.rtmToken, room, czurID.toString(), chatJoinResult)
    }

    fun sendMsg(text: String) {
        chatManager.sendMessage(text)
    }

    fun getActiveSpeaker(): LiveData<String> = displayManager.getActiveSpeaker()

    /**
     * 断开声网的链接, 离开会议室
     */
    fun leave() {
        Log.i(TAG, "断开声网连接")
        // 先释放音频焦点
        abandonAudioFocus()
        chatManager.leaveChanel() // 离开聊天室
        displayManager.leaveChannel() // 离开视频通道
    }

    var afr :AudioFocusRequest ?= null

    /**
     * 会议开始时请求音频焦点
     */
     fun requestAudioFocus() {
        Log.i(TAG, "requestAudioFocus.请求音频焦点")

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            val playbackAttributes = AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
                .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                .build()

            // 音频管理
            afr = AudioFocusRequest
                .Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT)
                .setOnAudioFocusChangeListener(afChangeListener)
                .setAudioAttributes(playbackAttributes)
                .build();
            val res = audioManager.requestAudioFocus(afr!!)
            if (res != -999) {
                try {

                    val result = audioManager.requestAudioFocus(
                        afr!!
                    )
                    if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                        logI("${TAG}.requestAudioFocus.8.0+音频焦点请求成功")
                    } else {
                        logI("${TAG}.requestAudioFocus.8.0+音频焦点请求失败")                   }
                } catch (e: InterruptedException) {
                    e.printStackTrace()
                }
            }
        } else {
//            // 8.0之前
            val result = audioManager.requestAudioFocus(
                afChangeListener,
                AudioManager.STREAM_VOICE_CALL,
                AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
            )
            if (result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                Log.i(TAG, "requestAudioFocus.音频焦点请求成功")
            } else {
                Log.i(TAG, "requestAudioFocus.音频焦点请求失败")
            }
            logI("${TAG}.requestAudioFocus.8.0-音频焦点请求:${result}")

        }
    }
    /**
     * 释放音频焦点
     */
     fun abandonAudioFocus() {
        Log.i(TAG, "释放音频焦点")

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            afr?.let { audioManager.abandonAudioFocusRequest(it) }
        }else{
            audioManager.abandonAudioFocus(afChangeListener)
        }
    }

    fun setRemoteVideoStreamType(uid: Int, videoStreamHigh: Int) {
        displayManager.setRemoteVideoStreamType(uid, videoStreamHigh)
    }

}
