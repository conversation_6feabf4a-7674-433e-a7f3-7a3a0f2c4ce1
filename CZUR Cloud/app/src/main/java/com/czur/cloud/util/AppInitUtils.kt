package com.czur.cloud.util

import android.app.Application
import android.content.Context
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.common.CZURConstants
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.ui.base.CzurCloudApplication
import com.czur.czurutils.log.initLogUtil
import com.czur.czurutils.log.logI
import com.czur.czurutils.upload.HttpUtils
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

const val CZURLogTag = "CZUR"

object AppInitUtils {

    // 初始化CZURLog日志系统
    @JvmStatic
    fun initCZURLog(application: Application){
        initLogUtil(application) {
            configTag = "CZUR"//默认tag,默认 czurLogUtil

            // log输出位置设置
            logOutputSwitch {
                consoleShowAll = true   // 输出到控制台
                fileSaveAll = true      // 保存到文件中
            }

            // log显示样式设置
            logStyle {
                showHeader = true      // 显示头
                showArgIndex = true    // 显示参数索引
            }

            // log保存文件设置
            //CZURConstants.MIRROR_PATH + "log/";
            logSaveConfig {
                customLogSaveDir = File(CZURConstants.MIRROR_PATH, "log/") // Log保存位置
                maxSaveDays = 7 // 保存天数
                maxFileCount = 5    // 最多保存5个文件
                maxFileSizeInByte = 5 * 1024 * 1024     // 单个文件最大5MB
                logFileExtension = "log"    // 文件扩展名

                showInfoEachLine = false    // 不显示每行的Log信息

                logFileHead {
                    addFileHead = true // 是否添加头信息
                }
            }
        }
    }

    // 打包日志文件,并上传oss
    @JvmStatic
    fun uploadLogFile(){
        logI("AppInitUtils.uploadLogFile")
        val clientId = UserPreferences.getInstance().userId ?: ""
        // 查找指定文件夹下的所有文件
        val logFilePath = CZURConstants.MIRROR_PATH + "log/"
        // 以当前时间格式化为年月日时分秒为文件名
        val sdf = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
        val zipFileName = CZURConstants.MIRROR_PATH + "log" + sdf.format(Date()) + ".zip"
        logI("AppInitUtils.uploadLogFile.logFilePath=$logFilePath,zipFileName=$zipFileName")

        // 初始化 HttpUtils的基础参数
        val context: Context = CzurCloudApplication.getApplication().applicationContext
        val baseUrl = BuildConfig.BASE_URL
        val ossEndpoint = BuildConfig.OSS_ENDPOINT //OSS_ENDPOINT = "oss-cn-beijing.aliyuncs.com";
//        val logBucket = BuildConfig.MIRROR_BUCKET // "changer-resource"
        val logBucket = "changer-resource"
        val logTokenApi = "v3/public/oss/token"

        GlobalScope.launch {
            HttpUtils.init(baseUrl, ossEndpoint, logBucket, logTokenApi)

            // 创建zip文件
            createZipFile(logFilePath, zipFileName)

            // 上传文件
            val zipFile = File(zipFileName)

            val ossModel = HttpUtils.getOssInfo(clientId)
            val prefix: String? = ossModel?.Prefix
            val ossPath = prefix?.substring(0, prefix.length - 1) + zipFile.name

            logI("uploadFile.zipFileName:$zipFileName,zipFile.length():${zipFile.length()}")
            logI("uploadLogFile.ossPath=${ossPath}")
            // oss://changer-resource/cn/105062/ 正式
            // oss://changer-resource/test/4123/ 测试
            // 上传oss
            val ret = HttpUtils.upload(context, ossModel, zipFile.absolutePath, ossPath)
            if (ret) {
                logI("uploadLogFile.上传文件成功!")
                ToastUtils.showLong("上传日志文件成功:)")
            } else {
                logI("uploadLogFile.上传文件失败!")
                ToastUtils.showLong("上传日志文件失败:(")
            }
        }




    }

    private fun createZipFile(sourceFolderPath: String, zipFilePath: String) {
        val sourceFolder = File(sourceFolderPath)
        val zipFile = File(zipFilePath)

        // 创建空的 zip 文件
        zipFile.createNewFile()

        val zipOutputStream = ZipOutputStream(FileOutputStream(zipFile))

        // 递归遍历文件夹中所有文件并添加到 zip 文件中
        addFilesToZip(sourceFolder, zipOutputStream, sourceFolder.name)

        // 关闭流
        zipOutputStream.close()
    }

    private fun addFilesToZip(file: File, zipOutputStream: ZipOutputStream, zipFilePath: String) {
        if (file.isDirectory) {
            // 如果是文件夹则递归遍历
            file.listFiles()?.forEach {
                addFilesToZip(it, zipOutputStream, "$zipFilePath/${it.name}")
            }
        } else {
            // 如果是文件则添加到 zip 文件中
            val buffer = ByteArray(1024)
            val fileInputStream = FileInputStream(file)

            zipOutputStream.putNextEntry(ZipEntry(zipFilePath))

            var length: Int

            while (fileInputStream.read(buffer).also { length = it } > 0) {
                zipOutputStream.write(buffer, 0, length)
            }

            // 关闭文件输入流
            fileInputStream.close()
        }
    }

}