package com.czur.cloud.model;

public class AuraErrorSitPictureModel {
/*
{
    "id":5,
    "relationId":"160687845107643824",
    "equipmentUUID":"CET15A2001P00007",
    "dateString":"2021-01-28",
    "errorImg":"test/3475/CET15A2001P00004/posture/a0e2de0b-937a-43bd-a14b-87f79ff25b3a.jpg",
    "createTime":1611821165000,
    "smallErrorImgUrl":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/3475/CET15A2001P00004/posture/a0e2de0b-937a-43bd-a14b-87f79ff25b3a.jpg?Expires=1611886663&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=J6OFmWnp4CoYgC3VpRVpOqffO0M%3D&x-oss-process=image%2Fresize%2Cm_lfit%2Cw_150%2Ch_150",
    "middleErrorImgUrl":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/3475/CET15A2001P00004/posture/a0e2de0b-937a-43bd-a14b-87f79ff25b3a.jpg?Expires=1611886663&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=FXXTQsOKOFW9vsDBkFc6g1BzXyg%3D&x-oss-process=image%2Fresize%2Cm_lfit%2Cw_1080%2Ch_1080",
    "errorImgUrl":"https://czur-aura.oss-cn-beijing.aliyuncs.com/test/3475/CET15A2001P00004/posture/a0e2de0b-937a-43bd-a14b-87f79ff25b3a.jpg?Expires=1611886663&OSSAccessKeyId=kNOVrivgFErHv3mU&Signature=Pv4El8CByXtfHvJGuzcM75XhyOQ%3D"
},
*/
    private String id;      //数据ID
    private String relationId;  //关系ID
    private String equipmentUUID;  //设备ID
    private String dateString;  //日期
    private String errorImgOSSKey;  //错误图片OSSKey
    private String smallErrorImgUrl;  //小图URL地址
    private String middleErrorImgUrl;  //中图URL地址
    private String errorImgUrl;  //原图URL地址
    private String createTime;  //创建时间


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public String getEquipmentUUID() {
        return equipmentUUID;
    }

    public void setEquipmentUUID(String equipmentUUID) {
        this.equipmentUUID = equipmentUUID;
    }

    public String getDateString() {
        return dateString;
    }

    public void setDateString(String dateString) {
        this.dateString = dateString;
    }

    public String getErrorImgOSSKey() {
        return errorImgOSSKey;
    }

    public void setErrorImgOSSKey(String errorImgOSSKey) {
        this.errorImgOSSKey = errorImgOSSKey;
    }

    public String getSmallErrorImgUrl() {
        return smallErrorImgUrl;
    }

    public void setSmallErrorImgUrl(String smallErrorImgUrl) {
        this.smallErrorImgUrl = smallErrorImgUrl;
    }

    public String getMiddleErrorImgUrl() {
        return middleErrorImgUrl;
    }

    public void setMiddleErrorImgUrl(String middleErrorImgUrl) {
        this.middleErrorImgUrl = middleErrorImgUrl;
    }

    public String getErrorImgUrl() {
        return errorImgUrl;
    }

    public void setErrorImgUrl(String errorImgUrl) {
        this.errorImgUrl = errorImgUrl;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
}
