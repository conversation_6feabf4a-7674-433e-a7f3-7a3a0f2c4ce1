package com.czur.cloud.util

import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.provider.OpenableColumns
import android.webkit.MimeTypeMap
import androidx.activity.result.ActivityResultLauncher
import com.blankj.utilcode.util.FileUtils
import com.czur.cloud.R
import com.czur.cloud.ui.eshare.EShareActivity
import com.czur.cloud.ui.eshare.engine.Constants
import com.czur.cloud.ui.eshare.engine.Constants.FILE_NOT_FOUND_EXCEPTION
import com.czur.cloud.ui.eshare.myenum.TransmitFileResultEnum
import com.czur.czurutils.log.logE
import java.io.File
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.InputStream
import java.io.OutputStream
import java.math.BigInteger
import java.nio.channels.Channels
import java.security.MessageDigest


object CzurFileUtils {
//    @SuppressLint("Range")
//    private fun getFileName(context: Context, uri: Uri): String? {
//        var displayName: String? = null
//        val cursor: Cursor? = context.contentResolver.query(uri, null, null, null, null)
//        if (cursor != null && cursor.moveToFirst()) {
//            displayName = cursor.getString(cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME))
//            cursor.close()
//        }
//        return displayName
//    }

    var md5WorkFlag = true//可以改变这个值来让md5计算工作停止
    fun getFileSize(context: Context, uri: Uri): String? {
        try {
            var size: String? = null
            val cursor: Cursor? = context.contentResolver.query(uri, null, null, null, null)
            if (cursor != null && cursor.moveToFirst()) {
                val sizeIndex = cursor.getColumnIndex(OpenableColumns.SIZE)
                if (!cursor.isNull(sizeIndex)) {
                    size = cursor.getString(sizeIndex)
                }
                cursor.close()
            }
            return size
        }catch (e: Exception){
            return "0"
        }

    }

    fun getFileMD5(fileUri: Uri?, context: Context): String? {
        md5WorkFlag = true
        var result: String? = ""
        val digest = MessageDigest.getInstance("MD5")
        var input: InputStream? = null
        try {
            input = fileUri?.let { context.contentResolver.openInputStream(it) }

            val buffer = ByteArray(8192)
            var read: Int
            if (input != null) {
                while (input.read(buffer).also { read = it } > 0 && md5WorkFlag) {
                    digest.update(buffer, 0, read)
                }
            }
            val md5sum = digest.digest()
            val bigInt = BigInteger(1, md5sum)
            result = bigInt.toString(16)
            result = String.format("%32s", result).replace(' ', '0')
        } catch (e: Exception) {
            e.printStackTrace()
            return if (e is FileNotFoundException) {
                TransmitFileResultEnum.CODE_9529.code.toString()
            } else {
                TransmitFileResultEnum.CODE_9528.code.toString()
            }
        } finally {
            input?.close()
        }
        return result
    }

    fun getInputStreamMD5(input: FileInputStream): String? {
        var digest: MessageDigest? = null
        val buffer = ByteArray(1024)
        var len: Int
        try {
            digest = MessageDigest.getInstance("MD5")
            while (input.read(buffer, 0, 1024).also { len = it } != -1) {
                digest.update(buffer, 0, len)
            }
            input.close()
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
        return bytesToHexString(digest.digest())
    }

    fun getFileMD5(file: File): String? {
        if (!file.isFile) {
            return null
        }
        var digest: MessageDigest? = null
        var `in`: FileInputStream? = null
        val buffer = ByteArray(1024)
        var len: Int
        try {
            digest = MessageDigest.getInstance("MD5")
            `in` = FileInputStream(file)
            while (`in`.read(buffer, 0, 1024).also { len = it } != -1) {
                digest.update(buffer, 0, len)
            }
            `in`.close()
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
        return bytesToHexString(digest.digest())
    }

    @SuppressLint("Range")
    fun getFileName(context: Context, uri: Uri): String {

        try {
            var result: String = ""
            if (uri.scheme == "content") {
                val cursor: Cursor? = context.contentResolver.query(uri, null, null, null, null)
                cursor.use { cursor ->
                    if (cursor != null && cursor.moveToFirst()) {
                        result = cursor.getString(cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME))
                    }
                }
            }
            if (result == null) {// 有可能空
                result = uri.path.toString()
                val cut = result.lastIndexOf('/')
                if (cut != -1) {
                    result = result.substring(cut + 1)
                }
            }
            return result
        }catch (e:IllegalArgumentException) {
            return FILE_NOT_FOUND_EXCEPTION
        }
        catch (e: Exception)
        {
            return ""
        }


    }

    fun openDocumentToPick(
        selectFileResultLauncher: ActivityResultLauncher<Intent>,
        eShareActivity: EShareActivity? = null
    ) {
//        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
        val intent = Intent(Intent.ACTION_GET_CONTENT)
        intent.addCategory(Intent.CATEGORY_OPENABLE)
        intent.type = "*/*"
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
//        eShareActivity?.startActivityForResult(intent,10001111)
        selectFileResultLauncher.launch(intent)
    }

    fun isFileExists(context: Context, fileUri: Uri): String {
        return try {
            val inputStream = context.contentResolver.openInputStream(fileUri)
            inputStream?.close()
            "" // 文件存在
        } catch (e: FileNotFoundException) {

            e.printStackTrace()// 文件不存在
            context.getString(R.string.transmit_file_not_find)
        } catch (e: SecurityException) {
            e.printStackTrace()
            context.getString(R.string.transmit_file_not_find)
        } catch (e: Exception) {
            e.printStackTrace()
            context.getString(R.string.transmit_file_error)
        }
    }


    /**
     * 把多个文件碎片合并成一个文件
     */
    fun mergeFiles(
        context: Context,
        parts: List<File>,
        outputFileName: String,
    ): Boolean {

        return try {

            val os: OutputStream = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val resolver = context.contentResolver
                val contentValues = ContentValues().apply {
                    put(MediaStore.MediaColumns.DISPLAY_NAME, outputFileName)
                    put(
                        MediaStore.MediaColumns.MIME_TYPE,
                       CzurFileUtils.getMimeType(outputFileName)
                    )
                    put(
                        MediaStore.MediaColumns.RELATIVE_PATH,
                        "${Environment.DIRECTORY_DOWNLOADS}/${createDownloadCzurWMAFolder(context)}"
                    )
                }
                val contentUri = MediaStore.Downloads.EXTERNAL_CONTENT_URI
                val uri = resolver.insert(contentUri, contentValues)
                resolver.openOutputStream(uri!!)!!
            } else {
                val downloadsDir =
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
                val outputFile =
                    File("${downloadsDir}/${createDownloadCzurWMAFolder(context)}", outputFileName)
                FileOutputStream(outputFile)
            }
            for (file in parts) {
            }
            Channels.newChannel(os).use { outChannel ->
                for (file in parts) {
                    FileInputStream(file).channel.use { inChannel ->
                        inChannel.transferTo(0, inChannel.size(), outChannel)
                    }
                }
            }

            // 删除已经合并的文件碎片 循环parts删除
            for (part in parts) {
                FileUtils.delete(part)
            }
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }

    }

    fun createDownloadCzurWMAFolder(context: Context): String {
        val file = File(context.filesDir, Constants.CZUR_SHARE_FOLDER)
        if (!file.exists()) {
            file.mkdir()
        }
        return if (createDownloadFolder(context, Constants.CZUR_SHARE_FOLDER)) {
            Constants.CZUR_SHARE_FOLDER
        } else {
            ""
        }
    }

    fun createDownloadFolder(context: Context, folderName: String): Boolean {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val downloadsCollection = MediaStore.Downloads.EXTERNAL_CONTENT_URI
                val projection = arrayOf(MediaStore.MediaColumns.RELATIVE_PATH)
                val selection = "${MediaStore.MediaColumns.RELATIVE_PATH}=?"
                val selectionArgs = arrayOf(
                    "${Environment.DIRECTORY_DOWNLOADS}/${folderName}"
                )
                context.contentResolver.query(
                    downloadsCollection,
                    projection,
                    selection,
                    selectionArgs,
                    null
                )?.use { cursor ->
                    // 如果已经存在，那么不创建
                    if (cursor.moveToFirst()) {
                        return true
                    }
                } ?: run {
                    return false
                }

                val resolver = context.contentResolver
                val contentValues = ContentValues().apply {
                    put(MediaStore.MediaColumns.DISPLAY_NAME, folderName)
                    put(MediaStore.MediaColumns.MIME_TYPE, "vnd.android.document/directory")
                    put(
                        MediaStore.MediaColumns.RELATIVE_PATH,
                        "${Environment.DIRECTORY_DOWNLOADS}/${folderName}"
                    )
                }

                resolver.insert(downloadsCollection, contentValues)
                return true
            } else {
                val downloadDir =
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
                        .toString()
                val newDir = File(downloadDir, folderName)
                if (newDir.exists()) {
                    return false
//                Toast.makeText(context, "Folder already exists", Toast.LENGTH_SHORT).show()
                } else {
                    newDir.mkdir()
                    return true
                }
            }

        } catch (e: Exception) {
            logE("创建CzurWMA文件夹失败 createDownloadFolder error:${e.message}")
            return false
        }


    }


    fun getMimeType(filePath: String): String {
        var type: String? = null
        val extension = MimeTypeMap.getFileExtensionFromUrl(filePath)
        if (extension != null) {
            type = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension.toLowerCase())
        }
        return type ?: "application/octet-stream"
    }

    fun getMimeTypeFromFile(file: File): String {
        return getMimeType(file.absolutePath)
    }


}