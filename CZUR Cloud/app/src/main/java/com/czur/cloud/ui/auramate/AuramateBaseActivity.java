package com.czur.cloud.ui.auramate;

import android.app.Activity;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.czur.cloud.event.aurahome.ATCheckDeviceIsOnlineEvent;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.popup.AuraMatePCPopup;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * 这个页面用于处理海外版本 插拔usb的消息弹窗
 */
public abstract class AuramateBaseActivity extends BaseActivity {
    //统一接收设备id
    protected String equipmentId;
    private AuraMatePCPopup pcPopup;
    protected boolean hasShowPcPopup;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        equipmentId = getIntent().getStringExtra("equipmentId");
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    protected abstract boolean PCNeedFinish();


    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        if (pcPopup != null) {
            pcPopup.cancel();
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(ATCheckDeviceIsOnlineEvent event) {
        switch (event.getEventType()) {
            case MODE_CHANGE:
                showPCModeDialog(event,PCNeedFinish());
                break;
        }
    }

    protected void showPCModeDialog(ATCheckDeviceIsOnlineEvent onlineEvent,boolean needFinish) {
        Activity activity = ActivityUtils.getTopActivity();
        if (activity != null && activity.getClass() != this.getClass()) {
            return;
        }
        if (!onlineEvent.getStatusBean().getDevice_mode().equals("USB-PC")) {
            if (pcPopup != null && !PCNeedFinish()) {
                pcPopup.cancel();
            }
            return;
        }

        if (!TextUtils.isEmpty(equipmentId) && !onlineEvent.getDeviceUdid().equals(equipmentId)) {
            return;
        }

        if (hasShowPcPopup) {
            return;
        }

        if (pcPopup == null) {
            pcPopup = new AuraMatePCPopup.Builder(this)
                    .setOnPositiveListener(new AuraMatePCPopup.Builder.OnEnsureClickListener() {
                        @Override
                        public void onEnsureClick() {
                            if (needFinish) {
                                if (AuramateBaseActivity.this instanceof AuraMateRemoteVideoActivity) {
                                    if (ActivityUtils.isActivityExistsInStack(AuraMateMissedCallActivity.class)) {
                                        ActivityUtils.finishActivity(AuramateBaseActivity.this);
                                    } else {
                                        if (ActivityUtils.isActivityExistsInStack(AuraMateActivity.class)) {
                                            ActivityUtils.finishToActivity(AuraMateActivity.class, false);
                                        } else {
                                            ActivityUtils.finishActivity(AuramateBaseActivity.this);
                                        }
                                    }
                                } else {
                                    if (ActivityUtils.isActivityExistsInStack(AuraMateActivity.class)) {
                                        ActivityUtils.finishToActivity(AuraMateActivity.class, false);
                                    } else {
                                        ActivityUtils.finishActivity(AuramateBaseActivity.this);
                                    }
                                }
                            }
                        }
                    }).create();

            if (!needFinish) {
                pcPopup.setOnCancelListener(new DialogInterface.OnCancelListener() {
                    @Override
                    public void onCancel(DialogInterface dialog) {
                        hasShowPcPopup = false;
                    }
                });
            }
        }
        if (!isFinishing()) {
            pcPopup.show();
            hasShowPcPopup = true;
        }
    }
}