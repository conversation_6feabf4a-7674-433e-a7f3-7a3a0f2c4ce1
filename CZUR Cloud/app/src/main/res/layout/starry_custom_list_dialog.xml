<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="240dp"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_gravity="center"
    android:background="@drawable/btn_rec_5_bg_with_white">

    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/starryMargin10"
        android:gravity="center"
        android:text="@string/starry_common_dialog_alert_title"
        android:textColor="@color/black_22"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/guideline2"
        />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_begin="50dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycleList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintHeight_default="wrap"
        app:layout_constraintHeight_max="400dp"
        android:layout_below="@+id/dialog_title"
        android:background="@color/starry_white_bg"
        android:overScrollMode="never"
        android:padding="@dimen/starryMargin10"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/guideline3"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guideline2"
        app:layout_constraintVertical_bias="0.0" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_end="50dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/gray_d8d8d8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/guideline3"
        />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/guideline3"        >

        <TextView
            android:id="@+id/negative_button"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/blue_positive_button"
            android:textSize="14sp"
            android:text="@string/starry_common_dialog_no" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/gray_e5" />

        <TextView
            android:id="@+id/positive_button"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/blue_positive_button"
            android:textSize="14sp"
            android:text="@string/starry_common_dialog_ok" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>