package com.czur.cloud.ui.starry.meeting.utils.meet

import com.czur.cloud.ui.starry.meeting.common.MsgProcessor
import com.czur.cloud.ui.starry.meeting.common.StreamType

/**
 * Created by 陈丰尧 on 2021/5/27
 * 用来操作会议流的工具类
 * 自己/远程的流, 切断/打开
 */

/**
 * 静音/打开自己的流
 * @param stream: 流的类型 video/audio/all
 * @param inUse: 是否启用这个流: true 启用; false 停掉
 * 这个方法不会真正的去操作声网的流, 而是向长连接发送数据,所有对流的操作,依赖于长连接的成员列表更新
 *  注:向长连接发送更新数据后, 长连接会立刻回一个成员列表更新的数据包
 * 操作本地流->将消息通知给长连接->长连接返回更新后的用户列表->根据最新的用户列表更新流的状态
 * @see [updateStreamStatus]
 */
fun switchSelf(stream: StreamType, inUse: Boolean) {
    when (stream) {
        StreamType.VIDEO -> MsgProcessor.syncAudioVideoStatus(videoInUse = inUse)
        StreamType.AUDIO -> MsgProcessor.syncAudioVideoStatus(audioInUse = inUse)
        StreamType.ALL -> MsgProcessor.syncAudioVideoStatus(inUse, inUse)
    }
}


/**
 * 静音/打开远程的流
 * @param stream: 流的类型 video/audio/all
 * @param inUse: 是否启用这个流: true 启用; false 停掉
 * @param target: 要操作的视频流,如果不传递该参数,则是要操作所有流
 */
fun switchRemote(stream: StreamType, inUse: Boolean, target: String? = null) {
    MsgProcessor.requestChangeRemoteStreamStatus(stream, inUse, target)
}