package com.czur.cloud.ui.starry.livedatabus

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import java.lang.ref.WeakReference

open class SafeMutableLiveData<T> : MutableLiveData<T>() {

//    private var lastLifecycleOwner: WeakReference<LifecycleOwner>? = null
//
//    fun safeObserve(owner: LifecycleOwner, observer: Observer<T>) {
//        lastLifecycleOwner?.get()?.let {
//            removeObservers(it)
//        }
//        lastLifecycleOwner = WeakReference(owner)
//        observe(owner, observer)
//    }

    private var weakLifecycleOwner: WeakReference<LifecycleOwner>? = null

    override fun observe(owner: LifecycleOwner, observer: Observer<in T>) {
        weakLifecycleOwner?.get()?.let {
            removeObservers(it)
        }
        weakLifecycleOwner = WeakReference(owner)
        super.observe(owner, observer)
    }

    override fun setValue(value: T) {
        try {
            super.setValue(value)
        } catch (e: Exception) {
            super.postValue(value)
        }
    }
}