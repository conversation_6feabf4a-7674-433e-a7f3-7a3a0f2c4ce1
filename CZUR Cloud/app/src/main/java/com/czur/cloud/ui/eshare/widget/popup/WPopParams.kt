package com.czur.czurwma.widget.popup

import android.content.Context
import android.view.View
import android.view.ViewGroup
import com.czur.cloud.R
import java.lang.ref.WeakReference

data class WPopParams(
    val layoutRes: Int, // 布局
    val activity: Context, // activity
    var isDim: Boolean = false,  // 是否半透明
    var dimValue: Float = 0.4f, // 半透明属性
    var cancelable: Boolean = true, // 点击外部可以dismiss
    var width: Int = ViewGroup.LayoutParams.WRAP_CONTENT,
    var height: Int = ViewGroup.LayoutParams.WRAP_CONTENT,
    val focusable: Boolean = true,
    val isShowShadow: Boolean = false // 需要xml中设置android:elevation="10px",同时这个属性变为true
) {
    var commonPopMargin = 1
    var longClickView: WeakReference<View>? = null   //长按点击事件的View
    var animRes = R.style.animAlpha    // 动画
}