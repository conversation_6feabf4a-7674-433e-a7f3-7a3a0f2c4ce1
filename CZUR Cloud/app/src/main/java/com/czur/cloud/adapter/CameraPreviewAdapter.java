package com.czur.cloud.adapter;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.entity.realm.PageEntity;
import com.czur.cloud.ui.camera.CameraActivity;
import com.czur.cloud.util.ScreenAdaptationUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class CameraPreviewAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_NORMA = 0;
    private CameraActivity mActivity;
    private List<PageEntity> datas;
    private boolean isListAdd;
    private int position;
    private boolean isVivoNotch = ScreenAdaptationUtils.hasLiuHaiInVivo();
    private int height = 0;
    private int width = 0;

    /**
     * 构造方法
     */
    public CameraPreviewAdapter(CameraActivity activity, List<PageEntity> entities) {
        this.mActivity = activity;
        isListAdd = false;
        if (entities != null) {
            this.datas = entities;
        } else {
            this.datas = new ArrayList<>();
        }

    }

    public void refreshData(List<PageEntity> entities, boolean isListAdd,int position) {
        this.isListAdd = isListAdd;
        this.datas = entities;
        this.position = position;
        notifyDataSetChanged();


    }

    public void setBackgroundHW(int width,int height){
        this.height = height;
        this.width = width;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (viewType == ITEM_TYPE_NORMA) {
            View view;
            if (isVivoNotch) {
                view = LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.item_camera_preview_vivo_notch, parent, false);
            } else {
                view = LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.item_camera_preview, parent, false);
            }
            return new NormalViewHolder(view);
        } else {
            return null;
        }
    }


    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {

        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(holder.getAdapterPosition());
            ViewGroup.LayoutParams layoutParams = mHolder.cameraPreviewItemRl.getLayoutParams();
//            int remainHeight = ScreenUtils.getScreenHeight() - ScreenUtils.getScreenWidth() * 4 / 3 - SizeUtils.dp2px(86);
            int remainHeight = height - width * 4 / 3 - SizeUtils.dp2px(86);
            layoutParams.height = remainHeight;
            layoutParams.width = remainHeight * 34 / 39;
            mHolder.cameraPreviewItemRl.setLayoutParams(layoutParams);

            if (isListAdd && holder.getAdapterPosition() == this.position) {
                addItemAnim(mHolder);
            }
            mHolder.cameraPageNumTv.setText(mHolder.mItem.getPageNum()+"");
            mHolder.cameraPreviewItemImg.refreshDrawableState();
            mHolder.cameraPreviewItemImg.setImageBitmap(ImageUtils.getBitmap(mHolder.mItem.getSmallPicUrl()));

            mHolder.cameraPreviewItemImg.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(mHolder.mItem, holder.getAdapterPosition());
                    }
                }

            });


        }

    }

    /**
     * @des: 条目添加动画
     * @params:
     * @return:
     */

    private void addItemAnim(NormalViewHolder mHolder) {

        AnimatorSet animatorSet = new AnimatorSet();//组合动画
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(mHolder.cameraPreviewItemImg, "scaleX", 0, 1.1f, 1.0f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(mHolder.cameraPreviewItemImg, "scaleY", 0, 1.1f, 1.0f);
        animatorSet.setDuration(600);
        animatorSet.setInterpolator(new DecelerateInterpolator());
        animatorSet.play(scaleX).with(scaleY);//两个动画同时开始
        animatorSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
            }

            @Override
            public void onAnimationStart(Animator animation) {
                super.onAnimationStart(animation);
            }
        });
        animatorSet.start();
    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }


    private class NormalViewHolder extends ViewHolder {
        public final View mView;
        PageEntity mItem;
        ImageView cameraPreviewItemImg;
        RelativeLayout cameraPreviewItemRl;
        TextView cameraPageNumTv;

        NormalViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            cameraPreviewItemImg = (ImageView) itemView.findViewById(R.id.camera_preview_item_img);
             cameraPageNumTv = (TextView) itemView.findViewById(R.id.camera_preview_page_num_tv);
            cameraPreviewItemRl = (RelativeLayout) itemView.findViewById(R.id.item_camera_rl);
        }


    }


    private OnItemClickListener onItemClickListener;

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(PageEntity PageEntity, int position);
    }


}
