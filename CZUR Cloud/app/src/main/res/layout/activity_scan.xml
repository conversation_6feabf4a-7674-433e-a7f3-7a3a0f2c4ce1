<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <RelativeLayout
        android:id="@+id/scan_top_bar"
        android:layout_width="match_parent"
        android:background="@color/black_2a"
        android:layout_height="44dp">

        <ImageView
            android:id="@+id/scan_back_btn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="9dp"
            android:padding="10dp"
            android:src="@mipmap/white_back_icon" />


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="18sp"
            android:textStyle="bold"
            android:text="@string/scan_title"
            android:layout_centerInParent="true"
            android:textColor="@color/white"/>

    </RelativeLayout>

    <cn.bingoogolapple.qrcode.zbar.ZBarView
        android:layout_below="@+id/scan_top_bar"
        android:id="@+id/zxingview"
        android:layout_height="match_parent"
        android:layout_width="match_parent"
        app:qrcv_animTime="1000"
        app:qrcv_borderColor="@color/whiteOpaque50"
        app:qrcv_borderSize="1dp"
        app:qrcv_cornerColor="@color/blue_29b0d7"
        app:qrcv_cornerLength="20dp"
        app:qrcv_cornerSize="3dp"
        app:qrcv_isOnlyDecodeScanBoxArea="true"
        app:qrcv_isScanLineReverse="true"
        app:qrcv_customGridScanLineDrawable="@drawable/scan_bg"
        app:qrcv_isShowDefaultGridScanLineDrawable="true"
        app:qrcv_isShowTipBackground="true"
        app:qrcv_isShowTipTextAsSingleLine="false"
        app:qrcv_isTipTextBelowRect="true"
        app:qrcv_maskColor="@color/blackOpaque40"
        app:qrcv_qrCodeTipText="@string/scan_text"
        app:qrcv_rectWidth="250dp"
        app:qrcv_scanLineColor="@color/blue_29b0d7"
        app:qrcv_scanLineMargin="0dp"
        app:qrcv_scanLineSize="2dp"
        app:qrcv_tipTextColor="@color/gary_93"
        app:qrcv_tipTextSize="15sp"
        app:qrcv_tipTextMargin="25dp"
        app:qrcv_toolbarHeight="44dp"
        app:qrcv_tipBackgroundColor="@color/transparent" />

</RelativeLayout>