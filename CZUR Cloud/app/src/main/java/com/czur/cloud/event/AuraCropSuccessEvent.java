package com.czur.cloud.event;

import com.czur.cloud.model.AuraCropModel;

public class AuraCropSuccessEvent extends BaseEvent {
    private AuraCropModel cropModel;
    private int position;
    private boolean isFolder;

    public AuraCropSuccessEvent(EventType eventType, int position, boolean isFolder, AuraCropModel cropModel) {
        super(eventType);
        this.isFolder=isFolder;
        this.position = position;
        this.cropModel = cropModel;
    }

    public boolean isFolder() {
        return isFolder;
    }
    public AuraCropModel getCropModel() {
        return cropModel;
    }

    public int getPosition() {
        return position;
    }


    @Override
    public boolean match(Object obj) {
        return true;
    }
}
