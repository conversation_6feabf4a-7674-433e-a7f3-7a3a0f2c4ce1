package com.czur.cloud.ui.auramate;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.czur.cloud.R;
import com.czur.cloud.util.validator.Validator;


public class AuraMateFragmentAttacher {
    private static final String TAG = "AuraMateFragmentAttacher";
    public static String INDEX_FRAGMENT_NAME = "aura_home_fragment_name";
    private FragmentManager fm;
    private String device, relationId;

    public AuraMateFragmentAttacher(FragmentManager fm, String device, String relationId) {
        this.fm = fm;
        this.device = device;
        this.relationId = relationId;

    }

    public Fragment showFragment(int willShowFragmentIndex, int currentShowFragmentIndex) {
        Fragment currentShowFragment = getFragment(currentShowFragmentIndex);
        Fragment willShowFragment = getFragment(willShowFragmentIndex);
        FragmentTransaction trx = fm.beginTransaction();
        if (willShowFragmentIndex != currentShowFragmentIndex) {

            if (Validator.isNotEmpty(currentShowFragment)) {
                trx.hide(currentShowFragment);
            } else {
            }

            if (Validator.isEmpty(willShowFragment)) {
                willShowFragment = createFragment(willShowFragmentIndex);
            } else {
            }

            if (!willShowFragment.isAdded()) {
                trx.add(R.id.aura_home_frameLayout, willShowFragment, INDEX_FRAGMENT_NAME + willShowFragmentIndex);
            } else {
            }
            trx.show(willShowFragment);
            trx.commit();
            return willShowFragment;
        }
        return willShowFragment;
    }

    public Fragment showFragment(int willShowFragmentIndex) {
        Fragment willShowFragment = getFragment(willShowFragmentIndex);
        FragmentTransaction trx = fm.beginTransaction();

        if (Validator.isEmpty(willShowFragment)) {
            willShowFragment = createFragment(willShowFragmentIndex);
        }

        if (!willShowFragment.isAdded()) {
            trx.add(R.id.aura_home_frameLayout, willShowFragment, INDEX_FRAGMENT_NAME + willShowFragmentIndex);
        } else {
        }

        trx.show(willShowFragment);
        trx.commit();
        return willShowFragment;
    }

    public Fragment createFragment(int index) {
        Fragment fragment = null;
        switch (index) {
            case 0:
                fragment = AuraMateFragment.newInstance(device);
                break;
            case 1:
                fragment = AuraMateRecordMoviesFragment.newInstance(device, relationId);
                break;
            case 2:
                fragment = AuraMateFilesFragment.newInstance(device, relationId);
                break;
        }
        return fragment;
    }

    public Fragment getFragment(int index) {
        return fm.findFragmentByTag(INDEX_FRAGMENT_NAME + index);
    }
}
