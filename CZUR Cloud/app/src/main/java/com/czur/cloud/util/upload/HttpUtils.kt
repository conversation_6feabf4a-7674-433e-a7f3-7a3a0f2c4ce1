package com.czur.czurutils.upload

import android.content.Context
import android.util.Log
import com.alibaba.sdk.android.oss.ClientConfiguration
import com.alibaba.sdk.android.oss.ClientException
import com.alibaba.sdk.android.oss.OSSClient
import com.alibaba.sdk.android.oss.ServiceException
import com.alibaba.sdk.android.oss.common.auth.OSSStsTokenCredentialProvider
import com.alibaba.sdk.android.oss.model.ObjectMetadata
import com.alibaba.sdk.android.oss.model.PutObjectRequest
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.Call
import okhttp3.FormBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import okio.IOException
import java.io.File


object HttpUtils {

    private const val STATUS_SUCCESS = 1000

//    const val BASE_URL_DEBUG: String = "https://test.czur.cc/api/"
//    const val BASE_URL_RELEASE: String = "https://cn.czur.cc/api/"
//    val BASE_URL = if (BuildConfig.DEBUG) BASE_URL_DEBUG else BASE_URL_RELEASE
    private var BASE_URL: String = ""

//    const val OSS_ENDPOINT = "oss-cn-beijing.aliyuncs.com"
    private var OSS_ENDPOINT: String = ""

//    const val STARRY_LOG_BUCKET = "changer-resource"
    private var LOG_BUCKET: String = ""

//    const val STARRY_LOG_TOKEN_API = "v3/public/oss/token"
    private var LOG_TOKEN_API: String = ""

    // 初始化 upload参数
    fun init(baseUrl:String,
             ossEndPoint: String,
             logBucket: String,
             logTokenApi: String
    ){
        BASE_URL = baseUrl
        OSS_ENDPOINT = ossEndPoint
        LOG_BUCKET = logBucket
        LOG_TOKEN_API = logTokenApi
    }

    // 获取OSS Token
    suspend fun getOssInfo(clientId: String): OssTokenModel? {

        if (BASE_URL == "" ||
                OSS_ENDPOINT == "" ||
                LOG_BUCKET == "" ||
                LOG_TOKEN_API == ""){
            return null
        }

//      val url = "https://test.czur.cc/api/v3/public/oss/token"
        val url = "$BASE_URL/$LOG_TOKEN_API"
        val client = OkHttpClient()
        val body: RequestBody = FormBody.Builder()
            .add("clientId", clientId)
            .build()
        val request: Request = Request.Builder()
            .post(body)
            .url(url)
            .build()
        Log.i("HttpUtils", "getSittingOssInfo.request=${request}")

        val call: Call = client.newCall(request)
        try {
            val response: Response = withContext(Dispatchers.IO) {
                call.execute()
            }

            val responseString = response.body?.string() ?: ""

            Log.i("HttpUtils", "getSittingOssInfo.responseString=${responseString}")
            if (responseString.trim().startsWith("<html>")
                || responseString.trim().endsWith("</html>")
            ) {
                return null
            }

            val baseModel: BaseModel = Gson().fromJson(responseString, BaseModel::class.java)
            if (response.isSuccessful) {
                val code = baseModel.code
                if (code == STATUS_SUCCESS) {
                    return baseModel.body
                }
            } else {
                return null
            }
        } catch (e: IOException) {
            e.printStackTrace()
        }
        return null
    }

    // 文件上传，使用已经取得的osstoken
    fun upload(context: Context, ossModel: OssTokenModel?, sourcePath: String, ossPath: String): Boolean {
        var retFlag = false
        val credentialProvider = OSSStsTokenCredentialProvider(
            ossModel?.AccessKeyId,
            ossModel?.AccessKeySecret,
            ossModel?.SecurityToken)
        val conf = ClientConfiguration()
        // 连接超时，默认15秒
        conf.connectionTimeout = 15 * 1000
        // socket超时，默认15秒
        conf.socketTimeout = 15 * 1000
        // 最大并发请求数，默认5个
        conf.maxConcurrentRequest = 10
        // 失败后最大重试次数，默认2次
        conf.maxErrorRetry = 2

        val ossClient = OSSClient(context, OSS_ENDPOINT, credentialProvider, conf)

        val put = PutObjectRequest(LOG_BUCKET, ossPath, sourcePath)

        // 设置文件元信息为可选操作。
        val metadata = ObjectMetadata()
        // metadata.setContentType("application/octet-stream"); // 设置content-type。
        // metadata.setContentMD5(BinaryUtil.calculateBase64Md5(uploadFilePath)); // 校验MD5。
        // 设置object的访问权限为私有
        // metadata.setContentType("application/octet-stream"); // 设置content-type。
        // metadata.setContentMD5(BinaryUtil.calculateBase64Md5(uploadFilePath)); // 校验MD5。
        // 设置object的访问权限为私有
        metadata.setHeader("x-oss-object-acl", "private")
        // 设置object的归档类型为标准存储
        // 设置object的归档类型为标准存储
        metadata.setHeader("x-oss-storage-class", "Standard")
        // 设置覆盖同名目标Object
        // metadata.setHeader("x-oss-forbid-overwrite", "true");
        // 指定Object的对象标签，可同时设置多个标签。
        // metadata.setHeader("x-oss-tagging", "a:1");
        // 指定OSS创建目标Object时使用的服务器端加密算法 。
        // metadata.setHeader("x-oss-server-side-encryption", "AES256");
        // 表示KMS托管的用户主密钥，该参数仅在x-oss-server-side-encryption为KMS时有效。
        // metadata.setHeader("x-oss-server-side-encryption-key-id", "9468da86-3509-4f8d-a61e-6eab1eac****");
        // 设置覆盖同名目标Object
        // metadata.setHeader("x-oss-forbid-overwrite", "true");
        // 指定Object的对象标签，可同时设置多个标签。
        // metadata.setHeader("x-oss-tagging", "a:1");
        // 指定OSS创建目标Object时使用的服务器端加密算法 。
        // metadata.setHeader("x-oss-server-side-encryption", "AES256");
        // 表示KMS托管的用户主密钥，该参数仅在x-oss-server-side-encryption为KMS时有效。
        // metadata.setHeader("x-oss-server-side-encryption-key-id", "9468da86-3509-4f8d-a61e-6eab1eac****");
//        put.metadata = metadata

        //上传文件。
        retFlag = try {
            ossClient.putObject(put)
            true
        } catch (e: ClientException) {
            // 本地异常如网络异常等
            e.printStackTrace()
            false
        } catch (e: ServiceException) {
            // 服务异常
            Log.e("HttpUtils", "uploadLogFile.RequestId" + e.requestId)
            Log.e("HttpUtils", "uploadLogFile.ErrorCode" + e.errorCode)
            Log.e("HttpUtils", "uploadLogFile.HostId" + e.hostId)
            Log.e("HttpUtils", "uploadLogFile.RawMessage" + e.rawMessage)
            false
        }
        Log.i("HttpUtils", "uploadLogFile.retFlag=$retFlag")

        // 上传完成，删除本地zip文件
        val zipFile = File(sourcePath)
        val b = zipFile.delete()
//        Log.i("HttpUtils", "uploadLogFile.sourcePath=$sourcePath is deleted!${b}")

        return retFlag
    }
}