package com.czur.cloud.ui.auramate;

import static androidx.annotation.Dimension.DP;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.Group;
import androidx.core.content.ContextCompat;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.common.ShareSDKCallback;
import com.czur.cloud.common.ShareSDKParams;
import com.czur.cloud.common.ShareSDKPlatforms;
import com.czur.cloud.common.ShareSDKType;
import com.czur.cloud.common.ShareSDKUtils;
import com.czur.cloud.entity.realm.OcrEntity;
import com.czur.cloud.event.AuraCropSuccessEvent;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.OriginalProgressEvent;
import com.czur.cloud.event.SwitchAuraFlattenEvent;
import com.czur.cloud.event.SwitchAuraMateColorEvent;
import com.czur.cloud.event.UserInfoEvent;
import com.czur.cloud.model.AuraCropModel;
import com.czur.cloud.model.AuraFileTotalModel;
import com.czur.cloud.model.AuraMateColorModel;
import com.czur.cloud.model.AuraResultModel;
import com.czur.cloud.model.ShareModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.reportfragment.ReportUtil;
import com.czur.cloud.ui.component.cropper.CropImage;
import com.czur.cloud.ui.component.dialog.SocialShareDialog;
import com.czur.cloud.ui.component.popup.EtBottomColorDialogPopup;
import com.czur.cloud.ui.component.popup.EtBottomDialogPopup;
import com.czur.cloud.util.CzurFrescoHelper;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.PermissionUtil;
import com.czur.cloud.util.share.FileUtil;
import com.czur.cloud.util.share.ShareContentType;
import com.czur.cloud.util.share.ShareUtils;
import com.czur.cloud.util.validator.Validator;
import com.davemorrissey.labs.subscaleview.SubsamplingScaleImageView;
import com.shuyu.frescoutil.listener.LoadFrescoListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

import io.realm.Realm;

public class AuraMatePreviewActivity extends AuramateBaseActivity implements View.OnClickListener {
    private ImageView etPreviewBackBtn;
    private ImageView etPreviewMoreBtn;
    private TextView etPreviewTitle1;
    private TextView etPreviewTitle2;
    private View etPreviewEdtBtn;
    private View etPreviewSaveBtn;
    private View etPreviewOcrBtn;
    private View etPreviewShareBtn;
    private ViewPager viewPager;
    private ImagePagerAdapter mAdapter;
    private HashMap<Integer, SubsamplingScaleImageView> viewMap = new HashMap<>();
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private String folderId;
    private String seqNum;
    private int currentItem;
    private List<AuraFileTotalModel.FileListBean> fileList;
    private int mode;
    private boolean isFolder;
    private EtBottomDialogPopup etBottomDialogPopup;
    private TextView etBottomSheetSingleTv;
    private TextView etBottomSheetSurfacesTv;
    private ImageView etBottomSheetSingleImg;
    private ImageView etBottomSheetSurfacesImg;
    //    private MiaoHttpEntity<FlattenImageModel> flattenImage;
    private Group originalGroup;
    private TextView etPreviewOriginalTv;
    private static final int SHARE_SUCCESS_CODE = 666;
    private long time;
    private String size;
    private boolean isUpdateProgress;
    private boolean isFlattenRun = true;
    private boolean isCacheBtn;
    private SocialShareDialog socialShareDialog;
    private boolean canBack;
    private String url;
    private String ownerId;

    private SimpleDateFormat formatter;
    private ImageView auraHomeWrongTagBtn;
    private ImageView auraHomeWrongTagFlagImg;
    private TextView auraHomeWrongTagTv;

    private EtBottomColorDialogPopup etBottomColorDialogPopup;
    private int color;
    private int beforeColor;
    private boolean isColorRun = true;
    private View auraHomePreviewColorBtn;


    private TextView etBottomSheetAutoTv;
    private ImageView etBottomSheetAutoImg;
    private TextView etBottomSheetColorTv;
    private ImageView etBottomSheetColorImg;
    private TextView etBottomSheetCardTv;
    private ImageView etBottomSheetCardImg;
    private TextView etBottomSheetGrayTv;
    private ImageView etBottomSheetGrayImg;
    private TextView etBottomSheetBlackTv;
    private ImageView etBottomSheetBlackImg;
    private TextView etBottomSheetNoneTv;
    private ImageView etBottomSheetNoneImg;
    private TextView etBottomSheetWhiteTv;
    private ImageView etBottomSheetWhiteImg;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.black_2a);
        BarUtils.setNavBarColor(this, getColor(R.color.black_2a));
        BarUtils.setStatusBarLightMode(this, false);
        setContentView(R.layout.activity_aura_home_preview);
        initComponent();
        createBottomSheetDialog();
        createBottomColorSheetDialog();
        initViewPager();
        registerEvent();
        getPreviewList();
    }

    @Override
    public boolean PCNeedFinish() {
        return false;
    }

    private void createBottomSheetDialog() {
        EtBottomDialogPopup.Builder builder = new EtBottomDialogPopup.Builder(AuraMatePreviewActivity.this, onBottomSheetClickListener);
        etBottomDialogPopup = builder.create();
        Window window = etBottomDialogPopup.getWindow();
        if (window != null) {
            etBottomSheetSingleTv = (TextView) window.findViewById(R.id.et_bottom_sheet_single_tv);
            etBottomSheetSurfacesTv = (TextView) window.findViewById(R.id.et_bottom_sheet_surfaces_tv);
            etBottomSheetSingleImg = (ImageView) window.findViewById(R.id.et_bottom_sheet_single_img);
            etBottomSheetSurfacesImg = (ImageView) window.findViewById(R.id.et_bottom_sheet_surfaces_img);
            socialShareDialog = new SocialShareDialog(this, shareDialogOnClickListener);
        }
    }


    private void initComponent() {
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
        ownerId = getIntent().getStringExtra("ownerId");
        size = getIntent().getStringExtra("size");
        folderId = getIntent().getStringExtra("folderId");
        seqNum = getIntent().getStringExtra("seqNum");
        String date = getIntent().getStringExtra("date");
        String[] arr = date.split("\\s+");
        isFolder = !folderId.equals("root");
        etPreviewBackBtn = (ImageView) findViewById(R.id.aura_home_preview_back_btn);
        etPreviewMoreBtn = (ImageView) findViewById(R.id.aura_home_preview_more_btn);
        etPreviewTitle1 = (TextView) findViewById(R.id.aura_home_preview_title_1);
        etPreviewTitle2 = (TextView) findViewById(R.id.aura_home_preview_title_2);
        etPreviewEdtBtn = (View) findViewById(R.id.aura_home_preview_edt_btn);
        etPreviewSaveBtn = (View) findViewById(R.id.aura_home_preview_save_btn);
        etPreviewOcrBtn = (View) findViewById(R.id.aura_home_preview_ocr_btn);
        etPreviewShareBtn = (View) findViewById(R.id.aura_home_preview_share_btn);
        viewPager = findViewById(R.id.aura_home_preview_viewpager);
        originalGroup = (Group) findViewById(R.id.aura_original_group);
        etPreviewOriginalTv = (TextView) findViewById(R.id.aura_home_preview_original_tv);
        auraHomePreviewColorBtn = (View) findViewById(R.id.aura_home_preview_color_btn);


        auraHomeWrongTagBtn = (ImageView) findViewById(R.id.aura_home_wrong_tag_btn);
        auraHomeWrongTagFlagImg = (ImageView) findViewById(R.id.aura_home_wrong_tag_flag_img);
        auraHomeWrongTagTv = (TextView) findViewById(R.id.aura_home_wrong_tag_tv);


        etPreviewOriginalTv.setText(String.format(getString(R.string.et_original), size));
        etPreviewTitle1.setText(arr[0]);
        etPreviewTitle2.setText(arr[1]);
        fileList = new ArrayList<>();
    }

    private void registerEvent() {
        viewPager.addOnPageChangeListener(viewPageListener);
        etPreviewMoreBtn.setOnClickListener(this);
        etPreviewBackBtn.setOnClickListener(this);
        etPreviewSaveBtn.setOnClickListener(this);
        etPreviewOcrBtn.setOnClickListener(this);
        etPreviewEdtBtn.setOnClickListener(this);
        etPreviewShareBtn.setOnClickListener(this);
        etPreviewOriginalTv.setOnClickListener(this);
        auraHomePreviewColorBtn.setOnClickListener(this);

        auraHomeWrongTagBtn.setOnClickListener(this);
        auraHomeWrongTagFlagImg.setOnClickListener(this);
        auraHomeWrongTagTv.setOnClickListener(this);
        setNetListener();
    }

    private void createBottomColorSheetDialog() {
        EtBottomColorDialogPopup.Builder builder = new EtBottomColorDialogPopup.Builder(AuraMatePreviewActivity.this, onBottomSheetClickListener1);
        etBottomColorDialogPopup = builder.create();
        Window window = etBottomColorDialogPopup.getWindow();
        if (window != null) {
            etBottomSheetAutoTv = (TextView) window.findViewById(R.id.et_bottom_sheet_auto_tv);
            etBottomSheetAutoImg = (ImageView) window.findViewById(R.id.et_bottom_sheet_auto_img);

            etBottomSheetColorTv = (TextView) window.findViewById(R.id.et_bottom_sheet_color_tv);
            etBottomSheetColorImg = (ImageView) window.findViewById(R.id.et_bottom_sheet_color_img);

            etBottomSheetCardTv = (TextView) window.findViewById(R.id.et_bottom_sheet_card_tv);
            etBottomSheetCardImg = (ImageView) window.findViewById(R.id.et_bottom_sheet_card_img);

            etBottomSheetGrayTv = (TextView) window.findViewById(R.id.et_bottom_sheet_gray_tv);
            etBottomSheetGrayImg = (ImageView) window.findViewById(R.id.et_bottom_sheet_gray_img);

            etBottomSheetBlackTv = (TextView) window.findViewById(R.id.et_bottom_sheet_black_tv);
            etBottomSheetBlackImg = (ImageView) window.findViewById(R.id.et_bottom_sheet_black_img);

            etBottomSheetNoneTv = (TextView) window.findViewById(R.id.et_bottom_sheet_none_tv);
            etBottomSheetNoneImg = (ImageView) window.findViewById(R.id.et_bottom_sheet_none_img);

            etBottomSheetWhiteTv = (TextView) window.findViewById(R.id.et_bottom_sheet_white_tv);
            etBottomSheetWhiteImg = (ImageView) window.findViewById(R.id.et_bottom_sheet_white_img);
        }
    }

    private EtBottomColorDialogPopup.Builder.OnBottomSheetClickListener onBottomSheetClickListener1 = new EtBottomColorDialogPopup.Builder.OnBottomSheetClickListener() {
        @Override
        public void onClick(int viewId) {
            switch (viewId) {
                case R.id.et_bottom_sheet_auto_btn:
                    setColor(viewPager.getCurrentItem());
                    beforeColor = color;
                    color = 5;
                    if (NetworkUtils.isConnected()) {
                        switchColor();
                    } else {
                        showMessage(R.string.toast_no_connection_network);
                    }
                    etBottomColorDialogPopup.dismiss();
                    break;
                case R.id.et_bottom_sheet_color_btn:
                    beforeColor = color;
                    color = 1;
                    if (NetworkUtils.isConnected()) {
                        switchColor();
                    } else {
                        showMessage(R.string.toast_no_connection_network);
                    }
                    etBottomColorDialogPopup.dismiss();
                    break;
                case R.id.et_bottom_sheet_card_btn:
                    beforeColor = color;
                    color = 4;
                    if (NetworkUtils.isConnected()) {
                        switchColor();
                    } else {
                        showMessage(R.string.toast_no_connection_network);
                    }
                    etBottomColorDialogPopup.dismiss();
                    break;
                case R.id.et_bottom_sheet_gray_btn:
                    beforeColor = color;
                    color = 2;
                    if (NetworkUtils.isConnected()) {
                        switchColor();
                    } else {
                        showMessage(R.string.toast_no_connection_network);
                    }
                    etBottomColorDialogPopup.dismiss();
                    break;
                case R.id.et_bottom_sheet_black_btn:
                    beforeColor = color;
                    color = 3;
                    if (NetworkUtils.isConnected()) {
                        switchColor();
                    } else {
                        showMessage(R.string.toast_no_connection_network);
                    }
                    etBottomColorDialogPopup.dismiss();
                    break;
                case R.id.et_bottom_sheet_white_btn:
                    beforeColor = color;
                    color = 6;
                    if (NetworkUtils.isConnected()) {
                        switchColor();
                    } else {
                        showMessage(R.string.toast_no_connection_network);
                    }
                    etBottomColorDialogPopup.dismiss();
                    break;
                case R.id.et_bottom_sheet_none_btn:
                    beforeColor = color;
                    color = 0;
                    if (NetworkUtils.isConnected()) {
                        switchColor();
                    } else {
                        showMessage(R.string.toast_no_connection_network);
                    }
                    etBottomColorDialogPopup.dismiss();
                    break;
                case R.id.et_preview_bottom_sheet_cancel_btn:
                    etBottomColorDialogPopup.dismiss();
                    break;
                default:
                    break;
            }
        }
    };

    private void switchColor() {
        time = System.currentTimeMillis();
        showProgressDialog(true, false);
        isColorRun = true;

        new Thread(() -> {
            try {
                MiaoHttpEntity<AuraMateColorModel> entity = HttpManager.getInstance().request().getAuraMateColorImage(fileList.get(viewPager.getCurrentItem()).getId(), color + "" + "", userPreferences.getUserId(), ownerId, AuraMateColorModel.class);
                if (entity == null) {
                    colorFailed(false);
                } else if (MiaoHttpManager.STATUS_SUCCESS == entity.getCode()) {
                    switchColorSuccess(entity);
                } else if (MiaoHttpManager.STATUS_IMAGE_PROCESS_ERROR == entity.getCode()) {
                    switchColorFailed(entity, false);
                } else {
                    while (isColorRun) {
                        MiaoHttpEntity<AuraMateColorModel> resultEntity = HttpManager.getInstance().request().getAuraMateColorImageResponse(fileList.get(viewPager.getCurrentItem()).getId(), color + "", userPreferences.getUserId(), ownerId, AuraMateColorModel.class);
                        if (resultEntity == null) {
                            colorFailed(false);
                        } else if (MiaoHttpManager.STATUS_SUCCESS == resultEntity.getCode()) {
                            switchColorSuccess(resultEntity);
                        } else if (MiaoHttpManager.STATUS_IMAGE_PROCESS_ERROR == resultEntity.getCode()) {
                            switchColorFailed(resultEntity, false);
                        } else {
                            if (System.currentTimeMillis() - time >= 60000) {
                                switchColorFailed(resultEntity, true);
                            }
                        }
                        Thread.sleep(1000);
                    }
                }
            } catch (Exception e) {
                colorFailed(false);
            }
        }).start();

    }

    /**
     * 根据用户选择的模式判断真正的模式
     */
    private void setColor(int position) {
        color = fileList.get(position).getColorMode();

    }

    /**
     * 根据用户选择的模式判断真正的模式
     */
    private void setBeforeColor(int position) {
        beforeColor = fileList.get(position).getColorMode();
    }

    /**
     * 切换失败
     */
    private void colorFailed(final boolean isTimeOut) {
        runOnUiThread(() -> {
            isColorRun = false;
            hideProgressDialog();
            if (isTimeOut) {
                showMessage(R.string.image_browse_flatten_timeout);
            } else {
                showMessage(R.string.image_browse_flatten_failed_alert);
            }
            color = beforeColor;
            fileList.get(viewPager.getCurrentItem()).setColorMode(color);
            refreshViewPager();
        });
    }

    /**
     * 切换失败
     */
    private void switchColorFailed(final MiaoHttpEntity<AuraMateColorModel> entity, final boolean isTimeOut) {
        runOnUiThread(() -> {
            if (entity.getCode() == MiaoHttpManager.STATUS_FlATTEN_ERROR) {
                beforeColor = color;
                isColorRun = false;
                hideProgressDialog();
                EventBus.getDefault().post(new SwitchAuraMateColorEvent(EventType.AURA_SWITCH_COLOR_FAILED, viewPager.getCurrentItem(), isFolder, entity.getBody()));
                fileList.get(viewPager.getCurrentItem()).setUserSelectMode(mode);
                fileList.get(viewPager.getCurrentItem()).setBig(entity.getBody().getUrl());
                fileList.get(viewPager.getCurrentItem()).setSingle(entity.getBody().getUrl());
                fileList.get(viewPager.getCurrentItem()).setSingleKey(entity.getBody().getOssKey());
                fileList.get(viewPager.getCurrentItem()).setMiddleSingle(entity.getBody().getOssMiddleKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setMiddle(entity.getBody().getOssMiddleKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setFileSize(entity.getBody().getFileSize().intValue());
                refreshViewPager();
            } else {
                colorFailed(isTimeOut);
            }
        });
    }

    /**
     * 切换模式
     */
    private void switchColorSuccess(final MiaoHttpEntity<AuraMateColorModel> entity) {
        runOnUiThread(() -> {
            Realm realm = Realm.getDefaultInstance();
            realm.executeTransaction(realm1 -> {
                OcrEntity ocrEntity = realm1.where(OcrEntity.class).equalTo("pageId", fileList.get(viewPager.getCurrentItem()).getId()).findFirst();
                if (Validator.isNotEmpty(ocrEntity)) {
                    if (Validator.isNotEmpty(ocrEntity.getOcrContent())) {
                        OcrEntity ocrEntity1 = realm1.createObject(OcrEntity.class, entity.getBody().getId());
                        ocrEntity1.setOcrContent(ocrEntity.getOcrContent());
                        realm1.insertOrUpdate(ocrEntity1);
                    }
                }
                if (Validator.isNotEmpty(ocrEntity)) {
                    ocrEntity.deleteFromRealm();
                }

            });

            beforeColor = color;
            isColorRun = false;
            EventBus.getDefault().post(new SwitchAuraMateColorEvent(EventType.AURA_SWITCH_COLOR_SUCCESS, viewPager.getCurrentItem(), isFolder, entity.getBody()));
            hideProgressDialog();
            fileList.get(viewPager.getCurrentItem()).setColorMode(color);
            fileList.get(viewPager.getCurrentItem()).setUserSelectMode(mode);
            fileList.get(viewPager.getCurrentItem()).setBig(entity.getBody().getUrl());
            fileList.get(viewPager.getCurrentItem()).setSingle(entity.getBody().getUrl());
            fileList.get(viewPager.getCurrentItem()).setMiddleSingle(entity.getBody().getOssMiddleKeyUrl());
            fileList.get(viewPager.getCurrentItem()).setSingleKey(entity.getBody().getOssKey());
            fileList.get(viewPager.getCurrentItem()).setMiddle(entity.getBody().getOssMiddleKeyUrl());
            fileList.get(viewPager.getCurrentItem()).setFileSize(entity.getBody().getFileSize().intValue());
            refreshViewPager();
            realm.close();
        });

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case ORIGINAL_PROGRESS:
                if (isUpdateProgress) {
                    OriginalProgressEvent originalProgressEvent = (OriginalProgressEvent) event;
                    String text = ((int) originalProgressEvent.getProgress() / 100) + "%";
                    etPreviewOriginalTv.setText(text);
                }
                break;

            case AURA_CROP_SUCCESS:
                AuraCropSuccessEvent cropSuccessEvent = (AuraCropSuccessEvent) event;
//                int cropSuccessEventPosition = cropSuccessEvent.getPosition();
                AuraCropModel cropModel = cropSuccessEvent.getCropModel();
                fileList.get(viewPager.getCurrentItem()).setMiddle(cropModel.getOssMiddleKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setSmall(cropModel.getOssSmallKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setBig(cropModel.getOssKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setSingleKey(cropModel.getOssKey());
                fileList.get(viewPager.getCurrentItem()).setSingle(cropModel.getOssKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setBook(cropModel.getOssKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setMiddleSingle(cropModel.getOssMiddleKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setMiddleBook(cropModel.getOssMiddleKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setSmallSingle(cropModel.getOssSmallKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setSmallBook(cropModel.getOssSmallKeyUrl());

                fileList.get(viewPager.getCurrentItem()).setFileSize(Integer.parseInt(cropModel.getFileSize()));
                refreshViewPager();

                break;
            default:
                break;
        }
    }

    /**
     * 通知系统相册更新
     */
    private void noticeAlbumUpdate(String sdPicPath) {
//        File sdPicFile = FileUtils.getFileByPath(sdPicPath);
//        Uri contentUri = Uri.fromFile(sdPicFile);
//        MediaScannerConnection.scanFile(AuraMatePreviewActivity.this,
//                new String[]{sdPicFile.getAbsolutePath()}, new String[]{"image/jpeg"},
//                (path, uri) -> {
//                });
        MediaScannerConnection.scanFile(AuraMatePreviewActivity.this,
                new String[]{sdPicPath},
                new String[]{"image/jpeg"},
                (path,url)->{});
    }

    private void initViewPager() {
        mAdapter = new ImagePagerAdapter();
        viewPager.setPageMargin(10 * DP);
        viewPager.setAdapter(mAdapter);
        viewPager.setOffscreenPageLimit(3);
        try {
            Field mFirstLayout = ViewPager.class.getDeclaredField("mFirstLayout");
            mFirstLayout.setAccessible(true);
            mFirstLayout.set(viewPager, true);
            viewPager.setCurrentItem(currentItem);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private ViewPager.OnPageChangeListener viewPageListener = new ViewPager.OnPageChangeListener() {
        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
        }

        @Override
        public void onPageSelected(int position) {
            currentItem = position;
            size = EtUtils.convertOriginalSize(fileList.get(position).getFileSize());
            Date date = new Date(Long.parseLong(fileList.get(position).getTakeOn()));
            String localeDate = formatter.format(date);
            String[] arr = localeDate.split("\\s+");
            etPreviewTitle1.setText(arr[0]);
            etPreviewTitle2.setText(arr[1]);
            if (CzurFrescoHelper.isCached(AuraMatePreviewActivity.this, Uri.parse(getBigUrl(position)))) {
                isCacheBtn = true;
                setOriginalBtnGone();
            } else {
                isCacheBtn = false;
                setOriginalBtnVisible();
            }
        }

        @Override
        public void onPageScrollStateChanged(int state) {
        }
    };

    private String getBigUrl(int position) {
        return fileList.get(position).getBig();
    }

    private String getBigShareKey(int position) {
        return fileList.get(position).getSingleKey();
    }

    private String getMiddleUrl(int position) {
        return fileList.get(position).getMiddle();
    }

    public class ImagePagerAdapter extends PagerAdapter {
        @Override
        public int getCount() {
            return fileList.size();
        }

        @NotNull
        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            SubsamplingScaleImageView zoomImage = initZoomFrescoImageView(position);
            container.addView(zoomImage);
            setMode(position);
            setColor(position);
            setBigImage(getBigUrl(position), position);
            if (viewPager.getCurrentItem() == position) {
                size = EtUtils.convertOriginalSize(fileList.get(position).getFileSize());
                Date date = new Date(Long.parseLong(fileList.get(position).getTakeOn()));
                String localeDate = formatter.format(date);
                String[] arr = localeDate.split("\\s+");
                etPreviewTitle1.setText(arr[0]);
                etPreviewTitle2.setText(arr[1]);
                if (CzurFrescoHelper.isCached(AuraMatePreviewActivity.this, Uri.parse(getBigUrl(position)))) {
                    isCacheBtn = true;
                    setOriginalBtnGone();
                } else {
                    isCacheBtn = false;
                    setOriginalBtnVisible();
                }
            }
            return zoomImage;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, @NotNull Object object) {
            container.removeView((View) object);
            SubsamplingScaleImageView zoomImage = viewMap.get(position);
            if (zoomImage != null) {
                viewMap.remove(position);
            }
        }


        @Override
        public boolean isViewFromObject(@NotNull View view, @NotNull Object object) {
            return view == object;
        }

        @Override
        public int getItemPosition(@NotNull Object object) {
            return POSITION_NONE;
        }

    }

    public SubsamplingScaleImageView initZoomFrescoImageView(int position) {
        SubsamplingScaleImageView zoomImage = viewMap.get(position);
        if (zoomImage == null) {
            zoomImage = setImageToIndex(position);
        }
        return zoomImage;
    }


    private SubsamplingScaleImageView setImageToIndex(final int index) {
        SubsamplingScaleImageView zoomImage = new SubsamplingScaleImageView(this);
        viewMap.put(index, zoomImage);
        return zoomImage;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.aura_home_preview_color_btn:
                if (checkNetwork()) break;
                setColor(viewPager.getCurrentItem());
                setBeforeColor(viewPager.getCurrentItem());
                changeColorMode();
                etBottomColorDialogPopup.show();
                break;
            case R.id.aura_home_wrong_tag_btn:
            case R.id.aura_home_wrong_tag_flag_img:
            case R.id.aura_home_wrong_tag_tv:
                Intent intent2 = new Intent(AuraMatePreviewActivity.this, EditQuestionTagActivity.class);
                intent2.putExtra("equipmentId", equipmentId);
                intent2.putExtra("ownerId", ownerId);
                intent2.putExtra("isFolder", isFolder);
                intent2.putExtra("position", viewPager.getCurrentItem());
                intent2.putExtra("image", fileList.get(viewPager.getCurrentItem()));
                if (isCacheBtn) {
                    intent2.putExtra("hasBig", true);
                } else {
                    intent2.putExtra("hasBig", false);
                }
                intent2.putExtra("equipmentId", equipmentId);
                ActivityUtils.startActivity(intent2);
                break;
            case R.id.aura_home_preview_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.aura_home_preview_more_btn:
                if (checkNetwork()) break;
                setMode(viewPager.getCurrentItem());
                switch (mode) {
                    case 1:
                        etBottomSheetSingleTv.setTextColor(ContextCompat.getColor(this, R.color.blue_29b0d7));
                        etBottomSheetSingleImg.setVisibility(View.VISIBLE);
                        etBottomSheetSurfacesTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                        etBottomSheetSurfacesImg.setVisibility(View.GONE);
                        break;
                    case 2:
                        etBottomSheetSingleTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                        etBottomSheetSingleImg.setVisibility(View.GONE);
                        etBottomSheetSurfacesTv.setTextColor(ContextCompat.getColor(this, R.color.blue_29b0d7));
                        etBottomSheetSurfacesImg.setVisibility(View.VISIBLE);
                        break;
                    default:
                        break;
                }
                etBottomDialogPopup.show();
                break;
            case R.id.aura_home_preview_edt_btn:
                if (checkNetwork()) break;
                Intent intent = new Intent(AuraMatePreviewActivity.this, AuraCropImageActivity.class);
                intent.putExtra("equipmentId", equipmentId);
                intent.putExtra("ownerId", ownerId);
                intent.putExtra("isFolder", isFolder);
                intent.putExtra("position", viewPager.getCurrentItem());
                intent.putExtra("image", fileList.get(viewPager.getCurrentItem()));
                if (isCacheBtn) {
                    intent.putExtra("hasBig", true);
                } else {
                    intent.putExtra("hasBig", false);
                }
                startActivityForResult(intent, CropImage.CROP_IMAGE_ACTIVITY_REQUEST_CODE);
                break;
            case R.id.aura_home_preview_save_btn:
                if (checkNetwork()) break;
                requestCopyToSdPermission(false);
                break;
            case R.id.aura_home_preview_share_btn:
                if (checkNetwork()) break;
                if (BuildConfig.IS_OVERSEAS) {
                    requestCopyToSdPermission(true);
                } else {
                    this.getShareImageUrl();
                }
                break;
            case R.id.aura_home_preview_ocr_btn:
                if (checkNetwork()) break;
                Intent intent1 = new Intent(AuraMatePreviewActivity.this, AuraMateOcrActivity.class);
                intent1.putExtra("fileId", fileList.get(viewPager.getCurrentItem()).getId());
                intent1.putExtra("isFolder", isFolder);
                intent1.putExtra("position", viewPager.getCurrentItem());
                intent1.putExtra("mode", fileList.get(viewPager.getCurrentItem()).getUserSelectMode() + "");
                if (isCacheBtn) {
                    intent1.putExtra("url", getBigUrl(viewPager.getCurrentItem()));
                } else {
                    intent1.putExtra("url", getMiddleUrl(viewPager.getCurrentItem()));
                }
                startActivity(intent1);
                break;
            case R.id.aura_home_preview_original_tv:
                if (checkNetwork()) break;
                showOriginal();
                break;
            default:
                break;
        }
    }

    public boolean checkNetwork() {
        boolean isNoNetwork = hasNetwork();
        return isNoNetwork || fileList.size() <= 0;
    }

    public boolean hasNetwork() {
        if (fileList.size() <= 0 && !NetworkUtils.isConnected()) {
            showMessage(R.string.toast_no_connection_network);
            return true;
        }
        return false;
    }


    public void showShare(int viewId) {
        ShareSDKParams params = new ShareSDKParams();
        params.setImageUrl(url).setSite(getString(R.string.app_name));

        switch (viewId) {
            case R.id.weixin_share:
                params.setShareType(ShareSDKType.SHARE_IMAGE).setUrl(url).setPlatform(ShareSDKPlatforms.WECHAT);
                break;
            case R.id.friend_share:
                params.setShareType(ShareSDKType.SHARE_IMAGE).setUrl(url).setPlatform(ShareSDKPlatforms.WECHAT_MOMENTS);
                break;
            case R.id.qq_share:
                params.setPlatform(ShareSDKPlatforms.QQ);
                break;
            case R.id.qq_zone_share:
                params.setTitleUrl(url).setTitle(getString(R.string.share_title)).setPlatform(ShareSDKPlatforms.QZONE);
                break;
            case R.id.weibo_share:
                String text =this.getResources().getString(R.string.share) ;
                params.setText(text );
                params.setPlatform(ShareSDKPlatforms.WEIBO);
                break;
            default:
                return;
        }
        params.setCallback(new ShareSDKCallback() {
            @Override
            public void onComplete() {
//                showMessage(R.string.share_success);
            }

            @Override
            public void onError() {
                showMessage(R.string.share_failed);
            }

            @Override
            public void onCancel() {
            }
        });
        ShareSDKUtils.INSTANCE.share(params);
    }

    /**
     * 得到分享图片接口
     */
    private void getShareImageUrl() {
        httpManager.request().auraShare(userPreferences.getUserId(), getBigShareKey(viewPager.getCurrentItem()), ShareModel.class, new MiaoHttpManager.Callback<ShareModel>() {
            @Override
            public void onStart() {
                showProgressDialog(true);
            }

            @Override
            public void onResponse(MiaoHttpEntity<ShareModel> entity) {
                hideProgressDialog();
                url = entity.getBody().getUrl();
                socialShareDialog.show();
            }

            @Override
            public void onFailure(MiaoHttpEntity<ShareModel> entity) {
                hideProgressDialog();
                showMessage(R.string.share_failed);
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                showMessage(R.string.share_failed);
            }
        });
    }

    /**
     * 根据用户选择的模式判断真正的模式
     */
    private void setMode(int position) {
        int userSelectMode = fileList.get(position).getUserSelectMode();
        if (userSelectMode == 0) {
            mode = fileList.get(position).getMode();
        } else {
            mode = fileList.get(position).getUserSelectMode();
        }
    }

    /**
     * 保存至相册
     */
    private void saveToAlbum(final Bitmap bitmap, boolean isShare) {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<String>() {
            @Override
            public String doInBackground() {
//                String path = Environment.getExternalStorageDirectory() + CZURConstants.SD_PATH + CZURConstants.AURA_MATE_PATH;
//                String path = CZURConstants.SD_PATH + CZURConstants.AURA_MATE_PATH;
                String path = CZURConstants.PICTURE_PATH + CZURConstants.AURA_MATE_PATH;
                String etPath = "";
                if (FileUtils.createOrExistsDir(path)) {
                    etPath = path + UUID.randomUUID() + CZURConstants.JPG;
                    ImageUtils.save(bitmap, etPath, Bitmap.CompressFormat.JPEG, false);
                }
                return etPath;
            }

            @Override
            public void onSuccess(String result) {
                hideProgressDialog();
                if (isShare) {
                    new ShareUtils.Builder(AuraMatePreviewActivity.this)
                            .setOnActivityResult(SHARE_SUCCESS_CODE)
                            // 指定分享的文件类型
                            .setContentType(ShareContentType.IMAGE)
                            // 设置要分享的文件 Uri
                            .setShareFileUri(FileUtil.getFileUri(AuraMatePreviewActivity.this, ShareContentType.FILE, new File(result)))
                            // 设置分享选择器的标题
                            .setTitle(getString(R.string.share_to))
                            .build()
                            // 发起分享
                            .shareBySystem();
                } else {
                    noticeAlbumUpdate(result);
                    showMessage(R.string.et_save_success);
                }
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                hideProgressDialog();
                if (!isShare) {
                    showMessage(R.string.et_save_failed);
                }
            }
        });
    }

    /**
     * 得到展示列表接口
     */
    private void getPreviewList() {
        httpManager.request().getAuraFiles(equipmentId, folderId, seqNum, 30 + "", ownerId, userPreferences.getUserId(), AuraFileTotalModel.class, new MiaoHttpManager.Callback<AuraFileTotalModel>() {
            @Override
            public void onStart() {
                showProgressDialog(true);
            }

            @Override
            public void onResponse(MiaoHttpEntity<AuraFileTotalModel> entity) {
                hideProgressDialog();
                int offset = entity.getBody().getOffset();
                if (offset != 0) {
                    currentItem = offset - 1;
                }
                fileList = entity.getBody().getFileList();
                refreshViewPager();
            }

            @Override
            public void onFailure(MiaoHttpEntity<AuraFileTotalModel> entity) {
                canBack = true;
                hideProgressDialog();
                if (entity.getCode() != MiaoHttpManager.IMG_NO_FOUND) {
                    showMessage(R.string.request_failed_alert);
                }
            }

            @Override
            public void onError(Exception e) {
                canBack = true;
                showMessage(R.string.request_failed_alert);
            }
        });
    }

//    private AuraFileTotalModel.FileListBean getItem() {
//        return fileList.get(viewPager.getCurrentItem());
//    }

    /**
     * 显示原图接口
     */
    private void showOriginal() {
        isUpdateProgress = true;
        String text = 0 + "%";
        etPreviewOriginalTv.setText(text);
        displayOriginalImage(getBigUrl(viewPager.getCurrentItem()));
    }

    /**
     * 显示原图
     */
    private void displayOriginalImage(final String url) {
        SubsamplingScaleImageView bigImageView = viewMap.get(viewPager.getCurrentItem());
        if (bigImageView != null) {
            isUpdateProgress = false;
            String text = 100 + "%";
            etPreviewOriginalTv.setText(text);
            originalGroup.setVisibility(View.GONE);
            CzurFrescoHelper.loadBigImage(AuraMatePreviewActivity.this, bigImageView, url, R.mipmap.default_gallery_img, true);
        }
    }

    private void downloadOriginalPicture(String url, boolean isShare) {
        File file = CzurFrescoHelper.getCache(AuraMatePreviewActivity.this, Uri.parse(url));
        if (file != null && file.exists()) {
            Bitmap fileBitmap = ImageUtils.getBitmap(file);
            saveToAlbum(fileBitmap, isShare);
        } else {
            CzurFrescoHelper.getFrescoImg(AuraMatePreviewActivity.this, url, 0, 0, new LoadFrescoListener() {
                @Override
                public void onSuccess(Bitmap bitmap) {
                    File file = CzurFrescoHelper.getCache(AuraMatePreviewActivity.this, Uri.parse(url));
                    if (file != null && file.exists()) {
                        Bitmap fileBitmap = ImageUtils.getBitmap(file);
                        saveToAlbum(fileBitmap, isShare);
                    }
                }

                @Override
                public void onFail() {
                    hideProgressDialog();
                    showMessage(isShare ? R.string.share_failed : R.string.et_save_failed);
                }
            });
        }
    }

    /**
     * 先从缓存拿大图 拿不到直接加载中图
     */
    private void setBigImage(String bigUrl, final int position) {
        SubsamplingScaleImageView zoomImage = initZoomFrescoImageView(position);
        if (CzurFrescoHelper.isCached(this, Uri.parse(bigUrl))) {
            CzurFrescoHelper.loadBigImage(AuraMatePreviewActivity.this, zoomImage, bigUrl, R.mipmap.default_gallery_img, false);
        } else {
            String middleUrl = getMiddleUrl(position);
            CzurFrescoHelper.loadBigImage(AuraMatePreviewActivity.this, zoomImage, middleUrl, R.mipmap.default_gallery_img, false);
        }
    }

    private void setOriginalBtnVisible() {
        originalGroup.setVisibility(View.VISIBLE);
        etPreviewOriginalTv.setText(String.format(getString(R.string.et_original), size));
    }

    private void setOriginalBtnGone() {
        originalGroup.setVisibility(View.GONE);
        etPreviewOriginalTv.setVisibility(View.GONE);
    }

    /**
     * 申请权限
     */
    private void requestCopyToSdPermission(boolean isShare) {
        PermissionUtils.permission(PermissionUtil.getStoragePermission())
//        PermissionUtils.permission(PermissionUtil.getStoragePermission())
                .rationale((activity, shouldRequest) -> {
                    showMessage(isShare ? R.string.denied_share : R.string.denied_sdcard);
                    shouldRequest.again(true);
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(@NotNull List<String> permissionsGranted) {
                        showProgressDialog(true);
                        downloadOriginalPicture(getBigUrl(viewPager.getCurrentItem()), isShare);
                    }

                    @Override
                    public void onDenied(@NotNull List<String> permissionsDeniedForever,
                                         @NotNull List<String> permissionsDenied) {
                        showMessage(isShare ? R.string.denied_share : R.string.denied_sdcard);
                    }
                })
                .theme(ScreenUtils::setFullScreen)
                .request();
    }

    /**
     * 选择模式
     */
    private void switchFlatten() {
        time = System.currentTimeMillis();
        showProgressDialog(true, false);
        isFlattenRun = true;
        new Thread(() -> {
            try {
                MiaoHttpEntity<AuraResultModel> entity = HttpManager.getInstance().request().getAuraFlattenSync(fileList.get(viewPager.getCurrentItem()).getId(), mode + "" + "", userPreferences.getUserId(), AuraResultModel.class);
                if (entity == null) {
                    flattenFailed(false);
                } else if (MiaoHttpManager.STATUS_SUCCESS == entity.getCode()) {
                    switchFlattenSuccess(entity);
                } else if (MiaoHttpManager.STATUS_IMAGE_PROCESS_ERROR == entity.getCode()) {
                    switchFlattenFailed(entity, false);
                } else {
                    while (isFlattenRun) {
                        MiaoHttpEntity<AuraResultModel> resultEntity = HttpManager.getInstance().request().getAuraSyncFlattenResult(fileList.get(viewPager.getCurrentItem()).getId(), mode + "", userPreferences.getUserId(), AuraResultModel.class);
                        if (resultEntity == null) {
                            flattenFailed(false);
                        } else if (MiaoHttpManager.STATUS_SUCCESS == resultEntity.getCode()) {
                            switchFlattenSuccess(resultEntity);
                        } else if (MiaoHttpManager.STATUS_IMAGE_PROCESS_ERROR == resultEntity.getCode()) {
                            switchFlattenFailed(resultEntity, false);
                        } else {
                            if (System.currentTimeMillis() - time >= 20000) {
                                switchFlattenFailed(resultEntity, true);
                            }
                        }
                        Thread.sleep(1000);
                    }
                }
            } catch (Exception e) {
                flattenFailed(false);
            }
        }).start();
    }

    /**
     * 切换失败
     */
    private void flattenFailed(final boolean isTimeOut) {
        runOnUiThread(() -> {
            isFlattenRun = false;
            hideProgressDialog();
            if (isTimeOut) {
                showMessage(R.string.image_browse_flatten_timeout);
            } else {
                showMessage(R.string.image_browse_flatten_failed_alert);
            }
            mode = 3 - mode;
            fileList.get(viewPager.getCurrentItem()).setUserSelectMode(mode);
            refreshViewPager();
        });
    }

    /**
     * 切换失败
     */
    private void switchFlattenFailed(final MiaoHttpEntity<AuraResultModel> entity, final boolean isTimeOut) {
        runOnUiThread(() -> {
            if (entity.getCode() == MiaoHttpManager.STATUS_FlATTEN_ERROR) {
                isFlattenRun = false;
                hideProgressDialog();
                EventBus.getDefault().post(new SwitchAuraFlattenEvent(EventType.AURA_SWITCH_FLATTEN_FAILED, viewPager.getCurrentItem(), isFolder, entity.getBody(), mode));
                fileList.get(viewPager.getCurrentItem()).setMiddle(entity.getBody().getOssMiddleKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setSmall(entity.getBody().getOssSmallKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setBig(entity.getBody().getUrl());
                fileList.get(viewPager.getCurrentItem()).setSingle(entity.getBody().getUrl());
                fileList.get(viewPager.getCurrentItem()).setSingleKey(entity.getBody().getOssKey());
                fileList.get(viewPager.getCurrentItem()).setBook(entity.getBody().getUrl());
                fileList.get(viewPager.getCurrentItem()).setMiddleSingle(entity.getBody().getOssMiddleKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setMiddleBook(entity.getBody().getOssMiddleKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setSmallSingle(entity.getBody().getOssSmallKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setSmallBook(entity.getBody().getOssSmallKeyUrl());
                fileList.get(viewPager.getCurrentItem()).setUserSelectMode(mode);
                fileList.get(viewPager.getCurrentItem()).setFileSize(entity.getBody().getFileSize());
                refreshViewPager();
            } else {
                flattenFailed(isTimeOut);
            }
        });
    }

    /**
     * 切换模式
     */
    private void switchFlattenSuccess(final MiaoHttpEntity<AuraResultModel> entity) {
        runOnUiThread(() -> {
            isFlattenRun = false;
            EventBus.getDefault().post(new SwitchAuraFlattenEvent(EventType.AURA_SWITCH_FLATTEN_SUCCESS, viewPager.getCurrentItem(), isFolder, entity.getBody(), mode));
            hideProgressDialog();
            fileList.get(viewPager.getCurrentItem()).setMiddle(entity.getBody().getOssMiddleKeyUrl());
            fileList.get(viewPager.getCurrentItem()).setSmall(entity.getBody().getOssSmallKeyUrl());
            fileList.get(viewPager.getCurrentItem()).setBig(entity.getBody().getUrl());

            fileList.get(viewPager.getCurrentItem()).setSingle(entity.getBody().getUrl());
            fileList.get(viewPager.getCurrentItem()).setSingleKey(entity.getBody().getOssKey());
            fileList.get(viewPager.getCurrentItem()).setBook(entity.getBody().getUrl());
            fileList.get(viewPager.getCurrentItem()).setMiddleSingle(entity.getBody().getOssMiddleKeyUrl());
            fileList.get(viewPager.getCurrentItem()).setMiddleBook(entity.getBody().getOssMiddleKeyUrl());
            fileList.get(viewPager.getCurrentItem()).setSmallSingle(entity.getBody().getOssSmallKeyUrl());
            fileList.get(viewPager.getCurrentItem()).setSmallBook(entity.getBody().getOssSmallKeyUrl());

            fileList.get(viewPager.getCurrentItem()).setUserSelectMode(mode);
            fileList.get(viewPager.getCurrentItem()).setFileSize(entity.getBody().getFileSize());
            refreshViewPager();
        });
    }

    /**
     * 刷新ViewPager
     */
    private void refreshViewPager() {
        canBack = false;
        try {
            Field mFirstLayout = ViewPager.class.getDeclaredField("mFirstLayout");
            mFirstLayout.setAccessible(true);
            mFirstLayout.set(viewPager, true);
            mAdapter.notifyDataSetChanged();
            viewPager.setCurrentItem(currentItem);
        } catch (Exception e) {
            e.printStackTrace();
        }
        canBack = true;
    }

    /**
     * 切换模式dialog监听
     */
    private EtBottomDialogPopup.Builder.OnBottomSheetClickListener onBottomSheetClickListener = new EtBottomDialogPopup.Builder.OnBottomSheetClickListener() {
        @Override
        public void onClick(int viewId) {
            switch (viewId) {
                case R.id.et_bottom_sheet_single_btn:
                    mode = 1;
                    if (fileList.get(viewPager.getCurrentItem()).getUserSelectMode() != mode) {
                        if (NetworkUtils.isConnected()) {
                            switchFlatten();
                        } else {
                            showMessage(R.string.toast_no_connection_network);
                        }
                    }
                    etBottomDialogPopup.dismiss();
                    break;
                case R.id.et_bottom_sheet_surfaces_btn:
                    mode = 2;
                    if (fileList.get(viewPager.getCurrentItem()).getUserSelectMode() != mode) {
                        if (NetworkUtils.isConnected()) {
                            switchFlatten();
                        } else {
                            showMessage(R.string.toast_no_connection_network);
                        }
                    }
                    etBottomDialogPopup.dismiss();
                    break;
                case R.id.et_preview_bottom_sheet_cancel_btn:
                    etBottomDialogPopup.dismiss();
                    break;
                default:
                    break;
            }
        }
    };

    /**
     * 分享dialog监听
     */
    private SocialShareDialog.ShareDialogOnClickListener shareDialogOnClickListener = new SocialShareDialog.ShareDialogOnClickListener() {
        @Override
        public void onShareItemClick(int viewId) {
            Context cnt = AuraMatePreviewActivity.this;
            boolean isInstall = false;
            switch (viewId) {
                case R.id.weixin_share:
                case R.id.friend_share:
                    isInstall = ReportUtil.isWeixinInstalled(cnt);
                    if (isInstall) {
                        showShare(viewId);
                    }else{
                        showMessage(cnt.getResources().getString(R.string.share_not_install_wechat));
                    }
                    socialShareDialog.dismiss();
                    break;
                case R.id.qq_share:
                case R.id.qq_zone_share:
                    isInstall = ReportUtil.isQQClientInstalled(cnt);
                    if (isInstall) {
                        showShare(viewId);
                    }else{
                        showMessage(cnt.getResources().getString(R.string.share_not_install_qq));
                    }
                    socialShareDialog.dismiss();
                    break;
                case R.id.weibo_share:
                    isInstall = ReportUtil.isWeiboInstalled(cnt);
                    if (isInstall) {
                        showShare(viewId);
                    }else{
                        showMessage(cnt.getResources().getString(R.string.share_not_install_sinaweibo));
                    }
                    socialShareDialog.dismiss();
                    break;
                case R.id.share_dialog_cancel_btn:
                    socialShareDialog.dismiss();
                default:
                    break;
            }
        }
    };

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
//        if (requestCode == CropImage.CROP_IMAGE_ACTIVITY_REQUEST_CODE && data != null) {
//
//        } else
//        if (requestCode == SHARE_SUCCESS_CODE) {
//        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && !canBack) { //监控/拦截/屏蔽返回键
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().post(new UserInfoEvent(EventType.UPDATE_CACHE));
        isFlattenRun = false;
        super.onDestroy();
    }

    private void changeColorMode() {
        switch (color) {
            case 1:
                etBottomSheetAutoTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetAutoImg.setVisibility(View.GONE);

                etBottomSheetColorTv.setTextColor(ContextCompat.getColor(this, R.color.blue_29b0d7));
                etBottomSheetColorImg.setVisibility(View.VISIBLE);

                etBottomSheetCardTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetCardImg.setVisibility(View.GONE);

                etBottomSheetGrayTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetGrayImg.setVisibility(View.GONE);

                etBottomSheetBlackTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetBlackImg.setVisibility(View.GONE);

                etBottomSheetWhiteTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetWhiteImg.setVisibility(View.GONE);

                etBottomSheetNoneTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetNoneImg.setVisibility(View.GONE);
                break;
            case 2:
                etBottomSheetAutoTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetAutoImg.setVisibility(View.GONE);

                etBottomSheetColorTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetColorImg.setVisibility(View.GONE);

                etBottomSheetCardTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetCardImg.setVisibility(View.GONE);

                etBottomSheetGrayTv.setTextColor(ContextCompat.getColor(this, R.color.blue_29b0d7));
                etBottomSheetGrayImg.setVisibility(View.VISIBLE);

                etBottomSheetBlackTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetBlackImg.setVisibility(View.GONE);

                etBottomSheetWhiteTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetWhiteImg.setVisibility(View.GONE);

                etBottomSheetNoneTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetNoneImg.setVisibility(View.GONE);
                break;
            case 3:
                etBottomSheetAutoTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetAutoImg.setVisibility(View.GONE);

                etBottomSheetColorTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetColorImg.setVisibility(View.GONE);

                etBottomSheetCardTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetCardImg.setVisibility(View.GONE);

                etBottomSheetGrayTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetGrayImg.setVisibility(View.GONE);

                etBottomSheetBlackTv.setTextColor(ContextCompat.getColor(this, R.color.blue_29b0d7));
                etBottomSheetBlackImg.setVisibility(View.VISIBLE);

                etBottomSheetWhiteTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetWhiteImg.setVisibility(View.GONE);

                etBottomSheetNoneTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetNoneImg.setVisibility(View.GONE);
                break;
            case 4:
                etBottomSheetAutoTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetAutoImg.setVisibility(View.GONE);

                etBottomSheetColorTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetColorImg.setVisibility(View.GONE);

                etBottomSheetCardTv.setTextColor(ContextCompat.getColor(this, R.color.blue_29b0d7));
                etBottomSheetCardImg.setVisibility(View.VISIBLE);

                etBottomSheetGrayTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetGrayImg.setVisibility(View.GONE);

                etBottomSheetBlackTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetBlackImg.setVisibility(View.GONE);

                etBottomSheetWhiteTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetWhiteImg.setVisibility(View.GONE);

                etBottomSheetNoneTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetNoneImg.setVisibility(View.GONE);
                break;
            case 5:
                etBottomSheetAutoTv.setTextColor(ContextCompat.getColor(this, R.color.blue_29b0d7));
                etBottomSheetAutoImg.setVisibility(View.VISIBLE);

                etBottomSheetColorTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetColorImg.setVisibility(View.GONE);

                etBottomSheetCardTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetCardImg.setVisibility(View.GONE);

                etBottomSheetGrayTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetGrayImg.setVisibility(View.GONE);

                etBottomSheetBlackTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetBlackImg.setVisibility(View.GONE);

                etBottomSheetWhiteTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetWhiteImg.setVisibility(View.GONE);

                etBottomSheetNoneTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetNoneImg.setVisibility(View.GONE);
                break;

            case 6:
                etBottomSheetAutoTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetAutoImg.setVisibility(View.GONE);

                etBottomSheetColorTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetColorImg.setVisibility(View.GONE);

                etBottomSheetCardTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetCardImg.setVisibility(View.GONE);

                etBottomSheetGrayTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetGrayImg.setVisibility(View.GONE);

                etBottomSheetBlackTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetBlackImg.setVisibility(View.GONE);

                etBottomSheetWhiteTv.setTextColor(ContextCompat.getColor(this, R.color.blue_29b0d7));
                etBottomSheetWhiteImg.setVisibility(View.VISIBLE);

                etBottomSheetNoneTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetNoneImg.setVisibility(View.GONE);
                break;

            case 0:
                etBottomSheetAutoTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetAutoImg.setVisibility(View.GONE);

                etBottomSheetColorTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetColorImg.setVisibility(View.GONE);

                etBottomSheetCardTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetCardImg.setVisibility(View.GONE);

                etBottomSheetGrayTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetGrayImg.setVisibility(View.GONE);

                etBottomSheetBlackTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetBlackImg.setVisibility(View.GONE);

                etBottomSheetWhiteTv.setTextColor(ContextCompat.getColor(this, R.color.black_22));
                etBottomSheetWhiteImg.setVisibility(View.GONE);

                etBottomSheetNoneTv.setTextColor(ContextCompat.getColor(this, R.color.blue_29b0d7));
                etBottomSheetNoneImg.setVisibility(View.VISIBLE);
                break;
            default:
                break;
        }
    }
}
