package com.czur.cloud.ui.starry.activity

import android.content.*
import android.net.Uri
import android.os.Bundle
import android.os.PowerManager
import android.provider.Settings
import android.view.Gravity
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.*
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.event.BaseEvent
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryCheckMeetingListEvent
import com.czur.cloud.netty.observer.NettyService
import com.czur.cloud.preferences.FirstPreferences
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.ui.base.BaseActivity
import com.czur.cloud.ui.home.IndexActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.common.StarryConstants.RESULT_CHECK_OVERLAYS_CODE
import com.czur.cloud.ui.starry.frgment.StarryFragmentAttacher
import com.czur.cloud.ui.starry.livedatabus.BlankActivityForJoinMeeting
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.meeting.MeetingMainActivity
import com.czur.cloud.ui.starry.meeting.baselib.utils.getString
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.model.JoinMeetData
import com.czur.cloud.ui.starry.model.JoinMeetModel
import com.czur.cloud.ui.starry.model.StarryCallInModel
import com.czur.cloud.ui.starry.utils.RomUtils
import com.czur.cloud.ui.starry.utils.RomUtils.isBackgroundStartAllowed
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.ui.starry.viewmodel.RecentlyViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import com.czur.cloud.ui.user.UserBindPhoneActivity
import com.czur.cloud.util.AppClearUtils.startNettyStarryService
import com.czur.cloud.util.validator.Validator
import com.czur.czurutils.log.*
import kotlinx.android.synthetic.main.starry_activity_home.*
import kotlinx.android.synthetic.main.starry_fragment_meet.*
import kotlinx.android.synthetic.main.starry_fragment_recently.*
import kotlinx.android.synthetic.main.starry_no_network_layout.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*

class StarryActivity : BaseActivity(), View.OnClickListener {

    companion object {
        const val TAG = "StarryActivity"
        var mainActivity: AppCompatActivity? = null

        // 加入会议
        @JvmStatic
        fun onJoinMeetingNew(context: Context) {

            val model = JoinMeetModel.joinMeetingReturnData ?: JoinMeetData()
            logI("${TAG}.onJoinMeeting.model=${model}")
            logI("${TAG}.onJoinMeeting.MeetingModel.isPCEnter=${MeetingModel.isPCEnter}")
            logI("${TAG}.onJoinMeeting.MeetingModel.isPCEnterMeetingCode=${MeetingModel.isPCEnterMeetingCode}")

            val name = UserPreferences.getInstance().user.name
            val subject = getString(R.string.starry_main_meeting_start_title, name)

            val meetingName = model.roomName ?: subject
            val roomNo = model.room ?: "0"

            val isCam = StarryPreferences.getInstance()?.lastCam ?: false
            val isMic = StarryPreferences.getInstance()?.lastMic ?: true

            val callInModel = StarryCallInModel()
            callInModel.apply {
                room_name = meetingName
                room = roomNo
                udid_from = ""
                callId = ""
                userid_from = ""
                nickname_from = ""
                headImage = ""
                isCamOpen = isCam
                isMicOpen = isMic
                userid = StarryPreferences.getInstance().userId
                czurId = StarryPreferences.getInstance().czurId
                accountNo = StarryPreferences.getInstance().accountNo
                agoraId = StarryPreferences.getInstance().starryUserinfoModel.id.toString()
            }

            val i = Intent(context, MeetingMainActivity::class.java)
            i.putExtra(MeetingMainActivity.KEY_BOOT_MODEL, callInModel)
            i.putExtra(MeetingMainActivity.KEY_ROOM, roomNo)
            i.putExtra(MeetingMainActivity.KEY_BOOT_TYPE, MeetingMainActivity.BOOT_TPE_JOIN)
            ActivityUtils.startActivity(i)

        }

        // 加入会议
        @JvmStatic
        fun onJoinMeetingFromWeb(context: Context) {

            val model = JoinMeetModel.joinMeetingReturnData ?: JoinMeetData()
            logI("${TAG}.onJoinMeetingFromWeb.model=${model}")

            val meetingName = model.roomName
            val roomNo = model.room ?: "0"

            val isCam = StarryPreferences.getInstance()?.lastCam ?: false
            val isMic = StarryPreferences.getInstance()?.lastMic ?: true

            val callInModel = StarryCallInModel()
            callInModel.apply {
                room_name = meetingName
                room = roomNo
                udid_from = ""
                callId = ""
                userid_from = ""
                nickname_from = ""
                headImage = ""
                isCamOpen = isCam
                isMicOpen = isMic
                userid = StarryPreferences.getInstance().userId
                czurId = StarryPreferences.getInstance().czurId
                accountNo = StarryPreferences.getInstance().accountNo
                agoraId = StarryPreferences.getInstance().starryUserinfoModel.id.toString()
            }

            val i = Intent(context, MeetingMainActivity::class.java)
            i.putExtra(MeetingMainActivity.KEY_BOOT_MODEL, callInModel)
            i.putExtra(MeetingMainActivity.KEY_ROOM, roomNo)
            i.putExtra(MeetingMainActivity.KEY_BOOT_TYPE, MeetingMainActivity.BOOT_TPE_JOIN)
            ActivityUtils.startActivity(i)
        }

    }

    init {
        if (mainActivity == null) {
            mainActivity = this
        }
    }

    private val starryViewModel by lazy {
        mainActivity?.let { ViewModelProvider(it).get(StarryViewModel::class.java) }
    }
    private val recentlyViewModel by lazy {
        ViewModelProvider(mainActivity ?: this).get(RecentlyViewModel::class.java)
    }

    private var starryFragmentAttacher: StarryFragmentAttacher? = null
    private var currentShowFragmentIndex = 0
    private var willShowFragmentIndex = 0
    private var showFragment: Fragment? = null

    private var isRedPointGoing = false     // 有会议进行中
    private var isRedPointDone = false     // 未接到时

    private var showFragmentIndex = 0 //

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
//        logI("${TAG}.onEvent=" + event.eventType)

        when (event.eventType) {

            // PC退出会议，需要刷新一下 MeetingModel.isPCEnter
            EventType.STARRY_MEETING_CMD_STOP_CALL,
            EventType.STARRY_MEETING_CMD_EXIT,
//            EventType.STARRY_MEETING_CMD_STOP,
            EventType.STARRY_CLOSE_MEETING -> {
                recentlyViewModel.getDingMeetingAndCallRecords()
            }

            // 查询正在进行的会议
            EventType.STARRY_MEETING_CMD_CHECK_MEETING_LIST -> {
                val newMeetingListEvent = event as StarryCheckMeetingListEvent
                val dataBean = newMeetingListEvent.params
                recentlyViewModel.listDoingMeeting.postValue(dataBean)
                recentlyViewModel.getCallRecordsRefresh()
                starryViewModel?.showMeetingRemindDialog()
            }

            EventType.STARRY_MEETING_CMD_STOP -> {
                if (MeetingModel.isCancelCallRecordsFlag) {
//                    MeetingModel.isCancelCallRecordsFlag = false
                    starryViewModel?.redpointRead()
                }
                recentlyViewModel.getDingMeetingAndCallRecords()
            }

            // Token失效,需要 重新登录,并需要断开长连接,重新连接
            EventType.STARRY_RESULT_CODE_INVALID_TOKEN -> {
                logI("${TAG}.onEvent.STARRY_RESULT_CODE_INVALID_TOKEN")
                // 先断开长连接,重新连接,并重新打开本activity
                if (recentlyViewModel.reloadCounts == 0) {
                    recentlyViewModel.reloadCounts = 1
                    // 延迟1分钟后重置reloadCounts为0
                    CoroutineScope(Dispatchers.Main).launch{
                        delay(1000 * 60 )
                        recentlyViewModel.reloadCounts = 0
                    }

                    ServiceUtils.stopService(NettyService::class.java)
                    try {
                        Thread.sleep(200)
                        startNettyStarryService()
                        Thread.sleep(100)
                        reload()
                        return
                    } catch (e: InterruptedException) {
                        e.printStackTrace()
                    }
                }

            }

            else -> {

            }
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStatusBarColor(R.color.gary_f9)
        setContentView(R.layout.starry_activity_home)
        logI("======CZUR Starry======")
        if (!getMeetingActivity() && recentlyViewModel.reloadCounts == 0){//栈里没有indexActivity说明是不正常状态,重启此activity
            recentlyViewModel.reloadCounts = 1
            reload()
            return
        }
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        showFragmentIndex = intent.getIntExtra("showFragmentIndex", 0)
        currentShowFragmentIndex = showFragmentIndex

        initComponent()
        registerEvent()
        initNetListener()

        initIndexFragment(savedInstanceState)
        ignoreBatteryOptimization()

        setStatusBarBackgroundStarry()


        if (!NetworkUtils.isConnected()) {
            showNoNetworkUI()
        }

        // 悬浮窗权限判断
        // 小米、vivo、oppo单独判断，开启后台弹出界面、锁屏显示功能权限
        checkPermissionBackgroundStart()

        if (showFragmentIndex == 0) {
            bottom_tab_meet.performClick()
        } else if (showFragmentIndex == 1) {
            bottom_tab_recently.performClick()
        }

        // 快捷菜单跳转来的
        val fromActivity = intent.getStringExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_TYPE) ?: ""
        if (fromActivity.isNotBlank() && fromActivity == StarryConstants.STARRY_BLANK_TYPE_SHORTCUT
            && UserPreferences.getInstance().isUserLogin){
            val defaultIntent = Intent(this@StarryActivity, BlankActivityForJoinMeeting::class.java)
            defaultIntent.putExtra(
                StarryConstants.STARRY_BLANK_CLIPBOARD_TYPE,
                StarryConstants.STARRY_BLANK_TYPE_SHORTCUT
            )
            ActivityUtils.startActivity(defaultIntent)
        }

    }

    // 小米、vivo、oppo单独判断，开启后台弹出界面、锁屏显示功能权限
    private fun checkPermissionBackgroundStart() {
        if (StarryPreferences.getInstance().hasShowedPermissionsDialog) {//只显示一次弹窗
            return
        }

        val isBgAllow = RomUtils.isBackgroundStartPermissionAllowed(this)
        val isShowLockAllow = RomUtils.isBackgroundLockPermissionAllowed(this)
        val isGrantOverlayAllow = RomUtils.isOverlayPermissionAllowed(this)
        val permissionResult = if (!StarryPreferences.getInstance().isSpecialXiaomi && RomUtils.isXiaomiVivo()) {
            //正常小米 vivo,判断3个权限
            isBgAllow && isShowLockAllow && isGrantOverlayAllow
        } else {//其余非小米机型,和特殊小米机型,只检测悬浮窗权限
            isGrantOverlayAllow
        }

        if (!permissionResult) {

            val pkgName = applicationContext?.packageName.toString()

            var permissionContentStr = ""
            var permissionItemContentStr = ""

            var permissionLockStr = ""//锁屏显示权限
            var permissionBackgroundStr = ""//后台弹出界面权限
            var permissionOverlayStr = ""//悬浮窗权限
            permissionLockStr = getString(R.string.starry_permission_show_on_lock)
            permissionBackgroundStr = getString(R.string.starry_permission_display_pop_up_background)
            permissionOverlayStr = getString(R.string.starry_permission_display_pop_up)

            if (RomUtils.isXiaomiVivo()) {
                permissionItemContentStr = "\n•" + permissionLockStr +
                        "\n•" + permissionBackgroundStr +
                        "\n•" + permissionOverlayStr
            } else {
                permissionItemContentStr =
                        "\n•" + permissionOverlayStr
            }

            permissionContentStr = String.format(
                    getString(R.string.starry_permission_explain),
                    permissionItemContentStr
            )

            StarryCommonPopup.Builder(this)
                    .setTitle(getString(R.string.starry_popupwindow_title))
                    .setMessage(permissionContentStr)
                    .setPositiveTitle(getString(R.string.starry_background_start_msg_open))
                    .setNegativeTitle(getString(R.string.starry_background_start_msg_cancel))
                    .setTextContentGravity(Gravity.LEFT or Gravity.CENTER_VERTICAL)
                    .setOnPositiveListener { dialog, _ ->
                        StarryPreferences.getInstance().hasShowedPermissionsDialog = true

                        val isXiaoMi = RomUtils.isXiaoMi()
                        val isVivo = RomUtils.isVivo()
                        if (!RomUtils.isXiaomiVivo()) {
                            if (!isGrantOverlayAllow) {//不是小米机型, 只监听悬浮窗
                                startActivity(
                                        Intent(
                                                Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                                                Uri.parse("package:" + this.packageName)
                                        )
                                )
                            }
                        } else
                            if (StarryPreferences.getInstance().isSpecialXiaomi
                                    || (!isGrantOverlayAllow && isBgAllow && isShowLockAllow)
                            ) {//特殊小米机型只会监听悬浮窗权限
                                startActivity(
                                        Intent(
                                                Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                                                Uri.parse("package:" + this.packageName)
                                        )
                                )
                            } else
                                if (isXiaoMi) {
//                        goToSetting(this)
                                    RomUtils.openXiaoMiPermissionEdit(this, pkgName)
                                } else {
                                    if (isVivo) {
                                        RomUtils.PermissionPageManagement.goToSetting(this)
//                            RomUtils.goAppDetailSetting(this, pkgName)
                                    } else {
                                        RomUtils.PermissionPageManagement.goToSetting(this@StarryActivity)
                                    }
                                }

                        dialog?.dismiss()

                    }
                    .setOnNegativeListener { dialog, _ ->
                        dialog.dismiss()
                        StarryPreferences.getInstance().hasShowedPermissionsDialog = true
                        LiveDataBus.get().with(StarryConstants.MEETING_PERMISSIONS_CANCEL).value = true
                    }
                    .create()
                    .show()
        }
    }

    private fun initNetListener() {
        NetworkUtils.registerNetworkStatusChangedListener(object : NetworkUtils.OnNetworkStatusChangedListener {
            override fun onDisconnected() {
                showNoNetworkUI()
            }

            override fun onConnected(networkType: NetworkUtils.NetworkType?) {
                showHasNetworkUI()
            }
        })
    }

    private fun showNoNetworkUI() {
        setStatusBarBackgroundNoNetwork()
        starry_main_no_network?.visibility = View.VISIBLE

    }

    private fun showHasNetworkUI() {
        starry_main_no_network?.visibility = View.GONE
        if (currentShowFragmentIndex == 0) {
            setStatusBarBackgroundStarry()
        } else {
            setStatusBarBackgroundRecently()
        }
    }

    private fun initComponent() {
        //检测权限方面是否是正常小米
        RomUtils.checkSpecialXiaomi(this)
        recentlyViewModel.getDingMeetingAndCallRecords()
        starryViewModel?.missedCallRecords()

        LiveDataBus.get()
                .with(StarryConstants.MEETING_NOJOIN_RED, Boolean::class.java)
                .observe(this) {
                    logI("${TAG}.LiveDataBus.missedCallRecords=${it}")
                    starryViewModel?.missedCallRecords()
                    // 不需要刷新，会自动刷新的
                    recentlyViewModel.getDingMeetingAndCallRecords()
                }

        // web跳转来的入会
        LiveDataBus.get()
                .with(StarryConstants.MEETING_REJOIN_MEETING_FROM_WEB, Boolean::class.java)
                .observe(this) {
                    logI("${TAG}.LiveDataBus.MEETING_REJOIN_MEETING_FROM_WEB=${it}")
                    launch {
                        delay(1500)
                        onJoinMeetingFromWeb(this@StarryActivity)
                    }
                }

        recentlyViewModel.isMissedCallRecordsFlag.observe(this) {
            isRedPointGoing = it
            if (isRedPointGoing || isRedPointDone) {
                bottom_tab_recently_red_point?.visibility = View.VISIBLE
            } else {
                bottom_tab_recently_red_point?.visibility = View.GONE
            }
        }

        starryViewModel?.isMissedCallRecordsFlag?.observe(this) {
            isRedPointDone = it
            if (isRedPointGoing || isRedPointDone) {
                bottom_tab_recently_red_point?.visibility = View.VISIBLE
            } else {
                bottom_tab_recently_red_point?.visibility = View.GONE
            }
        }

        if (BuildConfig.IS_OVERSEAS) {
            // 暂不判断手机号
        } else {
            // 邮箱登录后，提示绑定手机号
            if (Validator.isEmpty(UserPreferences.getInstance().userMobile)) {
                val i = Intent(this, UserBindPhoneActivity::class.java)
                i.putExtra("STARRY", true)
                ActivityUtils.startActivity(i)
                finish()
            }
        }

        recentlyViewModel.listRecently.observe(this) {
            if (currentShowFragmentIndex == 1) {
                starry_home_clear_btn?.visibility = View.GONE
                if (it.isNotEmpty()) {
                    starry_home_clear_btn?.visibility = View.VISIBLE
                }
            }
        }

        //检测权限方面是否是正常小米
        isBackgroundStartAllowed(this)
    }

    private fun registerEvent() {
        bottom_tab_meet?.setOnClickListener(this)
        bottom_tab_schedule?.setOnClickListener(this)
        bottom_tab_recently?.setOnClickListener(this)
        starry_home_back_btn?.setOnClickListener(this)
        starry_home_more_btn?.singleClick(this)
        starry_home_clear_btn?.singleClick(this)
        tv_click_refresh?.singleClick(this)
    }

    /**
     * @des: 初始化Fragment
     * @params:[savedInstanceState]
     * @return:void
     */
    private fun initIndexFragment(savedInstanceState: Bundle?) {
        val fm = supportFragmentManager
        starryFragmentAttacher = StarryFragmentAttacher(fm, "device", "relationId")
        if (savedInstanceState != null) {
            for (i in 0..1) {
                val fragment = starryFragmentAttacher?.getFragment(i)
                if (Validator.isNotEmpty(fragment)) {
                    fm.beginTransaction().hide(fragment!!).commit()
                }
            }
        }
        changeDefaultPage()
    }

    private fun changeDefaultPage() {
        changeTabIcon(currentShowFragmentIndex)
        showFragment = starryFragmentAttacher?.showFragment(currentShowFragmentIndex)

    }

    /**
     * @des: 选择主页tab
     * @params:[index]
     * @return:
     */
    private fun changeTabIcon(index: Int) {
        when (index) {
            0 -> {
                setSelectFalse()
                bottom_tab_meet_img?.isSelected = true
                bottom_tab_meet_title?.isSelected = true
            }
            1 -> {
                setSelectFalse()
                bottom_tab_recently_img?.isSelected = true
                bottom_tab_recently_title?.isSelected = true
            }
            else -> {
            }
        }
    }

    private fun setSelectFalse() {
        bottom_tab_meet_img?.isSelected = false
        bottom_tab_schedule_img?.isSelected = false
        bottom_tab_recently_img?.isSelected = false
        bottom_tab_meet_title?.isSelected = false
        bottom_tab_schedule_title?.isSelected = false
        bottom_tab_recently_title?.isSelected = false
    }

    /**
     * @des: 切换tab
     * @params:[index]
     * @return:
     */
    private fun changeFragment(id: Int) {
        when (id) {
            R.id.bottom_tab_meet -> {
                changeTabIcon(0)
                willShowFragmentIndex = 0
            }
            R.id.bottom_tab_recently -> {
                changeTabIcon(1)
                willShowFragmentIndex = 1
            }
            else -> {
            }
        }
        showFragment =
                starryFragmentAttacher?.showFragment(willShowFragmentIndex, currentShowFragmentIndex)
        currentShowFragmentIndex = willShowFragmentIndex
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.bottom_tab_meet -> {
                Thread.sleep(500)
                setStatusBarBackgroundStarry()
                changeFragment(v.id)
                changeTabIcon(0)
            }
            R.id.bottom_tab_recently -> {
                Thread.sleep(500)
                changeFragment(v.id)
                changeTabIcon(1)
                setStatusBarBackgroundRecently()
            }
            R.id.starry_home_back_btn -> ActivityUtils.finishToActivity(
                    IndexActivity::class.java,
                    false
            )
            R.id.starry_home_more_btn -> ActivityUtils.startActivity(StarryMenuActivity::class.java)

            R.id.starry_home_clear_btn -> {
                LiveDataBus.get().with(StarryConstants.MEETING_CLEAR).value = true
            }
            R.id.tv_click_refresh -> {
                if (!NetworkUtils.isConnected()) {
                    showProgressDialogMoment()
                    return
                }
            }
            else -> {
            }
        }
    }

    // user默认页面的状态栏背景
    private fun setStatusBarBackgroundStarry() {
        starry_tab_meeting?.visibility = View.VISIBLE
        starry_tab_recently?.visibility = View.GONE

        // 设置状态栏的文字颜色 isLightMode：true，黑色； false，白色；
        BarUtils.setStatusBarLightMode(window, false)

        // 状态栏 背景色
        setStatusBarColor(R.color.account_bg_color)
        // 导航栏背景色
        starry_home_top_bar?.background = getDrawable(R.color.account_bg_color)
        // 导航栏标题为空
        sitting_home_title?.text = getString(R.string.starry_title)
        sitting_home_title?.setTextColor(getColor(R.color.white))

        // 导航栏 更多按钮
        starry_home_more_btn?.visibility = View.VISIBLE
        starry_home_clear_btn?.visibility = View.GONE
        // 导航栏 返回按钮
        starry_home_back_btn?.setImageResource(R.mipmap.white_back_icon)
    }

    // 设置状态栏的背景色
    private fun setStatusBarBackgroundRecently() {

        starry_tab_meeting?.visibility = View.GONE
        starry_tab_recently?.visibility = View.VISIBLE

        // 设置状态栏的文字颜色 isLightMode：true，黑色； false，白色；
        BarUtils.setStatusBarLightMode(window, true)
        // 状态栏 背景色
        setStatusBarColor(R.color.starry_title_bar_bg_white)
        // 导航栏背景色
        starry_home_top_bar?.background = getDrawable(R.color.starry_title_bar_bg_white)
        // 导航栏标题为空
        sitting_home_title.text = getString(R.string.bottom_tab_recently_title)
        sitting_home_title?.setTextColor(getColor(R.color.account_title))

        // 导航栏 更多按钮不显示
        starry_home_more_btn?.visibility = View.GONE
        if (recentlyViewModel.listRecently.value?.isNotEmpty() == true) {
            starry_home_clear_btn?.visibility = View.VISIBLE
        } else {
            starry_home_clear_btn?.visibility = View.GONE
        }
        // 导航栏 返回按钮
        starry_home_back_btn?.setImageResource(R.mipmap.login_back_icon)
    }

    // 设置状态栏的背景色
    private fun setStatusBarBackgroundNoNetwork() {

        // 设置状态栏的文字颜色 isLightMode：true，黑色； false，白色；
        BarUtils.setStatusBarLightMode(window, true)
        // 状态栏 背景色
        setStatusBarColor(R.color.starry_title_bar_bg_white)
        // 导航栏背景色
        starry_home_top_bar?.background = getDrawable(R.color.starry_title_bar_bg_white)
        // 导航栏标题为空
        sitting_home_title.text = getString(R.string.starry_title)
        sitting_home_title?.setTextColor(getColor(R.color.account_title))

        // 导航栏 更多按钮不显示
        starry_home_more_btn?.visibility = View.GONE
        starry_home_clear_btn?.visibility = View.GONE
        // 导航栏 返回按钮
        starry_home_back_btn?.setImageResource(R.mipmap.login_back_icon)
    }

    override fun onResume() {
        super.onResume()
        logD("${TAG}.onResume")

        // 会议中，直接进入会议！！！
        if (MeetingModel.isInMeeting.value == true) {
            ActivityUtils.startActivity(MeetingMainActivity::class.java)
        }

        // 获取剪贴板内容
//        getClipboardContentDelay()

    }

    override fun onDestroy() {
        super.onDestroy()
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }

        starryViewModel?.clearedData()
    }

    /**
     * 忽略电池优化
     */
    private fun ignoreBatteryOptimization() {
        if (!FirstPreferences.getInstance(this).isFirstIgnoreBattery){
            return
        }
        val powerManager: PowerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        val hasIgnored: Boolean =
            powerManager.isIgnoringBatteryOptimizations(packageName)
        FirstPreferences.getInstance(this).setIsFirstIgnoreBattery(false)
        //  判断当前APP是否有加入电池优化的白名单，如果没有，弹出加入电池优化的白名单的设置对话框。
        if (!hasIgnored) {
            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
            intent.data = Uri.parse("package:$packageName")
            if (intent.resolveActivity(packageManager) != null) {
                startActivity(intent)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == RESULT_CHECK_OVERLAYS_CODE) {
            if (!Settings.canDrawOverlays(this)) {
                logI("${TAG}.onActivityResult.checkPermission.授权失败")
            } else {
//                logI("${TAG}.onActivityResult.checkPermission.授权成功")
            }
        }
    }

    private  fun getMeetingActivity(): Boolean {
        for (i in 0..4) { // 寻找5次, 其实1次就找到了
            ActivityUtils.getActivityList().forEach { aty ->
                if (aty::class.java.simpleName == IndexActivity::class.java.simpleName) {
                    return true
                }
            }
        }
        return false
    }

    fun reload() {
        val intent = intent
        overridePendingTransition(0, 0)
        intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION)
        finish()
        overridePendingTransition(0, 0)
        startActivity(intent)
    }
}