<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    android:orientation="vertical">

    <include layout="@layout/layout_sitting_top_bar_withspace" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingTop="@dimen/jingMargin30" >

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:singleLine="true"
            app:autoSizeTextType="uniform"
            app:autoSizeMinTextSize="19sp"
            app:autoSizeMaxTextSize="22sp"
            app:autoSizeStepGranularity="1sp"
            android:text="@string/sitting_home_light"
            android:textColor="@color/black_22"
            android:textSize="22sp"
            android:visibility="gone"/>

        <RelativeLayout
            android:id="@+id/rl_tool_bar_switch"
            android:layout_width="match_parent"
            android:paddingTop="@dimen/jingMargin10"
            android:paddingBottom="@dimen/jingMargin10"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="17.5dp"
                android:orientation="vertical">

                <com.czur.cloud.ui.component.MediumBoldTextView
                    android:id="@+id/sitting_happy_time_switch_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/sitting_home_light_switch"
                    android:textColor="@color/normal_blue"
                    android:textSize="16sp" />

            </LinearLayout>


            <com.github.iielse.switchbutton.SwitchView
                android:id="@+id/sitting_happy_time_switch"
                android:layout_width="50dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="17.5dp"
                app:isOpened="true"
                app:primaryColor="@color/jing_main_bg_color"
                app:primaryColorDark="@color/jing_main_bg_color"
                app:ratioAspect="0.6"
                android:layout_height="30dp"/>

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/light_progress_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="13.5dp"
            android:background="@color/gray_f7"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/jingMargin10"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/sitting_home_minus_btn"
                android:layout_width="@dimen/jingVolCircle"
                android:layout_height="@dimen/jingVolCircle"
                android:layout_centerVertical="true"
                android:layout_alignParentStart="true"
                android:padding="5dp"
                android:src="@mipmap/vol_minus_icon" />

            <ImageView
                android:id="@+id/sitting_home_plus_btn"
                android:layout_width="@dimen/jingVolCircle"
                android:layout_height="@dimen/jingVolCircle"
                android:layout_centerVertical="true"
                android:layout_alignParentEnd="true"
                android:padding="5dp"
                android:src="@mipmap/vol_plus_icon" />

            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/sitting_home_vol_level_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:text="3"
                android:textColor="@color/jing_main_bg_color"
                android:textSize="24sp" />


        </RelativeLayout>

        <com.czur.cloud.ui.component.seekbar.BubbleSeekBarSitting
            android:id="@+id/seekBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:paddingTop="10dp"
            android:layout_marginStart="22dp"
            android:layout_marginEnd="22dp"
            app:bsb_always_show_bubble="false"
            app:bsb_anim_duration="0"
            app:bsb_auto_adjust_section_mark="true"
            app:bsb_bubble_color="@color/normal_blue"
            app:bsb_hide_bubble="true"
            app:bsb_max="5"
            app:bsb_min="0"
            app:bsb_progress="5"
            app:bsb_section_count="5"
            app:bsb_second_track_color="@color/jing_main_bg_color"
            app:bsb_section_first_color="@color/white"
            app:bsb_thumb_fill_color="@color/jing_main_bg_color"
            app:bsb_track_color="@color/gray_d5"
            app:bsb_show_progress_in_float="false"
            app:bsb_show_section_mark="true"
            app:bsb_show_section_text="false"
            app:bsb_show_thumb_text="false"
            app:bsb_touch_to_seek="true"
            app:bsb_seek_step_section="true"
            />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>