package com.czur.cloud.netty.core;


import static com.czur.cloud.netty.core.HeartbeatNewHandlerKt.HEART_INTERVAL_SECOND;

import com.czur.cloud.netty.Config;

import java.nio.charset.Charset;
import java.util.concurrent.TimeUnit;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.DelimiterBasedFrameDecoder;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;
import io.netty.handler.timeout.IdleStateHandler;


public class CZURClientInitializer extends ChannelInitializer<SocketChannel> {

    private static class CZURClientInitializerInstance {
        private static final CZURClientInitializer INSTANCE = new CZURClientInitializer();
    }


    public static CZURClientInitializer getInstance() {
        return CZURClientInitializer.CZURClientInitializerInstance.INSTANCE;
    }

    //所有类型的超时时间
    private final long allIdleTime = 20;

    @Override
    protected void initChannel(SocketChannel socketChannel) throws Exception {
        ChannelPipeline pipeline = socketChannel.pipeline();
        ByteBuf delimiter = Unpooled.copiedBuffer(Config.MESSAGE_DELIMITER.getBytes());
        pipeline.addLast("framer", new DelimiterBasedFrameDecoder(Config.MAX_MESSAGE_SIZE, delimiter));
        pipeline.addLast("decoder", new StringDecoder(Charset.forName("UTF-8")));
        pipeline.addLast("encoder", new StringEncoder(Charset.forName("UTF-8")));
        pipeline.addLast("timeout", new IdleStateHandler(0, 0, allIdleTime, TimeUnit.SECONDS));
//        pipeline.addLast("heartbeat", HeartbeatHandler.getInstance());
        pipeline.addLast("heartbeat", new HeartbeatNewHandler(HEART_INTERVAL_SECOND));
        pipeline.addLast("handler", ClientMessageHandler.getInstance());
    }


}
