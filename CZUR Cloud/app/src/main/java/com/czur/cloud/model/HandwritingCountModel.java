package com.czur.cloud.model;

/**
 * Created by Focus on 2016/8/17.
 */
public class HandwritingCountModel {
    private String id;
    private String userId;
    private String ocrNum;
    private boolean isNotice;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOcrNum() {
        return ocrNum;
    }

    public void setOcrNum(String ocrNum) {
        this.ocrNum = ocrNum;
    }

    public boolean isNotice() {
        return isNotice;
    }

    public void setNotice(boolean notice) {
        isNotice = notice;
    }


}
