package com.czur.cloud.ui.starry.meeting.network.core;

import static com.czur.cloud.ui.starry.meeting.network.core.util.ResultUtilKt.makeEmptyEntity;
import static com.czur.cloud.ui.starry.meeting.network.core.util.ResultUtilKt.makeNetWorkErrorEntity;
import static com.czur.cloud.ui.starry.meeting.network.core.util.ResultUtilKt.makeServerErrorEntity;

import android.util.Log;

import com.czur.cloud.ui.starry.meeting.network.MiaoHttpManager;
import com.czur.cloud.ui.starry.meeting.network.core.util.RequestUtilKt;
import com.czur.cloud.ui.starry.meeting.network.core.util.ResultUtilKt;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.HashMap;

import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;

public class SyncHttpTask {
    private static final String TAG = "SyncHttpTask";

    private SyncHttpTask() {
    }

    public static SyncHttpTask getInstance() {
        return SingletonHolder.instance;
    }

    private static class SingletonHolder {
        static final SyncHttpTask instance = new SyncHttpTask();
    }

    public <T> MiaoHttpEntity<T> syncGet(String url, Type type, HashMap<String, String> headers, HashMap<String, String> postParam) {

        Request.Builder builder = new Request.Builder();
        // 添加请求参数和Url
        RequestUtilKt.addRequestParamsAndUrl(builder, url, postParam, null, "GET");


        // 添加请求头
        RequestUtilKt.addRequestHeaders(builder, headers);

        Request request = builder.build();

        return sendRequest(type, request);
    }


    public <T> MiaoHttpEntity<T> syncPost(String url, Type type, HashMap<String, String> postParam, HashMap<String, String> headers, String body) {
        Request.Builder requestBuilder = new Request.Builder();

        RequestUtilKt.addRequestParamsAndUrl(requestBuilder, url, postParam, body, "POST");

        RequestUtilKt.addRequestHeaders(requestBuilder, headers);

        Request request = requestBuilder.build();

        return sendRequest(type, request);
    }

    /**
     * 发送网络请求
     * 并将结果解析成MiaoHttpEntity类型
     *
     * @param type    数据解析类型
     * @param request HttpRequest
     */
    private <T> MiaoHttpEntity<T> sendRequest(Type type, Request request) {
        MiaoHttpEntity<T> entity = null;
        Response response = null;
        try {
            response = MiaoHttpManager.getInstance().getHttpClient().newCall(request).execute();
        } catch (IOException e) {
            Log.w("SyncHttpTask", "请求出错:", e);
            return makeNetWorkErrorEntity(e);
        }
        if (response.isSuccessful()) {
            final ResponseBody body = response.body();
            if (body == null) {
                entity = makeEmptyEntity();
            } else {
                String json = null;
                try {
                    json = body.string();
                    entity = ResultUtilKt.makeEntity(json, type);
                } catch (IOException e) {
                    entity = makeNetWorkErrorEntity(e);
                } finally {
                    // 生成正确 entity
                    body.close();
                }
            }

        } else {
            entity = makeServerErrorEntity(response.message());
        }
        return entity;
    }

}
