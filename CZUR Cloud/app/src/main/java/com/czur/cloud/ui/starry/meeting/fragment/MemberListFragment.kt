package com.czur.cloud.ui.starry.meeting.fragment

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.graphics.Color
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.widget.LinearLayout
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.RomUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.R
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants
import com.czur.cloud.ui.starry.activity.StarryActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.livedatabus.LiveDataBus
import com.czur.cloud.ui.starry.meeting.adapter.MemberAdapter
import com.czur.cloud.ui.starry.meeting.base.FloatFragment
import com.czur.cloud.ui.starry.meeting.baselib.utils.*
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_IOS_HOLD
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_JOINED
import com.czur.cloud.ui.starry.meeting.bean.UserStatus.STATUS_TIMEOUT
import com.czur.cloud.ui.starry.meeting.bean.UserStreamStatus
import com.czur.cloud.ui.starry.meeting.bean.vo.Member
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import com.czur.cloud.ui.starry.meeting.dialog.StarryChangeNickNameInputDialog
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup
import com.czur.cloud.ui.starry.meeting.dialog.bottommenu.PopBottomMenuWindow
import com.czur.cloud.ui.starry.meeting.dialog.bottommenu.PopLoadingWindow
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.meeting.model.ModelManager
import com.czur.cloud.ui.starry.meeting.utils.ui.addLineDecoration
import com.czur.cloud.ui.starry.meeting.viewmodel.ControlBarViewModel
import com.czur.cloud.ui.starry.meeting.viewmodel.MeetingViewModel
import com.czur.cloud.ui.starry.model.StarryAddressBookModel
import com.czur.cloud.ui.starry.model.StarryEnterpriseModel
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.ui.starry.viewmodel.StarryContactViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import com.czur.czurutils.log.logD
import com.czur.czurutils.log.logI
import com.eshare.api.utils.DisplayUtils
import kotlinx.android.synthetic.main.starry_fragment_members_list_float.*
import kotlinx.coroutines.delay

private const val TAG = "MemberListFragment"

class MemberListFragment: FloatFragment() {
    override fun getLayoutId() = R.layout.starry_fragment_members_list_float

    private val meetingViewModel: MeetingViewModel by viewModels({ requireActivity() })
    private val viewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryViewModel::class.java)
    }
    private val contactViewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryContactViewModel::class.java)
    }
    private val controlBarVM: ControlBarViewModel by viewModels({ requireActivity() })

    private val memberAdapter = MemberAdapter()

    private val remindClickedArrayList = arrayListOf<String>()
    @SuppressLint("ClickableViewAccessibility")
    override fun initView() {
        super.initView()

        // 去调整 RecyclerView 的复用逻辑和方式来解决 onBindViewHolder 没有调用的这个问题
        itemMemberRv?.setItemViewCacheSize(20)
        itemMemberRv?.layoutManager = LinearLayoutManager(requireContext())
        memberAdapter.setHasStableIds(true)
        itemMemberRv?.adapter = memberAdapter


        // recycleview点击其中的按钮
        itemMemberRv?.doOnItemClick{vh,view ->
            val pos = vh.bindingAdapterPosition
            val member = memberAdapter.getRawMember(pos)
            Log.i("Jason", "${TAG}.itemMemberRv.doOnItemClick.pos=${pos},member=${member}")
            member?.let {
                onItemViewClick(member, view, pos)
            } ?: true
        }

        // 分割线距离左侧有97的边距
        itemMemberRv.addLineDecoration(97)
        // 防止Item闪烁
        itemMemberRv.closeDefChangeAnimations()

        // 关闭
        btnCloseIv?.singleClick {
            logI("MemberListFragment.btnCloseIv-关闭")
            controlBarVM.memberListFragment = null
            dismiss()
        }
        // 取消静音全部
        user_bottom_bar_mic_on_ll?.singleClick {
            logI("MemberListFragment.user_bottom_bar_mic_on_ll-取消静音全部")
            if (meetingViewModel.clickNoNetwork()){
                return@singleClick
            }
            launch {
                meetingViewModel.openAllAudio()
                ToastUtils.showLong(R.string.starry_all_open_audio_msg)
            }
        }
        // 静音全部
        user_bottom_bar_mic_off_ll?.singleClick {
            logI("MemberListFragment.user_bottom_bar_mic_off_ll-静音全部")
            if (meetingViewModel.clickNoNetwork()){
                return@singleClick
            }
            launch {
                meetingViewModel.muteAllAudio()
                ToastUtils.showLong(R.string.starry_all_mute_audio_msg)
            }
        }
        user_bottom_bar_mic_off_iv?.setOnTouchListener(View.OnTouchListener { v, event ->
            logI("MemberListFragment.user_bottom_bar_mic_iv-静音全部")

            when(event?.action){
                MotionEvent.ACTION_DOWN -> {
                    user_bottom_bar_mic_off_iv?.background = requireContext().getDrawable(R.drawable.circle_with_gray)
                    true
                }
                MotionEvent.ACTION_UP -> {
                    user_bottom_bar_mic_off_iv?.background = requireContext().getDrawable(R.drawable.circle_with_gray_f2)
                    if (meetingViewModel.clickNoNetwork()){
                        return@OnTouchListener false
                    }
                    launch {
                        meetingViewModel.muteAllAudio()
                        ToastUtils.showLong(R.string.starry_all_mute_audio_msg)
                    }

                    true
                }
                else -> {
                    false
                }
            }
        })
        user_bottom_bar_mic_on_iv?.setOnTouchListener(View.OnTouchListener { v, event ->
            logI("MemberListFragment.user_bottom_bar_mic_iv-静音全部")

            when(event?.action){
                MotionEvent.ACTION_DOWN -> {
                    user_bottom_bar_mic_on_iv?.background = requireContext().getDrawable(R.drawable.circle_with_gray)
                    true
                }
                MotionEvent.ACTION_UP -> {
                    user_bottom_bar_mic_on_iv?.background = requireContext().getDrawable(R.drawable.circle_with_gray_f2)
                    if (meetingViewModel.clickNoNetwork()){
                        return@OnTouchListener false
                    }
                    launch {
                        meetingViewModel.openAllAudio()
                        ToastUtils.showLong(R.string.starry_all_open_audio_msg)
                    }

                    true
                }
                else -> {
                    false
                }
            }
        })

        // 锁房的点击事件
        user_bottom_bar_lock_ll?.singleClick {
            logI("MemberListFragment.user_bottom_bar_lock_ll-锁房的点击事件")
            if (meetingViewModel.clickNoNetwork()){
                return@singleClick
            }
            launch {
                meetingViewModel.switchLockStatus()
            }
        }

        updateControlVisible()

        // 添加人员
        user_bottom_bar_add_ll?.singleClick {
            logI("MemberListFragment.user_bottom_bar_add_ll-添加人员")
            if (meetingViewModel.isAdmin() || !meetingViewModel.isRoomLocked) {
                if (meetingViewModel.clickNoNetwork()){
                    return@singleClick
                }
                // 管理员,或者房间没有锁定时可用
                addChooseFragment()    // 显示人员选择Fragment
            } else {
                ToastUtils.showLong(R.string.toast_room_locked)
            }
        }
        user_bottom_bar_add_ll_company?.singleClick {
            logI("MemberListFragment.user_bottom_bar_add_ll_company-添加人员")
            if (!meetingViewModel.isRoomLocked) {
                if (meetingViewModel.clickNoNetwork()){
                    return@singleClick
                }
                // 管理员,或者房间没有锁定时可用
                addChooseFragment()
            } else {
                ToastUtils.showLong(R.string.toast_room_locked)
            }
        }

        LiveDataBus.get()
            .with(StarryConstants.STARRY_CONTACT_EDIT_MEETING, Boolean::class.java)
            .observe(this) {
                val member = controlBarVM.detailFragmentMember
                val meetingNo = member?.accountId
                meetingViewModel.memberList.value?.forEach { it ->
                    val meeting_no = it.accountId
                    if (meetingNo == meeting_no){
                        it.nickName = member.nickName.toString()
                    }
                }

                if (meetingViewModel.memberList.value != null) {
                    memberAdapter.rawData = meetingViewModel.memberList.value!!
                }

            }

        // 会议中添加联系人
        LiveDataBus.get()
            .with(StarryConstants.STARRY_CONTACT_ADDTO_MEETING, Boolean::class.java)
            .observe(this) {
                if (it) {
                    launch {
                        changeShowNickName()
                    }
                }
            }
    }

    override fun onDismiss() {
        super.onDismiss()
        logI("MemberListFragment.onDismiss")
    }

    private fun onItemViewClick(member: Member, view: View, pos: Int = -1): Boolean {
        logI("MemberListFragment.onItemViewClick.pos=${pos},member=${member}")
        return when (view.id) {
            R.id.itemMemberMoreIcon -> {
                logI("${TAG}.More")
                if (member.status == STATUS_JOINED || member.status == STATUS_IOS_HOLD) {
                    onCreateBottomSheetDialog(member)
                }else{
                    onCreateBottomSheetDialogOne(member)
                }
                true
            }

            R.id.itemMemberRemindIcon -> {
                logI("${TAG}.提醒")
                if (meetingViewModel.clickNoNetwork()){
                    return false
                }
                // 提醒功能就是再一次发送邀请
                launch {
                    when (meetingViewModel.remindMember(member.accountId)) {
                        true -> {
//                            remindClickedArrayList.remove(member.accountId)
//                            memberAdapter.setRemindClickList(remindClickedArrayList)
                        }
                        false -> {
                            memberAdapter.updateStatus(STATUS_TIMEOUT, pos)
                            logD("提醒失败")
                        }
                    }
                }
                true
            }

            // 静音/取消静音
            R.id.bottom_menu_audio -> {
                logI("${TAG}.静音/取消静音")
                if (member.audioStatus == UserStreamStatus.STREAM_DISABLE) {
                    ToastUtils.showLong(R.string.remote_audio_disable)
                } else {
                    if (meetingViewModel.clickNoNetwork()){
                        return false
                    }
                    meetingViewModel.requestChangeAudioStatus(member)
                }
                true
            }

            // 关闭摄像头/开启摄像头
            R.id.bottom_menu_video -> {
                logI("${TAG}.关闭摄像头/开启摄像头")
                if (member.videoStatus == UserStreamStatus.STREAM_DISABLE) {
                    ToastUtils.showLong(R.string.remote_video_disable)
                } else {
                    if (meetingViewModel.clickNoNetwork()){
                        return false
                    }
                    meetingViewModel.requestChangeVideoStatus(member)
                }
                true
            }

            // 转让管理员
            R.id.bottom_menu_change_admin -> {
                logI("${TAG}.转让管理员")
                if (meetingViewModel.clickNoNetwork()){
                    return false
                }
                showConfirmChangeAdminDialog(member)

                // MemberList点击事件
                memberAdapter.setIsSelfAdmin(false)
                true
            }

            // Detail
            R.id.itemMemberHeadIv,
            R.id.itemMemberNickNameTv,
            R.id.bottom_menu_detail -> {
                logI("${TAG}.详情")
                if (meetingViewModel.clickNoNetwork()){
                    return false
                }
                showMemberDetail(member)
                true
            }

            // 删除
            R.id.bottom_menu_del -> {
                logI("${TAG}.移除")
                if (meetingViewModel.clickNoNetwork()){
                    return false
                }
                showConfirmRemoveDialog(member)
                true
            }

            // 编辑自己在会昵称
            R.id.name_edit_btn -> {
                logI("${TAG}.修改昵称")
                if (meetingViewModel.clickNoNetwork()){
                    return false
                }
                changeNickNameDialog(member)
                true
            }

            else -> false
        }
    }

    // 查看详情
    private fun showMemberDetail(member: Member) {
        controlBarVM.detailFragment = MeetingContactDetailFragment()

        val addressBookModel = StarryAddressBookModel()
        addressBookModel.apply {
            accountNo = member.accountId
            meetingAccout = member.accountId
            meetingNo = member.accountId
            name = member.nickName
            czurId = member.czurID
        }
        controlBarVM.detailFragmentAddressBookModel = addressBookModel
        controlBarVM.detailFragmentMember = member

        if (MeetingModel.currentOrientationStatus == Configuration.ORIENTATION_LANDSCAPE) {
            controlBarVM.detailFragment?.show(
                ByScreenParams(),
                AnimDirection.RIGHT
            )
        }else{
            controlBarVM.detailFragment?.show(
                ByScreenParams(),
                AnimDirection.BOTTOM
            )
        }

    }

    private fun onCreateBottomSheetDialogOne(member: Member) {
        //初始化页面
        val layoutInflater = requireActivity().getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        // 获取屏幕宽高
        val weight = resources.displayMetrics.widthPixels
        val height = resources.displayMetrics.heightPixels
        //初始化该对象
        val mPopWindow = PopBottomMenuWindow(layoutInflater, weight, height)
        val list = listOf<String>(
//            getString(R.string.starry_member_bottom_menu_detail),
            getString(R.string.starry_member_bottom_menu_del),
        )
        mPopWindow.setDatasList(list,
            "",
            getString(R.string.starry_meeting_exit_admin_menu_cancel))
//        mPopWindow.setBackground(null)
        mPopWindow.setBackground(resources.getDrawable(R.drawable.popup_window_grey_bg))
//        mPopWindow.setBackgroundColor(R.color.starry_gray_bg_translate)
        mPopWindow.setLastItemIsRed(false)
        mPopWindow.apply {
            bottom_menu_cancel?.setOnClickListener{
                this.dismiss()
            }

            popup_window_dialog_cl?.setOnClickListener{
                this.dismiss()
            }

            mAdapter.setOnItemClickListener(object: PopBottomMenuWindow.MyAdapter.OnClickListener{
                override fun onclick(position: Int, item: String) {
                    logI("MainActivity.mAdapter.setOnItemClickListener.position=${position}")
                    onItemClickBottomSheetDialogOne(position, member)
                    <EMAIL>()
                }
            })

            show()
        }
    }
    private fun onItemClickBottomSheetDialogOne(position: Int, member: Member){
        when(position){
            // detail
//            0 -> {
//                showMemberDetail(member)
//            }
            // 确定
            0 -> {
                showConfirmRemoveDialog(member)
            }

        }
    }

    private fun showConfirmChangeAdminDialog(member: Member) {
        StarryCommonPopup.Builder(requireContext())
            .setTitle(getString(R.string.starry_popupwindow_title))
            .setMessage(com.czur.cloud.ui.starry.meeting.baselib.utils.getString(
                R.string.starry_popupwindow_msg_change_admin,
                member.nickName
            ))
            .setOnPositiveListener { dialog, _ ->
                meetingViewModel.transferAdministrator(member)

                dialog?.dismiss()
            }
            .setOnNegativeListener { dialog, _ -> dialog.dismiss() }
            .create()
            .show()
    }

    private fun showConfirmRemoveDialog(member: Member) {
        StarryCommonPopup.Builder(
            requireContext(),
            CloudCommonPopupConstants.COMMON_TWO_BUTTON_YES_NO
        )
            .setTitle(com.czur.cloud.ui.starry.meeting.baselib.utils.getString(R.string.starry_popupwindow_title))
            .setMessage(com.czur.cloud.ui.starry.meeting.baselib.utils.getString(
                R.string.starry_popupwindow_msg_remove,
                member.nickName
            ))
            .setOnPositiveListener { dialog, _ ->

                if (meetingViewModel.isAdmin()) {
                    meetingViewModel.removeTarget(member)
                    //去掉被移除的人员
                    contactViewModel.isCheckedMap.value?.remove(member.accountId)
                }else{
                    ToastUtils.showLong(R.string.starry_no_permission_msg)
                }
                dialog?.dismiss()
            }
            .setOnNegativeListener { dialog, _ -> dialog.dismiss() }
            .create()
            .show()
    }

    // 修改会中自己的昵称
    private fun changeNickNameDialog(member: Member) {
        val dlg = StarryChangeNickNameInputDialog(
            requireContext(),
            getString(R.string.starry_changenickname_title),
            object: StarryChangeNickNameInputDialog.clickCallBack{
                override fun yesClick(dialog: StarryChangeNickNameInputDialog) {
                    val nickName = dialog.editText?.text.toString()
                    // Emoji表情过滤
                    if (Tools.containsEmoji(nickName)){
                        dialog.yesButton?.let { Tools.setViewButtonEnable(it, false) }
                        ToastUtils.showLong(getString(R.string.starry_add_contact_judge))
                        dialog.yesButton?.postDelayed({
                            Tools.setViewButtonEnable(dialog.yesButton!!, true)
                                                      }, 1000)
                        return
                    }
                    member.nickName = nickName
                    meetingViewModel.updateNicknameTarget(member)
                    changeShowNickName()
                    if (resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                        if (RomUtils.isXiaomi()) {
                            requireActivity().window?.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)  //显示状态栏
                        }
                    }
                    dialog.dismiss()
                }

                override fun noClick(dialog: StarryChangeNickNameInputDialog) {
                    if (resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
                        if (RomUtils.isXiaomi()) {
                            requireActivity().window?.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)  //显示状态栏
                        }
                    }
                    dialog.dismiss()
                }

            }
        )
        dlg.show()
        //此处设置位置窗体大小
        dlg.window?.setLayout(DisplayUtils.dp2px(requireContext(), 300f), LinearLayout.LayoutParams.WRAP_CONTENT)
//        dlg.window?.setFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE, WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE)
        dlg.editText?.setHintTextColor(resources.getColor(R.color.starry_title_gray))
        dlg.editText?.invalidate()
        dlg.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
        if (resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            if (RomUtils.isXiaomi()) {
                requireActivity().window?.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                requireActivity().window?.statusBarColor = Color.TRANSPARENT
                requireActivity().window?.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)  //显示状态栏
            }
        }

    }

    private fun changeShowNickName(){
        Log.i(TAG, "changeShowNickName.memberList=${meetingViewModel.memberList.value}")
        if (meetingViewModel.memberList.value != null) {
            memberAdapter.rawData = meetingViewModel.memberList.value!!
        }
    }


    @SuppressLint("ResourceAsColor")
    private fun onCreateBottomSheetDialog(member: Member) {
        Log.i(TAG, "onCreateBottomSheetDialog.member=${member}")

        //初始化，上传本地视频和照片页面
        val layoutInflater = activity?.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        // 获取屏幕宽高
        val weight = resources.displayMetrics.widthPixels
        val height = resources.displayMetrics.heightPixels
        //初始化该对象
        val mPopWindow = PopLoadingWindow(layoutInflater, weight, height, member)
        mPopWindow.apply {
            bottom_menu_audio?.setOnClickListener{
                onItemViewClick(member, it)
                this.dismiss()
            }
            bottom_menu_video?.setOnClickListener {
                onItemViewClick(member, it)
                this.dismiss()
            }
            bottom_menu_change_admin?.setOnClickListener {
                onItemViewClick(member, it)
                this.dismiss()
            }
//            bottom_menu_detail?.setOnClickListener {
//                onItemViewClick(member, it)
//                this.dismiss()
//            }
            bottom_menu_del?.setOnClickListener {
                onItemViewClick(member, it)
                this.dismiss()
            }
            bottom_menu_cancel?.setOnClickListener {
                this.dismiss()
            }
            popup_window_dialog_rl?.setOnClickListener{
                this.dismiss()
            }

            show()
        }
    }

    /**
     * 根据是否是主持人来 控制 全部静音和房间锁定按钮是否显示
     */
    private fun updateControlVisible() {
        memberAdapter.setIsSelfAdmin(meetingViewModel.isAdmin())
        adminControlGroup.visibility = if (meetingViewModel.isAdmin()) View.VISIBLE else View.GONE
        user_bottom_bar_add_ll_company?.visibility = if (meetingViewModel.isAdmin()) View.GONE else View.VISIBLE

        // 个人普通用户，无添加按钮
//        val inEnterprise = StarryPreferences.getInstance().starryUserinfoModel?.inEnterprise ?: false
//        if (!inEnterprise){
//            user_bottom_bar_add_ll_company?.visibility = View.GONE
//        }

    }

    override fun initData() {
        super.initData()

        meetingViewModel.memberList.observe(this) {
            Log.i("Jason", "${TAG}.memberList.observe=${it}")


            // 人员列表
            launch {
                delay(100)
                memberAdapter.rawData = it
            }

            // 标题人数
            val maxCount = if (MeetingModel.memberLimitCount > 0) MeetingModel.memberLimitCount else UserHandler.portLimit
//            val currentCount = it.count { it1 ->
//                it1.status == 2 || it1.isAdmin
//            }
            val currentCount = it.size

            val title = getString(R.string.tv_member_title, currentCount, maxCount)
            memberTitleTv.text = title

            updateControlVisible()
        }

        // 锁定状态的图标
        meetingViewModel.isRoomLockedLive.observe(this) { isLocked ->
            if (isLocked) {
                user_bottom_bar_lock_iv?.setImageResource(R.mipmap.starry_user_all_lock_off)
                user_bottom_bar_lock?.text = getString(R.string.starry_user_all_lock_on)
                user_bottom_bar_lock?.setTextColor(getColor(R.color.starry_text_color_red))
                // 锁定房间后, 关闭人员选择Fragment
                // user_bottom_bar_add_ll_company?.visibility = View.GONE
                user_bottom_bar_add_ll_company?.alpha = 0.5f

            } else {
                user_bottom_bar_lock_iv?.setImageResource(R.mipmap.starry_user_all_lock_on)
                user_bottom_bar_lock?.text = getString(R.string.starry_user_all_lock_off)
                user_bottom_bar_lock?.setTextColor(getColor(R.color.starry_common_text_color))
                user_bottom_bar_add_ll_company?.alpha = 1.0f

            }
            updateControlVisible()
        }

        viewModel.userInfo.observe(this){
            updateControlVisible()
        }

        meetingViewModel.displayUids.observe(this){ it1 ->
            val isCheckedMapTmp = LinkedHashMap<String, String>()
            it1.forEach {
                isCheckedMapTmp[it.accountID] = it.nickName ?: ""
            }
            contactViewModel.isDisableCheckedMap.value = isCheckedMapTmp
//            logI("${TAG}.displayUids.observe.isCheckedMapTmp=${isCheckedMapTmp}")
        }

        ModelManager.membersModel.addressBookList.observe(this, Observer {
            changeShowNickName()
        })
    }

    /**
     * 添加人员选择Fragment
     */
    private fun addChooseFragment() {
//        ToastUtils.showLong("添加人员选择Fragment")
        viewModel.getEnterpriseMembers()
        viewModel.starryPrePageName = ""

        // 发起会议，选择公司和人员
        viewModel.setSelectType(StarryConstants.STARRY_SELECT_TYPE_ADD)
        contactViewModel.isCheckedMap.value?.clear()
        contactViewModel.tempCheckedMap.value?.clear()

        val isCheckedMapTmp = LinkedHashMap<String, String>()

        logI("${TAG}.addChooseFragment.meetingViewModel.displayUids.size=${meetingViewModel.displayUids.value?.size},${meetingViewModel.displayUids.value}")
        meetingViewModel.displayUids.value?.forEach {
            isCheckedMapTmp[it.accountID] = it.nickName ?: ""
        }

        // 2.1 发起会议：点击发起会议，显示密码开关页面。（单个企业也会显示）
        viewModel.meetingCompanyListFragment = MeetingCompanyListFragment()
        if ( MeetingModel.currentOrientationStatus == Configuration.ORIENTATION_LANDSCAPE ) {
            viewModel.meetingCompanyListFragment?.show(
                ByScreenParams(),
                AnimDirection.RIGHT
            )
        }else{
            viewModel.meetingCompanyListFragment?.show(
                ByScreenParams(),
                AnimDirection.BOTTOM
            )
        }
    }

    private fun onClickEnterCompany(enterprise: StarryEnterpriseModel){
        viewModel.setCurrentUserTypeCompany(enterprise)
        viewModel.starryPrePageName = ""

        if ( MeetingModel.currentOrientationStatus == Configuration.ORIENTATION_LANDSCAPE ) {
            MeetingCompanyListContactsFragment().show(
                ByScreenParams(),
                AnimDirection.RIGHT
            )
        }else {
//            val intent = Intent(requireActivity(), StarryCompanyListContactsActivity::class.java)
//            ActivityUtils.startActivity(intent)
            MeetingCompanyListContactsFragment().show(
                ByScreenParams(),
                AnimDirection.BOTTOM
            )
        }
    }

    override fun handleBackPressed(): Boolean {
        //处理自己的逻辑
        logI("${TAG}.handleBackPressed-关闭")
        controlBarVM.memberListFragment = null
        dismiss()
        return true
    }

}