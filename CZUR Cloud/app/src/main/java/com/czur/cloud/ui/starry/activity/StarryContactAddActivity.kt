package com.czur.cloud.ui.starry.activity

import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.R
import com.czur.cloud.event.EventType
import com.czur.cloud.event.StarryCommonEvent
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.ui.starry.base.StarryNewBaseActivity
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.meeting.baselib.utils.addNoSpaceFilter
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.singleClick
import com.czur.cloud.ui.starry.viewmodel.StarryContactViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import kotlinx.android.synthetic.main.starry_activity_contact_add.*
import kotlinx.android.synthetic.main.starry_layout_top_bar.*
import org.greenrobot.eventbus.EventBus

/**
 */
class StarryContactAddActivity : StarryNewBaseActivity() {

    override fun getLayout(): Int = R.layout.starry_activity_contact_add

    private val viewModel by lazy { StarryContactViewModel() }
    private val starryViewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryViewModel::class.java)
    }

    override fun initViews() {
        super.initViews()
        // title
        user_title?.text = getString(R.string.starry_title_add_contact)

        // back btn
        user_back_btn?.singleClick {
            ActivityUtils.finishActivity(this)
        }

        // Add
        contact_add_btn?.singleClick {
            addAddressBook()
        }

        contact_mobile_title?.addTextChangedListener(object : TextWatcher {
            private var temp: CharSequence? = null
            private var selectionStart = 0
            private var selectionEnd = 0

            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) { }

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                temp = s
            }

            override fun afterTextChanged(s: Editable) {
                selectionStart = contact_mobile_title?.selectionStart ?: 0
                selectionEnd = contact_mobile_title?.selectionEnd ?: 0
                val str = temp.toString()
                if (!TextUtils.isEmpty(str)) {
                    val l = str.length
                    if (l > StarryConstants.MEETING_NO_LEN_11) {
                        s.delete(selectionStart - 1, selectionEnd)
                        val tempSelection = selectionEnd
                        contact_mobile_title?.text = s
                        contact_mobile_title?.setSelection(tempSelection)
                    }
                }
                judgeButtonStatus()
            }
        })
        // 添加禁止输入空格的过滤器
        contact_mobile_title?.addNoSpaceFilter()

        contact_user_name?.addTextChangedListener(object : TextWatcher {
            private var temp: CharSequence? = null
            private var selectionStart = 0
            private var selectionEnd = 0
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                temp = s
            }

            override fun afterTextChanged(s: Editable) {
                selectionStart = contact_user_name?.selectionStart ?: 0
                selectionEnd = contact_user_name?.selectionEnd ?: 0
                val str = temp.toString()
                if (!TextUtils.isEmpty(str)) {
                    val l = Tools.getTextLength(str)
                    if (l > StarryConstants.MAX_DEVICE_NAME_LENGTH) {
                        s.delete(selectionStart - 1, selectionEnd)
                        val tempSelection = selectionEnd
                        contact_user_name?.text = s
                        contact_user_name?.setSelection(tempSelection)
                    }
                }
                judgeButtonStatus()
            }
        })
        // 添加禁止输入空格的过滤器
        contact_user_name?.addNoSpaceFilter()

        contact_user_name?.apply {
            isFocusable = true
            isFocusableInTouchMode = true
            requestFocus()
            findFocus()
        }
        this.window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)

        judgeButtonStatus()
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

    }

    private fun judgeButtonStatus() {
        val name = contact_user_name?.text ?: ""
        val mobile = contact_mobile_title?.text ?: ""

        if (name.isEmpty() || mobile.isEmpty()){
            Tools.setViewButtonEnable(contact_add_btn, false)
        }else{
            Tools.setViewButtonEnable(contact_add_btn, true)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
    }

    private fun addAddressBook() {
        val user_name = (contact_user_name?.text?.toString() ?: "").trim()
        val user_mobile = (contact_mobile_title?.text?.toString() ?: "").trim()
        if ((user_name == "") || (user_mobile == "")){
            ToastUtils.showLong(getString(R.string.starry_add_contact_msg))
            return
        }

        // 判断名称长度
        val tmp_len = Tools.getTextLength(user_name)
        if (tmp_len > StarryConstants.MAX_DEVICE_NAME_LENGTH){
            ToastUtils.showLong(getString(R.string.starry_add_contact_name_long))
            return
        }

        // 特殊字符检测
//        if (Tools.inputJudge(user_name) || Tools.containsEmoji(user_name)){
        if (Tools.containsEmoji(user_name)){
            ToastUtils.showLong(getString(R.string.starry_add_contact_judge))
            return
        }

        // 添加联系人账号位数调整：由之前的6位或11位有效调整为：
        // 输入6-11位数字均有效，其他位数按原有错误提示处理。(海外+国内版)
        if ((user_mobile.length < StarryConstants.MEETING_NO_LEN_6) ||
            (user_mobile.length > StarryConstants.MEETING_NO_LEN_11)){
            ToastUtils.showLong(getString(R.string.starry_add_contact_msg_len))
            return
        }

        if (user_mobile == StarryPreferences.getInstance().accountNo) {
            ToastUtils.showLong(R.string.starry_contact_add_self_msg)
            return
        }

        // 本地检查是否重复
        if (checkRepeatAddressBook(user_mobile)){
            ToastUtils.showLong(R.string.starry_contact_add_repeat_msg)
            return
        }

        // 账号输入“0”开头的6位/11位
        if (user_mobile.startsWith("0")){
            ToastUtils.showLong(R.string.starry_contact_add_zero_msg)
            return
        }

        if (!NetworkUtils.isConnected()) {
            return
        }

        val manager: InputMethodManager =
            getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        if (manager != null) manager.hideSoftInputFromWindow(
            contact_user_name?.windowToken,
            InputMethodManager.HIDE_NOT_ALWAYS
        )

        val accountId = "" //StarryPreferences.getInstance().accountNo
        launch {
            val flag = viewModel.addAddressBook(
                accountId,
                user_mobile,
                user_name
            )

            if (flag) {
                EventBus.getDefault().post(StarryCommonEvent(EventType.STARRY_CONTACT_ADDTO, ""))
//                ToastUtils.showLong(getString(R.string.starry_add_contact_success))
                ActivityUtils.finishActivity(StarryContactAddActivity::class.java)
            } else {
                ToastUtils.showLong(getString(R.string.starry_add_contact_fail))
            }
        }
    }

    // 本地检查是否重复
    private fun checkRepeatAddressBook(userMobile: String): Boolean {
        val count = starryViewModel.currentContactsList.value?.count{
            it.meetingNo == userMobile
        } ?: 0
        return count > 0
    }
}