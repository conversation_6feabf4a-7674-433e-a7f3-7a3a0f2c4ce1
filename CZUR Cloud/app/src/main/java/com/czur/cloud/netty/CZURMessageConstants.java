package com.czur.cloud.netty;

public class CZURMessageConstants {

    /**
     * 消息名称
     */
    public enum MessageName {
        // Starry Message
        // Starry
        // 新注册消息通知
        MESSAGE_ENTERPRISE_NOTICE("NEW_NOTICE"),

        // module是contacts  添加联系人(增删改)
        MESSAGE_NOTICE_MODULE_CONTACTS("contacts"),

        // module是enterprise
        MESSAGE_NOTICE_MODULE_ENTERPRISE("enterprise"),
        // action是NEW_NOTICE，module是other_device_login
        MESSAGE_NOTICE_MODULE_OTHER_DEVICE_LOGIN("other_device_login"),

        // MEETING会议相关
        MESSAGE_STARRY_MEETING("MEETING"),

        //接收到正在进行的会议
        MESSAGE_STARRY_CHECK_MEETING_LIST("CHECK_MEETING_LIST"),

        // SYNC消息
        MESSAGE_COMMON_SYNC("SYNC"),


        // 接收到 服务器的 指令
        MESSAGE_STARRY_MEETING_CMD("cmd"),

        //接收到 更新电话号的指令
        MESSAGE_STARRY_MEETING_UPDATE_PHONE_CMD("updatePhone"),


        ////////// cmd start ////////
        // 接收到的会议结束指令
        MESSAGE_STARRY_MEETING_CMD_STOP("stop"),
        //接收到的离开会议室指令
        MESSAGE_STARRY_MEETING_CMD_REMOVE("remove"),
        //接收到的音频控制关指令
        MESSAGE_STARRY_MEETING_CMD_MUTE_AUDIO("muteAudio"),
        //接收到的音频控制开指令
        MESSAGE_STARRY_MEETING_CMD_OPEN_AUDIO("openAudio"),
        //接收到的视频控制关指令
        MESSAGE_STARRY_MEETING_CMD_MUTE_VIDEO("muteVideo"),
        //接收到的视频控制开指令
        MESSAGE_STARRY_MEETING_CMD_OPEN_VIDEO("openVideo"),
        //接收到的成为会议管理员指令
        MESSAGE_STARRY_MEETING_CMD_HOST("host"),
        //接收到的开始屏幕分享指令
        MESSAGE_STARRY_MEETING_CMD_SHARE("share"),
        //接收到的开始屏幕分享指令
        MESSAGE_STARRY_MEETING_CMD_STOPSHARE("stopShare"),
        MESSAGE_STARRY_MEETING_CMD_QUITSHARE("quitShare"),
        //接收到更改绑定手机号的指令
        MESSAGE_STARRY_MEETING_CMD_UPDATE_USER_PHONE("update_phone"),
        // 其他端加入会议时收到的消息
        MESSAGE_STARRY_MEETING_CMD_OTHER_JOINED("otherJoined"),

        //接收到 注销退出指令
        MESSAGE_STARRY_CMD_DE_REGISTER("de_register"),

        // 长时间挂机熔断处理消息
        MESSAGE_STARRY_MEETING_CMD_STOP_FORCE("stop_force"),

        // 多端只能加入一个会议
        MESSAGE_STARRY_MEETING_CMD_OTHER_MEETING_JIONED("otherMeetingJoined"),

        //////// cmd end //////////

        //userUpdate 状态修改的成员信息
        MESSAGE_STARRY_ROOM_USER_UPDATE("userUpdate"),

        //roomUserList 会议成员列表
        MESSAGE_STARRY_ROOM_USER_LIST("roomUserList"),

        // starry加入会议
        MESSAGE_STARRY_JOIN_MEETING("StarryJoinMeeting"),

        // starry 管理员暂离后,会议人员全部退出,触发
        MESSAGE_STARRY_CLOSE_MEETING("CLOSE_MEETING"),

        // AuraMate
        /**
         * 注册上线
         **/
        MESSAGE_REGISTER_ONLINE("RegisterOnline"),
        /**
         * 设备上线通知
         **/
        MESSAGE_DEVICE_ONLINE("DeviceOnline"),
        /**
         * 检查设备在线
         **/
        MESSAGE_CHECK_DEVICE_IS_ONLINE("CheckDeviceIsOnline"),
        /**
         * 检查手机是否在线（回）
         **/
        MESSAGE_CHECK_APP_IS_ONLINE("CheckAppIsOnline"),

        MESSAGE_LIGHT_SWITCH("LightSwitch"),
        /**
         * 开灯
         **/
        MESSAGE_LIGHT_ON("LightSwitchOn"),
        /**
         * 关灯
         **/
        MESSAGE_LIGHT_OFF("LightSwitchOff"),
        /**
         * 灯光亮度等级
         **/
        MESSAGE_LIGHT_LEVEL("LightLevel"),
        /**
         * 灯光模式
         **/
        MESSAGE_LIGHT_MODE("LightMode"),
        /**
         * 音量
         **/
        MESSAGE_VOLUME_LEVEL("SPReminderSensitivityVolume"),

        /**
         * 设置提醒灵敏度
         **/
        MESSAGE_SITTING_POSITION_SENSITIVITY_LEVEL("SPReminderSensitivityLevel"),
        /**
         * 设置提醒灵敏度开关
         **/
        MESSAGE_SITTING_POSITION_SENSITIVITY_SWITCH("SPReminderSwitch"),

        /**
         * 设置错误坐姿查看图片开关
         **/
        MESSAGE_SITTING_WRONG_SIT_SWITCH("SPWrongSitSwitch"),

        /**
         * APP准备好去视频
         **/
        MESSAGE_APP_READY_FOR_VIDEO("AppReadyForVideo"),
        /**
         * 校验设备准备视频
         **/
        MESSAGE_DEVICE_READY_FOR_VIDEO("DeviceReadyForVideo"),
        /**
         * 解绑
         **/
        MESSAGE_UNBIND_DEVICE("UnbindDevice"),
        /**
         * 添加共享用户
         **/
        MESSAGE_TRANSFER_DEVICE("TransferDevice"),
        /**
         * 设备端和手机端关系发生改变
         **/
        MESSAGE_DEVICE_RELATION_CHANGE("DeviceRelationshipChange"),
        /**
         * 坐姿校准
         **/
        MESSAGE_SITTING_POSITION_CALIBRATE("SPCalibrateVideoChat"),
        /**
         * 坐姿校准拍照
         **/
        MESSAGE_SITTING_POSITION_PHOTO("SPCalibrate"),
        /**
         * 切换摄像头
         **/
        MESSAGE_VIDEO_SWITCH_CAMERA("VideoCameraSwitch"),
        /**
         * 切换系统语言
         **/
        MESSAGE_CHANGE_LANGUAGE("ChangeLanguage"),
        /**
         * 通知升级
         **/
        MESSAGE_UPDATE_FW("updateFW"),
        /**
         * 通知检查升级
         **/
        MESSAGE_NEED_CHECK_OTA_UPDATE("needCheckOTAUpdate"),
        /**
         * 高清查看
         **/
        MESSAGE_HD_VIEW("HDView"),
        MESSAGE_HD_VIDEO("VideoCameraSwitchHD"),
        START_RECORD("startRecord"),
        STOP_RECORD("stopRecord"),
        /**
         *手机端检查设备端视频通话是否有效
         */
        MESSAGE_CHECK_VIDEO_REQUEST_ACTIVE("CheckVideoRequestActive"),

        /**
         * 高清查看保存
         **/
        MESSAGE_HD_VIEW_SAVE("HDViewSave"),

        /**
         * 高清查看保存
         **/
        MESSAGE_HD_VIEW_SAVE_V2("HDViewSave_V2"),

        /**
         * 主动通知退出视频房间
         **/
        MESSAGE_VIDEO_REQUEST_CANCEL("VideoRequestCancel"),
        /**
         * 设备退出房间
         **/
        MESSAGE_VIDEO_CANCEL("VideoCancel"),
        /**
         * 视频ACTION
         **/
        MESSAGE_CALL_VIDEO("CALL_VIDEO"),
        MESSAGE_NO_INCOMING_CALL("NO_INCOMING_CALL"),
        MESSAGE_CALL_VIDEO_TRANSFER("CALL_VIDEO_T"),
        MESSAGE_AURA_MATE("AURA_API"),
        // 停止呼入页面
        MESSAGE_STARRY_STOP_CALL("STOP_CALL"),

        /**
         * 智能省电
         */
        MESSAGE_SMART_POWER_SAVING("SmartPowerSaving"),

        /**
         * 久坐提醒开关
         */
        MESSAGE_SEDENRARY_REMINDER_SWITCH("SedentaryReminderSwitch"),

        /**
         * 久坐提醒时间间隔
         */
        MESSAGE_SEDENRARY_REMINDER_DURATION("SedentaryReminderDuration");


        private String msg;

        MessageName(String msg) {
            this.msg = msg;
        }

        public String getMsg() {
            return msg;
        }

    }

    /**
     * 灯光亮度等级
     */
    public enum LightLevel {
        LEVEL_1("1"),
        LEVEL_2("2"),
        LEVEL_3("3"),
        LEVEL_4("4"),
        LEVEL_5("5"),
        LEVEL_6("6");

        private String level;

        LightLevel(String level) {
            this.level = level;
        }

        public String getLevel() {
            return level;
        }
    }

    /**
     * 灯光模式
     */
    public enum LightMode {

        LIGHT_MODE_NATURAL("1"),
        LIGHT_MODE_Read("2"),
        LIGHT_MODE_COMPUTER("3"),
        LIGHT_MODE_NIGHT("4");

        private String mode;

        LightMode(String mode) {
            this.mode = mode;
        }

        public String getMode() {
            return mode;
        }
    }

    /**
     * 灯光开关状态
     */
    public enum LightSwitch {
        LIGHT_SWITCH_ON("On"),
        LIGHT_SWITCH_OFF("Off");

        private String lightSwitch;

        LightSwitch(String lightSwitch) {
            this.lightSwitch = lightSwitch;
        }

        public String getLightSwitch() {
            return lightSwitch;
        }
    }

    /**
     * 坐姿提醒开关状态
     */
    public enum SensitivitySwitch {
        SENSITIVITY_SWITCH_ON("On"),
        SENSITIVITY_SWITCH_OFF("Off");

        private String sensitivitySwitch;

        SensitivitySwitch(String sensitivitySwitch) {
            this.sensitivitySwitch = sensitivitySwitch;
        }

        public String getSensitivitySwitch() {
            return sensitivitySwitch;
        }
    }

    /**
     * 坐姿提醒开关状态
     */
    public enum WrongSitSwitch {
        WRONG_SIT_SWITCH_ON("On"),
        WRONG_SIT_SWITCH_OFF("Off");

        private String wrongSitSwitch;

        WrongSitSwitch(String sensitivitySwitch) {
            this.wrongSitSwitch = sensitivitySwitch;
        }

        public String getWrongSitSwitch() {
            return wrongSitSwitch;
        }
    }

    /**
     * 坐姿提醒灵敏度等级
     */
    public enum SensitivityLevel {
        SENSITIVITY_LEVEL_HIGH("High"),
        SENSITIVITY_LEVEL_MEDIUM("Medium"),
        SENSITIVITY_LEVEL_LOW("Low");

        private String level;

        SensitivityLevel(String level) {
            this.level = level;
        }

        public String getLevel() {
            return level;
        }
    }

    public enum Video {
        ACTION("AURA_API"),
        METHOD("callStatusReport"),
        APPID_TO("com.czur.aura.home");
        private String value;

        Video(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum DeviceNotice {
        NOTIFICATION("auraNotification"),
        OFFLINE("OFFLINE"),
        FILE("FILE"),
        REPORT("REPORT");
        private String value;

        DeviceNotice(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum CallIn {
        YES("YES"),
        NO("NO");
        private String value;

        CallIn(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum CalibrateResult {
        OK("OK"),
        NG("NG"),
        FAILD("FAILD");
        private String value;

        CalibrateResult(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * 坐姿提醒灵敏度等级
     */
    public enum CameraSwitch {
        CAMERA_FRONT("Front"),
        CAMERA_ABOVE("Above");


        private String camera;

        CameraSwitch(String camera) {
            this.camera = camera;
        }

        public String getCamera() {
            return camera;
        }
    }

    /**
     * 灯光模式
     */
    public enum HdView {

        TAKING_PICTURES("TakingPictures"),
        TAKING_PICTURES_FAILURE("TakingPicturesFailure"),
        UPLOADING("Uploading"),
        UPLOADING_FAILURE("UploadingFailure"),
        UPLOAD_COMPLETED("UploadCompleted"),
        REJECT("Reject"),
        ERROR("Error");

        private String status;

        HdView(String status) {
            this.status = status;
        }

        public String getStatus() {
            return status;
        }
    }


}
