package com.czur.cloud.ui.component.popup;

import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;

/**
 * Created by Yz on 2018/3/20
 * Email：<EMAIL>
 */

public class EtBottomColorDialogPopup extends Dialog {
    public static final float DIMMED_OPACITY = 0.2f;


    public EtBottomColorDialogPopup(Context context) {
        super(context);
    }

    public EtBottomColorDialogPopup(Context context, int theme) {
        super(context, theme);
    }


    public static class Builder implements View.OnClickListener {
        private Context mContext;
        private RelativeLayout etBottomSheetAutoBtn;
        private TextView etBottomSheetAutoTv;
        private ImageView etBottomSheetAutoImg;
        private RelativeLayout etBottomSheetColorBtn;
        private TextView etBottomSheetColorTv;
        private ImageView etBottomSheetColorImg;
        private RelativeLayout etBottomSheetCardBtn;
        private TextView etBottomSheetCardTv;
        private ImageView etBottomSheetCardImg;
        private RelativeLayout etBottomSheetGrayBtn;
        private TextView etBottomSheetGrayTv;
        private ImageView etBottomSheetGrayImg;
        private RelativeLayout etBottomSheetBlackBtn;
        private TextView etBottomSheetBlackTv;
        private ImageView etBottomSheetBlackImg;
        private RelativeLayout etBottomSheetWhiteBtn;
        private TextView etBottomSheetWhiteTv;
        private ImageView etBottomSheetWhiteImg;
        private RelativeLayout etBottomSheetNoneBtn;
        private TextView etBottomSheetNoneTv;
        private ImageView etBottomSheetNoneImg;


        private RelativeLayout etPreviewBottomSheetCancelBtn;


        public Builder(Context mContext, OnBottomSheetClickListener onBottomSheetClickListener) {
            this.mContext = mContext;
            this.onBottomSheetClickListener = onBottomSheetClickListener;
        }

        /**
         * 点击事件接口
         **/
        public interface OnBottomSheetClickListener {
            /**
             * @param viewId
             */
            void onClick(int viewId);
        }

        private OnBottomSheetClickListener onBottomSheetClickListener;

        private void setOnBottomSheetClickListener(OnBottomSheetClickListener onBottomSheetClickListener) {
            this.onBottomSheetClickListener = onBottomSheetClickListener;

        }


        @Override
        public void onClick(View v) {
            switch (v.getId()) {
                case R.id.et_bottom_sheet_auto_btn:
                    etBottomSheetAutoTv.setTextColor(mContext.getResources().getColor(R.color.blue_29b0d7));
                    etBottomSheetAutoImg.setVisibility(View.VISIBLE);

                    etBottomSheetColorTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetColorImg.setVisibility(View.GONE);

                    etBottomSheetCardTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetCardImg.setVisibility(View.GONE);

                    etBottomSheetGrayTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetGrayImg.setVisibility(View.GONE);

                    etBottomSheetBlackTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetBlackImg.setVisibility(View.GONE);

                    etBottomSheetWhiteTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetWhiteImg.setVisibility(View.GONE);

                    etBottomSheetNoneTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetNoneImg.setVisibility(View.GONE);

                    if (onBottomSheetClickListener != null) {
                        onBottomSheetClickListener.onClick(R.id.et_bottom_sheet_auto_btn);
                    }
                    break;
                case R.id.et_bottom_sheet_color_btn:
                    etBottomSheetAutoTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetAutoImg.setVisibility(View.GONE);

                    etBottomSheetColorTv.setTextColor(mContext.getResources().getColor(R.color.blue_29b0d7));
                    etBottomSheetColorImg.setVisibility(View.VISIBLE);

                    etBottomSheetCardTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetCardImg.setVisibility(View.GONE);

                    etBottomSheetGrayTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetGrayImg.setVisibility(View.GONE);

                    etBottomSheetBlackTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetBlackImg.setVisibility(View.GONE);

                    etBottomSheetWhiteTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetWhiteImg.setVisibility(View.GONE);

                    etBottomSheetNoneTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetNoneImg.setVisibility(View.GONE);
                    if (onBottomSheetClickListener != null) {
                        onBottomSheetClickListener.onClick(R.id.et_bottom_sheet_color_btn);
                    }
                    break;
                case R.id.et_bottom_sheet_card_btn:
                    etBottomSheetAutoTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetAutoImg.setVisibility(View.GONE);

                    etBottomSheetColorTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetColorImg.setVisibility(View.GONE);

                    etBottomSheetCardTv.setTextColor(mContext.getResources().getColor(R.color.blue_29b0d7));
                    etBottomSheetCardImg.setVisibility(View.VISIBLE);

                    etBottomSheetGrayTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetGrayImg.setVisibility(View.GONE);

                    etBottomSheetBlackTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetBlackImg.setVisibility(View.GONE);

                    etBottomSheetWhiteTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetWhiteImg.setVisibility(View.GONE);

                    etBottomSheetNoneTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetNoneImg.setVisibility(View.GONE);
                    if (onBottomSheetClickListener != null) {
                        onBottomSheetClickListener.onClick(R.id.et_bottom_sheet_card_btn);
                    }
                    break;
                case R.id.et_bottom_sheet_gray_btn:
                    etBottomSheetAutoTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetAutoImg.setVisibility(View.GONE);

                    etBottomSheetColorTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetColorImg.setVisibility(View.GONE);

                    etBottomSheetCardTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetCardImg.setVisibility(View.GONE);

                    etBottomSheetGrayTv.setTextColor(mContext.getResources().getColor(R.color.blue_29b0d7));
                    etBottomSheetGrayImg.setVisibility(View.VISIBLE);

                    etBottomSheetBlackTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetBlackImg.setVisibility(View.GONE);


                    etBottomSheetWhiteTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetWhiteImg.setVisibility(View.GONE);

                    etBottomSheetNoneTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetNoneImg.setVisibility(View.GONE);
                    if (onBottomSheetClickListener != null) {
                        onBottomSheetClickListener.onClick(R.id.et_bottom_sheet_gray_btn);
                    }
                    break;
                case R.id.et_bottom_sheet_black_btn:
                    etBottomSheetAutoTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetAutoImg.setVisibility(View.GONE);

                    etBottomSheetColorTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetColorImg.setVisibility(View.GONE);

                    etBottomSheetCardTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetCardImg.setVisibility(View.GONE);

                    etBottomSheetGrayTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetGrayImg.setVisibility(View.GONE);

                    etBottomSheetBlackTv.setTextColor(mContext.getResources().getColor(R.color.blue_29b0d7));
                    etBottomSheetBlackImg.setVisibility(View.VISIBLE);


                    etBottomSheetWhiteTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetWhiteImg.setVisibility(View.GONE);

                    etBottomSheetNoneTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetNoneImg.setVisibility(View.GONE);
                    if (onBottomSheetClickListener != null) {
                        onBottomSheetClickListener.onClick(R.id.et_bottom_sheet_black_btn);
                    }
                    break;
                case R.id.et_bottom_sheet_white_btn:
                    etBottomSheetAutoTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetAutoImg.setVisibility(View.GONE);

                    etBottomSheetColorTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetColorImg.setVisibility(View.GONE);

                    etBottomSheetCardTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetCardImg.setVisibility(View.GONE);

                    etBottomSheetGrayTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetGrayImg.setVisibility(View.GONE);

                    etBottomSheetBlackTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetBlackImg.setVisibility(View.GONE);


                    etBottomSheetWhiteTv.setTextColor(mContext.getResources().getColor(R.color.blue_29b0d7));
                    etBottomSheetWhiteImg.setVisibility(View.VISIBLE);

                    etBottomSheetNoneTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetNoneImg.setVisibility(View.GONE);
                    if (onBottomSheetClickListener != null) {
                        onBottomSheetClickListener.onClick(R.id.et_bottom_sheet_white_btn);
                    }
                    break;

                case R.id.et_bottom_sheet_none_btn:
                    etBottomSheetAutoTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetAutoImg.setVisibility(View.GONE);

                    etBottomSheetColorTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetColorImg.setVisibility(View.GONE);

                    etBottomSheetCardTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetCardImg.setVisibility(View.GONE);

                    etBottomSheetGrayTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetGrayImg.setVisibility(View.GONE);

                    etBottomSheetBlackTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetBlackImg.setVisibility(View.GONE);


                    etBottomSheetWhiteTv.setTextColor(mContext.getResources().getColor(R.color.black_22));
                    etBottomSheetWhiteImg.setVisibility(View.GONE);

                    etBottomSheetNoneTv.setTextColor(mContext.getResources().getColor(R.color.blue_29b0d7));
                    etBottomSheetNoneImg.setVisibility(View.VISIBLE);
                    if (onBottomSheetClickListener != null) {
                        onBottomSheetClickListener.onClick(R.id.et_bottom_sheet_none_btn);
                    }
                    break;
                case R.id.et_preview_bottom_sheet_cancel_btn:
                    if (onBottomSheetClickListener != null) {
                        onBottomSheetClickListener.onClick(R.id.et_preview_bottom_sheet_cancel_btn);
                    }
                    break;
                default:
                    break;
            }
        }

        public EtBottomColorDialogPopup create() {
            LayoutInflater inflater = (LayoutInflater) mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);

            final EtBottomColorDialogPopup dialog = new EtBottomColorDialogPopup(mContext, R.style.SocialAccountDialogStyle);
            View layout = commonCustomPopLayout(inflater, dialog);
            dialog.setContentView(layout);
            dialog.setCanceledOnTouchOutside(true);
            WindowManager.LayoutParams params = dialog.getWindow().getAttributes();
            params.dimAmount = DIMMED_OPACITY;
            params.gravity = Gravity.BOTTOM;
            params.width = WindowManager.LayoutParams.MATCH_PARENT;
            params.height = SizeUtils.dp2px(410);
            dialog.getWindow().setAttributes(params);
            return dialog;
        }

        private View commonCustomPopLayout(LayoutInflater inflater, final EtBottomColorDialogPopup dialog) {
            WindowManager.LayoutParams lp = dialog.getWindow().getAttributes();
            lp.dimAmount = 0.8f;
            dialog.getWindow().setAttributes(lp);
            dialog.getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);

            View layout = inflater.inflate(R.layout.dialog_et_preview_color_bottom_sheet, null, false);
            RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(RelativeLayout.LayoutParams.MATCH_PARENT,
                    RelativeLayout.LayoutParams.WRAP_CONTENT);
            dialog.addContentView(layout, params);


            etBottomSheetAutoBtn = (RelativeLayout) layout.findViewById(R.id.et_bottom_sheet_auto_btn);
            etBottomSheetAutoTv = (TextView) layout.findViewById(R.id.et_bottom_sheet_auto_tv);
            etBottomSheetAutoImg = (ImageView) layout.findViewById(R.id.et_bottom_sheet_auto_img);
            etBottomSheetColorBtn = (RelativeLayout) layout.findViewById(R.id.et_bottom_sheet_color_btn);
            etBottomSheetColorTv = (TextView) layout.findViewById(R.id.et_bottom_sheet_color_tv);
            etBottomSheetColorImg = (ImageView) layout.findViewById(R.id.et_bottom_sheet_color_img);
            etBottomSheetCardBtn = (RelativeLayout) layout.findViewById(R.id.et_bottom_sheet_card_btn);
            etBottomSheetCardTv = (TextView) layout.findViewById(R.id.et_bottom_sheet_card_tv);
            etBottomSheetCardImg = (ImageView) layout.findViewById(R.id.et_bottom_sheet_card_img);
            etBottomSheetGrayBtn = (RelativeLayout) layout.findViewById(R.id.et_bottom_sheet_gray_btn);
            etBottomSheetGrayTv = (TextView) layout.findViewById(R.id.et_bottom_sheet_gray_tv);
            etBottomSheetGrayImg = (ImageView) layout.findViewById(R.id.et_bottom_sheet_gray_img);
            etBottomSheetBlackBtn = (RelativeLayout) layout.findViewById(R.id.et_bottom_sheet_black_btn);
            etBottomSheetBlackTv = (TextView) layout.findViewById(R.id.et_bottom_sheet_black_tv);
            etBottomSheetBlackImg = (ImageView) layout.findViewById(R.id.et_bottom_sheet_black_img);
            etBottomSheetWhiteBtn = (RelativeLayout) layout.findViewById(R.id.et_bottom_sheet_white_btn);
            etBottomSheetWhiteTv = (TextView) layout.findViewById(R.id.et_bottom_sheet_white_tv);
            etBottomSheetWhiteImg = (ImageView) layout.findViewById(R.id.et_bottom_sheet_white_img);
            etBottomSheetNoneBtn = (RelativeLayout) layout.findViewById(R.id.et_bottom_sheet_none_btn);
            etBottomSheetNoneTv = (TextView) layout.findViewById(R.id.et_bottom_sheet_none_tv);
            etBottomSheetNoneImg = (ImageView) layout.findViewById(R.id.et_bottom_sheet_none_img);

            etPreviewBottomSheetCancelBtn = (RelativeLayout) layout.findViewById(R.id.et_preview_bottom_sheet_cancel_btn);


            etBottomSheetAutoBtn.setOnClickListener(this);
            etBottomSheetColorBtn.setOnClickListener(this);
            etBottomSheetCardBtn.setOnClickListener(this);
            etBottomSheetGrayBtn.setOnClickListener(this);
            etBottomSheetBlackBtn.setOnClickListener(this);
            etBottomSheetNoneBtn.setOnClickListener(this);
            etBottomSheetWhiteBtn.setOnClickListener(this);
            etPreviewBottomSheetCancelBtn.setOnClickListener(this);

            return layout;
        }
    }
}
