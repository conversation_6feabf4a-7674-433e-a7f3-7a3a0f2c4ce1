package com.czur.cloud.ui.auramate;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;

public class AuraMateConnectFailActivity extends AuramateBaseActivity implements View.OnClickListener {

    private TextView tvReconnect;
    private TextView tvDial;
    private ImageView imgBack;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_mate_connect_fail);
        initView();
        initListener();
    }

    @Override
    protected boolean PCNeedFinish() {
        return !TextUtils.isEmpty(equipmentId);
    }

    private void initView() {
        imgBack = findViewById(R.id.img_back);
        tvReconnect = findViewById(R.id.tv_reconnect);
        tvDial = findViewById(R.id.tv_dial);

    }

    private void initListener() {
        imgBack.setOnClickListener(this);
        tvReconnect.setOnClickListener(this);
        tvDial.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.img_back:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.tv_reconnect:
                ActivityUtils.finishToActivity(AuraMateWifiHistoryActivity.class, false);
                break;
            case R.id.tv_dial:
                callPhone(getResources().getString(R.string.dial_number));
                break;
        }
    }

    public void callPhone(String phoneNum) {
        Intent intent = new Intent(Intent.ACTION_DIAL);
        Uri data = Uri.parse("tel:" + phoneNum);
        intent.setData(data);
        startActivity(intent);
    }

}

