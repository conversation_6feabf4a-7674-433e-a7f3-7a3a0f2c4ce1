package com.czur.cloud.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.R;
import com.czur.cloud.entity.AuraMateWrongTagModel;
import com.czur.cloud.util.validator.Validator;

import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class WrongQuestionTagAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_TAG = 0;

    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<AuraMateWrongTagModel> datas;
    private LayoutInflater mInflater;
private int selectPosition;

    /**
     * 构造方法
     */
    public WrongQuestionTagAdapter(Activity activity, List<AuraMateWrongTagModel> datas) {
        this.mActivity = activity;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<AuraMateWrongTagModel> datas,int selectPosition) {
        this.datas = datas;
        this.selectPosition = selectPosition;
        if (Validator.isNotEmpty(datas)){
            setSelect(datas, selectPosition);
        }

    }

    private void setSelect(List<AuraMateWrongTagModel> datas, int selectPosition) {
        for (int i = 0; i < datas.size(); i++) {
            if (i == selectPosition) {
                datas.get(i).setSelect(true);
            } else {
                datas.get(i).setSelect(false);
            }
        }
        notifyDataSetChanged();
    }


    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new TagViewHolder(mInflater.inflate(R.layout.item_wrong_quetion_object, parent, false));
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {

        if (holder instanceof TagViewHolder) {
            final TagViewHolder mHolder = (TagViewHolder) holder;
            mHolder.mItem = datas.get(position);
            mHolder.objectName.setText(mHolder.mItem.getTagName());
            if (mHolder.mItem.isSelect()) {
                mHolder.objectName.setTextColor(mActivity.getResources().getColor(R.color.blue_29b0d7));
            } else {
                mHolder.objectName.setTextColor(mActivity.getResources().getColor(R.color.black_22));
            }

            mHolder.item.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    setSelect(datas, position);
                    if (onTagItemClickListener != null) {
                        onTagItemClickListener.onTagClick(mHolder.mItem, position);
                    }
                }
            });


        }
    }

    public int getTotalSize() {
        return datas.size();
    }


    @Override
    public int getItemViewType(int position) {

        return ITEM_TYPE_TAG;
    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }


    private class TagViewHolder extends ViewHolder {
        public final View mView;
        AuraMateWrongTagModel mItem;
        LinearLayout item;
        TextView objectName;


        TagViewHolder(View itemView) {
            super(itemView);
            mView = itemView;


            item = (LinearLayout) itemView.findViewById(R.id.item);
            objectName = (TextView) itemView.findViewById(R.id.object_name);


        }


    }


    private class AddTagsHolder extends ViewHolder {

        public final View mView;
        RelativeLayout tagAddItem;


        public AddTagsHolder(View view) {
            super(view);
            mView = view;
            tagAddItem = (RelativeLayout) view.findViewById(R.id.tag_add_item);
        }
    }




    private OnTagItemClickListener onTagItemClickListener;

    public void setOnTagItemClickListener(OnTagItemClickListener onTagItemClickListener) {
        this.onTagItemClickListener = onTagItemClickListener;
    }

    public interface OnTagItemClickListener {
        void onTagClick(AuraMateWrongTagModel AuraMateWrongTagModel, int position);
    }


}
