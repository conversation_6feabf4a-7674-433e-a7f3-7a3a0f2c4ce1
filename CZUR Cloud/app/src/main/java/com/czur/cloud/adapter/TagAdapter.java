package com.czur.cloud.adapter;

import android.app.Activity;
import android.graphics.Typeface;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.R;
import com.czur.cloud.entity.realm.PageEntity;
import com.czur.cloud.entity.realm.TagEntity;

import java.util.List;

import io.realm.Realm;
import io.realm.RealmResults;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class TagAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_TAG = 0;
    private static final int ITEM_TYPE_ADD = 1;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<TagEntity> datas;
    private boolean isSelected;
    private LayoutInflater mInflater;
    private int emptyItemCounts;
    private int shouldShowItemCounts;
    private Realm realm;
    private long currentClickTime = 0;

    /**
     * 构造方法
     */
    public TagAdapter(Activity activity, List<TagEntity> datas, boolean isSelected, Realm realm) {
        this.realm = realm;
        this.mActivity = activity;
        this.isSelected = isSelected;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<TagEntity> books) {

        this.isSelected = isSelected;
        this.datas = books;
        notifyDataSetChanged();

    }

    public void refreshData(boolean isSelected) {
        this.isSelected = isSelected;
        notifyDataSetChanged();
    }


    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (viewType == ITEM_TYPE_TAG) {
            return new TagViewHolder(mInflater.inflate(R.layout.item_tag_normal, parent, false));
        } else {
            return new AddTagsHolder(mInflater.inflate(R.layout.item_tag_add, parent, false));
        }
    }




    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {

        if (holder instanceof TagViewHolder) {
            final TagViewHolder mHolder = (TagViewHolder) holder;
            mHolder.mItem = datas.get(position);
            RealmResults<PageEntity> results = realm.where(PageEntity.class)
                    .equalTo("isDelete", 0)
                    .equalTo("tagName", mHolder.mItem.getTagName())
                    .findAll();
            mHolder.tagItemName.setText(mHolder.mItem.getTagName() + String.format(mActivity.
                    getString(R.string.repeat_name_format), results.size() + ""));


            if (isSelected) {
                mHolder.tagItemName.setTypeface(Typeface.DEFAULT_BOLD);
                mHolder.tagInnerItem.setBackgroundResource(R.drawable.btn_rect_6_bg_gray_ec);
                if (mHolder.mItem.getIsSelf() == 0) {
                    mHolder.tagDeleteBtn.setVisibility(View.VISIBLE);
                    mHolder.tagItemName.setTextColor(mActivity.getResources().getColor(R.color.black_22));

                } else {
                    mHolder.tagDeleteBtn.setVisibility(View.VISIBLE);
                    mHolder.tagItemName.setTextColor(mActivity.getResources().getColor(R.color.black_22));
                }

            } else {

                mHolder.tagInnerItem.setClickable(true);
                mHolder.tagInnerItem.setEnabled(true);
                mHolder.tagInnerItem.setBackgroundResource(R.drawable.btn_rect_6_bg_gray_ec);
                mHolder.tagItemName.setTextColor(mActivity.getResources().getColor(R.color.black_22));
                mHolder.tagItemName.setTypeface(Typeface.DEFAULT_BOLD);
                mHolder.tagDeleteBtn.setVisibility(View.GONE);
            }


            mHolder.tagInnerItem.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onTagItemClickListener != null) {
                        if (System.currentTimeMillis() - currentClickTime > 500) {
                            onTagItemClickListener.onTagEntityClick(mHolder.mItem,  holder.getAdapterPosition() );
                            currentClickTime = System.currentTimeMillis();
                        }
                    }
                }
            });
            mHolder.tagDeleteBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onDeleteClickListener != null) {
                        onDeleteClickListener.onTagDeleteClick(mHolder.mItem,  holder.getAdapterPosition() );
                    }
                }
            });


        } else if (holder instanceof AddTagsHolder) {
            final AddTagsHolder mHolder = (AddTagsHolder) holder;
            mHolder.tagAddItem.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (addTagListener != null) {
                        addTagListener.onAddTagClickListener( holder.getAdapterPosition() );

                    }
                }
            });


        }
    }

    public int getTotalSize() {
        return datas.size();
    }


    @Override
    public int getItemViewType(int position) {

        if (position >= 0 && position < datas.size()) {
            return ITEM_TYPE_TAG;
        } else {
            return ITEM_TYPE_ADD;
        }
    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size() + 1;
    }


    private class TagViewHolder extends ViewHolder {
        public final View mView;
        TagEntity mItem;
        RelativeLayout tagItem;
        RelativeLayout tagInnerItem;
        TextView tagItemName;
        ImageView tagDeleteBtn;


        TagViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            tagItem = (RelativeLayout) itemView.findViewById(R.id.tag_inner_item);
            tagInnerItem = (RelativeLayout) itemView.findViewById(R.id.tag_inner_item);
            tagDeleteBtn = (ImageView) itemView.findViewById(R.id.tag_delete_btn);
            tagItemName = (TextView) itemView.findViewById(R.id.tag_item_name);

        }


    }


    private class AddTagsHolder extends ViewHolder {

        public final View mView;
        RelativeLayout tagAddItem;


        public AddTagsHolder(View view) {
            super(view);
            mView = view;
            tagAddItem = (RelativeLayout) view.findViewById(R.id.tag_add_item);
        }
    }


    private AddTagListener addTagListener;

    public interface AddTagListener {
        void onAddTagClickListener(int position);
    }

    public void setAddTagListener(AddTagListener addTagListener) {
        this.addTagListener = addTagListener;
    }

    private OnDeleteClickListener onDeleteClickListener;

    public interface OnDeleteClickListener {
        void onTagDeleteClick(TagEntity tagEntity, int position);
    }

    public void setOnDeleteClickListener(OnDeleteClickListener onDeleteClickListener) {
        this.onDeleteClickListener = onDeleteClickListener;
    }


    private OnTagItemClickListener onTagItemClickListener;

    public void setOnTagItemClickListener(OnTagItemClickListener onTagItemClickListener) {
        this.onTagItemClickListener = onTagItemClickListener;
    }

    public interface OnTagItemClickListener {
        void onTagEntityClick(TagEntity TagEntity, int position);
    }


}
