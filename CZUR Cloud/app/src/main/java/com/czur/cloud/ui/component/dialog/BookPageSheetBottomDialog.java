package com.czur.cloud.ui.component.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.czur.cloud.R;

public class BookPageSheetBottomDialog extends Dialog implements View.OnClickListener {
    private Context mContext;
    private RelativeLayout bookPageCorrectOrderLl;
    private TextView blueBookPageCorrectOrderText;
    private ImageView blueBookPageCorrectOrderImg;
    private TextView bookPageCorrectOrderText;
    private RelativeLayout bookPageInvertedOrderLl;
    private TextView bookPageInvertedOrderText;
    private TextView blueBookPageInvertedOrderText;
    private ImageView blueBookPageInvertedOrderImg;
    private RelativeLayout bookPageDialogCancelBtn;

    private RelativeLayout bookPageCorrectTimeOrderLl;
    private TextView bookPageCorrectTimeOrderText;
    private TextView blueBookPageCorrectTimeOrderText;
    private ImageView bookPageCorrectTimeImg;
    private RelativeLayout bookPageInvertedTimeOrderLl;
    private TextView bookPageInvertedTimeOrderText;
    private TextView blueBookPageInvertedTimeOrderText;
    private ImageView bookPageInvertedTimeImg;

    public BookPageSheetBottomDialog(Context context, OnPageBottomClickListener OnPageBottomClickListener) {
        //重点实现R.style.DialogStyle 动画效果
        this(context, R.style.SocialAccountDialogStyle);
        this.OnPageBottomClickListener = OnPageBottomClickListener;
        mContext = context;
    }

    public BookPageSheetBottomDialog(Context context, int themeResId) {
        super(context, themeResId);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_book_page_sheet);
        WindowManager.LayoutParams params = getWindow().getAttributes();

        bookPageCorrectOrderLl = (RelativeLayout)getWindow(). findViewById(R.id.book_page_correct_order_ll);
        blueBookPageCorrectOrderText = (TextView) getWindow().findViewById(R.id.blue_book_page_correct_order_text);
        blueBookPageCorrectOrderImg = (ImageView) getWindow().findViewById(R.id.blue_book_page_correct_order_img);
        bookPageCorrectOrderText = (TextView)getWindow(). findViewById(R.id.book_page_correct_order_text);
        bookPageInvertedOrderLl = (RelativeLayout) getWindow().findViewById(R.id.book_page_inverted_order_ll);
        bookPageInvertedOrderText = (TextView) getWindow().findViewById(R.id.book_page_inverted_order_text);
        blueBookPageInvertedOrderText = (TextView)getWindow(). findViewById(R.id.blue_book_page_inverted_order_text);
        blueBookPageInvertedOrderImg = (ImageView) getWindow().findViewById(R.id.blue_book_page_inverted_order_img);

        bookPageCorrectTimeOrderLl = (RelativeLayout) findViewById(R.id.book_page_correct_time_order_ll);
        bookPageCorrectTimeOrderText = (TextView) findViewById(R.id.book_page_correct_time_order_text);
        blueBookPageCorrectTimeOrderText = (TextView) findViewById(R.id.blue_book_page_correct_time_order_text);
        bookPageCorrectTimeImg = (ImageView) findViewById(R.id.book_page_correct_time_img);
        bookPageInvertedTimeOrderLl = (RelativeLayout) findViewById(R.id.book_page_inverted_time_order_ll);
        bookPageInvertedTimeOrderText = (TextView) findViewById(R.id.book_page_inverted_time_order_text);
        blueBookPageInvertedTimeOrderText = (TextView) findViewById(R.id.blue_book_page_inverted_time_order_text);
        bookPageInvertedTimeImg = (ImageView) findViewById(R.id.book_page_inverted_time_img);

        bookPageDialogCancelBtn = (RelativeLayout) getWindow().findViewById(R.id.book_page_dialog_cancel_btn);





        //设置显示的位置
        params.gravity = Gravity.BOTTOM;
        //设置dialog的宽度
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        getWindow().setAttributes(params);

        bookPageCorrectOrderLl.setOnClickListener(this);
        bookPageInvertedOrderLl.setOnClickListener(this);
        bookPageCorrectTimeOrderLl.setOnClickListener(this);
        bookPageInvertedTimeOrderLl.setOnClickListener(this);
        bookPageDialogCancelBtn.setOnClickListener(this);


    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.book_page_correct_order_ll:
                resetSelectedItem();
                //selected
                bookPageCorrectOrderText.setVisibility(View.GONE);
                blueBookPageCorrectOrderText.setVisibility(View.VISIBLE);
                blueBookPageCorrectOrderImg.setVisibility(View.VISIBLE);

                if (OnPageBottomClickListener !=null) {
                OnPageBottomClickListener.onItemClick(R.id.book_page_correct_order_ll);
                }
                break;
            case R.id.book_page_inverted_order_ll:
                resetSelectedItem();

                //selected
                bookPageInvertedOrderText.setVisibility(View.GONE);
                blueBookPageInvertedOrderText.setVisibility(View.VISIBLE);
                blueBookPageInvertedOrderImg.setVisibility(View.VISIBLE);

                if (OnPageBottomClickListener !=null) {
                    OnPageBottomClickListener.onItemClick(R.id.book_page_inverted_order_ll);
                }
                break;

            case R.id.book_page_correct_time_order_ll:
                resetSelectedItem();

                //selected
                bookPageCorrectTimeOrderText.setVisibility(View.GONE);
                blueBookPageCorrectTimeOrderText.setVisibility(View.VISIBLE);
                bookPageCorrectTimeImg.setVisibility(View.VISIBLE);

                if (OnPageBottomClickListener !=null) {
                    OnPageBottomClickListener.onItemClick(R.id.book_page_correct_time_order_ll);
                }
                break;
            case R.id.book_page_inverted_time_order_ll:
                resetSelectedItem();

                bookPageInvertedTimeOrderText.setVisibility(View.GONE);
                blueBookPageInvertedTimeOrderText.setVisibility(View.VISIBLE);
                bookPageInvertedTimeImg.setVisibility(View.VISIBLE);

                if (OnPageBottomClickListener !=null) {
                    OnPageBottomClickListener.onItemClick(R.id.book_page_inverted_time_order_ll);
                }
                break;
            case R.id.book_page_dialog_cancel_btn:
                if (OnPageBottomClickListener !=null) {
                    OnPageBottomClickListener.onItemClick(R.id.book_page_dialog_cancel_btn);
                }
                break;
            default:
                break;
        }
    }


    private void resetSelectedItem(){
        // 0
        bookPageCorrectOrderText.setVisibility(View.VISIBLE);
        blueBookPageCorrectOrderText.setVisibility(View.GONE);
        blueBookPageCorrectOrderImg.setVisibility(View.GONE);
        // 1
        bookPageInvertedOrderText.setVisibility(View.VISIBLE);
        blueBookPageInvertedOrderText.setVisibility(View.GONE);
        blueBookPageInvertedOrderImg.setVisibility(View.GONE);
        // 2
        bookPageCorrectTimeOrderText.setVisibility(View.VISIBLE);
        blueBookPageCorrectTimeOrderText.setVisibility(View.GONE);
        bookPageCorrectTimeImg.setVisibility(View.GONE);
        // 3
        bookPageInvertedTimeOrderText.setVisibility(View.VISIBLE);
        blueBookPageInvertedTimeOrderText.setVisibility(View.GONE);
        bookPageInvertedTimeImg.setVisibility(View.GONE);


    }
    /**

       点击事件接口

     **/
    public interface OnPageBottomClickListener {
        /**
         *
         * @param viewId
         */
        void onItemClick(int viewId);
    }
    private OnPageBottomClickListener OnPageBottomClickListener;

    private void setOnPageBottomClickListener(OnPageBottomClickListener onPageBottomClickListener) {
        this.OnPageBottomClickListener = onPageBottomClickListener;

    }


}