package com.czur.cloud.ui.user;

import android.os.Bundle;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.UserInfoEvent;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.ProgressButton;
import com.czur.cloud.ui.home.IndexActivity;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.StringUtilsKt;
import com.czur.cloud.util.validator.Validator;

import org.greenrobot.eventbus.EventBus;

import java.util.Locale;

/**
 * Created by Yz on 2018/2/28.
 * Email：<EMAIL>
 */

public class UserBindEmailActivity extends BaseActivity implements View.OnClickListener {


    private ImageView userBackBtn;
    private TextView userTitle;
    private EditText userBindEmailEdt;
    private EditText userBindEmailCodeEdt;
    private TextView userBindEmailSendCodeTv;
    private ProgressButton userBindEmailBtn;
    private boolean codeHasContent = false;
    private TimeCount timeCount;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private boolean isChangeEmail;
    private long currentTime;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_user_bind_email);
        initComponent();
        registerEvent();
    }

    private void initComponent() {

        isChangeEmail = getIntent().getBooleanExtra("changeEmail", false);
        userBackBtn = (ImageView) findViewById(R.id.user_back_btn);
        userTitle = (TextView) findViewById(R.id.user_title);
        userBindEmailEdt = (EditText) findViewById(R.id.user_bind_email_edt);
        userBindEmailCodeEdt = (EditText) findViewById(R.id.user_bind_email_code_edt);
        userBindEmailSendCodeTv = (TextView) findViewById(R.id.user_bind_email_send_code_tv);
        userBindEmailBtn = (ProgressButton) findViewById(R.id.user_bind_email_btn);
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);
//        userTitle.setText(R.string.user_change_email);
        userTitle.setText(R.string.bind_email);
//        if (isChangeEmail){
//            userTitle.setText(R.string.user_change_email);
//        }else {
//            userTitle.setText(R.string.bind_email);
//        }


    }

    private void registerEvent() {
        userBackBtn.setOnClickListener(this);
        userBindEmailCodeEdt.addTextChangedListener(codeTextWatcher);
        userBindEmailEdt.addTextChangedListener(codeTextWatcher);
        userBindEmailSendCodeTv.setOnClickListener(this);
        userBindEmailBtn.setOnClickListener(this);
        userBindEmailBtn.setSelected(false);
        userBindEmailBtn.setClickable(false);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.user_back_btn:
                ActivityUtils.finishActivity(this);

                break;
            case R.id.user_bind_email_send_code_tv:
                checkEmail();
                break;
            case R.id.user_bind_email_btn:
                checkIsHasMobileToBind();
                break;
            default:
                break;
        }
    }


    /**
     * @des:校验是否有手机号
     * @params:
     * @return:
     */
    private void checkIsHasMobileToBind() {
        if (isChangeEmail) {
            hasMailToBind();
        } else {
            noEmailToBind();
        }
    }

    /**
     * @des:没有邮箱绑定
     * @params:
     * @return:
     */
    private void noEmailToBind() {

        final String textMail = userBindEmailEdt.getText().toString();
        if (userBindEmailEdt.getText().length() == 0) {
            showMessage(R.string.login_alert_mail_empty);
        } else if (userBindEmailCodeEdt.getText().length() <= 5) {
            showMessage(R.string.edit_text_code_length);
        } else if (isValidatorLoginName(textMail)) {
            showMessage(R.string.login_alert_mail_error);
        } else if (textMail.equals(userPreferences.getUserEmail())) {
            showMessage(R.string.mail_toast_put_new_mail);
        } else {
            currentTime = System.currentTimeMillis();
            KeyboardUtils.hideSoftInput(this);
            httpManager.requestPassport().notBindUpdateMail(userPreferences.getIMEI(), CZURConstants.CLOUD_ANDROID,
                    userPreferences.getChannel(), userPreferences.getUserId(), userPreferences.getToken(), userPreferences.getUserId(),
                    textMail, userBindEmailCodeEdt.getText().toString(), String.class, new MiaoHttpManager.Callback<String>() {
                        @Override
                        public void onStart() {
                            userBindEmailBtn.startDelayLoading(UserBindEmailActivity.this);
                        }

                        @Override
                        public void onResponse(MiaoHttpEntity<String> entity) {
                            successDelay(entity, textMail, false);

                        }

                        @Override
                        public void onFailure(MiaoHttpEntity<String> entity) {
                            hideProgressDialog();
                            if (entity.getCode() == MiaoHttpManager.STATUS_EMAIL_BIND_OTHER_USER) {
                                failedDelay(R.string.mail_bind_other_user);
                            } else if (entity.getCode() == MiaoHttpManager.STATUS_INVALID_EMAIL) {
                                failedDelay(R.string.invalid_email);
                            } else if (entity.getCode() == MiaoHttpManager.STATUS_CODE_EXPIRED) {
                                failedDelay(R.string.mail_code_expired);
                            }

                        }

                        @Override
                        public void onError(Exception e) {
                            hideProgressDialog();
                            showMessage(R.string.request_failed_alert);
                        }
                    });
        }
    }

    public void noEmailSucces(MiaoHttpEntity<String> entity, String textMail) {
        userPreferences.setUserEmail(textMail);
        showMessage(R.string.user_mail_bind_success);
        EventBus.getDefault().post(new UserInfoEvent(EventType.BIND_EMAIL));
        ActivityUtils.startActivity(IndexActivity.class);
    }

    /**
     * @des:有邮箱绑定
     * @params:
     * @return:
     */
    private void hasMailToBind() {

        final String textMail = userBindEmailEdt.getText().toString();
        String code = userBindEmailCodeEdt.getText().toString();
        if (textMail.length() == 0) {
            showMessage(R.string.login_alert_mail_empty);

        } else if (code.length() <= 5) {
            showMessage(R.string.edit_text_code_length);

        } else if (isValidatorLoginName(textMail)) {
            showMessage(R.string.login_alert_mail_error);

        } else if (textMail.equals(userPreferences.getUserEmail())) {
            showMessage(R.string.mail_toast_put_new_mail);

        } else {
            currentTime = System.currentTimeMillis();
            KeyboardUtils.hideSoftInput(this);

            httpManager.requestPassport().updateMailSecond(userPreferences.getIMEI(), CZURConstants.CLOUD_ANDROID,
                    userPreferences.getChannel(), userPreferences.getUserId(), userPreferences.getToken(), userPreferences.getUserId(),
                    textMail, userPreferences.getUkey(), code, String.class, new MiaoHttpManager.Callback<String>() {
                        @Override
                        public void onStart() {
                            userBindEmailBtn.startDelayLoading(UserBindEmailActivity.this);
                        }

                        @Override
                        public void onResponse(MiaoHttpEntity<String> entity) {
                            successDelay(entity, textMail, true);
                        }

                        @Override
                        public void onFailure(MiaoHttpEntity<String> entity) {

                            if (entity.getCode() == MiaoHttpManager.STATUS_EMAIL_BIND_OTHER_USER) {
                                failedDelay(R.string.mail_bind_other_user);
                            } else if (entity.getCode() == MiaoHttpManager.STATUS_CODE_EXPIRED) {
                                failedDelay(R.string.mail_code_expired);
                            } else if (entity.getCode() == MiaoHttpManager.STATUS_INVALID_EMAIL) {
                                failedDelay(R.string.invalid_email);
                            } else if (entity.getCode() == MiaoHttpManager.STATUS_CODE_EXPIRED) {
                                failedDelay(R.string.mail_code_expired);
                            }
                        }

                        @Override
                        public void onError(Exception e) {
                            failedDelay(R.string.request_failed_alert);
                        }
                    });
        }
    }

    public void hasBindSuccess(MiaoHttpEntity<String> entity, String textMail) {
        userPreferences.setUserEmail(textMail);
        showMessage(R.string.mail_update_success);
        EventBus.getDefault().post(new UserInfoEvent(EventType.CHANGE_EMAIL));
        ActivityUtils.startActivity(IndexActivity.class);
    }

    private void setEmailForStarry(String email){
        // Starry,需要保存一下mail
//        StarryPreferences.getInstance().setAccountNo(email);
        //    "data": {
        //        "id": 4172,
        //        "accountNo": "********",
        //        "czurId": 4971,
        //        "name": "********",
        //        "kind": "2",
        //        "type": 0,
        //        "createTime": "2022-07-18 10:56:15",
        //        "updateTime": "2022-07-18 10:56:15",
        //        "headImage": "https://changer-passport.oss-cn-beijing.aliyuncs.com/dwkqw5ajobuc8n6.png",
        //        "pinyin": "********",
        //        "countryCode": "USA",
        //        "mail": "<EMAIL>",
        //        "num": 0,
        //        "bucketName": "czur-starry-na",
        //        "inEnterprise": true,
        //        "portLimit": 15,
        //        "remain": "",
        //        "endpoint": "oss-us-west-1.aliyuncs.com",
        //        "region": "oss-us-west-1",
        //        "admin": false
        //    }
    }

    private void failedDelay(final int failedText) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            showMessage(failedText);
                            userBindEmailBtn.stopLoading();
                            userBindEmailCodeEdt.setText(CZURConstants.EMPTY);
                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }


    private void successDelay(final MiaoHttpEntity<String> entity, final String textMail, final boolean hasEmail) {

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    long sleepTime;
                    if (System.currentTimeMillis() - currentTime < 1000) {
                        sleepTime = 1000 - (System.currentTimeMillis() - currentTime);
                    } else {
                        sleepTime = 1;
                    }
                    Thread.sleep(sleepTime);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (hasEmail) {
                                hasBindSuccess(entity, textMail);
                            } else {
                                noEmailSucces(entity, textMail);
                            }

                        }
                    });
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

    }

    /**
     * @des:校验邮箱
     * @params:
     * @return:
     */

    private void checkEmail() {
        String email = userBindEmailEdt.getText().toString();
        if (Validator.isNotEmpty(email)) {
            if (StringUtilsKt.isValidEmail(email)) {
                getEmailCode(email);
            } else {
                showMessage(R.string.login_alert_mail_error);
            }
        } else {
            showMessage(R.string.login_alert_mail_empty);
        }

    }

    /**
     * @des: 获取邮箱验证码
     * @params:[email]
     * @return:void
     */
    private void getEmailCode(String email) {
        Locale locale = getResources().getConfiguration().locale;
        String language = locale.toString();
        httpManager.requestPassport().mailCode(
                email,
                EtUtils.getLocale(language),
                String.class,
                new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() { }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                timeCountBegin();
                showMessage(R.string.toast_code_send);
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {

                if (entity.getCode() == MiaoHttpManager.STATUS_CODE_1_MIN) {
                    showMessage(R.string.toast_code_1_min);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_5_MIN_4_TIME) {
                    showMessage(R.string.toast_5_min_4_time);
                } else if (entity.getCode() == MiaoHttpManager.STATUS_5_TIME_IN_ONE_DAY) {
                    showMessage(R.string.toast_5_time_in_one_day);
                } else {
                    showMessage(R.string.request_failed_alert);
                }

            }

            @Override
            public void onError(Exception e) {

                showMessage((R.string.request_failed_alert));
            }
        });
    }

    private TextWatcher codeTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            // 输入的内容变化的监听
            if (s.length() > 0) {
                codeHasContent = true;
            } else {
                codeHasContent = false;
            }

            checkChangePhoneButtonToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 输入后的监听
            if (s.length() > 0) {
                codeHasContent = true;
            } else {
                codeHasContent = false;
            }
            checkChangePhoneButtonToClick();
        }
    };

    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private void checkChangePhoneButtonToClick() {

        boolean mailIsNotEmpty = Validator.isNotEmpty(userBindEmailEdt.getText().toString());
        boolean codeIsNotEmpty = Validator.isNotEmpty(userBindEmailCodeEdt.getText().toString());


        if (mailIsNotEmpty && codeIsNotEmpty && codeHasContent) {
            userBindEmailBtn.setSelected(true);
            userBindEmailBtn.setClickable(true);
        } else {
            userBindEmailBtn.setSelected(false);
            userBindEmailBtn.setClickable(false);
        }
    }


    /**
     * @des: 计时器开始倒计时60S
     * @params:
     * @return:void
     */
    private void timeCountBegin() {
        timeCount = new TimeCount(60000, 1000);
        timeCount.start();

    }

    /**
     * @des: 计时器
     * @params:
     * @return:
     */

    class TimeCount extends CountDownTimer {

        public TimeCount(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onFinish() {
            userBindEmailSendCodeTv.setText(R.string.resend_code);
            userBindEmailSendCodeTv.setClickable(true);
        }

        @Override
        public void onTick(long millisUntilFinished) {
            userBindEmailSendCodeTv.setClickable(false);
            userBindEmailSendCodeTv.setText(millisUntilFinished / 1000 + " s");
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (Validator.isNotEmpty(timeCount)) {
            timeCount.cancel();
        }
    }


}
