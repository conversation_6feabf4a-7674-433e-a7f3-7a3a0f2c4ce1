package com.czur.cloud.ui.auramate;


import static com.blankj.utilcode.util.ThreadUtils.runOnUiThread;
import static com.czur.czurutils.log.CZURLogUtilsKt.logD;
import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;
import static com.czur.czurutils.log.CZURLogUtilsKt.logTagD;
import static com.czur.czurutils.log.CZURLogUtilsKt.logTagI;

import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.adapter.AuraMatePopupAdapter;
import com.czur.cloud.adapter.AuraMateRecordFilesAdapter;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.model.AuraMateDeviceModel;
import com.czur.cloud.model.AuraRecordModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.LazyLoadBaseFragment;
import com.czur.cloud.ui.component.AuraLoadingView;
import com.czur.cloud.ui.component.popup.AuraHomePopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.component.popup.ProgressPopup;
import com.czur.cloud.ui.component.progressbar.RoundedRectProgressBar;
import com.czur.cloud.ui.mirror.download.DownloadRunnable;
import com.czur.cloud.ui.mirror.download.TaskInfo;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.UUID;

import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;


public class AuraMateRecordMoviesFragment extends LazyLoadBaseFragment implements View.OnClickListener {

    private TextView auraHomeSelectAllBtn;
    private TextView auraHomeCancelBtn;
    private TextView auraFilesTitleTv;
    private RelativeLayout auraHomeUnselectedTopBarRl;
    private RelativeLayout auraHomeCurrentItemRl;
    private RelativeLayout auraHomeAddBtn;
    private RelativeLayout auraHomeMultiSelectBtn;
    private RecyclerView dialogRecyclerView;
    private LinearLayout auraHomeBottomLl;
    private RelativeLayout auraHomeRenameRl;
    private RelativeLayout saveMovieRl;
    private RelativeLayout auraHomePdfRl;
    private RelativeLayout auraHomeMoveRl;
    private RelativeLayout auraHomeDeleteRl;
    private ImageView auraHomeRenameImg;
    private ImageView saveMovieIv;
    private TextView auraHomeRenameTv;
    private TextView saveMovieTv;
    private ImageView auraHomePdfImg;
    private TextView auraHomePdfTv;
    private ImageView auraHomeMoveImg;
    private TextView auraHomeMoveTv;
    private ImageView auraHomeDeleteImg;
    private TextView auraHomeDeleteTv;
    private AuraHomePopup.Builder builder;
    private AuraHomePopup auraHomePopup;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private AuraMatePopupAdapter deviceAdapter;
    private SmartRefreshLayout refreshLayout;
    private View auraMateEmptyRl;
    private List<String> folderIds;
    private List<String> fileIds;
    private List<String> pdfIds;
    private String seqNum;
    private String type;
    private String folderName;
    private RecyclerView auraRecyclerView;
    private LinkedHashMap<Integer, AuraRecordModel.ResultDTO> isCheckedMap = new LinkedHashMap<>();
    private EditText dialogEdt;
    private String equipmentId;
    private String initEquipmentId, initRelationId;
    private String ownerId;
    private AuraLoadingView loadingView;
    private SimpleDateFormat formatter;
    private boolean isPdfRun = true;
    private ProgressPopup progressPopup;
    private RoundedRectProgressBar progressBar;
    private TextView pdfDialogTitle;
    private AuraMateDeviceModel currentModel;
    private RelativeLayout wrongQuestionRl;
    private AuraMateActivity activity;
    private RelativeLayout rlNoNetWork;
    private TextView tvNoNetWork;
    private AuraMateRecordFilesAdapter adapter = null;
    private int currentPageNum = 1;

    private ArrayList<AuraRecordModel.ResultDTO> dataList = new ArrayList<>();
    private boolean isMultiSelect = false; // 是否开启选择

    private boolean isSelectAll = false;

    private int totalNum = 0;
    private int pageDataSize = 0;//每一页的数据量

    public static AuraMateRecordMoviesFragment newInstance(String device, String relationId) {
        AuraMateRecordMoviesFragment auraMateFilesFragment = new AuraMateRecordMoviesFragment();
        Bundle bundle = new Bundle();
        bundle.putString("device", device);
        bundle.putString("relationId", relationId);
        auraMateFilesFragment.setArguments(bundle);
        return auraMateFilesFragment;
    }


    @Override
    protected int getLayoutRes() {
        return R.layout.fragment_aura_record_files;
    }


    @Override
    protected void initView(View view) {
        EventBus.getDefault().register(this);
        activity = (AuraMateActivity) getActivity();
        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(getActivity());
        formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        loadingView = AuraLoadingView.addFullScreen(getActivity(), (RelativeLayout) view.findViewById(R.id.loading_container).getRootView());
//        loadingView.startLoading();
        refreshLayout = view.findViewById(R.id.refresh_layout);
        auraMateEmptyRl = view.findViewById(R.id.aura_mate_empty_rl);
        auraFilesTitleTv = (TextView) view.findViewById(R.id.aura_files_title_tv);
        auraRecyclerView = (RecyclerView) view.findViewById(R.id.aura_home_recyclerView);
        auraHomeCurrentItemRl = (RelativeLayout) view.findViewById(R.id.aura_home_current_item_rl);
        auraHomeSelectAllBtn = (TextView) view.findViewById(R.id.aura_home_select_all_btn);
        auraHomeCancelBtn = (TextView) view.findViewById(R.id.aura_home_cancel_btn);
        auraHomeUnselectedTopBarRl = (RelativeLayout) view.findViewById(R.id.aura_home_unselected_top_bar_rl);
        auraHomeAddBtn = (RelativeLayout) view.findViewById(R.id.aura_home_add_btn);
        auraHomeMultiSelectBtn = (RelativeLayout) view.findViewById(R.id.aura_home_multi_select_btn);
        FragmentActivity activity = getActivity();
        auraHomeBottomLl = (LinearLayout) activity.findViewById(R.id.aura_home_bottom_ll);
        auraHomeRenameRl = (RelativeLayout) activity.findViewById(R.id.aura_home_rename_rl);
        saveMovieRl = (RelativeLayout) activity.findViewById(R.id.save_movie_rl);
        auraHomePdfRl = (RelativeLayout) activity.findViewById(R.id.aura_home_pdf_rl);
        auraHomeMoveRl = (RelativeLayout) activity.findViewById(R.id.aura_home_move_rl);
        auraHomeDeleteRl = (RelativeLayout) activity.findViewById(R.id.aura_home_delete_rl);
        auraHomeRenameImg = (ImageView) activity.findViewById(R.id.aura_home_rename_img);
        saveMovieIv = (ImageView) activity.findViewById(R.id.save_movie_img);
        auraHomeRenameTv = (TextView) activity.findViewById(R.id.aura_home_rename_tv);
        saveMovieTv = (TextView) activity.findViewById(R.id.save_movie_tv);
        auraHomePdfImg = (ImageView) activity.findViewById(R.id.aura_home_pdf_img);
        auraHomePdfTv = (TextView) activity.findViewById(R.id.aura_home_pdf_tv);
        auraHomeMoveImg = (ImageView) activity.findViewById(R.id.aura_home_move_img);
        auraHomeMoveTv = (TextView) activity.findViewById(R.id.aura_home_move_tv);
        auraHomeDeleteImg = (ImageView) activity.findViewById(R.id.aura_home_delete_img);
        auraHomeDeleteTv = (TextView) activity.findViewById(R.id.aura_home_delete_tv);

        auraHomeMultiSelectBtn.setOnClickListener(this);
        auraHomeSelectAllBtn.setOnClickListener(this);
        auraHomeCancelBtn.setOnClickListener(this);
        auraHomeRenameRl.setOnClickListener(this);
        auraHomeDeleteRl.setOnClickListener(this);
        saveMovieRl.setOnClickListener(this);

        initAdapter();

        refreshLayout.setEnableOverScrollDrag(false);
        refreshLayout.setEnableOverScrollBounce(false);
        refreshLayout.setEnableAutoLoadMore(true);
        refreshLayout.setEnableRefresh(true);
        refreshLayout.setEnableNestedScroll(false);
        refreshLayout.setEnableFooterFollowWhenNoMoreData(true);
        refreshLayout.setEnableLoadMoreWhenContentNotFull(true);
        refreshLayout.setEnableLoadMore(true);
        refreshLayout.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                auraRecyclerView.stopScroll();
                loadMore();
            }
        });
        refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                loadDataFormStart();
            }
        });

//        initDialogList();
//        initAuraFolderRecyclerView();
        rlNoNetWork = view.findViewById(R.id.rl_no_network);
        tvNoNetWork = view.findViewById(R.id.tv_click_refresh);
        tvNoNetWork.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showProgressDialog();
                rlNoNetWork.setVisibility(View.GONE);
                getData(1, false);
//                resetToFresh();
//                getDevicesAndFolderList(false);
            }
        });
    }

    private void initAdapter() {
        adapter = new AuraMateRecordFilesAdapter(getActivity());
        auraRecyclerView.setHasFixedSize(true);
        auraRecyclerView.setLayoutManager(new LinearLayoutManager(getActivity()));
        auraRecyclerView.setAdapter(adapter);
        adapter.setOnItemClickListener(new AuraMateRecordFilesAdapter.onItemClickListener() {
            @Override
            public void onItemClick(int position) {
                if (!isMultiSelect) {
                    multiSelect();
                }
                ArrayList<AuraRecordModel.ResultDTO> adapterDatas = adapter.getDatas();
                AuraRecordModel.ResultDTO resultDTO = adapterDatas.get(position);
                boolean checked = resultDTO.isChecked();
                if (checked) {
                    // 取消选择
                    isCheckedMap.remove(resultDTO.getId());
                } else {
                    // 选择
                    isCheckedMap.put(resultDTO.getId(), resultDTO);
                }
                resultDTO.setChecked(!checked);
                adapter.refreshData(adapterDatas);
                checkAllDataSelect(adapterDatas);
            }
        });
    }

    private void initNetListener() {
        NetworkUtils.registerNetworkStatusChangedListener(new NetworkUtils.OnNetworkStatusChangedListener() {
            @Override
            public void onDisconnected() {
                rlNoNetWork.setVisibility(View.VISIBLE);
            }

            @Override
            public void onConnected(NetworkUtils.NetworkType networkType) {
                rlNoNetWork.setVisibility(View.GONE);
            }
        });
    }

    @Override
    public void onFragmentResume() {
        super.onFragmentResume();

    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if ((getArguments() != null ? getArguments().getString("device") : null) != null) {
//            initEquipmentId = getArguments().getString("device");
        }
        if ((getArguments() != null ? getArguments().getString("relationId") : null) != null) {
//            initRelationId = getArguments().getString("relationId");
        }
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
//        registerEvent();

        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(getActivity());

        if (!BuildConfig.IS_OVERSEAS){
            initNetListener();
            loadDataFormStart();
        }
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.aura_home_multi_select_btn:
                multiSelect();
                break;
            case R.id.aura_home_select_all_btn:
                selectAll();
                break;
            case R.id.aura_home_cancel_btn:
                cancelEvent();
                break;
            case R.id.aura_home_rename_rl:
                showRenameMovieDialog();
                break;
            case R.id.aura_home_delete_rl:
                showConfirmDeleteDialog();

                break;
            case R.id.save_movie_rl:
                saveMovieToLocal();
                break;
        }
    }

    private void showConfirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(getActivity(), CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        if (isAdded()) {
            builder.setTitle(getResources().getString(R.string.prompt));
            builder.setMessage(getResources().getString(R.string.confirm_delete));
        }
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                deleteFilesThread();
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }

    private void saveMovieToLocal() {
        showProgressDialog(false, false);
        AuraRecordModel.ResultDTO resultDTO = new AuraRecordModel.ResultDTO();
        ArrayList<TaskInfo> downloadInfoList = new ArrayList<>();
        for (Integer key : isCheckedMap.keySet()) {
            resultDTO = isCheckedMap.get(key);
            TaskInfo info = new TaskInfo();
            String filePath = CZURConstants.MIRROR_PATH + "Download/";
            info.setPath(filePath);
            info.setName(resultDTO.getFileName() + ".mp4");
            info.setUrl(resultDTO.getAgoraFilesKey());
            info.setContentLen(0L);
            downloadInfoList.add(info);
        }

        startSaveMovieTask(downloadInfoList);
    }

    private void startSaveMovieTask(ArrayList<TaskInfo> downloadInfoList) {
        if (downloadInfoList.isEmpty()) {
            hideProgressDialog(true);
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    cancelEvent();
                    ToastUtils.showLong(R.string.et_save_success);
                }
            });

            return;
        }
        logI("SaveMovieTask--" + downloadInfoList.get(0).getName() + "downloadInfoListSize=" + downloadInfoList.size());
        //创建下载任务
        DownloadRunnable runnable = new DownloadRunnable(downloadInfoList.get(0));
        runnable.setDownloadTaskCompleteListener(new DownloadRunnable.DownloadTaskCompleteListener() {
            @Override
            public void taskCompleteListener(String filePath) {
                final String sdPicPath = CZURConstants.MOVIES_PATH + CZURConstants.AURA_MATE_PATH + downloadInfoList.get(0).getName();

                FileUtils.copy(filePath, sdPicPath, new FileUtils.OnReplaceListener() {
                    @Override
                    public boolean onReplace(File srcFile, File destFile) {
                        return false;
                    }
                });

                activity.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.parse("file://" + sdPicPath)));
                downloadInfoList.remove(0);
                startSaveMovieTask(downloadInfoList);
            }
        });
        //开始下载任务
        new Thread(runnable).start();
    }

    private void renameFileThread(int id, String fileName) {

        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() throws Throwable {
                return renameFile(id, fileName);
            }

            @Override
            public void onSuccess(Boolean result) {
                if (result) {
                    //改名
                    loadDataFormStart();
                } else {
//                    ToastUtils.showLong(R.string.starry_msg_del_message_fail);
                }
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    private boolean renameFile(int id, String fileName) {
        String u_id = UserPreferences.getInstance().getUserId();
        String udid = UserPreferences.getInstance().getIMEI();
        String t_id = UserPreferences.getInstance().getToken();
        if (!NetworkUtils.isConnected()) {
            return false;
        }

        try {
            MediaType MEDIA_TYPE = MediaType.parse("application/json");

            JSONArray idsArray = new JSONArray();

            for (Integer key : isCheckedMap.keySet()) {
                String ids = String.valueOf(key);
                idsArray.put(ids);
                // TO-DO：处理key和value
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", id);
            jsonObject.put("fileName", fileName);

            RequestBody body = RequestBody.create(MEDIA_TYPE, jsonObject.toString());

            Request request = new Request
                    .Builder()
                    .header("udid", udid)
                    .header("App-Key", CZURConstants.CLOUD_ANDROID)
                    .header("T-ID", t_id)
                    .header("U-ID", u_id)
                    .header("Content-Type", "application/json")
                    .url(BuildConfig.BASE_URL + CZURConstants.AURA_RENAME_MOVIES)
                    .post(body)
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(request).execute();

            String responseString = response.body().string();

            JSONObject resultJsonObject = new JSONObject(responseString);
            int code = resultJsonObject.getInt("code");
            logI("renameFile.responseString=" + responseString);

            if (code == 1000) {
                isCheckedMap.clear();
                return true;
            } else {
                return false;
            }


        } catch (Exception e) {
            logE("submitSittingHappyPicture.e=" + e.toString());
            return false;
        }


    }

    private void deleteFilesThread() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Boolean>() {
            @Override
            public Boolean doInBackground() throws Throwable {

                return deleteFiles();
            }

            @Override
            public void onSuccess(Boolean result) {
                if (result) {
                    //删除成功
                    loadDataFormStart();
                } else {
                    ToastUtils.showLong(R.string.starry_msg_del_message_fail);
                }
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    private boolean deleteFiles() {
        String u_id = UserPreferences.getInstance().getUserId();
        String udid = UserPreferences.getInstance().getIMEI();
        String t_id = UserPreferences.getInstance().getToken();
        if (!NetworkUtils.isConnected()) {
            return false;
        }

        try {
            MediaType MEDIA_TYPE = MediaType.parse("application/json");

            JSONArray idsArray = new JSONArray();

            for (Integer key : isCheckedMap.keySet()) {
                String ids = String.valueOf(key);
                idsArray.put(ids);
                // TO-DO：处理key和value
            }
            JSONObject json = new JSONObject().put("ids", idsArray);
            logTagD("song", json.toString());

            RequestBody body = RequestBody.create(MEDIA_TYPE, json.toString());

            Request request = new Request
                    .Builder()
                    .header("udid", udid)
                    .header("App-Key", CZURConstants.CLOUD_ANDROID)
                    .header("T-ID", t_id)
                    .header("U-ID", u_id)
                    .header("Content-Type", "application/json")
                    .url(BuildConfig.BASE_URL + CZURConstants.AURA_DELETE_MOVIES)
                    .post(body)
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(request).execute();

            String responseString = response.body().string();

            JSONObject jsonObject = new JSONObject(responseString);
            int code = jsonObject.getInt("code");
            logI("deleteFiles.responseString=" + responseString);

            if (code == 1000) {
                isCheckedMap.clear();
                return true;
            } else {
                return false;
            }


        } catch (Exception e) {
            logE("submitSittingHappyPicture.e=" + e.toString());
            return false;
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {

    }

    private void loadDataFormStart() {
        getData(1, false);
    }

    private void loadMore() {
        currentPageNum++;
        getData(currentPageNum, true);
    }

    private void getData(int pageNum, boolean isLoadMore) {
        if (!NetworkUtils.isConnected()) {
            rlNoNetWork.postDelayed(new Runnable() {
                @Override
                public void run() {
                    hideProgressDialog();
                }
            }, 300);

            rlNoNetWork.setVisibility(View.VISIBLE);
            refreshLayout.finishLoadMore();
            refreshLayout.finishRefresh();
            return;
        }

        this.currentPageNum = pageNum;
        boolean needScrollToBottom = false;
        if (isLoadMore && adapter.getDatas().size() < pageDataSize) {
            // 如果当前数据不满足一页,为了不出现少数据的情况,就当刷新处理,但是要滚动到最底部
            currentPageNum = 1;
            needScrollToBottom = true;
        }

        boolean finalNeedScrollToBottom = needScrollToBottom;
        httpManager.request().getAuraRecordMovies(
                userPreferences.getUserId(),
                String.valueOf(pageNum),
                AuraRecordModel.class,
                new MiaoHttpManager.CallbackNetwork<AuraRecordModel>() {
                    @Override
                    public void onNoNetwork() {
                        showMessage(R.string.toast_no_connection_network);
                    }

                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<AuraRecordModel> entity) {
                        hideProgressDialog();
                        totalNum = entity.getBody().getTotal();
                        pageDataSize = entity.getBody().getPageSize();
                        logTagD("song", "total=" + entity.getBody().getTotal());
                        if (isLoadMore) {
                            refreshLayout.finishLoadMore();
                            if (currentPageNum != 1 && entity.getBody().getResult().isEmpty()) {
                                currentPageNum = currentPageNum - 1;
                            } else {
                                dataList.addAll(entity.getBody().getResult());
                                adapter.refreshData(dataList);
                            }

                            if (finalNeedScrollToBottom && !adapter.getDatas().isEmpty()) {
                                auraRecyclerView.smoothScrollToPosition(adapter.getItemCount() - 1);
                            }
                        } else {

                            refreshLayout.finishRefresh();
                            dataList.clear();
                            dataList.addAll(entity.getBody().getResult());
                            adapter.refreshData(dataList);
                        }
                        refreshSelectUI();
                        cancelEvent();
                        checkAllDataSelect(dataList);

                        if (dataList.isEmpty()) {
                            auraHomeUnselectedTopBarRl.setVisibility(View.GONE);
                            auraMateEmptyRl.setVisibility(View.VISIBLE);
                        } else {
                            auraHomeUnselectedTopBarRl.setVisibility(View.VISIBLE);
                            auraMateEmptyRl.setVisibility(View.GONE);
                        }
                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<AuraRecordModel> entity) {
                        hideProgressDialog();
                        if (!NetworkUtils.isConnected()) {
                            showMessage(R.string.toast_no_connection_network);
                        }
                        if (isLoadMore) {
                            refreshLayout.finishLoadMore();
                        } else {
                            refreshLayout.finishRefresh();
                        }
                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        if (!NetworkUtils.isConnected()) {
                            showMessage(R.string.toast_no_connection_network);
                        }
                        if (isLoadMore) {
                            refreshLayout.finishLoadMore();
                        } else {
                            refreshLayout.finishRefresh();
                        }
                    }
                });
    }

    private void multiSelect() {
        if (!dataList.isEmpty()) {
            isMultiSelect = !isMultiSelect;
            adapter.setCheckMode(isMultiSelect);
            if (isMultiSelect) {
                showSelectTopBar();
            } else {
                hideSelectTopBar();
            }
        }
    }

    private void showSelectTopBar() {
        auraHomeBottomLl.setVisibility(View.VISIBLE);
        saveMovieRl.setVisibility(View.VISIBLE);
        auraHomeMoveRl.setVisibility(View.GONE);
        auraHomePdfRl.setVisibility(View.GONE);
        auraHomeRenameRl.setVisibility(View.VISIBLE);
        auraHomeDeleteRl.setVisibility(View.VISIBLE);
        darkAll();
        auraHomeUnselectedTopBarRl.setVisibility(View.GONE);
        auraHomeCancelBtn.setVisibility(View.VISIBLE);
        auraHomeSelectAllBtn.setVisibility(View.VISIBLE);
        auraHomeCancelBtn.setText(R.string.cancel);
        auraFilesTitleTv.setVisibility(View.VISIBLE);
        auraFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
        auraHomeSelectAllBtn.setText(R.string.select_all);
    }

    private void hideSelectTopBar() {
        auraHomeBottomLl.setVisibility(View.GONE);
        auraHomeUnselectedTopBarRl.setVisibility(View.VISIBLE);
        auraHomeCancelBtn.setVisibility(View.GONE);
        auraHomeSelectAllBtn.setVisibility(View.GONE);
        auraFilesTitleTv.setVisibility(View.GONE);
//        checkDeviceSize();
    }

    private void selectAll() {
        dataList = adapter.getDatas();
        if (!isSelectAll) {
            for (int i = 0; i < dataList.size(); i++) {
                AuraRecordModel.ResultDTO resultDTO = dataList.get(i);
                resultDTO.setChecked(true);
                isCheckedMap.put(resultDTO.getId(), resultDTO);
            }
            isSelectAll = true;
        } else {
            for (int i = 0; i < dataList.size(); i++) {
                AuraRecordModel.ResultDTO resultDTO = dataList.get(i);
                resultDTO.setChecked(false);
            }
            isCheckedMap.clear();
            isCheckedMap = new LinkedHashMap<>();
            isSelectAll = false;
        }
        adapter.refreshData(dataList);
        refreshBottomBtnsLayoutUI();
        refreshSelectUI();
    }

    private void refreshSelectUI() {
        if (!isSelectAll) {
            auraHomeSelectAllBtn.setText(R.string.select_all);
        } else {
            auraHomeSelectAllBtn.setText(R.string.not_select_all);
        }
        auraFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
    }

    private void cancelEvent() {
        isMultiSelect = false;
        isSelectAll = false;
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        ArrayList<AuraRecordModel.ResultDTO> adapterDatas = adapter.getDatas();
        for (int i = 0; i < adapterDatas.size(); i++) {
            AuraRecordModel.ResultDTO resultDTO = adapterDatas.get(i);
            resultDTO.setChecked(false);
        }
        adapter.setCheckMode(isMultiSelect);
        adapter.refreshData(adapterDatas);
        hideSelectTopBar();
    }

    private void checkAllDataSelect(ArrayList<AuraRecordModel.ResultDTO> adapterDatas) {
        boolean allChecked = true;
        for (int i = 0; i < adapterDatas.size(); i++) {
            AuraRecordModel.ResultDTO resultDTO = adapterDatas.get(i);
            if (!resultDTO.isChecked()) {
                allChecked = false;
            }
        }
        isSelectAll = allChecked;
        refreshBottomBtnsLayoutUI();
        refreshSelectUI();
    }

    private void refreshBottomBtnsLayoutUI() {
        if (isCheckedMap.isEmpty()) {
            darkDelete();
            darkRename();
            darkSave();
        } else if (isCheckedMap.size() == 1) {
            showRename();
            showDelete();
            showSave();
        } else {
            isCheckedMap.size();
            darkRename();
            showSave();
            showDelete();
        }
    }

    private void darkAll() {
        darkDelete();
        darkRename();
        darkSave();
    }

    private void showRename() {
        auraHomeRenameRl.setClickable(true);
        auraHomeRenameRl.setEnabled(true);
        auraHomeRenameImg.setSelected(true);
        if (isAdded()) {
            auraHomeRenameTv.setTextColor(getResources().getColor(R.color.white));
        }
    }

    private void darkRename() {
        auraHomeRenameRl.setClickable(false);
        auraHomeRenameRl.setEnabled(false);
        auraHomeRenameImg.setSelected(false);
        if (isAdded()) {
            auraHomeRenameTv.setTextColor(getResources().getColor(R.color.dark_text));
        }
    }

    private void showSave() {
        saveMovieRl.setClickable(true);
        saveMovieRl.setEnabled(true);
        saveMovieIv.setSelected(true);
        if (isAdded()) {
            saveMovieTv.setTextColor(getResources().getColor(R.color.white));
        }
    }

    private void darkSave() {
        saveMovieRl.setClickable(false);
        saveMovieRl.setEnabled(false);
        saveMovieIv.setSelected(false);
        if (isAdded()) {
            saveMovieTv.setTextColor(getResources().getColor(R.color.dark_text));
        }
    }

    private void showDelete() {
        auraHomeDeleteRl.setClickable(true);
        auraHomeDeleteRl.setEnabled(true);
        auraHomeDeleteImg.setSelected(true);
        if (isAdded()) {
            auraHomeDeleteTv.setTextColor(getResources().getColor(R.color.white));
        }
    }

    private void darkDelete() {
        auraHomeDeleteRl.setClickable(false);
        auraHomeDeleteRl.setEnabled(false);
        auraHomeDeleteImg.setSelected(false);
        if (isAdded()) {
            auraHomeDeleteTv.setTextColor(getResources().getColor(R.color.dark_text));
        }
    }

    private void showRenameMovieDialog() {
        final CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(getActivity(), CloudCommonPopupConstants.EDT_TWO_BUTTON);
        if (isAdded()) {
            builder.setTitle(getResources().getString(R.string.prompt));
            builder.setMessage(getResources().getString(R.string.input_movie_name));
        }
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (Validator.isNotEmpty(dialogEdt.getText().toString())) {
                    //不能含有表情
                    if (EtUtils.containsEmoji(dialogEdt.getText().toString())) {
                        showMessage(R.string.nickname_toast_symbol);
                    } else {
                        int id = 0;
                        for (Integer key : isCheckedMap.keySet()) {
                            id = key;
                        }
                        renameFileThread(id, dialogEdt.getText().toString());
                        dialog.dismiss();
                    }
                } else {
                    showMessage(R.string.tip_file_rename_length_toast);
                }
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        dialogEdt = (EditText) commonPopup.getWindow().findViewById(R.id.edt);
        commonPopup.show();
    }

    @Override
    public void onResume() {
        super.onResume();

    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            if (activity != null) {
                auraHomeRenameRl = (RelativeLayout) activity.findViewById(R.id.aura_home_rename_rl);
                auraHomeRenameRl.setOnClickListener(this);
                auraHomeDeleteRl = (RelativeLayout) activity.findViewById(R.id.aura_home_delete_rl);
                auraHomeDeleteRl.setOnClickListener(this);
                saveMovieRl = (RelativeLayout) activity.findViewById(R.id.save_movie_rl);
                saveMovieRl.setOnClickListener(this);

                refreshLayout.autoRefresh();
            }
        }
    }
}








