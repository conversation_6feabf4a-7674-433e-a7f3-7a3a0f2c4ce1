package com.czur.cloud.ui.component;

import android.animation.Animator;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.AnimationDrawable;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import com.badoo.mobile.util.WeakHandler;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;

import java.util.concurrent.atomic.AtomicBoolean;

public class LoadingView extends RelativeLayout {

    private WeakHandler handler;
    private Context context;
    private Activity activity;
    private ImageView cat;
    private long sleepTime;
    public boolean isLoading;
    private AnimationDrawable animationDrawable;
    private AtomicBoolean isSyncLoading;
    private boolean isCallback;

    public LoadingView(Activity context) {
        super(context);
        this.context = context;
        activity = context;
        init();
    }

    public void setLoadingTime(long sleepTime) {
        this.sleepTime = sleepTime;
    }

    public LoadingView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.context = context;
        init();
    }

    private void init() {
        isSyncLoading = new AtomicBoolean(false);
        handler = new WeakHandler();
        setVisibility(View.GONE);
        setClickable(true);
        setBackgroundColor(Color.WHITE);
        cat = new ImageView(context);
        cat.setImageResource(R.mipmap.loading);
        int dp15 = SizeUtils.dp2px(52f);
        LayoutParams paramSecond = new LayoutParams(LayoutParams.WRAP_CONTENT, dp15);
        paramSecond.addRule(RelativeLayout.CENTER_IN_PARENT, RelativeLayout.TRUE);
        addView(cat, paramSecond);
    }

    public static LoadingView addFullScreen(Activity context, ViewGroup parentLayout) {
        LoadingView loadingView = new LoadingView(context);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        parentLayout.addView(loadingView, params);
        return loadingView;
    }

    public void startLoading() {
        if (!isLoading) {
            isSyncLoading.set(true);
            isLoading = true;
            LoadingView.this.setAlpha(1);
            setVisibility(View.VISIBLE);
            cat.setVisibility(GONE);
            handler.postDelayed(new HandlerDelayedTask(),300);
        }
    }

    private class HandlerDelayedTask implements Runnable {
        @Override
        public void run() {
            if (isLoading) {
                cat.setVisibility(VISIBLE);
                Animation imgAnim = AnimationUtils.loadAnimation(
                        context, R.anim.et_dialog_anim);
                cat.startAnimation(imgAnim);
            }
        }
    }

    public boolean isLoading() {
        return isLoading;
    }

    public AtomicBoolean isSyncLoading() {
        return isSyncLoading;
    }
    public void stopLoading() {
        ObjectAnimator hideCircleAnim = ObjectAnimator.ofFloat(cat, "alpha", 1.0f, 0);
        hideCircleAnim.setInterpolator(new AccelerateDecelerateInterpolator());
        hideCircleAnim.setDuration(500);
        hideCircleAnim.start();

        ObjectAnimator hideLoadingAnim = ObjectAnimator.ofFloat(LoadingView.this, "alpha", 1.0f, 0);
        hideLoadingAnim.setInterpolator(new AccelerateDecelerateInterpolator());
        hideLoadingAnim.setDuration(500);
        hideLoadingAnim.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {

                if (!isCallback&&(float)animation.getAnimatedValue()<0.3f){
                    isCallback = true;
                    isLoading = false;
                    isSyncLoading.set(false);
                    Handler handler = new Handler();
                    handler.post(new Runnable() {
                        @Override
                        public void run() {

                            if (onLoadingFinishListener != null) {
                                onLoadingFinishListener.onFinish();
                            }
                        }

                    });
                }


            }
        });
        hideLoadingAnim.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                LoadingView.this.setVisibility(GONE);
                isLoading = false;
                isSyncLoading.set(false);
                activity = null;
                if (!isCallback){
                    Handler handler = new Handler();
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            if (onLoadingFinishListener != null) {
                                onLoadingFinishListener.onFinish();
                            }
                        }

                    });
                }

            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }

        });
        hideLoadingAnim.start();

    }

    private OnLoadingFinish onLoadingFinishListener;

    public interface OnLoadingFinish {
        void onFinish();
    }

    public void setOnLoadingFinishListener(OnLoadingFinish onLoadingFinishListener) {
        this.onLoadingFinishListener = onLoadingFinishListener;
    }
}
