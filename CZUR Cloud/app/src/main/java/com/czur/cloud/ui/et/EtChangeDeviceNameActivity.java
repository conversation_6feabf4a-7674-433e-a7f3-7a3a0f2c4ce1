package com.czur.cloud.ui.et;

import android.os.Bundle;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.RenameDeviceEvent;
import com.czur.cloud.model.BaseModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.util.validator.Validator;

import org.greenrobot.eventbus.EventBus;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class EtChangeDeviceNameActivity extends BaseActivity implements View.OnClickListener {

    private ImageView normalBackBtn;
    private UserPreferences userPreferences;
    private TextView changeDeviceNameBtn;
    private TextView deviceRealNameTv;
    private EditText changeDeviceNameEdt;
    private HttpManager httpManager;
    private String deviceId;
    private String realName;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_et_change_device_name);
        initComponent();
        registerEvent();

    }


    private void initComponent() {

        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        deviceId = getIntent().getStringExtra("deviceId");
        realName = getIntent().getStringExtra("realName");
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        changeDeviceNameBtn = (TextView) findViewById(R.id.change_device_name_btn);
        deviceRealNameTv = (TextView) findViewById(R.id.device_real_name_tv);
        changeDeviceNameEdt = (EditText) findViewById(R.id.change_device_name_edt);
        deviceRealNameTv.setText(realName);

    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        changeDeviceNameBtn.setOnClickListener(this);
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {

            case R.id.normal_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.change_device_name_btn:
                final String inputName = realName + changeDeviceNameEdt.getText().toString();
                final String submitDeviceName = isSame(changeDeviceNameEdt.getText().toString())? realName : inputName;
                if (Validator.isNotEmpty(changeDeviceNameEdt.getText().toString())) {
                    httpManager.request().changerDeviceName(deviceId, userPreferences.getUserId(), submitDeviceName, BaseModel.class, new MiaoHttpManager.Callback<BaseModel>() {
                        @Override
                        public void onStart() {
                            showProgressDialog();
                        }

                        @Override
                        public void onResponse(MiaoHttpEntity<BaseModel> entity) {
                            hideProgressDialog();
                            EventBus.getDefault().post(new RenameDeviceEvent(EventType.RENAME_DEVICE, submitDeviceName));
                            finish();
                        }

                        @Override
                        public void onFailure(MiaoHttpEntity<BaseModel> entity) {
                            hideProgressDialog();
                            showMessage(R.string.request_failed_alert);
                        }

                        @Override
                        public void onError(Exception e) {
                            hideProgressDialog();
                            showMessage(R.string.request_failed_alert);
                        }
                    });
                } else {
                    showMessage(R.string.device_info_name_entity);
                }
                break;
            default:
                break;
        }
    }
    private boolean isSame(String editTextStr){
        if (editTextStr.equals("ET16")||editTextStr.equals("Et16")||editTextStr.equals("eT16")||editTextStr.equals("et16")){
            return  true;
        }
        return false;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
}
