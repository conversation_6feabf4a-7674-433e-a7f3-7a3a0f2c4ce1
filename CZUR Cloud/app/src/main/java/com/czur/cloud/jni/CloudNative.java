package com.czur.cloud.jni;

import android.content.res.AssetManager;
import android.graphics.Bitmap;

import com.czur.cloud.entity.JniEntity;
public class CloudNative {
    private static CloudNative instance = null;

    public static CloudNative getInstance() {
        if (instance == null) {
            instance = new CloudNative();
        }
        return instance;
    }

    static {
        System.loadLibrary("czur_cloud_native");
    }


    //初始化并且读取模型文件识别页码
    public native long initNativeAndReadXml(AssetManager assetManager);

    //获取4个角 12个点XY坐标
    public native JniEntity getBorderPoints(long AlgAdd, byte[] buf, int w, int h);

    //返回预览最后一帧的截图Bitmap
    public native Bitmap getPrintScreenImg(long AlgAdd, byte[] buf, int w, int h,Bitmap.Config config);



    /**
     * @deprecated  use cut_notebook_content instead
     * @brief 拍照获取4个角 4个点XY坐标并且返回裁剪后的Bitmap
     * @param AlgAdd
     * @param buf
     * @param w
     * @param h
     * @param bitmap
     * @return
     */
    public native Bitmap getPointsAndCut(long AlgAdd, byte[] buf, int w, int h,Bitmap bitmap);

    /**
     * @deprecated  use notebook_color_mode instead
     * @param AlgAdd
     * @param bitmap
     * @param path
     * @param smallPath
     */
    public native void doColorMode(long AlgAdd, Bitmap bitmap,String path,String smallPath);

    /////////// added by lhw 2019-05-24//////////////

    /**
     *
     * @brief 裁剪出笔记本内容区域
     * @param algAddr  alg instance address
     * @param imgData  camera raw data
     * @param dataLength camera raw data length
     * @return cutted image
     */
    public native Bitmap cut_notebook_content(long algAddr, byte[] imgData,int dataLength);


    /**
     * @brief 笔记本颜色模式,无需传图，内部会在cut_notebook_content存储
     * @param algAddr
     * @param path
     * @param smallPath
     */
    public native void notebook_color_mode(long algAddr, String path,String smallPath);

    //// ADD  notebook focus simple  algorithm
    public native void  set_focus_sensitivity(float sens);






}
