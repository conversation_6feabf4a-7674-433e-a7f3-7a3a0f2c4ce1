package com.czur.cloud.ui.et;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.EtFilesAdapter;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.CropSuccessEvent;
import com.czur.cloud.event.DeleteFilesEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.SwitchFlattenEvent;
import com.czur.cloud.model.CropModel;
import com.czur.cloud.model.EtFileModel;
import com.czur.cloud.model.PdfModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.component.popup.PDFSettingPopup;
import com.czur.cloud.ui.component.popup.ProgressPopup;
import com.czur.cloud.ui.component.progressbar.RoundedRectProgressBar;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.reflect.TypeToken;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class EtFilesActivity extends BaseActivity implements View.OnClickListener {
    private EtFilesAdapter etFilesAdapter;
    private LinkedHashMap<String, String> isCheckedMap;
    private EditText dialogEdt;
    private UserPreferences userPreferences;
    private HttpManager httpManager;
    private ImageView etFilesBackBtn;
    private TextView etFilesSelectAllBtn;
    private TextView etFilesNoTitleTv;
    private TextView etFilesTitleTv;
    private TextView etFilesCancelBtn;
    private RelativeLayout etFilesUnselectedTopBarRl;
    private RelativeLayout etFilesMultiSelectBtn;
    private RecyclerView etRecyclerView;
    private LinearLayout etFilesBottomLl;
    private RelativeLayout etFilesDeleteRl;
    private RelativeLayout etFilesPdfRl;
    private RelativeLayout etFilesEmptyRl;
    private RelativeLayout etFilesMoveRl;
    private String folderId;
    private String seqNum;
    private List<String> fileIds;
    private List<String> pdfIds;
    private String folderName;
    private ProgressPopup progressPopup;
    private RoundedRectProgressBar progressBar;
    private TextView pdfDialogTitle;
    private boolean isPdfRun = true;
    private TextView etFolderPdfTv;
    private TextView etFolderMoveTv;
    private TextView etFolderDeleteTv;
    private ImageView etFolderPdfImg;
    private ImageView etFolderMoveImg;
    private ImageView etFolderDeleteImg;
    private String tempId;
    //一次加载个数
    private final int SIZE = 51;
    private SmartRefreshLayout refreshLayout;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_et_files);
        initComponent();
        initEtFilesRecyclerView();
        registerEvent();
        showProgressDialog();
        getEtRefreshList();
    }


    private void initComponent() {
        folderName = getIntent().getStringExtra("folderName");
        folderId = getIntent().getStringExtra("folderId");
        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        etFilesBackBtn = (ImageView) findViewById(R.id.et_files_back_btn);
        etFilesSelectAllBtn = (TextView) findViewById(R.id.et_files_select_all_btn);
        etFilesNoTitleTv = (TextView) findViewById(R.id.et_files_no_title_tv);
        etFilesTitleTv = (TextView) findViewById(R.id.et_files_title_tv);
        etFilesCancelBtn = (TextView) findViewById(R.id.et_files_cancel_btn);
        etFilesUnselectedTopBarRl = (RelativeLayout) findViewById(R.id.et_files_unselected_top_bar_rl);
        etFilesMultiSelectBtn = (RelativeLayout) findViewById(R.id.et_files_multi_select_btn);
        etRecyclerView = (RecyclerView) findViewById(R.id.et_files_recyclerView);
        etFilesBottomLl = (LinearLayout) findViewById(R.id.et_folder_bottom_ll);
        etFilesDeleteRl = (RelativeLayout) findViewById(R.id.et_folder_delete_rl);
        etFilesPdfRl = (RelativeLayout) findViewById(R.id.et_folder_pdf_rl);
        etFilesMoveRl = (RelativeLayout) findViewById(R.id.et_folder_move_rl);
        etFilesEmptyRl = (RelativeLayout) findViewById(R.id.et_files_empty_rl);
        etFolderPdfTv = (TextView) findViewById(R.id.et_folder_pdf_tv);
        etFolderMoveTv = (TextView) findViewById(R.id.et_folder_move_tv);
        etFolderDeleteTv = (TextView) findViewById(R.id.et_folder_delete_tv);
        etFolderPdfImg = (ImageView) findViewById(R.id.et_folder_pdf_img);
        etFolderMoveImg = (ImageView) findViewById(R.id.et_folder_move_img);
        etFolderDeleteImg = (ImageView) findViewById(R.id.et_folder_delete_img);
        etFilesNoTitleTv.setText(folderName);
        etFilesNoTitleTv.setSelected(true);

        refreshLayout = findViewById(R.id.refresh_layout);
        refreshLayout.setEnableOverScrollDrag(false);
        refreshLayout.setEnableOverScrollBounce(false);
        refreshLayout.setEnableAutoLoadMore(true);
        refreshLayout.setEnableRefresh(true);
        refreshLayout.setEnableNestedScroll(false);
        refreshLayout.setEnableFooterFollowWhenNoMoreData(true);
        refreshLayout.setEnableLoadMoreWhenContentNotFull(false);
        refreshLayout.setEnableLoadMore(true);
        refreshLayout.setOnLoadMoreListener(new com.scwang.smartrefresh.layout.listener.OnLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                etRecyclerView.stopScroll();
                loadMore(SIZE);
            }
        });
        refreshLayout.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                resetToFresh();
                getEtRefreshList();
            }
        });
    }

    /**
     * @des: 是否显示空文件夹提示区域
     * @params:
     * @return:
     */

    private void isShowEmptyPrompt() {
        if (etFilesAdapter.getDatas() != null && etFilesAdapter.getDatas().size() > 0) {
            etRecyclerView.setVisibility(View.VISIBLE);
            etFilesEmptyRl.setVisibility(View.GONE);
        } else {
            etRecyclerView.setVisibility(View.GONE);
            etFilesEmptyRl.setVisibility(View.VISIBLE);
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case MOVE_SUCCESS:
                showProgressDialog();
                resetToFresh();
                getEtRefreshList();
                break;
            case CROP_SUCCESS:
                CropSuccessEvent cropSuccessEvent = (CropSuccessEvent) event;
                if (cropSuccessEvent.isFolder()) {
                    int position = 0;
                    CropModel cropModel = cropSuccessEvent.getCropModel();
                    for (EtFileModel.FilesBean filesBean : etFilesAdapter.getDatas()) {
                        if (filesBean.getId().equals(cropModel.getFileId())) {
                            position = etFilesAdapter.getDatas().indexOf(filesBean);
                            break;
                        }
                    }
                    etFilesAdapter.getDatas().get(position).setFlatten(cropModel.getOssKey());
                    etFilesAdapter.getDatas().get(position).setSmallOssKey(cropModel.getOssSmallKey());
                    etFilesAdapter.getDatas().get(position).setMiddleOssKey(cropModel.getOssMiddleKey());
                    etFilesAdapter.getDatas().get(position).setSmall(cropModel.getOssSmallKeyUrl());
                    etFilesAdapter.getDatas().get(position).setMiddle(cropModel.getOssMiddleKeyUrl());
                    etFilesAdapter.notifyDataSetChanged();
                }
                break;
            case SWITCH_FLATTEN_SUCCESS:
            case SWITCH_COLOR_SUCCESS:
                showProgressDialog();
                SwitchFlattenEvent switchSuccessEvent = (SwitchFlattenEvent) event;
                if (switchSuccessEvent.isFolder()) {
                    int position = 0;
                    for (EtFileModel.FilesBean filesBean : etFilesAdapter.getDatas()) {
                        if (filesBean.getId().equals(tempId)) {
                            position = etFilesAdapter.getDatas().indexOf(filesBean);
                            break;
                        }
                    }
                    if (position > SIZE) {
                        List<EtFileModel.FilesBean> subList = etFilesAdapter.getDatas().subList(position - SIZE, etFilesAdapter.getDatas().size());
                        etFilesAdapter.getDatas().removeAll(subList);
                        getSeqNum(etFilesAdapter.getDatas());
                        loadMore(SIZE * 2 + 1);
                    } else {
                        etFilesAdapter.getDatas().clear();
                        getEtRefreshList();
                    }
                }
                break;
            default:
                break;
        }
    }


    /**
     * @des: 初始化列表
     * @params:
     * @return:
     */

    private void initEtFilesRecyclerView() {
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        isCheckedMap = new LinkedHashMap<>();
        etFilesAdapter = new EtFilesAdapter(this, false);
        etFilesAdapter.setOnItemCheckListener(onItemCheckListener);
        etFilesAdapter.setOnEtFilesClickListener(onItemClickListener);
        etFilesAdapter.setOnEtFilesLongClickListener(longClickListener);
        etRecyclerView.setHasFixedSize(true);
        etRecyclerView.setLayoutManager(new GridLayoutManager(this, 3));
        etRecyclerView.setAdapter(etFilesAdapter);
    }


    private void registerEvent() {
        etFilesSelectAllBtn.setOnClickListener(this);
        etFilesCancelBtn.setOnClickListener(this);
        etFilesMultiSelectBtn.setOnClickListener(this);
        etFilesDeleteRl.setOnClickListener(this);
        etFilesBackBtn.setOnClickListener(this);
        etFilesMoveRl.setOnClickListener(this);
        etFilesPdfRl.setOnClickListener(this);
    }


    /**
     * @des: 下拉加载
     * params:
     * @return:
     */


    private void loadMore(int size) {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            List<EtFileModel.FilesBean> addFilesBeans;

            @Override
            public Void doInBackground() {
                addFilesBeans = getFilesLoadMore(seqNum, size);
                if (addFilesBeans != null && addFilesBeans.size() > 0) {
                    etFilesAdapter.getDatas().addAll(addFilesBeans);
                    getSeqNum(addFilesBeans);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                checkSize(isCheckedMap, etFilesAdapter.getDatas().size());
                if (addFilesBeans == null) {
                    refreshLayout.finishLoadMore(false);
                } else if (addFilesBeans.size() == 0) {
                    refreshLayout.finishLoadMoreWithNoMoreData();
                } else {
                    etFilesAdapter.notifyDataSetChanged();
                    refreshLayout.finishLoadMore(true);
                }
                isShowEmptyPrompt();
            }
        });

    }


    /**
     * @des: 刷新列表
     * @params:
     * @return:
     */
    public void getEtRefreshList() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            @Override
            public Void doInBackground() {
                List<EtFileModel.FilesBean> refreshBeans = getEtFiles();
                if (refreshBeans != null && refreshBeans.size() > 0) {
                    etFilesAdapter.getDatas().addAll(refreshBeans);
                    getSeqNum(refreshBeans);
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                isShowEmptyPrompt();
                refreshFiles();
                refreshLayout.finishRefresh();
                hideProgressDialog();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                if (!NetworkUtils.isConnected()) {
                    showMessage(R.string.toast_no_connection_network);
                }
                refreshLayout.finishRefresh(false);
                hideProgressDialog();
            }
        });
    }

    /**
     * @des: 重置选中状态并且刷新
     * @params:
     * @return:
     */
    private void refreshFiles() {
        isMultiSelect = false;
        isSelectAll = false;
        hideSelectTopBar();
        etFilesAdapter.refreshData(isMultiSelect, isCheckedMap);
    }

    private void resetCheckList() {
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
    }

    /**
     * @des: 图片列表接口
     * @params:
     * @return:
     */
    private List<EtFileModel.FilesBean> getEtFiles() {
        final MiaoHttpEntity<EtFileModel> etFileEntity = httpManager.request().getEtFileSync(folderId, SIZE + "", userPreferences.getUserId(), EtFileModel.class);
        if (etFileEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
            return etFileEntity.getBody().getFiles();
        } else {
            return null;
        }
    }

    /**
     * @des: 重置准备刷新
     * @params:
     * @return:
     */

    private void resetToFresh() {
        etFilesAdapter.getDatas().clear();
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        refreshLayout.resetNoMoreData();
        refreshLayout.closeHeaderOrFooter();
    }

    /**
     * @des: 获得下拉加载ID
     * @params:
     * @return:
     */
    private void getSeqNum(List<EtFileModel.FilesBean> seqNumBeans) {
        if (seqNumBeans.size() > 0) {
            seqNum = seqNumBeans.get(seqNumBeans.size() - 1).getSeqNum() + "";
        }
    }

    /**
     * @des: 加载更过数据
     * @params:
     * @return:
     */
    private List<EtFileModel.FilesBean> getFilesLoadMore(String seqNum, int size) {
        MiaoHttpEntity<EtFileModel.FilesBean> etFileEntity = httpManager.request().getFilesLoadMore(folderId, seqNum + "", size + "",
                userPreferences.getUserId(), new TypeToken<List<EtFileModel.FilesBean>>() {
                }.getType());
        if (etFileEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
            return etFileEntity.getBodyList();
        } else {
            return null;
        }
    }

    /**
     * @des: Item选中监听
     * @params:
     * @return:
     */

    private EtFilesAdapter.OnItemCheckListener onItemCheckListener = new EtFilesAdapter.OnItemCheckListener() {
        @Override
        public void onItemCheck(int position, EtFileModel.FilesBean filesBean, LinkedHashMap<String, String> isCheckedMap, int totalSize) {
            EtFilesActivity.this.isCheckedMap = isCheckedMap;
            checkSize(isCheckedMap, totalSize);
        }
    };
    /**
     * @des: Item点击监听
     * @params:
     * @return:
     */

    private EtFilesAdapter.OnEtFilesClickListener onItemClickListener = new EtFilesAdapter.OnEtFilesClickListener() {
        @Override
        public void onEtFilesClick(EtFileModel.FilesBean filesBean, int position, CheckBox checkBox) {
            if (isMultiSelect) {
                checkBox.setChecked(!checkBox.isChecked());
            } else {
                Intent intent = new Intent(EtFilesActivity.this, EtPreviewActivity.class);
                intent.putExtra("isPic", false);
                intent.putExtra("folderId", folderId);
                intent.putExtra("mode", filesBean.getUserSelectMode());
                intent.putExtra("seqNum", filesBean.getSeqNum() + "");
                intent.putExtra("date", filesBean.getLocaleDate());
                tempId = filesBean.getId();
                ActivityUtils.startActivity(intent);
            }
        }
    };
    private EtFilesAdapter.OnEtFilesLongClickListener longClickListener = new EtFilesAdapter.OnEtFilesLongClickListener() {
        @Override
        public void OnEtFilesLongClick(int position, EtFileModel.FilesBean filesBean, LinkedHashMap<String, String> isCheckedMap) {
            multiSelect();
            etFilesTitleTv.setText(R.string.select_one_et);
            etFilesAdapter.refreshData(true);
        }
    };

    /**
     * @des: 检查选中个数
     * @params:
     * @return:
     */

    private void checkSize(LinkedHashMap<String, String> isCheckedMap, int totalSize) {
        judgeToShowBottom(isCheckedMap);
        if (isCheckedMap.size() == 1) {
            etFilesTitleTv.setText(R.string.select_one_et);
        } else if (isCheckedMap.size() > 1) {
            etFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
        } else {
            if (isMultiSelect) {
                etFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
            }
        }
        checkSelectAll(isCheckedMap);
    }

    /**
     * @des: 根据数量显示bottom
     * @params:
     * @return:
     */

    private void judgeToShowBottom(LinkedHashMap<String, String> isCheckedMap) {
        int fileSize = 0;
        pdfIds = new ArrayList<>();
        fileIds = new ArrayList<>();
        for (Map.Entry<String, String> stringStringEntry : isCheckedMap.entrySet()) {
            fileSize++;
            fileIds.add(stringStringEntry.getKey());
            pdfIds.add(stringStringEntry.getValue());
        }
        if (fileSize > 0) {
            showAll();
        } else {
            darkAll();
        }

    }

    private void checkSelectAll(LinkedHashMap<String, String> isCheckedMap) {
        //如果选择不是全部Item  text变为取消全选
        if (isCheckedMap.size() < etFilesAdapter.getDatas().size()) {
            etFilesSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        } else {
            etFilesSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;
        }
    }

    private boolean isMultiSelect = false;
    private boolean isSelectAll = false;

    /**
     * @des: 多选
     * @params:
     * @return:
     */

    private void multiSelect() {
        if (Validator.isNotEmpty(etFilesAdapter.getDatas())) {
            isMultiSelect = !isMultiSelect;
            etFilesAdapter.refreshData(isMultiSelect);
            if (isMultiSelect) {
                showSelectTopBar();
            } else {
                hideSelectTopBar();
            }
        }
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.et_files_cancel_btn:
                cancelEvent();
                break;
            case R.id.et_files_select_all_btn:
                selectAll();
                break;
            case R.id.et_files_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.et_files_multi_select_btn:
                multiSelect();
                break;
            case R.id.et_folder_move_rl:
                Intent intent = new Intent(EtFilesActivity.this, EtMoveActivity.class);
                String files = EtUtils.transFiles(fileIds);
                intent.putExtra("isRoot", false);
                intent.putExtra("files", files);
                intent.putExtra("folderId", folderId);
                ActivityUtils.startActivity(intent);
                resetCheckList();
                refreshFiles();
                break;
            case R.id.et_folder_pdf_rl:
                if (pdfIds.size() > 100) {
                    showMessage(R.string.pdf_100_files_tip);
                } else {
                    generatePdfDialog();
                }
                break;
            case R.id.et_folder_delete_rl:
                confirmDeleteDialog();
                break;
            default:
                break;
        }
    }

    /**
     * @des: 创建或者重命名文件夹
     * @params:
     * @return:
     */

    private void generatePdfDialog() {
        PDFSettingPopup.Builder builder = new PDFSettingPopup.Builder(EtFilesActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setOnPdfClickListener(new PDFSettingPopup.Builder.OnPdfClickListener() {
            @Override
            public void onClick(DialogInterface dialog) {

                if (Validator.isNotEmpty(dialogEdt.getText().toString())) {
                    //不能含有表情
                    if (EtUtils.containsEmoji(dialogEdt.getText().toString())) {
                        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(EtFilesActivity.this, CloudCommonPopupConstants.COMMON_ONE_BUTTON);
                        builder.setTitle(getResources().getString(R.string.prompt));
                        builder.setMessage(getResources().getString(R.string.nickname_toast_symbol));
                        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                dialog.dismiss();
                            }
                        });
                        CloudCommonPopup commonPopup = builder.create();
                        commonPopup.show();
                    } else {
                        generatePdf();
                        dialog.dismiss();
                    }

                } else {
                    showMessage(R.string.tip_file_rename_length_toast);
                }

            }
        });


        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        PDFSettingPopup commonPopup = builder.create();
        dialogEdt = (EditText) commonPopup.getWindow().findViewById(R.id.create_pdf_edt);
        commonPopup.show();
    }

    /**
     * @des: 生成Pdf
     * @params:
     * @return:
     */

    private void generatePdf() {
        final ProgressPopup.Builder builder = new ProgressPopup.Builder(EtFilesActivity.this);
        builder.setTitle(getResources().getString(R.string.pdf_ready_text));
        builder.setProgress(0);
        progressPopup = builder.create();
        progressBar = (RoundedRectProgressBar) progressPopup.getWindow().findViewById(R.id.progress);
        pdfDialogTitle = (TextView) progressPopup.getWindow().findViewById(R.id.title);
        progressPopup.show();
        requestServerProgress();
    }

    /**
     * @des: 请求服务器生成pdf百分比
     * @params:
     * @return:
     */
    @SuppressWarnings("AlibabaAvoidManuallyCreateThread")
    private void requestServerProgress() {
        isPdfRun = true;
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    MiaoHttpEntity<PdfModel> generatePdfEntity = HttpManager.getInstance().request().pdf(userPreferences.getUserId(), EtUtils.transFiles(pdfIds), dialogEdt.getText().toString(), userPreferences.getPdfType() + "", userPreferences.getPdfQuality() + "", userPreferences.getPdfIsHorizontal() + "", PdfModel.class);
                    if (generatePdfEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                        while (isPdfRun) {
                            MiaoHttpEntity<PdfModel> resultEntity = HttpManager.getInstance().request().pdfResult(userPreferences.getUserId(), generatePdfEntity.getBody().getId(), generatePdfEntity.getBody().getRandomKey(), PdfModel.class);
                            if (resultEntity.getCode() == MiaoHttpManager.STATUS_PDF_RETURN_CODE) {
                                Thread.sleep(1000);
                            } else if (resultEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                                final PdfModel pdfModel = resultEntity.getBody();
                                if (pdfModel.getPercent() != null) {
                                    runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            pdfDialogTitle.setText(getString(R.string.pdf_server_generating) + String.format("%.0f", Double.parseDouble(pdfModel.getPercent())) + "%");
                                            progressBar.setProgress(EtUtils.stringToInt(pdfModel.getPercent()));
                                        }
                                    });
                                } else {
                                    generatePdfSuccess();
                                }
                            } else {
                                generatePdfFailed();
                            }
                        }
                    } else {
                        generatePdfFailed();

                    }
                } catch (Exception e) {
                    generatePdfFailed();
                }
            }
        }).start();

    }


    private void generatePdfFailed() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                isPdfRun = false;
                progressPopup.dismiss();
                showMessage(R.string.request_server_error);
                resetCheckList();
                refreshFiles();
            }
        });

    }

    private void generatePdfSuccess() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                isPdfRun = false;
                progressPopup.dismiss();
                showMessage(R.string.pdf_server_generating_success);
                resetCheckList();
                refreshFiles();
                ActivityUtils.startActivity(EtPdfActivity.class);
            }
        });

    }


    /**
     * @des: 显示选择TopBar
     * @params:
     * @return:
     */

    private void showSelectTopBar() {
        etFilesBottomLl.setVisibility(View.VISIBLE);
        etFilesBackBtn.setVisibility(View.GONE);
        etFilesUnselectedTopBarRl.setVisibility(View.GONE);
        etFilesCancelBtn.setVisibility(View.VISIBLE);
        etFilesSelectAllBtn.setVisibility(View.VISIBLE);
        etFilesCancelBtn.setText(R.string.cancel);
        etFilesTitleTv.setVisibility(View.VISIBLE);
        etFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
        etFilesNoTitleTv.setVisibility(View.GONE);
        etFilesSelectAllBtn.setText(R.string.select_all);
    }

    /**
     * @des: 隐藏选择TopBar
     * @params:
     * @return:
     */

    private void hideSelectTopBar() {
        etFilesBottomLl.setVisibility(View.GONE);
        etFilesUnselectedTopBarRl.setVisibility(View.VISIBLE);
        etFilesBackBtn.setVisibility(View.VISIBLE);
        etFilesCancelBtn.setVisibility(View.GONE);
        etFilesSelectAllBtn.setVisibility(View.GONE);
        etFilesTitleTv.setVisibility(View.GONE);
        etFilesNoTitleTv.setVisibility(View.VISIBLE);
        etFilesNoTitleTv.setText(folderName);
    }

    /**
     * @des: 显示删除Dialog
     * @params:
     * @return:
     */

    private void confirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(EtFilesActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.confirm_delete));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                delete(EtUtils.transFiles(fileIds));
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        CloudCommonPopup commonPopup = builder.create();
        commonPopup.show();
    }


    /**
     * @des: 选中所有
     * @params:
     * @return:
     */

    private void selectAll() {
        if (!isSelectAll) {
            for (int i = 0; i < etFilesAdapter.getDatas().size(); i++) {
                if (!isCheckedMap.containsKey((etFilesAdapter.getDatas().get(i).getId()))) {
                    isCheckedMap.put(etFilesAdapter.getDatas().get(i).getId(), etFilesAdapter.getDatas().get(i).getFlatten());
                }
            }
            etFilesSelectAllBtn.setText(R.string.not_select_all);
            isSelectAll = true;
        } else {
            isCheckedMap.clear();
            isCheckedMap = new LinkedHashMap<>();
            etFilesSelectAllBtn.setText(R.string.select_all);
            isSelectAll = false;
        }
        etFilesTitleTv.setText(String.format(getString(R.string.select_num_et), isCheckedMap.size() + ""));
        etFilesAdapter.refreshData(true, isCheckedMap);
    }

    /**
     * @des: 取消事件
     * @params:
     * @return:
     */

    private void cancelEvent() {
        darkAll();
        etFilesBottomLl.setVisibility(View.GONE);
        fileIds = new ArrayList<>();
        pdfIds = new ArrayList<>();
        isMultiSelect = false;
        isSelectAll = false;
        isCheckedMap.clear();
        isCheckedMap = new LinkedHashMap<>();
        etFilesAdapter.refreshData(false, isCheckedMap);
        hideSelectTopBar();
    }

    /**
     * @des: 删除文件或者图片
     * @params:
     * @return:
     */

    private void delete(String key) {
        httpManager.request().deleteFiles(userPreferences.getUserId(), key, "", String.class, new MiaoHttpManager.CallbackNetwork<String>() {
            @Override
            public void onNoNetwork() {
                showMessage(R.string.toast_no_connection_network);
            }

            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                refreshAfterDeleteSuccess(key);
                hideProgressDialog();
                EventBus.getDefault().post(new DeleteFilesEvent(EventType.DELETE_FILE, key));
            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                resetCheckList();
                refreshFiles();
            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();
                resetCheckList();
                refreshFiles();
            }
        });
    }


    private void refreshAfterDeleteSuccess(String key) {
        Iterator<EtFileModel.FilesBean> it = etFilesAdapter.getDatas().iterator();
        while (it.hasNext()) {
            if (key.contains(it.next().getId())) {
                it.remove();
            }
        }
        cancelEvent();
        getSeqNum(etFilesAdapter.getDatas());
        loadMore(SIZE);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    private void darkAll() {
        darkPdf();
        darkSave();
        darkDelete();
    }


    private void showAll() {
        showDelete();
        showPdf();
        showMove();
    }

    private void showMove() {
        etFilesMoveRl.setClickable(true);
        etFilesMoveRl.setEnabled(true);
        etFolderMoveImg.setSelected(true);
        etFolderMoveTv.setTextColor(getResources().getColor(R.color.white));
    }

    private void darkSave() {
        etFilesMoveRl.setClickable(false);
        etFilesMoveRl.setEnabled(false);
        etFolderMoveImg.setSelected(false);
        etFolderMoveTv.setTextColor(getResources().getColor(R.color.dark_text));
    }


    private void showDelete() {
        etFilesDeleteRl.setClickable(true);
        etFilesDeleteRl.setEnabled(true);
        etFolderDeleteImg.setSelected(true);
        etFolderDeleteTv.setTextColor(getResources().getColor(R.color.white));
    }

    private void darkDelete() {
        etFilesDeleteRl.setClickable(false);
        etFilesDeleteRl.setEnabled(false);
        etFolderDeleteImg.setSelected(false);
        etFolderDeleteTv.setTextColor(getResources().getColor(R.color.dark_text));
    }

    private void showPdf() {
        etFilesPdfRl.setClickable(true);
        etFilesPdfRl.setEnabled(true);
        etFolderPdfImg.setSelected(true);
        etFolderPdfTv.setTextColor(getResources().getColor(R.color.white));
    }

    private void darkPdf() {
        etFilesPdfRl.setClickable(false);
        etFilesPdfRl.setEnabled(false);
        etFolderPdfImg.setSelected(false);
        etFolderPdfTv.setTextColor(getResources().getColor(R.color.dark_text));
    }

}
