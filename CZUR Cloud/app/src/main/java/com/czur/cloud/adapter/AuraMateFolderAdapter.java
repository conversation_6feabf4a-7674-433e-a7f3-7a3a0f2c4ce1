package com.czur.cloud.adapter;

import android.app.Activity;
import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.czur.cloud.R;
import com.czur.cloud.cache.AuraCustomImageRequest;
import com.czur.cloud.entity.AuraEntity;
import com.czur.cloud.model.AuraHomeFileModel;
import com.facebook.drawee.backends.pipeline.Fresco;
import com.facebook.drawee.interfaces.DraweeController;
import com.facebook.drawee.view.SimpleDraweeView;
import com.facebook.imagepipeline.request.ImageRequestBuilder;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * Created by shaojun on 2020/3/30
 * Email：<EMAIL>
 */


public class AuraMateFolderAdapter extends RecyclerView.Adapter<ViewHolder> {
    private static final int ITEM_TYPE_FOLDER = 0;
    private static final int ITEM_TYPE_FILE = 1;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<AuraHomeFileModel.FilesBean> filesDatas;
    private List<AuraHomeFileModel.FoldersBean> folderDatas;
    //是否进入选择
    private boolean isSelectItem;
    private LayoutInflater mInflater;
    private LinkedHashMap<String, AuraEntity> isCheckedMap = new LinkedHashMap<>();

    public AuraMateFolderAdapter(Activity activity, List<AuraHomeFileModel.FilesBean> filesDatas, List<AuraHomeFileModel.FoldersBean> folderDatas, boolean isSelectItem) {
        this.mActivity = activity;
        this.isSelectItem = isSelectItem;
        this.filesDatas = filesDatas;
        this.folderDatas = folderDatas;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<AuraHomeFileModel.FilesBean> filesDatas, List<AuraHomeFileModel.FoldersBean> folderDatas, boolean isSelectItem, LinkedHashMap<String, AuraEntity> isCheckedMap) {
        this.isSelectItem = isSelectItem;
        this.filesDatas = filesDatas;
        this.folderDatas = folderDatas;
        this.isCheckedMap = isCheckedMap;
        notifyDataSetChanged();
    }


    public void refreshData(boolean isSelectItem) {
        this.isSelectItem = isSelectItem;
        notifyDataSetChanged();
    }

    public void refreshData(int position, List<AuraHomeFileModel.FilesBean> filesDatas) {
        this.filesDatas = filesDatas;
        notifyDataSetChanged();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        if (viewType == ITEM_TYPE_FOLDER) {
            return new ETFolderHolder(mInflater.inflate(R.layout.item_et_folder, parent, false));
        } else {
            return new EtFilesHolder(mInflater.inflate(R.layout.item_et_files, parent, false));
        }
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {
        if (holder instanceof ETFolderHolder) {
            final ETFolderHolder mHolder = (ETFolderHolder) holder;
            mHolder.mItem = folderDatas.get(position);
            mHolder.itemNameTv.setText(mHolder.mItem.getName());
            mHolder.itemSizeTv.setText(String.format(mActivity.getString(R.string.item_picture_count), mHolder.mItem.getFileCounts() + ""));
            if (isSelectItem) {
                mHolder.checkBox.setVisibility(View.VISIBLE);
                mHolder.checkBox.setTag(mHolder.mItem.getId());
            } else {
                mHolder.checkBox.setVisibility(View.GONE);
            }
            mHolder.checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        if (!isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //选中时添加
                            AuraEntity auraEntity = new AuraEntity();
                            auraEntity.setType(0);
                            isCheckedMap.put(mHolder.mItem.getId(), auraEntity);
                        }
                    } else {
                        if (isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //没选中时移除
                            isCheckedMap.remove(mHolder.mItem.getId());
                        }
                    }
                    if (onItemCheckListener != null) {
                        onItemCheckListener.onItemCheck(position, isCheckedMap, folderDatas.size(), mHolder.mItem.getName());
                    }
                }
            });

            if (isCheckedMap != null) {
                mHolder.checkBox.setChecked(isCheckedMap.containsKey(mHolder.mItem.getId()) ? true : false);
            } else {
                mHolder.checkBox.setChecked(false);
            }

            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onAuraFolderClickListener != null) {
                        onAuraFolderClickListener.onAuraFolderClick(mHolder.mItem, null, position, mHolder.checkBox);
                    }

                }
            });
        }
        if (holder instanceof EtFilesHolder) {
            final EtFilesHolder mHolder = (EtFilesHolder) holder;
            mHolder.mItem = filesDatas.get(position - folderDatas.size());
            Uri lowResUri = Uri.parse(mHolder.mItem.getSmall());
            AuraCustomImageRequest imageRequest = new AuraCustomImageRequest(ImageRequestBuilder.newBuilderWithSource(lowResUri));
            imageRequest.setImageRequestType(1);
            imageRequest.setImageRequestObject("small");
            DraweeController controller = Fresco.newDraweeControllerBuilder()
                    .setImageRequest(imageRequest)
                    .setOldController(mHolder.etFilesImg.getController())
                    .build();
            mHolder.etFilesImg.setController(controller);
            if (isSelectItem) {
                mHolder.checkBox.setVisibility(View.VISIBLE);
                mHolder.checkBox.setTag(mHolder.mItem.getId());
            } else {
                mHolder.checkBox.setVisibility(View.GONE);
            }
            mHolder.checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    if (isChecked) {
                        if (!isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //选中时添加
                            AuraEntity auraEntity = new AuraEntity();
                            auraEntity.setType(1);
                            auraEntity.setOsskey(mHolder.mItem.getSingleKey());
                            isCheckedMap.put(mHolder.mItem.getId(), auraEntity);
                        }
                    } else {
                        if (isCheckedMap.containsKey(mHolder.checkBox.getTag())) {
                            //没选中时移除
                            isCheckedMap.remove(mHolder.mItem.getId());
                        }
                    }
                    if (onItemCheckListener != null) {
                        onItemCheckListener.onItemCheck(position, isCheckedMap, filesDatas.size(), null);
                    }
                }
            });

            if (isCheckedMap != null) {
                mHolder.checkBox.setChecked(isCheckedMap.containsKey(mHolder.mItem.getId()) ? true : false);
            } else {
                mHolder.checkBox.setChecked(false);
            }

            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onAuraFolderClickListener != null) {
                        onAuraFolderClickListener.onAuraFolderClick(null, mHolder.mItem, position, mHolder.checkBox);
                    }

                }
            });

        }

    }

    @Override
    public int getItemCount() {
        return filesDatas.size() + folderDatas.size();
    }


    private static class EtFilesHolder extends ViewHolder {
        public final View mView;
        AuraHomeFileModel.FilesBean mItem;
        SimpleDraweeView etFilesImg;
        CheckBox checkBox;


        EtFilesHolder(View itemView) {
            super(itemView);
            mView = itemView;
            etFilesImg = (SimpleDraweeView) itemView.findViewById(R.id.et_files_img);
            checkBox = (CheckBox) itemView.findViewById(R.id.check);
            ViewGroup.LayoutParams layoutParams = itemView.getLayoutParams();
            layoutParams.width = (ScreenUtils.getScreenWidth() - SizeUtils.dp2px(16)) / 3;
            layoutParams.height = (int) (layoutParams.width * 1.33f);
            itemView.setLayoutParams(layoutParams);
        }

    }


    private static class ETFolderHolder extends ViewHolder {
        public final View mView;
        AuraHomeFileModel.FoldersBean mItem;
        private CheckBox checkBox;
        private TextView itemNameTv;
        private TextView itemSizeTv;


        ETFolderHolder(View itemView) {
            super(itemView);
            mView = itemView;
            checkBox = (CheckBox) itemView.findViewById(R.id.check);
            itemNameTv = (TextView) itemView.findViewById(R.id.item_name_tv);
            itemSizeTv = (TextView) itemView.findViewById(R.id.item_size_tv);
            ViewGroup.LayoutParams layoutParams = itemView.getLayoutParams();
            layoutParams.width = (ScreenUtils.getScreenWidth() - SizeUtils.dp2px(16)) / 3;
            layoutParams.height = (int) (layoutParams.width * 1.33f);
            itemView.setLayoutParams(layoutParams);
        }

    }


    @Override
    public int getItemViewType(int position) {
        if (position >= 0 && position < folderDatas.size()) {
            return ITEM_TYPE_FOLDER;
        } else {
            return ITEM_TYPE_FILE;
        }
    }


    /**
     * 加载更多的数据
     */
    public void setLoadMoreData(List<AuraHomeFileModel.FilesBean> filesDatas, List<AuraHomeFileModel.FoldersBean> folderDatas) {
        this.filesDatas = filesDatas;
        this.folderDatas = folderDatas;
        notifyDataSetChanged();
    }


    public int getTotalSize() {
        return filesDatas.size() + folderDatas.size();
    }


    public View inflate(Context context, int layoutId) {
        if (layoutId <= 0) {
            return null;
        }
        return LayoutInflater.from(context).inflate(layoutId, null);
    }


    private OnAuraFolderClickListener onAuraFolderClickListener;

    public void setOnAuraFolderClickListener(OnAuraFolderClickListener onAuraFolderClickListener) {
        this.onAuraFolderClickListener = onAuraFolderClickListener;
    }

    public interface OnAuraFolderClickListener {
        void onAuraFolderClick(AuraHomeFileModel.FoldersBean foldersBean, AuraHomeFileModel.FilesBean filesBean, int position, CheckBox checkBox);
    }


    private OnItemCheckListener onItemCheckListener;

    public void setOnItemCheckListener(OnItemCheckListener onItemCheckListener) {
        this.onItemCheckListener = onItemCheckListener;
    }

    public interface OnItemCheckListener {
        void onItemCheck(int position, LinkedHashMap<String, AuraEntity> isCheckedMap, int totalSize, String folderName);

    }


}
