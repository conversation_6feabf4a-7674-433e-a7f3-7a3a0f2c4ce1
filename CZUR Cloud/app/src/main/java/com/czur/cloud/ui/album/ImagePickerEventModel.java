package com.czur.cloud.ui.album;

import com.czur.cloud.entity.ImageFolder;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.EventType;

import java.io.Serializable;

public class ImagePickerEventModel extends BaseEvent implements Serializable {
    ImageFolder imageFolder;

    public ImagePickerEventModel(EventType eventType, ImageFolder imageFolder) {
        super(eventType);
        this.imageFolder = imageFolder;
    }

    public ImageFolder getImageFolder() {
        return imageFolder;
    }

    public void setImageFolder(ImageFolder imageFolder) {
        this.imageFolder = imageFolder;
    }

    @Override
    public boolean match(Object obj) {
        return false;
    }
}
