package com.czur.cloud.model;

public class BaiduTokenModel {


    /**
     * refresh_token : 25.d34c68a57b1bee456dced922cbc1e493.*********.1870931911.282335-11253149
     * expires_in : 2592000
     * session_key : 9mzdDAMu2tw1+9o6PaUMRFt4yEsZhZuej6FevwTgAqC28cAx9vocjHi4uiXkD3wQActY8g+CMDdu+DhRTRUj4KletV4MRA==
     * access_token : 24.76141c537a630f8e8f4d102d5b24172d.2592000.**********.282335-11253149
     * scope : brain_ocr_passport vis-ocr_定额发票识别 brain_ocr_quota_invoice vis-ocr_车辆vin码识别 brain_ocr_vin brain_ocr_train_ticket brain_ocr_taxi_receipt brain_numbers brain_ocr_vat_invoice brain_ocr_handwriting public vis-ocr_ocr brain_ocr_scope brain_ocr_general brain_ocr_general_basic brain_ocr_general_enhanced vis-ocr_business_license brain_ocr_webimage brain_all_scope brain_ocr_idcard brain_ocr_driving_license brain_ocr_vehicle_license vis-ocr_plate_number brain_solution brain_ocr_plate_number brain_ocr_accurate brain_ocr_accurate_basic brain_ocr_receipt brain_ocr_business_license brain_solution_iocr wise_adapt lebo_resource_base lightservice_public hetu_basic lightcms_map_poi kaidian_kaidian ApsMisTest_Test权限 vis-classify_flower lpq_开放 cop_helloScope ApsMis_fangdi_permission smartapp_snsapi_base iop_autocar oauth_tp_app smartapp_smart_game_openapi oauth_sessionkey smartapp_swanid_verify smartapp_opensource_openapi smartapp_opensource_recapi
     * session_secret : 61b66bdcec5755e0811deba0055b97b2
     */

    private String refresh_token;
    private int expires_in;
    private String session_key;
    private String access_token;
    private String scope;
    private String session_secret;

    public String getRefresh_token() {
        return refresh_token;
    }

    public void setRefresh_token(String refresh_token) {
        this.refresh_token = refresh_token;
    }

    public int getExpires_in() {
        return expires_in;
    }

    public void setExpires_in(int expires_in) {
        this.expires_in = expires_in;
    }

    public String getSession_key() {
        return session_key;
    }

    public void setSession_key(String session_key) {
        this.session_key = session_key;
    }

    public String getAccess_token() {
        return access_token;
    }

    public void setAccess_token(String access_token) {
        this.access_token = access_token;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getSession_secret() {
        return session_secret;
    }

    public void setSession_secret(String session_secret) {
        this.session_secret = session_secret;
    }
}
