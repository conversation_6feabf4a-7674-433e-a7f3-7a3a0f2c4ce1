<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@mipmap/logo_background">

    <include
        android:id="@+id/eshare_layout_top_bar_rl"
        layout="@layout/eshare_layout_top_bar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/file_list_rv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="25dp"
        android:layout_marginTop="25dp"
        android:layout_marginEnd="25dp"
        android:layout_marginBottom="15dp"

        android:background="@drawable/eshare_circle_color_blue1_5dp"
        android:divider="@drawable/eshare_rec_color_blue_14dp"
        android:paddingStart="15dp"
        android:paddingTop="20dp"
        android:paddingEnd="15dp"
        android:paddingBottom="20dp"
        android:showDividers="middle"
        app:layout_constraintBottom_toTopOf="@+id/empty_tips_tv"
        app:layout_constraintTop_toBottomOf="@id/eshare_layout_top_bar_rl" />

    <TextView
        android:id="@+id/empty_tips_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="42dp"
        android:text="@string/is_transmit_dont_close"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@id/done_tv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
    <TextView
        android:id="@+id/show_more_history_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="42dp"
        android:text="@string/more_history_btn"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/empty_tips_tv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
    <TextView
        android:id="@+id/done_tv"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginStart="25dp"
        android:layout_marginEnd="25dp"
        android:layout_marginBottom="50dp"
        android:background="@drawable/eshare_circle_color_white_5dp"
        android:gravity="center"
        android:text="@string/transmit_download_done"
        android:textColor="@color/eshare_common_bg"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>