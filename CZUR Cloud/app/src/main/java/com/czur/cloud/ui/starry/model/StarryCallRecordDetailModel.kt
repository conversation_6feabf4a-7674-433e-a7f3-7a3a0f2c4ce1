package com.czur.cloud.ui.starry.model

import com.czur.cloud.ui.starry.network.parse.GMT8DateJsonDeserializer
import com.google.gson.annotations.JsonAdapter
import java.io.Serializable
import java.text.SimpleDateFormat
import java.util.*

/*
{
"data": {
    "id": 124760,
    "accountNo": ***********,
    "meetingId": 14819,
    "createTime": "2021-11-30 09:54:04",
    "duration": "0",
    "direction": "OUT",
    "status": "2",
    "isDelete": false,
    "read": true,
    "num": 5,
    "meetingName": "新的会议",
    "creator": ***********,
    "meetingDuration": 134,
    "meetingStatus": 3,
    "details": [
        {
            "accountNo": ***********,
            "name": "jason",
            "response": 1,
            "isMarked": false,
            "isInEnterprise": false,
            "isInAddressbook": true
        }
    ],
    "enter": true
}
}
* */
data class StarryCallRecordDetailModel(
    val id: String="",
    val accountNo: String="",
    val meetingId: String="",
    @JsonAdapter(GMT8DateJsonDeserializer::class)
    val createTime: Date,
    val duration: String="",
    val direction: String="",
    val status: Int=0,
    val isDelete: Boolean=false,
    val read: Boolean=false,
    val num: Int=0,
    val meetingName: String="",
    val creator: String="",
    val meetingDuration: Int = 0,
    val meetingStatus: Int = 0,
    val meetingCode: String = "",
    val meetingPassword: String = "",
    val details: List<StarryCallRecordDetailMemberModel> = listOf()
): Serializable{
    fun getTimeStr(): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ROOT)
        return sdf.format(createTime)
    }
}

data class StarryCallRecordDetailMemberModel(
    val accountNo: String,
    val name: String,
    val response: String,
    val isMarked: Boolean,
    val isInAddressbook: Boolean,
    val createTime: String,
    val isInEnterprise: Boolean,
    var isMyself: Boolean = false,  //是否是自己
    val id: String

): Serializable