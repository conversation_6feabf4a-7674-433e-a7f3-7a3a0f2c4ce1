<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <include layout="@layout/layout_user_top_bar" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="13.5dp"
            android:visibility="visible"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/user_remove_account_msg"
            android:layout_marginStart="@dimen/starryMargin20"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:layout_marginTop="@dimen/starryMargin30"
            android:textSize="@dimen/starryFontSize16"
            android:textColor="@color/black_22"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/user_remove_account_msg2"
            android:textColor="@color/starry_delete_red"
            android:layout_marginStart="@dimen/starryMargin20"
            android:layout_marginEnd="@dimen/starryMargin20"
            android:layout_marginTop="@dimen/starryMargin30"
            android:textSize="@dimen/starryFontSize14"
            />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="13.5dp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/next_setp_btn_ll"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_alignParentBottom="true"
        android:gravity="center">

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/next_setp_btn"
            android:layout_width="250dp"
            android:layout_height="40dp"
            android:background="@drawable/btn_rec_5_bg_with_blue"
            android:gravity="center"
            android:text="@string/next_step"
            android:textColor="@color/white" />

    </LinearLayout>

</RelativeLayout>

