apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'androidx.navigation.safeargs.kotlin'
apply plugin: 'com.kanyun.kace'
apply plugin: 'kotlin-parcelize'
apply plugin: 'realm-android'
apply plugin: 'org.jetbrains.kotlin.android'

// TODO OVERSEAS 区分国内海外渠道差分
println "-------------------------"
if (isOverSea.toBoolean()) {
    apply from: './oversea.gradle'
    println "------> isOverSea <------"
}else {
    apply from: './dimension.gradle'
    println "------> isDomestic <------"
}
println "-------------------------"
// TODO OVERSEAS 区分国内海外渠道差分

//不能去掉
realm {
    syncEnabled = true
}

kapt {
    useBuildCache = true
    javacOptions {
        option("-Xmaxerrs", 500)
    }
}

android {
    compileSdk  34
    useLibrary 'org.apache.http.legacy'
    namespace 'com.czur.cloud'
    buildFeatures {
        dataBinding = true
        buildConfig true
    }

//    androidExtensions {
//        experimental = true
//    }

    defaultConfig {
        applicationId "com.czur.cloud"
        minSdkVersion 26
        targetSdkVersion 35
        multiDexEnabled true

        manifestPlaceholders = [
                JPUSH_PKGNAME: applicationId,
                JPUSH_APPKEY : "0794d20b5c12c8225704d29b", //JPush 上注册的包名对应的 Appkey.
                JPUSH_CHANNEL: "developer-default", //暂时填写默认值即可.
        ]
        resValue("integer", "message_api_version", rootProject.ext.android.versionMessageApi + "")
    }

    sourceSets {
        main {
            manifest.srcFile 'src/main/AndroidManifest.xml'
            java.srcDirs = ['src/main/java']
            resources.srcDirs = ['src/main/resources']
            res.srcDirs = ['src/main/res']
            assets.srcDirs = ['src/main/assets']
            jniLibs.srcDirs = ['src/main/jniLibs']
        }

        czur.res.srcDirs = ['src/domestic/res']
        czur.java.srcDirs = ['src/domestic/java']
        czur.jniLibs.srcDirs = ['src/domestic/jniLibs']
        czur.manifest.srcFile 'src/domestic/AndroidManifest.xml'

        huawei.res.srcDirs = ['src/domestic/res']
        huawei.java.srcDirs = ['src/domestic/java']
        huawei.jniLibs.srcDirs = ['src/domestic/jniLibs']
        huawei.manifest.srcFile 'src/domestic/AndroidManifest.xml'

        vivo.res.srcDirs = ['src/domestic/res']
        vivo.java.srcDirs = ['src/domestic/java']
        vivo.jniLibs.srcDirs = ['src/domestic/jniLibs']
        vivo.manifest.srcFile 'src/domestic/AndroidManifest.xml'

        xiaomi.res.srcDirs = ['src/domestic/res']
        xiaomi.java.srcDirs = ['src/domestic/java']
        xiaomi.jniLibs.srcDirs = ['src/domestic/jniLibs']
        xiaomi.manifest.srcFile 'src/domestic/AndroidManifest.xml'

        oppo.res.srcDirs = ['src/domestic/res']
        oppo.java.srcDirs = ['src/domestic/java']
        oppo.jniLibs.srcDirs = ['src/domestic/jniLibs']
        oppo.manifest.srcFile 'src/domestic/AndroidManifest.xml'

        qihoo.res.srcDirs = ['src/domestic/res']
        qihoo.java.srcDirs = ['src/domestic/java']
        qihoo.jniLibs.srcDirs = ['src/domestic/jniLibs']
        qihoo.manifest.srcFile 'src/domestic/AndroidManifest.xml'

        yingyongbao.res.srcDirs = ['src/domestic/res']
        yingyongbao.java.srcDirs = ['src/domestic/java']
        yingyongbao.jniLibs.srcDirs = ['src/domestic/jniLibs']
        yingyongbao.manifest.srcFile 'src/domestic/AndroidManifest.xml'

        overseas.res.srcDirs = ['src/overseas/res']
        overseas.java.srcDirs = ['src/overseas/java']
        overseas.manifest.srcFile 'src/overseas/AndroidManifest.xml'
    }

    signingConfigs {
        debug {
            // 添加V1V2签名
            v1SigningEnabled true
            v2SigningEnabled true
        }

        release {
            keyAlias 'czur_cloud'
            keyPassword 'changer'
            storeFile file('keystore/czur.jks')
            storePassword 'changer'
            // 添加V1V2签名
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        release {
            debuggable false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            //是否清理无用资源
            shrinkResources false
            //是否启用zipAlign压缩
            zipAlignEnabled true
            signingConfig signingConfigs.release
        }
        debug {
            debuggable true
            //true：启用混淆,false:不启用
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            shrinkResources false
            zipAlignEnabled false
            signingConfig signingConfigs.release

            // 对调试 build 停用 Crashlytics
            ext.enableCrashlytics = false
            // 禁止自动生成 build ID
            ext.alwaysUpdateBuildId = false
            // 禁用PNG压缩。
            crunchPngs false
        }
    }

    packagingOptions{
        exclude 'META-INF/INDEX.LIST'
        exclude 'META-INF/io.netty.versions.properties'

    }

    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            def newName
            newName = "${applicationId}-" + variant.buildType.name + "-${variant.versionName}" + ".apk"
            outputFileName = new File(newName)
        }
        def buildType = variant.buildType.name
        def productFlavor = variant.productFlavors[0].name
        def isOverseas = productFlavor.contains('overseas')
        if (!isOverseas) {
            productFlavor = 'domestic'
            buildConfigField('long', 'CLIENT_TIMEOUT', "8000")//修改接口连接超时时间
        }else {
            buildConfigField('long', 'CLIENT_TIMEOUT', "15000")
        }

        buildConfigField('boolean', 'IS_OVERSEAS', isOverseas.toString())


        def appName = rootProject.ext.get("config").get(productFlavor).get("appName")
        def checkForceUpdate = rootProject.ext.get("config").get(productFlavor).get(buildType).get("checkForceUpdate")
        def checkOCRUrl = rootProject.ext.get("config").get(productFlavor).get(buildType).get("checkOCRUrl")
        def checkAuraMateUpdateUrl = rootProject.ext.get("config").get(productFlavor).get(buildType).get("checkAuraMateUpdateUrl")
        def checkMirrorUpdateUrl = rootProject.ext.get("config").get(productFlavor).get(buildType).get("checkMirrorUpdateUrl")
        def umengAppKey = rootProject.ext.get("config").get(productFlavor).get("umengAppKey")
        def ossBucket = rootProject.ext.get("config").get(productFlavor).get("ossBucket")
        def ossEndpoint = rootProject.ext.get("config").get(productFlavor).get(buildType).get("ossEndpoint")
        def ossBucketMirror = rootProject.ext.get("config").get(productFlavor).get(buildType).get("ossBucketMirror")
        def agoraAppId = rootProject.ext.get("config").get(productFlavor).get("agoraAppId")
        def agoraStarryAppId = rootProject.ext.get("config").get(productFlavor).get(buildType).get("agoraStarryAppId")
        def agoraStarryAppCert = rootProject.ext.get("config").get(productFlavor).get(buildType).get("agoraStarryAppCert")
        def baseUrl = rootProject.ext.get("config").get(productFlavor).get(buildType).get("baseUrl")
        def baseStarryUrl = rootProject.ext.get("config").get(productFlavor).get(buildType).get("baseStarryUrl")
        def shareStarryUrl = rootProject.ext.get("config").get(productFlavor).get(buildType).get("shareStarryUrl")
        def passportUrl = rootProject.ext.get("config").get(productFlavor).get(buildType).get("passportUrl")
        def feedbackUrl = rootProject.ext.get("config").get(productFlavor).get(buildType).get("feedbackUrl")
        def privacyUrl = rootProject.ext.get("config").get(productFlavor).get(buildType).get("privacyUrl")
        def termsUrl = rootProject.ext.get("config").get(productFlavor).get(buildType).get("termsUrl")
        resValue("string", "app_name", appName)
        buildConfigField('String', 'CHECK_FORCE_UPDATE', checkForceUpdate)
        buildConfigField('String', 'CHECK_OCR_URL', checkOCRUrl)
        buildConfigField('String', 'CHECK_AURA_MATE_UPDATE_URL', checkAuraMateUpdateUrl)
        buildConfigField('String', 'CHECK_MIRROR_UPDATE_URL', checkMirrorUpdateUrl)
        buildConfigField('String', 'UMENG_APP_KEY', umengAppKey)
        buildConfigField('String', 'OSS_BUCKET', ossBucket)
        buildConfigField('String', 'OSS_ENDPOINT', ossEndpoint)
        buildConfigField('String', 'AGORA_APP_ID', agoraAppId)
        buildConfigField('String', 'AGORA_STARRY_APP_ID', agoraStarryAppId)
        buildConfigField('String', 'AGORA_STARRY_APP_CERT', agoraStarryAppCert)
        buildConfigField('String', 'BASE_URL', baseUrl)
        buildConfigField('String', 'BASE_STARRY_URL', baseStarryUrl)
        buildConfigField('String', 'SHARE_STARRY_URL', shareStarryUrl)
        buildConfigField('String', 'PASSPORT_URL', passportUrl)
        buildConfigField('String', 'FEEDBACK_URL', feedbackUrl)
        buildConfigField('String', 'MIRROR_BUCKET', ossBucketMirror)
        buildConfigField('String', 'PRIVACY_AGREEMENT', privacyUrl)
        buildConfigField('String', 'TERMS_URL', termsUrl)
    }
    //Jason 20201111
    flavorDimensions "default"

    productFlavors {
        czur {
            applicationId "com.czur.cloud"
            manifestPlaceholders = [appName            : "com.czur.cloud",
                                    UMENG_CHANNEL_VALUE: "czur"]
            versionCode rootProject.ext.android.versionCode
            versionName rootProject.ext.android.versionName
            dimension "default"
        }
        huawei {
            applicationId "com.czur.cloud"
            manifestPlaceholders = [appName            : "com.czur.cloud",
                                    UMENG_CHANNEL_VALUE: "huawei"]
            versionCode rootProject.ext.android.versionCode
            versionName rootProject.ext.android.versionName
            dimension "default"
        }
        vivo {
            applicationId "com.czur.cloud"
            manifestPlaceholders = [appName            : "com.czur.cloud",
                                    UMENG_CHANNEL_VALUE: "vivo"]
            versionCode rootProject.ext.android.versionCode
            versionName rootProject.ext.android.versionName
            dimension "default"
        }
        xiaomi {
            applicationId "com.czur.cloud"
            manifestPlaceholders = [appName            : "com.czur.cloud",
                                    UMENG_CHANNEL_VALUE: "xiaomi"]
            versionCode rootProject.ext.android.versionCode
            versionName rootProject.ext.android.versionName
            dimension "default"
        }
        oppo {
            applicationId "com.czur.cloud"
            manifestPlaceholders = [appName            : "com.czur.cloud",
                                    UMENG_CHANNEL_VALUE: "oppo"]
            versionCode rootProject.ext.android.versionCode
            versionName rootProject.ext.android.versionName
            dimension "default"
        }
        qihoo {
            applicationId "com.czur.cloud"
            manifestPlaceholders = [appName            : "com.czur.cloud",
                                    UMENG_CHANNEL_VALUE: "qihoo"]
            versionCode rootProject.ext.android.versionCode
            versionName rootProject.ext.android.versionName
            dimension "default"
        }
        yingyongbao {
            applicationId "com.czur.cloud"
            manifestPlaceholders = [appName            : "com.czur.cloud",
                                    UMENG_CHANNEL_VALUE: "yingyongbao"]
            versionCode rootProject.ext.android.versionCode
            versionName rootProject.ext.android.versionName
            dimension "default"
        }
        overseas {
            applicationId "com.czur.global.cloud"
            manifestPlaceholders = [appName            : "com.czur.global.cloud",
                                    UMENG_CHANNEL_VALUE: "overseas"]
            versionCode rootProject.ext.android.versionCodeOverSea
            versionName rootProject.ext.android.versionNameOverSea
            dimension "default"
            resValue("integer", "message_api_version", rootProject.ext.android.versionMessageApiOversea + "")
        }
    }

    dexOptions {
        // 预编译
        preDexLibraries = true
        //开启incremental dexing,优化编译效率，这个功能android studio默认是关闭的。
        //使用增量模式构建
        incremental true
        javaMaxHeapSize "16g"     //增加java堆内存大小，最大堆内存
        maxProcessCount 8
        //线程数
        threadCount = 8
        //是否支持大工程模式
        jumboMode = true


        dexInProcess true // 在 Gradle 守护中运行 dx, 可以很大改善性能 Whether to run the {@code dx} compiler as a separate process or inside the Gradle daemon JVM.
        //  <p>Running {@code dx} in-process can greatly improve performance, but is still experimental.
        threadCount 8 // 默认运行 dx 的线程数, 默认值为4, 多核可加大. Number of threads to use when running dx. Defaults to 4.
    }

    lint {
        abortOnError false
        checkDependencies true
        checkReleaseBuilds false
    }

    compileOptions {
        targetCompatibility = '17'
        sourceCompatibility = '17'
    }
    lintOptions {
        checkDependencies true
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }

    repositories {
        mavenCentral()
        flatDir {
            dirs 'libs'
        }
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    configurations.configureEach {
        resolutionStrategy.eachDependency { DependencyResolveDetails details ->
            if (details.requested.group == 'com.facebook.soloader') {
                details.useVersion "0.12.1"
            }
        }
    }
}

kotlin {
    jvmToolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}


dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])
    implementation "com.google.android.material:material:1.0.0"

    implementation project(':PdfViewer')
    implementation 'androidx.activity:activity-compose:1.9.3'
    implementation platform('androidx.compose:compose-bom:2024.09.03')
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.ui:ui-graphics'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation 'androidx.compose.material3:material3'
    androidTestImplementation platform('androidx.compose:compose-bom:2024.09.03')

    debugImplementation 'androidx.compose.ui:ui-tooling'
    debugImplementation 'androidx.compose.ui:ui-test-manifest'
    implementation 'androidx.core:core-ktx:1.13.1'

    //FastBleLib
//    implementation 'com.clj.fastble:FastBleLib:2.4.0'
    implementation 'com.github.Jasonchenlijian:FastBle:2.4.0'

    implementation "androidx.appcompat:appcompat:1.7.0"

    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    //OkHttp3
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'
    //Gson
    implementation 'com.google.code.gson:gson:2.10'
    //EventBus
    implementation 'org.greenrobot:eventbus:3.3.1'
    //工具类
    implementation 'com.blankj:utilcodex:1.31.1'
    //Weak Handler
    implementation 'com.badoo.mobile:android-weak-handler:1.1'
    //Fresco
    implementation 'com.facebook.fresco:fresco:2.6.0'
    implementation 'com.facebook.fresco:imagepipeline-okhttp3:2.5.0'
    //ios SwitchButton
    implementation 'com.github.zcweng:switch-button:0.0.3@aar'
    //生成PDF
    implementation 'com.itextpdf:itextg:5.5.10'
    //aliyun OSS
    implementation 'com.aliyun.dpa:oss-android-sdk:2.9.12'
    //bugly
    implementation 'com.tencent.bugly:crashreport:4.0.4'
    //二维码扫描
    implementation 'com.github.bingoogolapple.BGAQRCode-Android:zxing:1.3.8'
    implementation 'com.github.bingoogolapple.BGAQRCode-Android:zbar:1.3.8'

    //下拉刷新
    implementation 'com.baoyz.pullrefreshlayout:library:1.2.0'
    //gif
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.23'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'androidx.preference:preference-ktx:1.2.1'
    implementation fileTree(dir: 'libs', include: ['*.aar', '*.jar'], exclude: [])
    implementation 'androidx.room:room-common:2.6.1'

    // region google 手写体识别
    overseasImplementation('com.google.api-client:google-api-client-android:1.23.0'){
        exclude module: 'httpclient'
        exclude group: 'com.google.guava', module: 'guava-jdk5'
    }
    overseasImplementation 'com.google.http-client:google-http-client-gson:1.23.0' exclude module: 'httpclient'
    overseasImplementation ('com.google.apis:google-api-services-vision:v1-rev369-1.23.0') {
        exclude group:'com.google.guava', module: 'guava-jdk5'
    }
    // endregion

    //ViewPager
//    implementation 'com.github.lsjwzh.RecyclerViewPager:lib:v1.1.1@aar'

    //netty
    implementation 'io.netty:netty-all:4.1.63.Final'

    implementation 'com.github.moyokoo:Diooto:1.16'
    implementation 'com.github.chrisbanes:PhotoView:2.0.0'

    //AuraMate引入新的刷新控件
    implementation 'com.scwang.smartrefresh:SmartRefreshLayout:1.1.2'
    //AuraMate 报告图
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
    // Kotlin协程
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1'

    //imageloader
    implementation 'com.nostra13.universalimageloader:universal-image-loader:1.9.5'

    // Activity swipe back
    implementation 'me.imid.swipebacklayout.lib:library:1.1.0'

    // region-- push begin
    // vivo
    czurImplementation files(rootProject.ext.deps.vivoPush)
    huaweiImplementation files(rootProject.ext.deps.vivoPush)
    xiaomiImplementation files(rootProject.ext.deps.vivoPush)
    vivoImplementation files(rootProject.ext.deps.vivoPush)
    oppoImplementation files(rootProject.ext.deps.vivoPush)
    qihooImplementation files(rootProject.ext.deps.vivoPush)
    yingyongbaoImplementation files(rootProject.ext.deps.vivoPush)
    // huawei
    czurImplementation rootProject.ext.deps.huaweiPush
    huaweiImplementation rootProject.ext.deps.huaweiPush
    xiaomiImplementation rootProject.ext.deps.huaweiPush
    vivoImplementation rootProject.ext.deps.huaweiPush
    oppoImplementation rootProject.ext.deps.huaweiPush
    qihooImplementation rootProject.ext.deps.huaweiPush
    yingyongbaoImplementation rootProject.ext.deps.huaweiPush
    // meizu
    czurImplementation(group: 'com.meizu.flyme.internet', name: 'push-internal', version: rootProject.ext.deps.meizuPushVersion, ext: 'aar')
    huaweiImplementation(group: 'com.meizu.flyme.internet', name: 'push-internal', version: rootProject.ext.deps.meizuPushVersion, ext: 'aar')
    xiaomiImplementation(group: 'com.meizu.flyme.internet', name: 'push-internal', version: rootProject.ext.deps.meizuPushVersion, ext: 'aar')
    vivoImplementation(group: 'com.meizu.flyme.internet', name: 'push-internal', version: rootProject.ext.deps.meizuPushVersion, ext: 'aar')
    oppoImplementation(group: 'com.meizu.flyme.internet', name: 'push-internal', version: rootProject.ext.deps.meizuPushVersion, ext: 'aar')
    qihooImplementation(group: 'com.meizu.flyme.internet', name: 'push-internal', version: rootProject.ext.deps.meizuPushVersion, ext: 'aar')
    yingyongbaoImplementation(group: 'com.meizu.flyme.internet', name: 'push-internal', version: rootProject.ext.deps.meizuPushVersion, ext: 'aar')
    // oppo
    czurImplementation files(rootProject.ext.deps.oppoPush)
    huaweiImplementation files(rootProject.ext.deps.oppoPush)
    xiaomiImplementation files(rootProject.ext.deps.oppoPush)
    vivoImplementation files(rootProject.ext.deps.oppoPush)
    oppoImplementation files(rootProject.ext.deps.oppoPush)
    qihooImplementation files(rootProject.ext.deps.oppoPush)
    yingyongbaoImplementation files(rootProject.ext.deps.oppoPush)
    // xiaomi
    czurImplementation files(rootProject.ext.deps.xiaomiPush)
    huaweiImplementation files(rootProject.ext.deps.xiaomiPush)
    xiaomiImplementation files(rootProject.ext.deps.xiaomiPush)
    vivoImplementation files(rootProject.ext.deps.xiaomiPush)
    oppoImplementation files(rootProject.ext.deps.xiaomiPush)
    qihooImplementation files(rootProject.ext.deps.xiaomiPush)
    yingyongbaoImplementation files(rootProject.ext.deps.xiaomiPush)
    // -- push end

    czurImplementation rootProject.ext.deps.jpush
    czurImplementation rootProject.ext.deps.jcore
    huaweiImplementation rootProject.ext.deps.jpush
    huaweiImplementation rootProject.ext.deps.jcore
    xiaomiImplementation rootProject.ext.deps.jpush
    xiaomiImplementation rootProject.ext.deps.jcore
    vivoImplementation rootProject.ext.deps.jpush
    vivoImplementation rootProject.ext.deps.jcore
    oppoImplementation rootProject.ext.deps.jpush
    oppoImplementation rootProject.ext.deps.jcore
    qihooImplementation rootProject.ext.deps.jpush
    qihooImplementation rootProject.ext.deps.jcore
    yingyongbaoImplementation rootProject.ext.deps.jpush
    yingyongbaoImplementation rootProject.ext.deps.jcore
    // endregion

    //新版ios button
    implementation 'com.github.iielse:switchbutton:1.0.4'
    // push 解析
    implementation 'org.apache.commons:commons-lang3:3.7'
    // 解除android p反射限制
    implementation 'me.weishu:free_reflection:2.2.0'
    implementation 'org.lsposed.hiddenapibypass:hiddenapibypass:2.0'

    //完美解决AppBarLayout的滑动问题
    implementation 'com.github.yuruiyin:AppbarLayoutBehavior:v1.0.2'
    implementation 'com.davemorrissey.labs:subsampling-scale-image-view:3.10.0'
    //给任意控件加阴影
    implementation 'com.github.lihangleo2:ShadowLayout:3.3.2'

    overseasImplementation 'com.google.firebase:firebase-messaging:22.0.0'
    overseasImplementation 'com.google.firebase:firebase-core:21.0.0'
    overseasImplementation 'com.google.firebase:firebase-iid:21.1.0'
    overseasImplementation 'com.google.android.gms:play-services-base:18.0.1'

    // pinyin4j
    implementation 'com.github.open-android:pinyin4j:2.5.0'

    // 声网
    implementation 'io.agora.rtc:full-screen-sharing:4.1.1.5'
    implementation 'io.agora.rtc:agora-special-full:4.1.1.5'

    // 即时通讯sdk
    implementation 'io.agora.rtm:rtm-sdk:1.5.3'

    // TODO OVERSEAS 海外需要注掉 华为推送
    implementation 'com.huawei.agconnect:agconnect-core:1.9.1.301'

    // region jetpack lib
    implementation 'androidx.navigation:navigation-fragment-ktx:2.5.3'
    implementation 'androidx.navigation:navigation-ui-ktx:2.5.3'
//    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.0'
    implementation 'com.github.bumptech.glide:glide:4.14.2'

    def lifecycle_version = "2.6.1"
    // ViewModel
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-viewmodel:$lifecycle_version"
    // LiveData
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
    // Lifecycles only (without ViewModel or LiveData)
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:$lifecycle_version"
    // helpers for implementing LifecycleOwner in a Service
    implementation "androidx.lifecycle:lifecycle-service:$lifecycle_version"

    implementation "androidx.activity:activity-ktx:1.7.0"
    implementation "androidx.fragment:fragment-ktx:1.5.5"

    // 分页
    def paging_version = "3.1.1"
    implementation "androidx.paging:paging-runtime-ktx:$paging_version"
    // endregion

    implementation "androidx.viewpager2:viewpager2:1.0.0"
    implementation 'com.google.android:flexbox:2.0.1'
    implementation 'com.noober.background:core:1.6.0'//view背景库
    // Guava
    implementation 'com.google.guava:guava:30.1-android'
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'

//    implementation 'com.yanzhenjie:permission:2.0.3'

    // viewpager2的指示器
    implementation 'com.github.zhpanvip:viewpagerindicator:1.2.1'

    // 您需要使用https://github.com/JakeWharton/ThreeTenABP才能将LocalDateTime与Android API <26一起使用。
    implementation 'com.jakewharton.threetenabp:threetenabp:1.4.0'

    //oppo推送需求
    implementation 'commons-codec:commons-codec:1.11'
    implementation 'androidx.annotation:annotation:1.6.0'

    // android13 notification
    implementation 'androidx.work:work-runtime-ktx:2.8.0'

    // CZURLog日志插件
    implementation 'com.gitee.czur_dl:czurutils:v1.6.2'

    implementation 'com.fasterxml.jackson.module:jackson-module-kotlin:2.12.5'


}