package com.czur.cloud.event;

public enum EventType {

    //AuraHome
    NETTY_CONSOLE,
    AURA_BIND_SUCCESS,
    AURA_MATE_ONLINE,

    AURA_MOVE_SUCCESS,
    AURA_CROP_SUCCESS,
    AURA_RENAME_DEVICE,
    AURA_DELETE_FILE,
    AURA_SWITCH_FLATTEN_SUCCESS,
    AURA_SWITCH_FLATTEN_FAILED,
    AURA_SWITCH_COLOR_SUCCESS,
    AURA_SWITCH_COLOR_FAILED,
    ADD_AURA_SHARE_USER,
    DELETE_AURA_SHARE_USER,
    EXIT_SHARE_USER,
    TRANSFER_AURA_MATE,
    UNBIND_AURA_MATE,
    ADD_<PERSON>ONG_QUESTION_TAG,
    <PERSON>LETE_WRONG_QUESTION_TAG,
    DELETE_WRONG_QUESTION,

    MODE_CHANGE,
    CHECK_DEVICE_IS_ONLINE,
    CHECK_APP_IS_ONLINE,
    LIGHT_SWITCH,
    LIGHT_LEVEL,
    LIGHT_MODE,
    SP_SWITCH,
    <PERSON>_<PERSON>EVE<PERSON>,
    <PERSON>_VOLUME,
    SYSTEM_LANGUAGE,
    <PERSON>ART_POWER,
    <PERSON>DENTARY_REMINDER_SWITCH,
    SEDENTARY_REMINDER_DURATION,


    DEVICE_VIDEO_REQUEST,
    APP_VIDEO_REQUEST,
    APP_IS_READY_FOR_VIDEO,
    DEVICE_IS_READY_FOR_VIDEO,
    DEVICE_CANCEL_VIDEO,
    VIDEO_CANCEL,
    VIDEO_CAMERA_SWITCH,

    CHECK_VIDEO_REQUEST_ACTIVE,

    SITTING_POSITION_VIDEO,
    SITTING_POSITION_CALIBRATE,


    SITTING_POSITION_NOTICE_SENSITIVITY,
    SITTING_POSITION_NOTICE_SWITCH,
    SITTING_WARNING,
    AURA_MATE_OFFLINE,
    AURA_MATE_CHANGED,
    AURA_MATE_UPDATE,
    AURA_MATE_READY_UPDATE,
    AURA_MATE_NEW_FILES,
    HD_VIEW,
    HD_SAVE_VIEW,
    HD_SAVE_VIEW_V2,
    SWITCH_HD_RESULT,
    DEVICE_IS_READY_FOR_VIDEO_TIME_OUT,
    SITTING_POSITION_VIDEO_TIME_OUT,
    SITTING_POSITION_CALIBRATE_TIME_OUT,
    GET_VIDEO_ROOM_CHANNEL,
    CHANGE_LANGUAGE,


    AURA_MATE_OCR_AGAIN,
    SITTING_POSITION_FIRST_CALIBRATE,
    REFRESH_MISSED_CALL,

    //登录、注册、修改密码
    LOG_IN,
    THIRD_PARTY_LOGIN_IN,
    REGISTER_SUCCESS,
    THIRD_PARTY_REGISTER_SUCCESS,
    THIRD_PARTY_BIND_ACCOUNT_SUCCESS,
    RESET_PASSWORD,

    //登出
    TOKEN_TIME_OUT_TO_LOGIN,
    THIRD_TOKEN_TIME_OUT_TO_LOGIN,
    LOG_OUT,
    DE_REGISTER, //de_register

    //个人中心
    BIND_PHONE,
    CHANGE_PHONE,
    BIND_EMAIL,
    CHANGE_EMAIL,
    EDIT_USER_IMAGE,
    CHANGE_PASSWORD,
    USER_EDIT_NAME,
    UPDATE_CACHE,
    CHANGE_USER_ID, // 海外userid 更新mobile

    //更新
    HAS_NEW_VERSION,
    HAS_NEW_VERSION_OK,     // 可以点击啦
    IS_LATEST_VERSION,
    FIRST_CLICK_NEW,
    //扫描图片
    SCAN_ALBUM_FOLDER,

    //主画面设备
    ADD_EQUIPMENT,
    REMOVE_EQUIPMENT,

    //Book
    HANDWRITING_COUNT_ADD,
    HANDWRITING_COUNT_REDUCE,
    ADD_TAG,
    ADD_TAGS,
    CHANGE_TAG,
    DELETE_TAG,
    DELETE_TAGS,
    DELETE_TAG_IN_PREVIEW,
    SELECT_LANGUAGE,

    //同步
    STOP_SYNC,
    SYNC_IS_STOP,
    SYNC_SPACE_IS_NOT_ENOUGH,
    SYNC_IS_FINISH,
    SYNC_ANIM_FINISH,
    BOOKS_OR_PAGES_CHANGED,
    PDFS_CHANGED,
    IS_SYNCHRONIZING,
    STOP_SYNC_TIME_COUNT,
    RESET_TIME_COUNT,
    REMOVE_BOOK,

    //ET
    REMOVE_ET,
    TRANSFER_SHARE_USER,
    UNBIND_DEVICE,
    ADD_SHARE_USER,
    RENAME_DEVICE,
    CHANGE_PUBLIC_STATUS,
    DELETE_SHARE_USER,
    DOWNLOAD_MP3_FAILED,
    DOWNLOAD_MP3_SUCCESS,
    WIFI_CONNECT_SUCCESS,
    WIFI_NO_NEED_KEY,
    BIND_SUCCESS,
    MOVE_SUCCESS,
    CROP_SUCCESS,
    SWITCH_FLATTEN_SUCCESS,
    SWITCH_FLATTEN_FAILED,
    SWITCH_COLOR_SUCCESS,
    SWITCH_COLOR_FAILED,
    ORIGINAL_PROGRESS,
    CACHE_SET_IMAGE_AND_BTN,
    CACHE_SAVE,
    DELETE_FILE,

    // 错误坐姿的menu的消息
    //menu_feedback
    AURA_MATE_SIT_MENU_FEEDBACK,
    //menu_standarpic
    AURA_MATE_SIT_MENU_STANDARPIC,


    ////坐姿仪
    SITTING_INIT_STATUS,        // 设备初始状态
    SITTING_UNSCAN_BLE,     // 没有扫描到ble设备
    SITTING_NO_OPEN_BLE,     // ble设备不在线或没有开启蓝牙
    SITTING_UNBIND,         // 解绑
    SITTING_BIND_SUCCESS,   // 绑定成功
    SITTING_IS_ONLINE,      // 设备上线
    SITTING_IS_ONLINE_UI,      // 设备上线,并刷新大圆圈
    SITTING_OFFLINE,        // 设备离线
    SITTING_AUTH_WRITE_FAIL,  //写入失败
    SITTING_IS_UPDATE,      // 设备升级
    SITTING_DEVICE_UPDATING,      // 设备升级中
    SITTING_DEVICE_AUTOCONNECT,      // 设备自己抢自己，自动连接
    SITTING_SCROLL_TOP,        // 刷新并移到顶部
    SITTING_DEVICE_RECONNECT,      // 断开重连
    SITTING_CANCEL_UPDATE,      // 固件升级取消,刷新消息

    SITTING_AUTH_SUCCESS,   // 鉴权成功
    SITTING_AUTH_FAIL,      // 鉴权失败

    SITTING_AUTH_CLEAR_SUCCESS,   // 首次鉴权设备清理数据成功
    SITTING_AUTH_CLEAR_FAIL,      // 鉴权失败

    SITTING_MTU_SUCCESS,    // MTU成功
    SITTING_MTU_FAIL,    // MTU失败
    SITTING_MTU_FAIL_RETRY,    // MTU失败,重连

    SITTING_TIMEZONE_SUCCESS,    // TIMEZONE成功
    SITTING_TIMEZONE_FAIL,    // TIMEZONE失败

    SITTING_LOADING,   //加载loading

    SITTING_CHANGE_VOLUME,      //音量
    SITTING_CHANGE_SENSITIVITY, //坐姿灵敏度
    SITTING_CHANGE_LONGSIT,     //久坐时长
    SITTING_CHANGE_DEVICENAME,  //改变设备名
    SITTING_CHANGE_LIGHTLEVEL, //提示灯亮度
    SITTING_CHANGE_HAPPYTIME, //坐姿心情瞬间
    SITTING_CHANGE_MODEL,   //坐姿模式
    SITTING_CHANGE_HAPPYTIME_SWITCH,   //开心瞬间、错误坐姿开关
    SITTING_CHANGE_ERRORTIME_SWITCH,   //开心瞬间、错误坐姿开关

    SITTING_FEEDBACK_SETTING,     // 设备主动发出 // 配置结果：0 //"3BYTE：命令名三级参数    字符串：0，成功；1，失败"
    SITTING_FEEDBACK_INPUT,     // 设备主动发出 //坐姿录入结果：1 // "字符串：0，初次成功；1，重新录入成功;2，初次录入失败。"
    SITTING_FEEDBACK_MTU,
    SITTING_EXPER_EXIT,      // 退出坐姿体验:2
    SITTING_EXPER_STATUS,      //坐姿体验状态： 8
    SITTING_APPGET_STATUS,      //坐姿监控模式    //0：智能坐姿监控；1：自定义坐姿监控；
    SITTING_APPGET_SILENT,      //  静音状态
    SITTING_APPGET_ALGO,      //  算法学习更新json数据


    // 坐姿图片返回的消息类型
    SITTING_PICTURE_STANDAR,    //标准坐姿：2
    SITTING_PICTURE_ERROR,      //错误坐姿：3
    SITTING_PICTURE_HAPPY,      //最开心图：4
    SITTING_PICTURE_STANDAR_ERROR,  //标准坐姿错误图：5

    SITTING_PICTURE_STANDAR_CHANGE, // 标准坐姿改变

    //报告：1
    SITTING_REPORT_NOW,  //当前报告：0
    SITTING_REPORT_HISTORY,  //全部未传报告：1
    SITTING_APPGET_REPORT_HISTORY,  // 历史报告发送状态  0， 发送完成；1，未发送完成
    SITTING_REPORT_SUCCESS, // 报告上传成功
    SITTING_ALGO_SUCCESS, // algo上传成功

    //打开通知-READ
    SITTING_OPEN_NOTIFY_SUCCESS,   //打开通知成功
    SITTING_OPEN_NOTIFY_FAIL,   //打开通知失败
    SITTING_OPEN_NOTIFY_PIC_SUCCESS,   //打开图片通知成功
    SITTING_OPEN_NOTIFY_PIC_FAIL,   //打开图片通知失败
    SITTING_OPEN_NOTIFY_PICERR_SUCCESS,   //打开错误图片通知成功
    SITTING_OPEN_NOTIFY_PICERR_FAIL,   //打开错误图片通知失败

    // 固件升级
    SITTING_UPDATE_RESULT670,     //设备升级结果 字符串：0，升级成功；1，升级发生错误
    SITTING_UPDATE_UPDATEING660,     //设备开始升级 //设备端返回：0，开始升级；1，其他错误"
    SITTING_UPDATE_RECV_OK650,     //固件接收完毕  字符串：0，接收成功；1，接收数据发生错误	设备端必须保证数据接收结果被正确发送给APP一次
    SITTING_UPDATE_RESEND,     //固件续发  "设备端：续发文件偏移位置   //APP：固件偏移数据+MD5校验值"
    SITTING_UPDATE_QUERY,     //固件版本查询  设备端返回：固件版本号字符串"
    SITTING_UPDATE_RECV_SIZE,   // 固件接收长度
    SITTING_UPDATE_CANCEL,   // 固件升级取消

    SITTING_RECONNECT, // 断线重连
    SITTING_SYS_LOGOUT, // 退出登录消息，断开ble

    SITTING_HAPPYPIC_DELETE_SUCCESS, // 开心图片删除
    SITTING_NETWORK_ONLINE, // 网络在线、离线
    SITTING_NETWORK_OFFLINE, // 网络在线、离线

    SITTING_APP_RESUME, // app从后台唤醒到前台了

    SITTING_TIMEZONE_CHANGE, // 日期、时间、时区变化了


    // Starry
    STARRY_DEVICE_ONLINE,
    STARRY_DEVICE_OFFLINE,

    STARRY_PLAYOUT_WARN,//规避中兴手机会议中听不见音频的问题
    STARRY_REJOIN_SUCCESS,//意外断网后，声网自动重新入会成功后回调，通知服务器重新入会成功

    STARRY_MESSAGE_SELECT, // 消息菜单，选择
    STARRY_MESSAGE_READ, // 消息菜单，已读
    STARRY_MESSAGE_DELETE, // 消息菜单，删除

    STARRY_CONTACT_ADD, //添加联系人
    STARRY_CONTACT_ADDTO, //添加到联系人
    STARRY_CONTACT_DEL, //删除联系人
    STARRY_CONTACT_EDIT, //编辑联系人
    STARRY_CONTACT_EDIT_MEETING, //编辑联系人

    STARRY_RECENTLY_DEL, //删除会议记录

    STARRY_COMPANY_NOTICE, //加入企业邀请通知消息
    STARRY_COMPANY_NOTICE_DELAY, //加入企业邀请通知消息-延迟获取

    STARRY_COMPANY_ADD_CONTACTS, //添加联系人通知消息(增删改)
    STARRY_COMPANY_NOTICE_REMOVE, //移除企业邀请通知消息
    STARRY_COMPANY_JOIN, //同意加入企业
    STARRY_COMPANY_REJECT, //拒绝加入企业
    STARRY_COMPANY_EXIT, //退出企业
    STARRY_COMPANY_INVENT_GONE, //企业邀请过期
    STARRY_COMPANY_NOTICE_CLEAR, //通知消息清空

    STARRY_NOTICE_OTHER_DEVICE_LOGIN, //在其他设备登录
    STARRY_RESULT_CODE_INVALID_TOKEN, // token失效

    STARRY_ROOM_USER_LIST, //加入会议的成员列表
    STARRY_MEETING_CMD_STOP, //接收到的会议结束指令
    STARRY_MEETING_CMD_STOP_ALERT, //接收到的会议结束指令,退出提示
    STARRY_MEETING_CMD_REMOVE, //接收到的离开会议室指令
    STARRY_MEETING_CMD_EXIT, //离开会议室
    STARRY_MEETING_CMD_STOP_CALL,   // PC端已接听, 停止呼叫页面
    STARRY_ROOM_USER_UPDATE, // 状态修改的成员信息

    STARRY_MEETING_ERROR_EXIT, //发起会议错误，退出；

    STARRY_JOIN_MEETING_FAIL, //加入会议连接超时，退出；

    STARRY_MEETING_CMD_MUTE_AUDIO, //接收到的音频控制关指令
    STARRY_MEETING_CMD_OPEN_AUDIO, //接收到的音频控制开指令
    //接收到的视频控制关指令
    STARRY_MEETING_CMD_MUTE_VIDEO,
    //接收到的视频控制开指令
    STARRY_MEETING_CMD_OPEN_VIDEO,
    //接收到的成为会议管理员指令
    STARRY_MEETING_CMD_HOST,
    //接收到的开始屏幕分享指令
    STARRY_MEETING_CMD_SHARE,
    STARRY_MEETING_CMD_STOPSHARE,
    STARRY_MEETING_CMD_QUITSHARE,
    // 其他端加入会议时收到的消息
    STARRY_MEETING_CMD_OTHER_JOINED,
    // 多端只能加入一个会议
    STARRY_MEETING_CMD_OTHER_MEETING_JOINED,

    // 长时间挂机熔断处理消息
    STARRY_MEETING_CMD_STOP_FORCE,

    // 接收到正在进行的会议
    STARRY_MEETING_CMD_CHECK_MEETING_LIST,

    STARRY_SHOW_NONETWORK_ALERT, //无网络弹窗
    STARRY_DISMISS_NONETWORK_ALERT, //隐藏无网络弹窗

    STARRY_CLOSE_MEETING, //管理员暂离后,会议人员全部退出,触发

    STARRY_USER_TOUCH_SCREEN, //用户操作屏幕触发

    STARRY_MESSAGE_CALL_VIDEO_CALLING_MEETING, //呼叫中收到来电
    STARRY_MESSAGE_CALL_VIDEO_IN_MEETING, //会议中收到来电
    STARRY_MESSAGE_CALL_VIDEO_IN_BACKGROUND, //放置后台收到来电

    AURAMATE_MESSAGE_CALL_VIDEO_IN_BACKGROUND, //放置后台收到AuraMate视频请求

    CLICK_IMAGE_FOLDER,//点击相册,跳转相册内部

    STARRY_USER_PHOTO_CHANE, //用户更换头像

    STARRY_MEETING_CLIPBOARD,   // starry读取剪贴板内容

    STARRY_EQUIPMENTLIST_REFRESH, // 刷新list

    STARRY_EQUIPMENTLIST_CHECK_ADD, // 检查是否自动添加Starry

    STARRY_MEETING_REJOIN_FROM_WEB,    // 会议中跳转入会

    STARRY_CHANGE_PHONE, //改绑手机号

    STARRY_CHANGE_CAMARA_MIC_STATUS, //更改摄像头和麦克风的开关状态

    STARRY_NO_INCOMING_CALL, //没有正在呼入的

    STARRY_MEETING_RECORDING, //会议正在录制

    HAS_FORCE_NEW_VERSION, //强制更新

    HAS_NO_FORCE_NEW_VERSION, //没有强制更新

    CHECK_FORCE_NEW_VERSION, //检查有没有强制更新

    SHORTCUT_JOIN_MEET, //从快捷菜单跳转,弹出入会弹窗
    SHORTCUT_JOIN_MEET_NOLOGIN, //从快捷菜单跳转,弹出入会弹窗

    ESHARE_READY_TO_CONNECT, //准备链接eshare投屏,需要关闭其他投屏

    ESHARE_SCANED_JOIN_MEETING, //正在eshare投屏,快捷方式进入要加入会议

    ESHARE_JOINED_MEETING_SCAN, //正在会议,快捷方式进入扫一扫

    ESHARE_AND_MEETING_ESHARE, //正在eshare投屏和会议,要把eshare提到前台
    ESHARE_AND_MEETING_MEETING, //正在eshare投屏和会议,要把meeting提到前台

}

