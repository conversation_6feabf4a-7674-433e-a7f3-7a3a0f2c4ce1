package com.czur.cloud.ui.mirror.comm;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;

import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.util.Base64;

import com.blankj.utilcode.util.FileUtils;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;

public class FastBleHexUtils {

    /**
     * 从一个byte[]数组中截取一部分
     * @param src
     * @param begin
     * @param count
     * @return
     */
    public static byte[] subBytes(byte[] src, int begin, int count) {
        byte[] bs = new byte[count];
        for (int i=begin;i<begin+count; i++) bs[i-begin] = src[i];
        return bs;
    }

    //System.arraycopy()方法
    public static byte[] byteMerger(byte[] bt1, byte[] bt2){
        byte[] bt3 = new byte[bt1.length+bt2.length];
        System.arraycopy(bt1, 0, bt3, 0, bt1.length);
        System.arraycopy(bt2, 0, bt3, bt1.length, bt2.length);
        return bt3;
    }


    // 10进制转16进制
    public static String tenToHexStr(int tenNumber){
//        String ret = Integer.toHexString(tenNumber).toUpperCase();
        int l = String.valueOf(tenNumber).length();
        if (l<2)
            l =2;

        String format = "%0" + l + "x";
        String ret = String.format(format, tenNumber).toUpperCase();//高位补0
        return ret;
    }

    public static String tenToHexStr(long tenNumber){
//        String ret = Long.toHexString(tenNumber).toUpperCase();
        String ret = String.format("%08x", tenNumber).toUpperCase();//高位补0
        return ret;
    }

    // 10进制转化为16进制，保留指定的位数
    public static String intTenToHex(int n,int size) {
        StringBuffer s = new StringBuffer();
        String a;
        char []b = {'0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'};
        while(n != 0){
            s = s.append(b[n%16]);
            n = n/16;
        }
        a = s.reverse().toString();
        a  = add_zore(a,size);
        return a;
    }
    private static String add_zore(String str, int size){
        if (str.length()<size){
            str= "0"+str;
            str=add_zore(str,size);
            return str;
        }else {
            return str;
        }
    }

    /**
     * 字符串转换成十六进制字符串
     * @return String 每个Byte之间空格分隔，如: [61 6C 6B]
     */
    public static String str2HexStr(String str) {

        char[] chars = "0123456789ABCDEF".toCharArray();
        StringBuilder sb = new StringBuilder("");
        byte[] bs = str.getBytes();
        int bit;

        for (int i = 0; i < bs.length; i++) {
            bit = (bs[i] & 0x0f0) >> 4;
            sb.append(chars[bit]);
            bit = bs[i] & 0x0f;
            sb.append(chars[bit]);
//            sb.append(' ');
        }
        return sb.toString().trim();
    }

    public static String tenToHex(int num){
        String array[]={"0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F"};
        int temp;
        StringBuffer strb = new StringBuffer();
        for(int i=0;i<8;i++){
            temp=num&15;
            strb.append(array[temp]);
            num=num>>>4;
        }

        return strb.reverse().toString();
    }

    /**
     * bytes字符串转换为Byte值
     * @param //String src Byte字符串，每个Byte之间没有分隔符
     * @return byte[]
     */
    public static byte[] hexStr2Bytes(String src) {
        int m=0,n=0;
        int l=src.length()/2;
        System.out.println(l);
        byte[] ret = new byte[l];
        for (int i = 0; i < l; i++)
        {
            m=i*2+1;
            n=m+1;
//            String nm = "0x" + src.substring(i*2, m) + src.substring(m,n);
            String nm = src.substring(i*2, m) + src.substring(m,n);
//            ret[i] = Byte.decode(nm);
            ret[i] = (byte)(Integer.parseInt(nm, 16) );
        }
        return ret;
    }


    /** 16进制的字符串转换成16进制字符串数组
     * @param src
     * @return
     */
    public static byte[] HexString2Bytes(String src) {
        int len = src.length() / 2;
        byte[] ret = new byte[len];
        byte[] tmp = src.getBytes();
        for (int i = 0; i < len; i++) {
            ret[i] = uniteBytes(tmp[i * 2], tmp[i * 2 + 1]);
        }
        return ret;
    }

    public static byte uniteBytes(byte src0, byte src1) {
        byte _b0 = Byte.decode("0x" + new String(new byte[]{src0})).byteValue();
        _b0 = (byte) (_b0 << 4);
        byte _b1 = Byte.decode("0x" + new String(new byte[]{src1})).byteValue();
        byte ret = (byte) (_b0 ^ _b1);
        return ret;
    }

    /*
     * 字节数组转16进制字符串显示
     */
    public static String bytes2HexString(byte[] b, int length) {
        String r = "";

        for (int i = 0; i < length; i++) {
            String hex = Integer.toHexString(b[i] & 0xFF);
            if (hex.length() == 1) {
                hex = "0" + hex;
            }
            r += hex.toUpperCase();
        }

        return r;
    }

    /**
     * 十六进制转换字符串
     * @param //String str Byte字符串(Byte之间无分隔符 如:[616C6B])
     * @return String 对应的字符串
     */
    public static String hexStr2Str(String hexStr)
    {
        hexStr = hexStr.toUpperCase();
        String str = "0123456789ABCDEF";
        char[] hexs = hexStr.toCharArray();
        byte[] bytes = new byte[hexStr.length() / 2];
        int n;

        for (int i = 0; i < bytes.length; i++)
        {
            n = str.indexOf(hexs[2 * i]) * 16;
            n += str.indexOf(hexs[2 * i + 1]);
            bytes[i] = (byte) (n & 0xff);
        }
        return new String(bytes);
    }

    /**
     * bytes转换成十六进制字符串
     * @param //byte[] b byte数组
     * @return String 每个Byte值之间空格分隔
     */
    public static String byte2HexStr(byte[] b)
    {
        String stmp="";
        StringBuilder sb = new StringBuilder("");
        for (int n=0;n<b.length;n++)
        {
            stmp = Integer.toHexString(b[n] & 0xFF);
            sb.append((stmp.length()==1)? "0"+stmp : stmp);
            //sb.append(" ");
        }
        return sb.toString().toUpperCase().trim();
    }

    /**
     * String的字符串转换成unicode的String
     * @param //String strText 全角字符串
     * @return String 每个unicode之间无分隔符
     * @throws Exception
     */
    public static String strToUnicode(String strText)
            throws Exception
    {
        char c;
        StringBuilder str = new StringBuilder();
        int intAsc;
        String strHex;
        for (int i = 0; i < strText.length(); i++)
        {
            c = strText.charAt(i);
            intAsc = (int) c;
            strHex = Integer.toHexString(intAsc);
            if (intAsc > 128)
                str.append("\\u" + strHex);
            else // 低位在前面补00
                str.append("\\u00" + strHex);
        }
        return str.toString();
    }

    /**
     * unicode的String转换成String的字符串
     * @param //String hex 16进制值字符串 （一个unicode为2byte）
     * @return String 全角字符串
     */
    public static String unicodeToString(String hex)
    {
        int t = hex.length() / 6;
        StringBuilder str = new StringBuilder();
        for (int i = 0; i < t; i++)
        {
            String s = hex.substring(i * 6, (i + 1) * 6);
            // 高位需要补上00再转
            String s1 = s.substring(2, 4) + "00";
            // 低位直接转
            String s2 = s.substring(4);
            // 将16进制的string转为int
            int n = Integer.valueOf(s1, 16) + Integer.valueOf(s2, 16);
            // 将int转换为字符
            char[] chars = Character.toChars(n);
            str.append(new String(chars));
        }
        return str.toString();
    }
    //-------------------------------------------------------
// 判断奇数或偶数，位运算，最后一位是1则为奇数，为0是偶数
    static public int isOdd(int num)
    {
        return num & 0x1;
    }
    //-------------------------------------------------------
    static public int HexToInt(String inHex)//Hex字符串转int
    {
        return Integer.parseInt(inHex, 16);
    }
    //-------------------------------------------------------
    static public byte HexToByte(String inHex)//Hex字符串转byte
    {
        return (byte)Integer.parseInt(inHex,16);
    }
    //-------------------------------------------------------
    static public String Byte2Hex(Byte inByte)//1字节转2个Hex字符
    {
        return String.format("%02x", inByte).toUpperCase();
    }
    //-------------------------------------------------------
    static public String ByteArrToHex(byte[] inBytArr)//字节数组转转hex字符串
    {
        StringBuilder strBuilder=new StringBuilder();
        int j=inBytArr.length;
        for (int i = 0; i < j; i++)
        {
            strBuilder.append(Byte2Hex(inBytArr[i]));
            strBuilder.append(" ");
        }
        return strBuilder.toString();
    }
    //-------------------------------------------------------
    static public String ByteArrToHex(byte[] inBytArr,int offset,int byteCount)//字节数组转转hex字符串，可选长度
    {
        StringBuilder strBuilder=new StringBuilder();
        int j=byteCount;
        for (int i = offset; i < j; i++)
        {
            strBuilder.append(Byte2Hex(inBytArr[i]));
        }
        return strBuilder.toString();
    }
    //-------------------------------------------------------
//转hex字符串转字节数组
    static public byte[] HexToByteArr(String inHex)//hex字符串转字节数组
    {
        int hexlen = inHex.length();
        byte[] result;
        if (isOdd(hexlen)==1)
        {//奇数
            hexlen++;
            result = new byte[(hexlen/2)];
            inHex="0"+inHex;
        }else {//偶数
            result = new byte[(hexlen/2)];
        }
        int j=0;
        for (int i = 0; i < hexlen; i+=2)
        {
            result[j]=HexToByte(inHex.substring(i,i+2));
            j++;
        }
        return result;
    }


    //16进制字符串转字节数组
    /*
     * 字符转换为字节
     */
    private static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }

    /*
     * 16进制字符串转字节数组
     */
    public static byte[] hexString2Bytes(String hex) {

        if ((hex == null) || (hex.equals(""))){
            return null;
        }
        else if (hex.length()%2 != 0){
            return null;
        }
        else{
            hex = hex.toUpperCase();
            int len = hex.length()/2;
            byte[] b = new byte[len];
            char[] hc = hex.toCharArray();
            for (int i=0; i<len; i++){
                int p=2*i;
                b[i] = (byte) (charToByte(hc[p]) << 4 | charToByte(hc[p+1]));
            }
            return b;
        }

    }

    //字符串转字节数组
    /*
     * 字符串转字节数组
     */
    public static byte[] string2Bytes(String s){
        byte[] r = s.getBytes();
        return r;
    }

    //字节数组转字符串
    /*
     * 字节数组转字符串
     */
    public static String bytes2String(byte[] b) throws Exception {
        String r = new String (b,"UTF-8");
        return r;
    }

    ////////////////////////

    //byte转为string显示
    public static String getStringValue(byte[] mValue) {
        if (mValue == null || 0 > mValue.length)
            return null;
        final byte[] strBytes = new byte[mValue.length];
        for (int i = 0; i != (mValue.length); ++i)
            strBytes[i] = mValue[i];
        return new String(strBytes);
    }

    public static String getLocalJson(String fileName,Context context) {
        //将json数据变成字符串
        StringBuilder stringBuilder = new StringBuilder();
        try {
            //获取assets资源管理器
            AssetManager assetManager = context.getAssets();
            //通过管理器打开文件并读取
            BufferedReader bf = new BufferedReader(new InputStreamReader(
                    assetManager.open(fileName)));
            String line;
            while ((line = bf.readLine()) != null) {
                stringBuilder.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return stringBuilder.toString();
    }


    //
    // 将Bitmap转化成byte数组，ratio表示压缩率
    public static byte[] getBitmapByte(Bitmap bitmap, int ratio) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100 / ratio, out);
        try {
            out.flush();
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return out.toByteArray();
    }

    //从byte数组中解析出来Bitmap
    public static Bitmap getBitmapFromByte(byte[] temp) {
        Bitmap bitmap = null;
        if (temp != null) {
            try {
                BitmapFactory.Options opts =  new  BitmapFactory.Options();
                opts.inJustDecodeBounds  =  false  ;//inJustDecodeBounds 需要设置为false，如果设置为true，那么将返回null
//                opts.inSampleSize  = 4 ;
                bitmap = BitmapFactory. decodeByteArray(temp, 0, temp.length, opts);

//                bitmap = BitmapFactory.decodeByteArray(temp, 0, temp.length);
            } catch (Exception e) {
                logE("getBitmapFromByte exception ="+e.toString());
            }
        }


        // Test
//        String localPicturePath = "/storage/emulated/0/CZUR/";
//        String filename = "czur_test.png";
//        FastBleHexUtils.saveBitmap(bitmap, localPicturePath, filename);

        return bitmap;
    }

    public static Bitmap getBitmapFromString(String strImage){
        byte[] x = Base64.decode(strImage, Base64.DEFAULT); //convert from base64 to byte array
        Bitmap bmp = BitmapFactory.decodeByteArray(x,0,x.length);
        return bmp;
    }

    public static Bitmap getBitmapFromByte1(byte[] temp) {
        int width = 800;
        int height = 600;
        Bitmap bitmap = null;
        if (temp != null) {
            try {
//                bitmap = BitmapFactory.decodeByteArray(temp, 0, temp.length);

                // Convert to JPG
//                Size previewSize = camera.getParameters().getPreviewSize();
                YuvImage yuvimage=new YuvImage(temp, ImageFormat.NV21, width, height, null);
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                yuvimage.compressToJpeg(new Rect(0, 0, width, height), 80, baos);
                byte[] jdata = baos.toByteArray();

                // Convert to Bitmap
                bitmap = BitmapFactory.decodeByteArray(jdata, 0, jdata.length);

            } catch (Exception e) {
                logE("getBitmapFromByte exception ="+e.toString());
            }
        }
        return bitmap;
    }
    //还有其他的一些方法，比如从存储空间中解析一张图片为Bitmap对象。

    // 保存图片到本地
    public static void saveBitmap(Bitmap bitmap, String pic_path, String filename) {
        // 测试输出
        FileOutputStream out = null;
        try {
            if (FileUtils.createOrExistsDir(pic_path)) {
//                ImageUtils.save(bitmap, etPath, Bitmap.CompressFormat.JPEG, false);
                String name = pic_path + filename;
                out = new FileOutputStream(name);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        try {
            if (null != out) {
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, out);
                out.flush();
                out.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static boolean getLocalStandarPicture(String filepath){
        File file = new File(filepath);
        if (file.exists()) {
            Bitmap bm = BitmapFactory.decodeFile(filepath);
            //将图片显示到ImageView中
            FastBleOperationUtils.setBmpStandar(bm);
            return true;
        }
        return false;
    }

    public  static  String bufferToHex( byte  bytes[]) {
        return  bufferToHex(bytes,  0 , bytes.length);
    }

    private   static  String bufferToHex( byte  bytes[],  int  m,  int  n) {
        StringBuffer stringbuffer = new  StringBuffer( 2  * n);
        int  k = m + n;
        for  ( int  l = m; l < k; l++) {
            appendHexPair(bytes[l], stringbuffer);
        }
        return  stringbuffer.toString();
    }

    protected   static   char  hexDigits[] = {  '0' ,  '1' ,  '2' ,  '3' ,  '4' ,  '5' ,  '6' ,
            '7' ,  '8' ,  '9' ,  'a' ,  'b' ,  'c' ,  'd' ,  'e' ,  'f'  };

    private   static   void  appendHexPair( byte  bt, StringBuffer stringbuffer) {
        char  c0 = hexDigits[(bt &  0xf0 ) >>  4 ]; // 取字节中高 4 位的数字转换, >>> 为逻辑右移，将符号位一起右移,此处未发现两种符号有何不同
        char  c1 = hexDigits[bt &  0xf ]; // 取字节中低 4 位的数字转换
        stringbuffer.append(c0);
        stringbuffer.append(c1);
    }

    /**
     * 16进制转ASCII
     *
     * @param hex
     * @return
     */
    public static String hex2Str(String hex) {
        StringBuilder sb = new StringBuilder();
        StringBuilder temp = new StringBuilder();
        //49204c6f7665204a617661 split into two characters 49, 20, 4c...
        for (int i = 0; i < hex.length() - 1; i += 2) {
            //grab the hex in pairs
            String output = hex.substring(i, (i + 2));
            //convert hex to decimal
            int decimal = Integer.parseInt(output, 16);
            //convert the decimal to character
            sb.append((char) decimal);
            temp.append(decimal);
        }
        return sb.toString();
    }

}
