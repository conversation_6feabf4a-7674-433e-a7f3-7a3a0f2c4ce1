package com.czur.cloud.ui.component.dialog;

import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.View;
import android.view.WindowManager;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.EtPickDateAdapter;
import com.czur.cloud.entity.EtSummaryDayEntity;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.component.recyclerview.CustomRecyclerView;
import com.czur.cloud.ui.component.stickydecoration.PowerfulStickyDecoration;
import com.czur.cloud.ui.component.stickydecoration.listener.PowerGroupListener;
import com.google.gson.reflect.TypeToken;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class EtPickDateDialog extends Dialog implements View.OnClickListener {
    private CustomRecyclerView etPickDateRv;
    private TextView closeBtn;
    private EtPickDateAdapter etPickDateAdapter;
    private List<EtSummaryDayEntity> etSummaryDayEntityList;
    private BaseActivity activity;
    private ProgressBar progressBar;
    private String date;
    private PowerfulStickyDecoration decoration;
    private OnItemClickListener onItemClickListener;
    private LinearLayoutManager linearLayoutManager;

    public EtPickDateDialog(@NonNull BaseActivity context, String date, OnItemClickListener onItemClickListener) {
        super(context, R.style.Theme_AppCompat_Dialog);
        this.activity = context;
        this.date = date;
        this.onItemClickListener = onItemClickListener;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_et_pick_date);
        initView();
        initData(date);
        initLister();
    }

    @Override
    public void show() {
        super.show();
        //设置背景透明和宽高，不然会出现白色直角问题
        WindowManager.LayoutParams params = Objects.requireNonNull(getWindow()).getAttributes();
        params.width = (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 300, activity.getResources().getDisplayMetrics());
        params.height = (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 330, activity.getResources().getDisplayMetrics());
        getWindow().setAttributes(params);
        getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
    }

    private void initView() {
        closeBtn = findViewById(R.id.et_pick_date_btn);
        etPickDateRv = findViewById(R.id.et_pick_date_rv);
        linearLayoutManager = new LinearLayoutManager(activity);
        etPickDateRv.setLayoutManager(linearLayoutManager);
        progressBar = findViewById(R.id.et_pick_date_progress);
    }

    private void initLister() {
        closeBtn.setOnClickListener(this);
        PowerGroupListener listener = new PowerGroupListener() {
            @Override
            public String getGroupName(int position) {
                if (etPickDateAdapter != null && etPickDateAdapter.getData() != null && etPickDateAdapter.getData().size() > position) {
                    return etPickDateAdapter.getData().get(position).getDay().substring(0, 4);
                }
                return null;
            }

            @Override
            public View getGroupView(int position) {
                //获取自定定义的组View
                if (etPickDateAdapter != null && etPickDateAdapter.getData() != null && etPickDateAdapter.getData().size() > position) {
                    String newDate = etPickDateAdapter.getData().get(position).getDay().substring(0, 4);
                    View view = getLayoutInflater().inflate(R.layout.item_et_pick_date_title, null, false);
                    ((TextView) view.findViewById(R.id.et_date_title)).setText(newDate);
                    return view;
                }
                return null;
            }
        };
        decoration = PowerfulStickyDecoration.Builder
                .init(listener)
                .setGroupBackground(activity.getResources().getColor(R.color.white))
                .setGroupHeight((int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 30, activity.getResources().getDisplayMetrics()))
                .build();

        etPickDateRv.addItemDecoration(decoration);


    }

    private void initData(String date) {
        etPickDateRv.setVisibility(View.GONE);
        progressBar.setVisibility(View.VISIBLE);
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<Void>() {
            int selectIndex;

            @Override
            public Void doInBackground() {
                etSummaryDayEntityList = getSummaryDay();
                assert etSummaryDayEntityList != null;
                for (int i = 0; i < etSummaryDayEntityList.size(); i++) {
                    EtSummaryDayEntity entity = etSummaryDayEntityList.get(i);
                    entity.setDay(entity.getDay().replace("-", "."));
                    if (entity.getDay().equals(date)) {
                        selectIndex = i;
                    }
                }
                return null;
            }

            @Override
            public void onSuccess(Void result) {
                activity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        etPickDateAdapter = new EtPickDateAdapter(activity, etSummaryDayEntityList, selectIndex, onItemClickListener);
                        etPickDateRv.setAdapter(etPickDateAdapter);
                        etPickDateRv.setVisibility(View.VISIBLE);
                        progressBar.setVisibility(View.GONE);
                        if (selectIndex > 0) {
                            if (!etSummaryDayEntityList.get(selectIndex - 1).getDay().substring(0, 4).equals(etSummaryDayEntityList.get(selectIndex).getDay().substring(0, 4))) {
                                linearLayoutManager.scrollToPositionWithOffset(selectIndex, 0);
                            } else {
                                if (selectIndex == 1 || (selectIndex > 1 && !etSummaryDayEntityList.get(selectIndex - 2).getDay().substring(0, 4).equals(etSummaryDayEntityList.get(selectIndex).getDay().substring(0, 4)))) {
                                    linearLayoutManager.scrollToPositionWithOffset(selectIndex - 1, -(int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 30, activity.getResources().getDisplayMetrics()));
                                } else {
                                    linearLayoutManager.scrollToPositionWithOffset(selectIndex - 1, 0);
                                }
                            }
                        }
                    }
                });
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
                activity.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        progressBar.setVisibility(View.GONE);
                    }
                });

            }
        });

    }

    private List<EtSummaryDayEntity> getSummaryDay() {
        final MiaoHttpEntity<EtSummaryDayEntity> etSummaryDay = HttpManager.getInstance().request().getEtSummaryDaySync(
                UserPreferences.getInstance(getContext()).getUserId(), new TypeToken<List<EtSummaryDayEntity>>() {
                }.getType());
        if (etSummaryDay.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
            List<EtSummaryDayEntity> tempList = etSummaryDay.getBodyList();
            Collections.reverse(tempList);
            return tempList;
        } else {
            return null;
        }
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.et_pick_date_btn:
                dismiss();
                break;
        }
    }


    public interface OnItemClickListener {
        void onClick(String day);
    }

}
