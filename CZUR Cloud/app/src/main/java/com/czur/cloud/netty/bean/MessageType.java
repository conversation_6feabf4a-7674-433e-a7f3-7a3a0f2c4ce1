package com.czur.cloud.netty.bean;


public enum MessageType {

    HEARTBEAT("HEARTBEAT"), // 心跳包

    CONNECTED("CONNECTED"),// 连接成功

    ONLINE("ONLINE"), // 设备上线

    OFFLINE("OFFLINE"), // 设备OFFLINE

    CLOSE("CLOSE"), // 关闭连接

    COMMAND("COMMAND"), // 指令消息
    CALL_VIDEO("CALL_VIDEO"), // 指令消息
    AURA_API("AURA_API"), // 视频回执


    VIDEO("VIDEO"), // 指令消息

    // Starry MEETING
    MEETING("MEETING"), // 会议消息

    BUSINESS("BIZ"), // 自定义的业务
    DEVICE("Android"), // 自定义的业务
    OS("Android"),
    AURA_MATE("Aura Mate"),

    BUSINESS_EXCEPTION("BIZ_EXCEPTION"); // 系统业务异常

    private String type;

    MessageType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
