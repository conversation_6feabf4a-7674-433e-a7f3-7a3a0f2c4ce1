package com.czur.cloud.ui.auramate;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.ui.component.NoHintEditText;
import com.czur.cloud.util.validator.Validator;

/**
 * Created by Yz on 2018/3/7.
 * Email：<EMAIL>
 */

public class AuraMateWifiActivity extends AuramateBaseActivity implements View.OnClickListener {

    private ImageView normalBackBtn;
    private NoHintEditText auraHomeConnectWifiNameEdt;
    private NoHintEditText auraHomeConnectWifiPasswordEdt;
    private TextView nextStepBtn;
    private boolean nameHasContent = false;
    private String ssid, password;
    private boolean noNeedKey;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_home_wifi);
        initComponent();
        registerEvent();
    }

    @Override
    protected boolean PCNeedFinish() {
        return !TextUtils.isEmpty(equipmentId);
    }

    private void initComponent() {
        ssid = getIntent().getStringExtra("ssid");
        password = getIntent().getStringExtra("password");
        noNeedKey = getIntent().getBooleanExtra("noNeedKey", false);
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        auraHomeConnectWifiNameEdt = (NoHintEditText) findViewById(R.id.aura_home_connect_wifi_name_edt);
        auraHomeConnectWifiPasswordEdt = (NoHintEditText) findViewById(R.id.aura_home_connect_wifi_password_edt);
        nextStepBtn = (TextView) findViewById(R.id.next_step_btn);
    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        nextStepBtn.setOnClickListener(this);
        nextStepBtn.setSelected(false);
        nextStepBtn.setClickable(false);
        auraHomeConnectWifiNameEdt.addTextChangedListener(nameTextWatcher);
        if (!TextUtils.isEmpty(ssid)) {
            auraHomeConnectWifiNameEdt.setText(ssid);
        }
        if (!TextUtils.isEmpty(password)) {
            auraHomeConnectWifiPasswordEdt.setText(password);
        }
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.next_step_btn:
                Intent intent = new Intent(AuraMateWifiActivity.this, AuraMateWifiConnectActivity.class);
                intent.putExtra("wifiName", auraHomeConnectWifiNameEdt.getText().toString());
                intent.putExtra("wifiPsw", auraHomeConnectWifiPasswordEdt.getText().toString());
                intent.putExtra("noNeedKey", noNeedKey);
                intent.putExtra("equipmentId", equipmentId);
                ActivityUtils.startActivity(intent);
                ActivityUtils.finishActivity(this);
                break;
            case R.id.normal_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            default:
                break;
        }
    }


    private TextWatcher nameTextWatcher = new TextWatcher() {


        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            if (s.length() > 0) {
                nameHasContent = true;
            } else {
                nameHasContent = false;
            }
            checkNextStepButtonToClick();
        }

        @Override
        public void beforeTextChanged(CharSequence s, int start, int count,
                                      int after) {
        }

        @Override
        public void afterTextChanged(Editable s) {
            if (s.length() > 0) {
                nameHasContent = true;
            } else {
                nameHasContent = false;
            }
            checkNextStepButtonToClick();
        }
    };


    /**
     * @des: 检查注册按钮是否可以点击
     * @params:
     * @return:
     */

    private void checkNextStepButtonToClick() {
        boolean nameIsNotEmpty = Validator.isNotEmpty(auraHomeConnectWifiNameEdt.getText().toString());
        if (nameIsNotEmpty && nameHasContent) {
            nextStepBtn.setSelected(true);
            nextStepBtn.setClickable(true);
        } else {
            nextStepBtn.setSelected(false);
            nextStepBtn.setClickable(false);
        }
    }

}
