<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:fitsSystemWindows="true"
    android:layout_height="match_parent"
    android:background="@color/gary_f9">

    <include
        android:id="@+id/change_password_top_bar"
        layout="@layout/layout_user_top_bar" />

    <LinearLayout
        android:background="@color/gary_ff"
        android:id="@+id/user_old_password_ll"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_below="@+id/change_password_top_bar"
        android:orientation="vertical">

        <EditText
            android:id="@+id/user_old_password_edt"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:textStyle="bold"
            android:layout_marginStart="17dp"
            android:background="@null"
            android:maxLines="1"
            android:digits="@string/password_digits"
            android:hint="@string/user_old_password"
            android:textColor="@color/normal_blue"
            android:textColorHint="@color/gary_c4"
            android:textSize="15sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginStart="17dp"
            android:background="@color/gary_ef" />
    </LinearLayout>
    <LinearLayout
        android:background="@color/gary_ff"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_below="@+id/user_old_password_ll" android:orientation="vertical">

        <EditText
            android:id="@+id/user_first_password_edt"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:textStyle="bold"
            android:layout_marginStart="17dp"
            android:background="@null"
            android:maxLines="1"
            android:maxLength="20"
            android:digits="@string/password_digits"
            android:hint="@string/user_new_password"
            android:textColor="@color/normal_blue"
            android:textColorHint="@color/gary_c4"
            android:textSize="15sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gary_ef" />
    </LinearLayout>



    <com.czur.cloud.ui.component.ProgressButton
        android:layout_marginTop="23dp"
        android:layout_gravity="center_horizontal"
        android:id="@+id/user_change_password_btn"
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:background="@drawable/selector_register_btn"
        app:progress_btn_tv="@string/confirm_text" />

</LinearLayout>