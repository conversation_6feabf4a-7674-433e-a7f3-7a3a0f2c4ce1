package com.czur.cloud.adapter;

import android.app.Activity;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.entity.realm.SPReportEntity;
import com.czur.cloud.ui.auramate.reportfragment.ReportUtil;
import com.czur.cloud.util.validator.Validator;
import com.github.mikephil.charting.charts.PieChart;
import com.github.mikephil.charting.data.PieData;
import com.github.mikephil.charting.data.PieDataSet;
import com.github.mikephil.charting.data.PieEntry;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

import io.realm.Realm;
import io.realm.Sort;

/**
 * Created by shaojun on 2020/4/1
 * Email：<EMAIL>
 */


public class ReportAdapter extends RecyclerView.Adapter<ViewHolder> {
    private static final int ITEM_TYPE_NORMA = 0;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<SPReportEntity> datas;
    private LayoutInflater mInflater;
//    private onItemClickListener mOnItemClickLitener;

    /**
     * 构造方法
     */
    public ReportAdapter(Activity activity, List<SPReportEntity> datas) {
        this.mActivity = activity;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<SPReportEntity> datas) {
        this.datas = datas;
        notifyDataSetChanged();
    }

    public void refreshData(Realm realm) {
        this.datas = realm.where(SPReportEntity.class).sort("createTime", Sort.DESCENDING).findAll();
        notifyDataSetChanged();
    }


    @NotNull
    @Override
    public ViewHolder onCreateViewHolder(@NotNull ViewGroup parent, int viewType) {
//        View view = LayoutInflater.from (parent.getContext()).inflate (R.layout.item_report_all,parent,false);
//        NormalViewHolder holder = new NormalViewHolder(view);
//
//        return holder;
        return new NormalViewHolder(mInflater.inflate(R.layout.item_report_all, parent, false));
    }

    @Override
    public void onBindViewHolder(@NotNull ViewHolder holder, final int position) {
        if (holder instanceof NormalViewHolder) {
            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);

            int type = mHolder.mItem.getType();

            mHolder.report_main_ll.setVisibility(View.VISIBLE);
            mHolder.report_other_rl.setVisibility(View.GONE);

            // 海外不显示日周月报的报告列表
            if (BuildConfig.IS_OVERSEAS){
                if (type > 0){
                    return;
                }
            }
            if (type > 0){
                mHolder.report_main_ll.setVisibility(View.GONE);
                mHolder.report_other_rl.setVisibility(View.VISIBLE);

                if (mHolder.mItem.getHaveRead() == 0) {
                    mHolder.new_rl_other.setVisibility(View.VISIBLE);
                } else {
                    mHolder.new_rl_other.setVisibility(View.GONE);
                }

//                String sTitle = mHolder.mItem.getTitle();
                String sDateTime = mHolder.mItem.getTitle();
                String sType = mHolder.mItem.getType()+"";
                String sTitle = ReportUtil.getFormateReportTitle(sDateTime, sType,1);
                if (Validator.isNotEmpty(sTitle)) {
                    mHolder.report_use_time_title.setText(sTitle);
                }

                return;
            }
//            float currentValues = Float.parseFloat(mHolder.mItem.getProportion());
            float currentValues = 0.0f;
            if (Validator.isNotEmpty(mHolder.mItem.getProportion())) {
                currentValues = Float.parseFloat(mHolder.mItem.getProportion());
            }
            String pushTime = mHolder.mItem.getPushTime();
            if (Validator.isNotEmpty(pushTime)) {
                mHolder.reportDateTv.setText(pushTime.substring(0, pushTime.length() - 3));
            }
            String beginTime = mHolder.mItem.getBeginTime();
            String endTime = mHolder.mItem.getEndTime();
            if (Validator.isNotEmpty(beginTime)) {
                mHolder.reportUseTime.setText(String.format(mActivity.getString(R.string.use_time), beginTime.substring(11, 16), endTime.substring(11, 16)));
            }
            if (Validator.isNotEmpty(endTime)) {
                drawChart(mHolder.pieChart, mHolder.mItem);

                mHolder.reportUseTimeMain.setText(String.format(mActivity.getString(R.string.use_time_main), mHolder.mItem.getUsingDuration() + ""));
                mHolder.reportUseTimeSub.setText(String.format(mActivity.getString(R.string.use_time_sub), mHolder.mItem.getRightProportion() + "%",
//                        mHolder.mItem.getModerateProportion() + "%",
//                        mHolder.mItem.getMildProportion() + "%",
                        mHolder.mItem.getMildProportion() + "%",
                        mHolder.mItem.getModerateProportion() + "%",
                        mHolder.mItem.getSeriousProportion() + "%"));

                mHolder.tvRight.setText(mHolder.mItem.getRightProportion() + "%");
//                mHolder.tvMicroError.setText(mHolder.mItem.getModerateProportion() + "%");
//                mHolder.tvError.setText(mHolder.mItem.getMildProportion() + "%");
                mHolder.tvMicroError.setText(mHolder.mItem.getMildProportion() + "%");
                mHolder.tvError.setText(mHolder.mItem.getModerateProportion() + "%");
                mHolder.tvSeriousError.setText(mHolder.mItem.getSeriousProportion() + "%");
            }
            if (mHolder.mItem.getHaveRead() == 0) {
                mHolder.newRl.setVisibility(View.VISIBLE);
            } else {
                mHolder.newRl.setVisibility(View.GONE);
            }
            if (currentValues <= 33.3F) {
                mHolder.reportPrompt.setText(mActivity.getString(R.string.sit_low_prompt));
            } else if (currentValues > 33.3F && currentValues <= 66.6F) {
                mHolder.reportPrompt.setText(mActivity.getString(R.string.sit_middle_prompt));
            } else {
                mHolder.reportPrompt.setText(mActivity.getString(R.string.sit_high_prompt));
            }

        }
    }


    @Override
    public int getItemViewType(int position) {
        return ITEM_TYPE_NORMA;
    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }

    private class NormalViewHolder extends ViewHolder {
        public final View mView;
        SPReportEntity mItem;
        TextView reportDateTv;
        TextView newReportTv;
        TextView reportUseTime;
        TextView reportUseTimeMain;
        TextView reportUseTimeSub;
        TextView reportPrompt;
        RelativeLayout newRl, new_rl_other;
        TextView tvRight, tvMicroError, tvError, tvSeriousError;
        PieChart pieChart;
        LinearLayout report_main_ll;
        RelativeLayout report_other_rl;
        TextView report_use_time_title;


        NormalViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            newRl = (RelativeLayout) itemView.findViewById(R.id.new_rl);
            new_rl_other = (RelativeLayout) itemView.findViewById(R.id.new_rl_other);
            reportDateTv = (TextView) itemView.findViewById(R.id.report_date_tv);
            newReportTv = (TextView) itemView.findViewById(R.id.new_report_tv);
            reportUseTime = (TextView) itemView.findViewById(R.id.report_use_time);
            reportUseTimeMain = (TextView) itemView.findViewById(R.id.report_use_time_main);
            reportUseTimeSub = (TextView) itemView.findViewById(R.id.report_use_time_sub);
            reportPrompt = (TextView) itemView.findViewById(R.id.report_prompt);

            report_main_ll = (LinearLayout) itemView.findViewById(R.id.report_main_ll);
            report_other_rl = (RelativeLayout) itemView.findViewById(R.id.report_other_rl);

            tvRight = itemView.findViewById(R.id.tv_right);
            tvMicroError = itemView.findViewById(R.id.tv_micro_error);
            tvError = itemView.findViewById(R.id.tv_error);
            tvSeriousError = itemView.findViewById(R.id.tv_serious_error);
            pieChart = itemView.findViewById(R.id.pie_chart);

            report_use_time_title = itemView.findViewById(R.id.report_use_time_title);

            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //可以选择直接在本位置直接写业务处理
                    //Toast.makeText(context,"点击了xxx",Toast.LENGTH_SHORT).show();
                    //此处回传点击监听事件
                    if(mOnItemClickListener != null){
                        mOnItemClickListener.onItemClick(v, datas.get(getLayoutPosition()));
                    }
                }
            });

        }
    }


    private onItemClickListener mOnItemClickListener;

    public void setOnItemClickListener(onItemClickListener onItemClickListener) {
        this.mOnItemClickListener = onItemClickListener;
    }

    public interface onItemClickListener {
        void onItemClick(View view, SPReportEntity SPReportEntity);
    }


    private void drawChart(PieChart pieChart, SPReportEntity entity) {
        pieChart.setUsePercentValues(true);
        pieChart.getDescription().setEnabled(false);
        pieChart.setDrawHoleEnabled(true);
        pieChart.setHoleColor(Color.WHITE);
        pieChart.setHoleRadius(60);
        pieChart.setDrawCenterText(false);
        pieChart.setDrawEntryLabels(false);
        pieChart.setRotationAngle(-90);
        pieChart.getLegend().setEnabled(false);
        pieChart.setRotationEnabled(false);
        pieChart.setHighlightPerTapEnabled(false);
        pieChart.setExtraOffsets(0, 0, 0, 0);
        ArrayList<PieEntry> entries = new ArrayList<>();
        entries.add(new PieEntry(Float.parseFloat(entity.getRightProportion())));
        entries.add(new PieEntry(Float.parseFloat(entity.getMildProportion())));
        entries.add(new PieEntry(Float.parseFloat(entity.getModerateProportion())));
        entries.add(new PieEntry(Float.parseFloat(entity.getSeriousProportion())));

        PieDataSet dataSet = new PieDataSet(entries, "");
        dataSet.setColors(mActivity.getResources().getColor(R.color.green_8ae5b1),
                mActivity.getResources().getColor(R.color.yellow_faec94),
                mActivity.getResources().getColor(R.color.orange_fbb779),
                mActivity.getResources().getColor(R.color.red_f07575));
        dataSet.setDrawValues(false);
        dataSet.setDrawIcons(false);

        PieData data = new PieData(dataSet);
        data.setDrawValues(false);
        data.setHighlightEnabled(false);
        pieChart.setData(data);
        pieChart.invalidate();
    }

}
