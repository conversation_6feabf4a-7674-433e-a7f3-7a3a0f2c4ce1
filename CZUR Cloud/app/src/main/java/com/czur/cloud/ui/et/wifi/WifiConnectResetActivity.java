package com.czur.cloud.ui.et.wifi;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.ui.base.BaseActivity;
import com.czur.cloud.ui.et.EtManageActivity;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class WifiConnectResetActivity extends BaseActivity implements View.OnClickListener {
    private ImageView normalBackBtn;
    private TextView wifiResetNextStepBtn;

    private String ssid, password, deviceId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_wifi_reset);
        EventBus.getDefault().register(this);
        initComponent();
        registerEvent();
    }

    private void initComponent() {
        deviceId = getIntent().getStringExtra("deviceId");
        password = getIntent().getStringExtra("password");
        ssid = getIntent().getStringExtra("ssid");

        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        wifiResetNextStepBtn = (TextView) findViewById(R.id.wifi_reset_next_step_btn);
    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        wifiResetNextStepBtn.setOnClickListener(this);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEvent(BaseEvent event) {
        switch (event.getEventType()) {
            case DOWNLOAD_MP3_FAILED:
                showMessage(R.string.download_mp3_failed);
                finish();
                break;
            default:
                break;
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.wifi_reset_next_step_btn:
                Intent intent = new Intent(this, WifiConnectActivity.class);
                intent.putExtra("ssid", ssid);
                intent.putExtra("password", password);
                intent.putExtra("deviceId", deviceId);
                startActivity(intent);
                break;
            case R.id.normal_back_btn:
                ActivityUtils.finishToActivity(EtManageActivity.class, false);
                break;
            default:
                break;
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        ActivityUtils.finishToActivity(EtManageActivity.class, false);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        removeStickyEvent();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}
