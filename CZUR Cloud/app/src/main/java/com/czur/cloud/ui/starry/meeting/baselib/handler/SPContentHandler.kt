package com.czur.cloud.ui.starry.meeting.baselib.handler

import android.content.ContentValues
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.czur.cloud.ui.starry.meeting.baselib.utils.*
import com.czur.czurutils.log.logI
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2021/8/4
 * 获取/设置简单数据类型的Handler
 * @see SPContentProvider
 */
abstract class SPContentHandler {
    companion object {
    }

    // 协程使用
    private val job: Job = Job()
    private val scope = CoroutineScope(job)

    // liveData与对应Key值的map, 在数据更新后, 自动更新数据
    abstract val keyLiveMap: Map<String, LiveTrans<*>>

    // sp值的缓存
    protected val spValueCache = hashMapOf<String, String>()

    /**
     * 设置值
     * @param name  值的key
     * @param value 具体的值 只支持基本数据类型
     */
    protected inline fun <reified T> setValue(
        name: String,
        value: T,
    ) {
        updateBatch(name to value)
    }

    protected inline fun <reified T> getCacheValue(name: String): T? {
        val cacheValue = spValueCache[name]
        cacheValue?.let {
            return when (T::class.java) {
                String::class.java, java.lang.String::class.java -> {
                    it as T
                }
                Integer::class.java, java.lang.Integer::class.java -> {
                    it.toInt() as T
                }
                Long::class.java, java.lang.Long::class.java -> {
                    it.toLong() as T
                }
                Float::class.java, java.lang.Float::class.java -> {
                    it.toFloat() as T
                }
                Double::class.java, java.lang.Double::class.java -> {
                    it.toDouble() as T
                }
                Boolean::class.java, java.lang.Boolean::class.java -> {
                    it.toBoolean() as T
                }
                else -> null
            }
        }
        return null
    }

    /**
     * 获取值
     * @param name          值的key
     * @param defaultValue  如果没有值时的默认值
     */
    protected inline fun <reified T> getValue(
        name: String,
        defaultValue: T
    ): T {
//        logV(javaClass.simpleName, "获取值:${name}")
        val cacheValue = getCacheValue<T>(name)
        if (cacheValue != null) {
//            logV(javaClass.simpleName, "从缓存重获取数据")
            return cacheValue
        }

        return defaultValue
    }

    /**
     * 初始化LiveData
     * @param initLive  更新初始值的方法,该方法会异步执行
     */
    protected fun <T> createLive(initLive: () -> T): LiveData<T> {
        val live = DifferentLiveData<T>()
        scope.launch {
            live.postValue(initLive())
        }
        return live
    }

    /**
     * 批量更新
     */
    protected inline fun <reified T> updateBatch(vararg updatePair: Pair<String, T>) {
        val cv = ContentValues()
        updatePair.forEach { (name, value) ->
            when (value) {
                is String -> cv.put(name, value)
                is Int -> cv.put(name, value)
                is Long -> cv.put(name, value)
                is Float -> cv.put(name, value)
                is Double -> cv.put(name, value)
                is Boolean -> cv.put(name, value)
                else -> {
                    val valueType = T::class.java.simpleName
                    logI("不支持的数据类型:$valueType (key:$name)")
                }
            }
            // 直接更新本地缓存
            spValueCache[name] = value.toString()
        }
    }

    /**
     * 将String类型的值转换为对应类型的值
     */
    class LiveTrans<T>(private val liveData: MutableLiveData<T>, private val trans: (String) -> T) {
        fun update(newData: String) {
            liveData.value = trans(newData)
        }
    }
}