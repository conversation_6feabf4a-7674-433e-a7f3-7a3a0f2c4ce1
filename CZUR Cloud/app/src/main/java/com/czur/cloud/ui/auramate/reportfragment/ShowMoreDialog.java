package com.czur.cloud.ui.auramate.reportfragment;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import com.czur.cloud.R;

public class ShowMoreDialog extends Dialog implements View.OnClickListener {

    private TextView experienceNow;
    Context context;
    View registerView;

    public ShowMoreDialog(Context context) {
        super(context);
        this.context = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
// 这句代码换掉dialog默认背景，否则dialog的边缘发虚透明而且很宽
        // 总之达不到想要的效果
        getWindow().setBackgroundDrawableResource(android.R.color.transparent);
        registerView = View.inflate(this.context, R.layout.layout_auramate_show_more_dialog, null);
        setContentView(registerView);
        // 这句话起全屏的作用
        getWindow().setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT);
        initView();
        initListener();
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
//        this.dismiss();  如果加上这个就是点击哪里，都要进行消失
        return super.onTouchEvent(event);
    }

    private void initListener() {
        experienceNow.setOnClickListener(this);
    }

    private void initView() {
        experienceNow = (TextView) findViewById(R.id.ib_known);
        experienceNow.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ib_known:
                this.dismiss();
                break;
        }

    }
}