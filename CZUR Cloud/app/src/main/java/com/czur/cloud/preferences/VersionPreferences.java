package com.czur.cloud.preferences;

import android.content.Context;
import android.content.SharedPreferences;

import com.czur.cloud.entity.VersionInfoEntity;

import java.util.concurrent.atomic.AtomicReference;

/**
 * Created by 陈丰尧 on 2022/11/29
 */
public class VersionPreferences extends BasePreferences {
    private static final String PREF_NAME = "VersionPreferences";
    private static final AtomicReference<VersionPreferences> INSTANCE = new AtomicReference<>();

    private static final String PREF_KEY_DOWNLOAD_ID = "downloadId";
    private static final String PREF_KEY_DOWNLOAD_APK_NAME = "apkName";

    private static final String PREF_KEY_DOWNLOAD_REAL_APK_NAME = "realApkName";

    private static final String PREF_KEY_REMOTE_VERSION_CODE = "rVersionCode";
    private static final String PREF_KEY_REMOTE_VERSION_NAME = "rVersionName";
    private static final String PREF_KEY_REMOTE_PKG_URL = "rPkgUrl";
    private static final String PREF_KEY_REMOTE_NOTES = "rNotes";

    private VersionPreferences(Context context) {
        super(context, PREF_NAME);
    }

    public static VersionPreferences init(Context context) {
        for (; ; ) {
            VersionPreferences current = INSTANCE.get();
            if (current != null) {
                return current;
            }
            current = new VersionPreferences(context.getApplicationContext());
            if (INSTANCE.compareAndSet(null, current)) {
                return current;
            }
        }
    }

    public static VersionPreferences getInstance() {
        if (INSTANCE.get() == null) {
            throw new RuntimeException("还未初始化, 请在Application中初始化");
        }
        return INSTANCE.get();
    }

    public void saveDownloadInfo(long downloadId, String apkName, String realApkName) {
        put(PREF_KEY_DOWNLOAD_ID, downloadId);
        put(PREF_KEY_DOWNLOAD_APK_NAME, apkName);
        put(PREF_KEY_DOWNLOAD_REAL_APK_NAME, realApkName);
    }


    public long getDownloadId() {
        return (long) get(PREF_KEY_DOWNLOAD_ID, -1L);
    }

    public String getApkName() {
        return get(PREF_KEY_DOWNLOAD_APK_NAME, "").toString();
    }

    public String getRealApkName() {
        return get(PREF_KEY_DOWNLOAD_REAL_APK_NAME, "").toString();
    }

    public void saveRemoteVersionEntity(VersionInfoEntity remoteVersionInfo) {
        put(PREF_KEY_REMOTE_VERSION_CODE, remoteVersionInfo.getVersionCode());
        put(PREF_KEY_REMOTE_VERSION_NAME, remoteVersionInfo.getVersionName());
        put(PREF_KEY_REMOTE_PKG_URL, remoteVersionInfo.getPackageUrl());
        put(PREF_KEY_REMOTE_NOTES, remoteVersionInfo.getNotes());
    }

    public VersionInfoEntity getSavedRemoteVersionEntity() {
        SharedPreferences sp = getSharedPreferences();
        int versionCode = sp.getInt(PREF_KEY_REMOTE_VERSION_CODE, -1);
        if (versionCode < 0) return null;

        String versionName = sp.getString(PREF_KEY_REMOTE_VERSION_NAME, "");
        String url = sp.getString(PREF_KEY_REMOTE_PKG_URL, "");
        String notes = sp.getString(PREF_KEY_REMOTE_NOTES, "");
        return new VersionInfoEntity(versionCode, versionName, url, notes);
    }
}
