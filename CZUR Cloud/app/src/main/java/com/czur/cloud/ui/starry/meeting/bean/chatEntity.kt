package com.czur.cloud.ui.starry.meeting.bean

import com.czur.cloud.ui.starry.meeting.bean.vo.Member
import com.czur.cloud.ui.starry.meeting.bean.vo.MemberInfo
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created by 陈丰尧 on 2021/5/13
 */
private val sdf = SimpleDateFormat("HH:mm", Locale.ROOT)
const val FROM_ID_SYSTEM = "000"

abstract class ChatMsg(
    val fromCzurID: String,     // 消息来源
    var read: Boolean = false   // 是否已读
) {
    // 消息的ID
    val id: String = UUID.randomUUID().toString()

    // 消息时间
    val time: Long = System.currentTimeMillis()

    // 消息状态
    var status: ChatSendMsgStatus = ChatSendMsgStatus.SUCCESS

    // 是否是自己发的消息
    val isSelf: Boolean
        get() = fromCzurID == UserHandler.czurId.toString()

    val timeStr: String by lazy {
        sdf.format(time)
    }
}

abstract class ChatMemberInfoMsg(fromCzurID: String, read: Boolean = false) :
    ChatMsg(fromCzurID, read) {
    var nickName: String = fromCzurID
    var hasUpdate: Boolean = false
    var headImg: String? = null

    init {
        if (fromCzurID == UserHandler.czurId) {
            // 代表是自己发送的消息
            nickName = UserHandler.nickname
            headImg = UserHandler.headImage
            hasUpdate = true
        }else if (fromCzurID == FROM_ID_SYSTEM) {
            // 系统(自己发的)上线消息
            nickName = UserHandler.nickname
            headImg = UserHandler.headImage
            hasUpdate = true
        }
    }

    /**
     * 根据Member信息更新用户信息
     * @return: true: 数据有更新
     *          false: 数据没有更新
     */
    fun updateInfoByMember(member: MemberInfo?): Boolean {
        if (member == null) {
            return false
        }
        nickName = member.nickName
        headImg = member.headImg
        hasUpdate = true
        return true
    }
}


enum class ChatSendMsgStatus {
    SENDING,    // 发送中
    SUCCESS,    // 成功 接收的所有消息 状态都是成功
    FAIL        // 失败
}

// 用户状态消息
open class ChatActionMsg(fromCzurID: String, val action: ChatMemberMsgAction) :
    ChatMemberInfoMsg(fromCzurID)

/**
 * 自己上线的消息
 */
class SelfOnLineMsg : ChatActionMsg(
    FROM_ID_SYSTEM,
    ChatMemberMsgAction.ONLINE
)

// 文字类型消息
class ChatTextMsg(fromCzurID: String, val content: String) : ChatMemberInfoMsg(fromCzurID) {
    companion object {
        fun mkSend(content: String): ChatTextMsg {
            return ChatTextMsg(UserHandler.czurId.toString(), content).apply {
                status = ChatSendMsgStatus.SENDING
            }
        }
    }
}

// 时间消息
class ChatTimeMsg : ChatMsg("-1", true)

enum class ChatMemberMsgAction {
    ONLINE, // 上线
    OFFLINE,// 下线
    ADMIN,  // 成为了管理员
}
