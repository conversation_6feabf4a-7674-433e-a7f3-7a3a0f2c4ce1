<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/gary_f9"
    android:fitsSystemWindows="true"
    android:gravity="center_horizontal">


    <LinearLayout
        android:id="@+id/user_top_bar"
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:background="@color/gary_f9"
        android:layout_alignParentTop="true"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <ImageView

                android:id="@+id/add_book_back_btn"
                android:layout_width="29.5dp"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="9dp"
                android:padding="10dp"
                android:src="@mipmap/login_back_icon" />


            <com.czur.cloud.ui.component.MediumBoldTextView
                android:id="@+id/add_book_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/account_title"
                android:textSize="18sp" />

        </RelativeLayout>
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/add_book_rl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/user_top_bar"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="116.5dp"
        android:layout_marginTop="53dp">

        <RelativeLayout
            android:id="@+id/add_book_bg_rl"
            android:layout_width="226.5dp"
            android:layout_height="316dp"
            android:background="@mipmap/add_book_big_bg">

            <TextView
                android:id="@+id/add_book_name_tv"
                android:layout_width="112dp"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:ellipsize="end"
                android:gravity="center"
                android:lines="3"
                android:textColor="@color/white"
                android:textSize="28sp" />

        </RelativeLayout>

        <ImageView
            android:layout_width="20dp"
            android:layout_height="316dp"
            android:layout_toRightOf="@+id/add_book_bg_rl"
            android:background="@mipmap/add_book_big_bg_land" />

    </RelativeLayout>


    <LinearLayout
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:id="@+id/change_keyboard_ll"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="30dp"
        android:orientation="vertical">

        <com.czur.cloud.ui.component.NoHintEditText
            android:id="@+id/add_book_edt"
            android:layout_width="250dp"
            android:layout_height="40dp"
            android:layout_above="@+id/add_book_btn"
            android:layout_marginBottom="27.5dp"
            android:background="@drawable/btn_rec_5_bg_with_gray_ef"
            android:gravity="center"
            android:hint="@string/book_default_name"
            android:maxLines="1"
            android:maxLength="20"
            android:textColor="@color/black_22"
            android:textColorHint="@color/gary_c4"
            android:textSize="15sp"
            android:textStyle="bold" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/add_book_btn"
            android:layout_width="250dp"
            android:layout_height="40dp"
            android:layout_alignParentBottom="true"
            android:background="@drawable/selector_register_btn"
            android:clickable="false"
            android:gravity="center"
            android:text="@string/confirm_text"
            android:textColor="@color/white"
            android:textSize="15sp" />

    </LinearLayout>


</RelativeLayout>

