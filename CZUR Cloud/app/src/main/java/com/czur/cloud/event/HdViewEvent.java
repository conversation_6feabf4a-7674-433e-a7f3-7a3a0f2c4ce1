package com.czur.cloud.event;

public class HdViewEvent extends BaseEvent {


	private String deviceId;
	private String hdViewType;
	private String oss_bucket;
	private String oss_key;
	public HdViewEvent(EventType eventType, String deviceId, String hdViewType,String oss_key,String oss_bucket) {
		super(eventType);
		this.deviceId=deviceId;
		this.hdViewType=hdViewType;
		this.oss_bucket=oss_bucket;
		this.oss_key=oss_key;
	}
	public String getDeviceId() {
		return deviceId;
	}

	public String getHdViewType() {
		return hdViewType;
	}

	public String getOss_bucket() {
		return oss_bucket;
	}

	public String getOss_key() {
		return oss_key;
	}

	@Override
	public boolean match(Object obj) {
		return true;
	}
}
