package com.czur.cloud.model;

public class AuraMateShareUserModel {


    /**
     * id : 16
     * equipmentUid : 1111111111
     * ownerId : 2771
     * memberId : 2771
     * deviceName : AB
     * owned : true
     * memberName : 18123456789
     */

    private int id;
    private String equipmentUid;
    private int ownerId;
    private int memberId;
    private String deviceName;
    private boolean owned;
    private String memberName;
    private String memberPhoto;
    private boolean isSelect;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getEquipmentUid() {
        return equipmentUid;
    }

    public void setEquipmentUid(String equipmentUid) {
        this.equipmentUid = equipmentUid;
    }

    public int getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(int ownerId) {
        this.ownerId = ownerId;
    }

    public int getMemberId() {
        return memberId;
    }

    public void setMemberId(int memberId) {
        this.memberId = memberId;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public boolean isOwned() {
        return owned;
    }

    public void setOwned(boolean owned) {
        this.owned = owned;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getMemberPhoto() {
        return memberPhoto;
    }

    public void setMemberPhoto(String memberPhoto) {
        this.memberPhoto = memberPhoto;
    }
    public boolean isSelect() {
        return isSelect;
    }

    public void setSelect(boolean select) {
        isSelect = select;
    }
}
