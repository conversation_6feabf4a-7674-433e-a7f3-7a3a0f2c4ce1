package com.czur.cloud.ui.starry.meeting.dialog

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.RelativeLayout
import android.widget.TextView
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.ScreenUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.model.CountryCode
import com.czur.cloud.ui.account.ChoseCountryListPopupWindow
import com.czur.cloud.ui.starry.meeting.baselib.utils.getString
import com.czur.cloud.util.validator.StringUtils
import java.util.*

/**
 * 通用弹出dialog
 */
class RemindChoseCountryPopup : Dialog {
    val isChecked = false

    constructor(context: Context?, theme: Int) : super(context!!, theme) {}

    class Builder(private val context: Context) {
        private var message: String? = null
        private var title: String? = null
        private var btnPositiveTitle: String? = null
        private var positiveTextColor: Int? = null  // 文字颜色
        private var negativeTextColor: Int? = null // 消极的文字颜色
        private var btnNegativeTitle: String? = null
        private var positiveListener: DialogInterface.OnClickListener? = null
        private var onNegativeListener: DialogInterface.OnClickListener? = null
        private var onDismissListener: DialogInterface.OnDismissListener? = null
        private var canceledOnTouchOutside: Boolean = false
        private var textViewContentGravity: Int = -999
        private var countryCodeList: ArrayList<CountryCode> = arrayListOf()
        private var selectCountryCode : CountryCode = CountryCode()
        private var selectCountryPosition : Int = -1 //数字没有意义, 只是记录是否有手动选择的操作 -1为没有手动选择过
        fun setMessage(message: String?): Builder {
            this.message = message
            return this
        }

        fun setTitle(title: String?): Builder {
            this.title = title
            return this
        }

        fun setPositiveTitle(title: String?): Builder {
            this.btnPositiveTitle = title
            return this
        }

        fun setPositiveTextColor(color: Int): Builder {
            this.positiveTextColor = color
            return this
        }

        fun setNegativeTitle(title: String?): Builder {
            this.btnNegativeTitle = title
            return this
        }

        fun setNegativeTextColor(color: Int): Builder {
            negativeTextColor = color
            return this
        }

        fun setOnPositiveListener(positiveListener: DialogInterface.OnClickListener?): Builder {
            this.positiveListener = positiveListener
            return this
        }

        fun setOnNegativeListener(onNegativeListener: DialogInterface.OnClickListener?): Builder {
            this.onNegativeListener = onNegativeListener
            return this
        }

        fun setOnDismissListener(onDismissListener: DialogInterface.OnDismissListener?): Builder {
            this.onDismissListener = onDismissListener
            return this
        }

        fun setCanceledOnTouchOutside(canceledOnTouchOutside: Boolean): Builder {
            this.canceledOnTouchOutside = canceledOnTouchOutside
            return this
        }

        fun setTextContentGravity(textViewContentGravity: Int): Builder {
            this.textViewContentGravity = textViewContentGravity
            return this
        }

        fun setCountryList(countryCodeList: ArrayList<CountryCode>) : Builder{
            this.countryCodeList = countryCodeList
            return this
        }

        fun create(): RemindChoseCountryPopup {
            val inflater =
                context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
            val dialog = RemindChoseCountryPopup(context, R.style.TransparentProgressDialog)
            val layout = commonCustomPopLayout(inflater, dialog, countryCodeList)
            dialog.setContentView(layout)
            dialog.setCanceledOnTouchOutside(canceledOnTouchOutside)
            dialog.setCancelable(false)
            val params = dialog.window?.attributes
            params?.dimAmount = DIMMED_OPACITY
            if (ScreenUtils.isLandscape()) {//针对高版本(小米12) 横屏状态时,弹出提示窗时,无法拉下状态栏
                dialog.window?.setFlags(
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
                )
            }
            return dialog
        }

        private fun commonCustomPopLayout(
            inflater: LayoutInflater,
            dialog: RemindChoseCountryPopup,
            countryCodeList: ArrayList<CountryCode>
        ): View {
            val lp = dialog.window?.attributes
            lp?.dimAmount = 0.8f
            dialog.window?.attributes = lp
            dialog.window?.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            val layout = inflater.inflate(R.layout.starry_chose_country_popup, null, false)
            val params = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            dialog.addContentView(layout, params)
            val title = layout.findViewById<View>(R.id.title) as TextView
            val message = layout.findViewById<View>(R.id.message) as TextView
            val positiveBtn = layout.findViewById<View>(R.id.positive_button) as TextView
            val choseCountryTv = layout.findViewById<View>(R.id.chosen_country_tv) as TextView
            val backgroundRl = layout.findViewById<View>(R.id.background_rl) as RelativeLayout

            if (textViewContentGravity != -999) {
                message.gravity = textViewContentGravity
            }
            if (StringUtils.isNotEmpty(this.message)) {
                message.text = this.message + StringUtils.EMPTY
            } else {
                message.text = StringUtils.EMPTY
            }
            if (StringUtils.isNotEmpty(this.title)) {
                title.text = this.title + StringUtils.EMPTY
            } else {
                title.text = StringUtils.EMPTY
            }
            if (StringUtils.isNotEmpty(this.btnPositiveTitle)) {
                positiveBtn.text = this.btnPositiveTitle + StringUtils.EMPTY
            } else {
                positiveBtn.text = StringUtils.EMPTY
            }


            // 设置字体颜色
            positiveTextColor?.let {
                positiveBtn.setTextColor(it)
            }


            if (positiveListener != null) {
                positiveBtn.setOnClickListener {
                    positiveListener?.onClick(
                        dialog,
                        selectCountryPosition
                    )
                }
            } else {
                positiveBtn.setOnClickListener { dialog.dismiss() }
            }

            if (selectCountryCode.countryCode.isEmpty()) {//设置默认选择国家
                for ((index,value) in countryCodeList.withIndex()) {
                    if (value.defaultCountry) {
                        selectCountryPosition = index
                        selectCountryCode = value
                        break
                    }
                }
                choseCountryTv.text = getCountryName(selectCountryCode)
            }

            choseCountryTv.setOnClickListener {
                showCountryListPopup(context, choseCountryTv, countryCodeList)
            }

            if (onDismissListener != null) {
                dialog.setOnDismissListener(onDismissListener)
            }
            return layout
        }


        fun showCountryListPopup(
            context: Context,
            view: TextView,
            countryCodeList: ArrayList<CountryCode>
        ) {
            val layoutInflater =
                context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater

            val choseCountryPopupWindow = ChoseCountryListPopupWindow(
                layoutInflater, view.getWidth(), 800, countryCodeList
            )
            choseCountryPopupWindow.setChoseListener { countryCode: CountryCode?,position: Int ->

                if (countryCode != null) {
                    selectCountryCode = countryCode
                    selectCountryPosition = position
                }
                view.text = countryCode?.let { getCountryName(it) }
            }

            if (selectCountryCode.countryCode.isEmpty()){
                val countryCodeListFilter= countryCodeList.filter {
                    it.defaultCountry
                }

                if (countryCodeListFilter.isNotEmpty()){
                    selectCountryCode = countryCodeListFilter[0]
                }
            }

            choseCountryPopupWindow.setSelectedCountry(selectCountryCode)
            choseCountryPopupWindow.showDropDown(view)
        }

        private fun getCountryName(countryCode: CountryCode): String {
            return if (BuildConfig.IS_OVERSEAS) { //海外版
                // 海外版本，繁体中文标记为：countryNameTw，其它均为Us；
                // 无需安装系统语言判断
                val countryName = getString(R.string.countryCode)
                // 繁体中文
                if (countryName == "countryNameTw") {
                    countryCode.countryNameTw
                } else {
                    countryCode.countryNameUs
                }
            } else { //中国版本
                countryCode.countryName
            }
        }
    }

    companion object {
        const val DIMMED_OPACITY = 0.2f
    }



}