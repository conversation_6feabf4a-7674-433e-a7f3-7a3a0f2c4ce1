package com.czur.cloud.ui.starry.meeting.fragment

import android.content.Context
import android.content.res.Configuration
import android.graphics.Rect
import android.text.Editable
import android.text.TextWatcher
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.KeyboardUtils
import com.czur.cloud.R
import com.czur.cloud.ui.starry.meeting.adapter.ChatAdapter
import com.czur.cloud.ui.starry.meeting.base.FloatFragment
import com.czur.cloud.ui.starry.meeting.baselib.utils.InputHandleUtil
import com.czur.cloud.ui.starry.meeting.viewmodel.ChatViewModel
import com.czur.cloud.ui.starry.meeting.viewmodel.ControlBarViewModel
import com.czur.cloud.ui.starry.meeting.viewmodel.MeetingViewModel
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.czurutils.log.logI
import kotlinx.android.synthetic.main.starry_fragment_chat_float.*
import kotlinx.android.synthetic.main.starry_fragment_members_list_float.btnCloseIv

typealias OnBackPressedTypeAlias = () -> Unit

/**
 * 解决 Fragment 中 OnBackPressed 事件, 默认结束当前Fragment依附的Activity
 * @param type true:结束当前Activity，false：响应callback回调
 */
fun MeetingChartFragment.setOnHandleBackPressed(
        type: Boolean = true,
        callback: OnBackPressedTypeAlias? = null
) {
    requireActivity().onBackPressedDispatcher.addCallback(
            this,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    if (type) {
//                requireActivity().finish()
                    } else {
                        callback?.invoke()
                    }
                }
            })
}

class MeetingChartFragment : FloatFragment() {
    override fun getLayoutId() = R.layout.starry_fragment_chat_float

    private val meetingVM: MeetingViewModel by viewModels({ requireActivity() })
    private val chatVM: ChatViewModel by viewModels({ requireActivity() })
    private val controlBarVM: ControlBarViewModel by viewModels({ requireActivity() })
    private val adapter = ChatAdapter()

    override fun onStart() {
        super.onStart()
        // 弹出带输入框的对话框，弹起软键盘，背景抬起。
        requireActivity().window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
    }

    override fun onStop() {
        super.onStop()
        // 弹出带输入框的对话框，弹起软键盘，背景不抬起。
        requireActivity().window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
    }

    override fun initView() {
        super.initView()

        Tools.setViewButtonEnable(btn_send_tv, false);

        // 关闭
        btnCloseIv.setOnClickListener {
            logI("MeetingChartFragment.btnCloseIv-关闭")
            dismiss()
            controlBarVM.chartFragment = null
        }

        btn_send_tv?.setOnClickListener {
            if (meetingVM.clickNoNetwork()) {
                return@setOnClickListener
            }
            val text = chatInputEt.text.toString()
            meetingVM.sendMsg(text)
            chatInputEt.setText("")
            logI("MeetingChartFragment.btn_send_tv.text=${text}")
        }

        chatRv.layoutManager = LinearLayoutManager(requireContext())
        chatRv.adapter = adapter

        setSoftInputVisibleListener()

        chatInputEt?.apply {
            isFocusable = true
            isFocusableInTouchMode = true
            requestFocus()
            findFocus()
            val inputManager: InputMethodManager = this.context
                    .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            inputManager.showSoftInput(this, 0)
        }

        chatInputEt.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun afterTextChanged(s: Editable?) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val text = s.toString().trim()
                if (text.isNotEmpty()) {
                    Tools.setViewButtonEnable(btn_send_tv, true);
                } else {
                    Tools.setViewButtonEnable(btn_send_tv, false);
                }
            }
        })


        if (resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            // 底部按钮跟随弹起
            InputHandleUtil().handleInputView(chat_main_cl)
        }
    }

    //软键盘弹起时文字滚动到顶部
    private fun setSoftInputVisibleListener() {
        KeyboardUtils.registerSoftInputChangedListener(activity!!.window, KeyboardUtils.OnSoftInputChangedListener { height: Int ->
            if (height > 0) {
                    rvScrollToBottom()
            }
        })
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        logI("MeetingChartFragment.onConfigurationChanged.newConfig=${newConfig}")
//        if ( resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE ) {
//            // 底部按钮跟随弹起
//            InputHandleUtil().handleInputView(chat_main_cl)
//        }

    }

    override fun initData() {
        super.initData()
        chatVM.chatMsg.observe(this) {

            // 判断recyclerView在添加数据之前是否还能继续向上滚动(手指向下滑)
            // 如果能,表示目前RecyclerView处于最下方
            // 则在添加数据之后, 自动更新RecyclerView的显示位置
            adapter.dataList = it
            rvScrollToBottom()
//            if (adapter.itemCount > 0) {
//                chatRv.scrollToPosition(adapter.itemCount - 1)
//                // 再向下滑动一点, 保证滑到底
//                chatRv.scrollBy(0, 1000)
//            }
        }
    }

    fun rvScrollToBottom() {
        if (adapter.itemCount > 0) {
            chatRv.scrollToPosition(adapter.itemCount - 1)
            // 再向下滑动一点, 保证滑到底
            chatRv.post {
                chatRv.scrollBy(0, 11000)
            }

        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        chatVM.chatFloatShow = true
    }

    override fun onDetach() {
        super.onDetach()
        chatVM.chatFloatShow = false
        KeyboardUtils.unregisterSoftInputChangedListener(activity!!.window)
    }

    override fun handleBackPressed(): Boolean {
        //处理自己的逻辑
        logI("MeetingChartFragment.handleBackPressed-关闭")
        dismiss()
        return true
    }

    private fun isSoftShowing(): Boolean {
        if (this.getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {//横屏
            //获取当屏幕内容的高度
            val screenHeight: Int = activity!!.getWindow().getDecorView().height
            //获取View可见区域的bottom
            val rect = Rect()
            //DecorView即为activity的顶级view
            activity!!.getWindow().getDecorView().getWindowVisibleDisplayFrame(rect)
            //考虑到虚拟导航栏的情况（虚拟导航栏情况下：screenHeight = rect.bottom + 虚拟导航栏高度）
            //选取screenHeight*2/3进行判断
            return screenHeight * 2 / 3 > rect.bottom
        } else {//竖屏
            //获取当屏幕内容的高度
            val screenHeight: Int = activity!!.getWindow().getDecorView().getHeight()
            //获取View可见区域的bottom
            val rect = Rect()
            //DecorView即为activity的顶级view
            activity!!.getWindow().getDecorView().getWindowVisibleDisplayFrame(rect)
            //考虑到虚拟导航栏的情况（虚拟导航栏情况下：screenHeight = rect.bottom + 虚拟导航栏高度）
            //选取screenHeight*2/3进行判断
            return screenHeight * 2 / 3 > rect.bottom
        }

    }
}
