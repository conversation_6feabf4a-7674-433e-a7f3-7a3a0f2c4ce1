package com.czur.cloud.ui.books.sync;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.alibaba.sdk.android.oss.ClientException;
import com.alibaba.sdk.android.oss.OSS;
import com.alibaba.sdk.android.oss.ServiceException;
import com.alibaba.sdk.android.oss.model.GetObjectRequest;
import com.alibaba.sdk.android.oss.model.GetObjectResult;
import com.alibaba.sdk.android.oss.model.ObjectMetadata;
import com.alibaba.sdk.android.oss.model.PutObjectRequest;
import com.alibaba.sdk.android.oss.model.PutObjectResult;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ServiceUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.common.OSSInstance;
import com.czur.cloud.entity.realm.BookEntity;
import com.czur.cloud.entity.realm.BookPdfEntity;
import com.czur.cloud.entity.realm.DownloadEntity;
import com.czur.cloud.entity.realm.PageEntity;
import com.czur.cloud.entity.realm.PdfDownloadEntity;
import com.czur.cloud.entity.realm.SyncBookEntity;
import com.czur.cloud.entity.realm.SyncEntity;
import com.czur.cloud.entity.realm.SyncPageEntity;
import com.czur.cloud.entity.realm.SyncPdfEntity;
import com.czur.cloud.entity.realm.SyncTagEntity;
import com.czur.cloud.entity.realm.TagEntity;
import com.czur.cloud.event.BaseEvent;
import com.czur.cloud.event.BooksOrPagesChangedEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.NoticeServiceEnoughStopEvent;
import com.czur.cloud.event.NoticeServiceIsStopEvent;
import com.czur.cloud.event.ResetTimeCountEvent;
import com.czur.cloud.event.StopSyncTimeCountEvent;
import com.czur.cloud.event.SyncFinishEvent;
import com.czur.cloud.event.SynchronizingEvent;
import com.czur.cloud.model.BaseModel;
import com.czur.cloud.model.FileSizeModel;
import com.czur.cloud.model.UserInfoModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.starry.livedatabus.BlankActivityForNotification;
import com.czur.cloud.util.Android16NotificationUtils;
import com.czur.cloud.util.Android16ServiceUtils;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.Gson;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;

import io.realm.Realm;
import io.realm.RealmResults;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class SyncService extends BaseService {
    public static final String TAG = SyncService.class.getName();
    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    private boolean needStop = false;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private List<SyncPageEntity> syncPageList;
    private List<SyncBookEntity> syncBookList;
    private List<SyncTagEntity> syncTagList;
    private List<SyncPdfEntity> syncPdfList;
    private String syncDir;
    private long beginTime;
    private String dirPath;
    private String dirPdfPath;
    private List<String> pdfIds;
    private List<String> pdfNames;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        initNotification();
        EventBus.getDefault().register(this);
    }

    /**
     * Activity中一启动Service之后，就会调用 onStartCommand()方法
     */
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        initNotification();
        initComponent();
        initThreadToSync();
        return START_STICKY;
    }

    private void initNotification() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            int messageId = 1;
            Intent intent = new Intent(getApplicationContext(), BlankActivityForNotification.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//            PendingIntent contentIntent = PendingIntent.getActivity(getApplicationContext(), messageId, intent, PendingIntent.FLAG_UPDATE_CURRENT);
            PendingIntent contentIntent;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S){
                contentIntent = PendingIntent.getActivity(getApplicationContext(), messageId, intent, PendingIntent.FLAG_IMMUTABLE);
            }else {
                contentIntent = PendingIntent.getActivity(getApplicationContext(), messageId, intent, PendingIntent.FLAG_UPDATE_CURRENT);
            }
            // 提高进程优先级 ，就会在通知栏中出现自己的应用，如果不想提高优先级，可以把这个注释
            // 参数1：id 参数2：通知
            String channelId = "com.czur.cloud";
            String channelName = getString(R.string.background);
            NotificationChannel notificationChannel = null;

            notificationChannel = new NotificationChannel(channelId,
                    channelName, NotificationManager.IMPORTANCE_LOW);
            notificationChannel.setShowBadge(false);
            notificationChannel.enableVibration(false);
            notificationChannel.setSound(null, null);
            notificationChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            NotificationManager manager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            manager.createNotificationChannel(notificationChannel);
            Notification notification = new NotificationCompat.Builder(this)
                    .setChannelId(channelId)
                    .setContentTitle(getString(R.string.aura_mate_title))
                    .setCategory(Notification.CATEGORY_CALL)
                    .setWhen(System.currentTimeMillis())
                    .setSmallIcon(R.mipmap.small_icon)
                    .setContentIntent(contentIntent)
                    .build();
            startForeground(messageId, notification);
        }
    }

    private void initComponent() {
        pdfIds = new ArrayList<>();
        pdfNames = new ArrayList<>();
        syncBookList = new ArrayList<>();
        syncPageList = new ArrayList<>();
        syncTagList = new ArrayList<>();
        syncPdfList = new ArrayList<>();

        httpManager = HttpManager.getInstance();
        userPreferences = UserPreferences.getInstance(this);

        syncDir = getFilesDir() + File.separator + CZURConstants.SYNC_PATH;
        dirPath = getFilesDir() + File.separator + userPreferences.getUserId() + CZURConstants.PAGE_PATH;
        dirPdfPath = getFilesDir() + File.separator + userPreferences.getUserId() + File.separator + CZURConstants.PDF_PATH;
    }

    /**
     * 初始化单线程线程池
     */
    private void initThreadToSync() {
        new Thread(this::syncTask).start();
    }

    /**
     * 同步流程
     */
    private void syncTask() {
        try (Realm realm = Realm.getDefaultInstance()) {
            logI("SyncService.syncTask().开始同步");
            EventBus.getDefault().postSticky(new SynchronizingEvent(EventType.IS_SYNCHRONIZING));
            EventBus.getDefault().post(new StopSyncTimeCountEvent(EventType.STOP_SYNC_TIME_COUNT));

//            logI("step1 获取服务器时间");
            String serverTime = getServerTime();
            beginTime = System.currentTimeMillis();
            if (Validator.isEmpty(serverTime)) {
                logE("获取服务器时间失败");
                syncFinish(beginTime);
                return;
            }

//            logI("step2 根据服务器时间去获得数据");
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
            SyncEntity serverSyncEntity;
            if (userPreferences.getSyncTime() == 0) {
                serverSyncEntity = getFileByTime("");
            } else {
                String lastSyncTime = formatter.format(new Date(userPreferences.getSyncTime()));
                serverSyncEntity = getFileByTime(lastSyncTime);
            }

            if (needStop) {
                syncStop(beginTime);
                return;
            }
            if (Validator.isEmpty(serverSyncEntity)) {
                syncFinish(beginTime);
                return;
            }

//            logI("step3 根据书和页两个List去构建下载List");
            List<SyncEntity.CzurBooksNotesBean> booksList = serverSyncEntity.getCzurBooksNotes();
            List<SyncEntity.CzurBooksPagesBean> pagesList = serverSyncEntity.getCzurBooksPages();
            List<SyncEntity.CzurBooksPageTagsBean> tagsList = serverSyncEntity.getCzurBooksPageTags();
            List<SyncEntity.CzurBooksPdfsBean> pdfsList = serverSyncEntity.getCzurBooksPdfs();
            List<DownloadEntity> downloadList = makeDownLoadList(pagesList, realm);
            List<PdfDownloadEntity> pdfDownloadList = makePdfDownLoadList(pdfsList, realm);
            if (needStop) {
                syncStop(beginTime);
                return;
            }

//            logI("step4 合并下载文件");
            mergeDownloadFile(downloadList, realm);
            mergePdfDownloadFile(pdfDownloadList, realm);
            if (needStop) {
                syncStop(beginTime);
                return;
            }

//            logI("step5 需要同步的数据添加至同步表中, 数据表中设置为不需要更新");
            copyToSyncTable(serverTime, realm);
            if (needStop) {
                syncStop(beginTime);
                return;
            }

//            logI("step6 需要同步的数据拷贝到data/file/sync 文件夹下");
            copyToSyncDir();
            copyPdfToSyncDir();
            if (needStop) {
                syncStop(beginTime);
                return;
            }

//            logI("step7 检查云存储剩余空间是否足够");
            getUserInfo();
            boolean isNotEnough = checkCloudIsEnough(realm);
            if (isNotEnough) {
                EventBus.getDefault().postSticky(new NoticeServiceEnoughStopEvent(EventType.SYNC_SPACE_IS_NOT_ENOUGH));
                syncStop(beginTime);
                return;
            }

            if (needStop) {
                syncStop(beginTime);
                return;
            }

//            logI("step8 下载并且处理文件");
            HashSet<String> fileNoExistSet = downloadAndProcessFile(realm);
            HashSet<String> pdfNoExistSet = downloadPdfFile(realm);
            boolean isAllPageDownload = isAllDownloadAndMakeSmallImg(realm);
            boolean isAllPdfDownload = isAllPdfDownload(realm);
//            logE("同步：下载完成");


//            logI("step9 检查并更新需要同步表数据");
            checkAndUpdateSyncData(booksList, tagsList, realm);
            if (needStop) {
                syncStop(beginTime);
                return;
            }

//            logI("step10 上传文件并且删除同步文件夹的文件");
            checkIsUploadedAndDeleteFilesInSyncDir(realm);
            boolean isAllPagesUpload = isAllPagesUpload(realm);
            boolean isAllPdfUpload = isAllPdfsUpload(realm);
            if (needStop) {
                syncStop(beginTime);
                return;
            }


            checkSyncPdf(pdfsList, realm);

//            logI(isAllPageDownload, isAllPdfDownload, isAllPagesUpload, isAllPdfUpload);
            if (isAllPageDownload && isAllPdfDownload && isAllPagesUpload && isAllPdfUpload) {
                SyncEntity syncEntity = transFormRealmListToSyncList(syncBookList, syncPageList, syncTagList, syncPdfList);

//                logI("step11 上传同步表中的数据并且删除同步表");
//                logE(new Gson().toJson(syncEntity));
                boolean isPush = syncDataToServer(syncEntity);
                if (!isPush) {
                    syncFinish(beginTime);
                    return;
                }
                try {
                    long saveTime = formatter.parse(serverTime).getTime();
                    userPreferences.setSyncTime(saveTime);
                } catch (ParseException e) {
                    logE(e.toString());
                    syncFinish(beginTime);
                    return;
                }
            } else {
                syncFinish(beginTime);
                return;
            }

            if (needStop) {
                syncStop(beginTime);
                return;
            }

//            logI("step12 合并数据前与推送数据对比检查");
            checkMergeData(booksList, pagesList, tagsList, pdfsList, fileNoExistSet, pdfNoExistSet, realm);
            clearSyncTables(realm);

//            logI("step13 合并books,pages,tags,pdfs数据");
            boolean hasDeleteBooks = mergeBook(booksList, realm);
            boolean hasDeletePages = mergePages(pagesList, realm);
            boolean hasDeleteTags = mergeTags(tagsList, realm);
            boolean hasDeletePdfs = mergePdf(pdfsList, realm);


//            logI("step13 转换星标数据并且为本地页表填充笔记本名字");
            transStarPageToTag(realm);
            fillNoteName(booksList, realm);
//            logI(hasDeleteBooks, hasDeletePages, hasDeleteTags);
            if (hasDeleteBooks || hasDeletePages || hasDeleteTags) {
                EventBus.getDefault().postSticky(new BooksOrPagesChangedEvent(EventType.BOOKS_OR_PAGES_CHANGED));
            }
            if (hasDeletePdfs) {
                EventBus.getDefault().postSticky(new BooksOrPagesChangedEvent(EventType.PDFS_CHANGED));
            }
            logI("SyncService.syncTask().同步完成");
            if (hasUpdateFiles(realm)) {
//                logI("同步过程中有数据更新，重新计时");
                if (ServiceUtils.isServiceRunning(AutoSyncTimeCountService.class)) {
                    EventBus.getDefault().post(new ResetTimeCountEvent(EventType.RESET_TIME_COUNT));
                } else {
                    Intent intent = new Intent(SyncService.this, AutoSyncTimeCountService.class);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        startForegroundService(intent);
                    } else {
                        startService(intent);
                    }
                }
            }
            syncFinish(beginTime);
        }

    }

    /**
     * 清空同步表
     */
    private void clearSyncTables(Realm realm) {
        realm.beginTransaction();
        realm.where(SyncBookEntity.class).findAll().deleteAllFromRealm();
        realm.where(SyncPageEntity.class).findAll().deleteAllFromRealm();
        realm.where(SyncTagEntity.class).findAll().deleteAllFromRealm();
        realm.where(SyncPdfEntity.class).findAll().deleteAllFromRealm();
        realm.commitTransaction();
    }

    /**
     * 是否有更新的页或书
     */
    private boolean hasUpdateFiles(Realm realm) {
        RealmResults<PageEntity> pageEntities = realm.where(PageEntity.class)
                .notEqualTo("isDirty", 0)
                .equalTo("isTemp", 0)
                .findAll();
        RealmResults<BookEntity> bookEntities = realm.where(BookEntity.class)
                .equalTo("isDirty", 1)
                .findAll();

        RealmResults<TagEntity> tagEntities = realm.where(TagEntity.class)
                .equalTo("isDirty", 1)
                .findAll();

        RealmResults<BookPdfEntity> pdfEntities = realm.where(BookPdfEntity.class)
                .equalTo("isDirty", 1)
                .findAll();
//        logI("hasUpdateFiles", pageEntities.size() > 0 || bookEntities.size() > 0 || tagEntities.size() > 0);
        return pageEntities.size() > 0 || bookEntities.size() > 0 || tagEntities.size() > 0 || pdfEntities.size() > 0;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(BaseEvent event) {
        if (event.getEventType() == EventType.STOP_SYNC) {
            needStop = true;
        }
    }

    /**
     * step1 获取服务器时间
     */
    private String getServerTime() {
        try {
            if (NetworkUtils.isConnected()) {
                MiaoHttpEntity<String> serverTimeEntity = httpManager.request().getServerTime(
                        userPreferences.getUserId(), String.class);
                if (serverTimeEntity == null)
                    return null;

                if (serverTimeEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                    String serverTime = serverTimeEntity.getBody();
                    return serverTime;
                } else {
                    return null;
                }
            } else {
                return null;
            }
        } catch (Exception e) {
            logE(e.toString());
            e.printStackTrace();
        }
        syncStop(beginTime);
        return null;
    }

    /**
     * step2 根据服务器时间去获得数据
     */
    private SyncEntity getFileByTime(String serverTime) {
        try {
            MiaoHttpEntity<SyncEntity> fileByTimeEntity = httpManager.request().getFileByTime(
                    userPreferences.getUserId(), serverTime, SyncEntity.class);

            if (fileByTimeEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return fileByTimeEntity.getBody();

            } else {
                return null;
            }
        } catch (Exception e) {
            logE("getFileByTime.e="+e.toString());
            e.printStackTrace();
        }
        syncStop(beginTime);
        return null;
    }

    /**
     * step3 根据书和页两个List去构建下载List
     */
    private List<DownloadEntity> makeDownLoadList(List<SyncEntity.CzurBooksPagesBean> pagesList, Realm realm) {
        List<DownloadEntity> downloadEntities = new ArrayList<>();
        for (SyncEntity.CzurBooksPagesBean pagesBean : pagesList) {
            PageEntity pageEntity = realm.where(PageEntity.class).equalTo("pageId", pagesBean.getId()).findFirst();

            //  本地数据已经发生更改已本地数据为主，增量数据丢弃继续遍历
            if (pageEntity != null && pageEntity.getIsDirty() != 0) {
                continue;
            }
            //  增量数据为已删除数据无需下载文件，增量数据丢弃继续遍历
            if (pagesBean.getIsDelete()) {
                continue;
            }
            //  文件移动、标签更改等非文件操作的数据无需下载文件，增量数据丢弃继续遍历
            if (pageEntity != null && pageEntity.getUuid().equals(pagesBean.getUuid())) {
                continue;
            }

            realm.beginTransaction();
            DownloadEntity downloadEntity = realm.createObject(DownloadEntity.class);
            downloadEntity.setFileId(pagesBean.getId());
            downloadEntity.setUuid(pagesBean.getUuid());
            realm.insertOrUpdate(downloadEntity);
            downloadEntities.add(downloadEntity);
            realm.commitTransaction();

        }

        return downloadEntities;
    }

    private List<PdfDownloadEntity> makePdfDownLoadList(List<SyncEntity.CzurBooksPdfsBean> pdfList, Realm realm) {
        List<PdfDownloadEntity> downloadEntities = new ArrayList<>();
        for (SyncEntity.CzurBooksPdfsBean pdfsBean : pdfList) {
            BookPdfEntity bookPdfEntity = realm.where(BookPdfEntity.class).equalTo("pdfId", pdfsBean.getId()).findFirst();

            //  本地数据已经发生更改已本地数据为主，增量数据丢弃继续遍历
            if (bookPdfEntity != null && bookPdfEntity.getIsDirty() != 0) {
                continue;
            }
            //  增量数据为已删除数据无需下载文件，增量数据丢弃继续遍历
            if (pdfsBean.getIsDelete()) {
                continue;
            }
            realm.beginTransaction();
            PdfDownloadEntity pdfDownloadEntity = realm.createObject(PdfDownloadEntity.class);
            pdfDownloadEntity.setPdfID(pdfsBean.getId());
            realm.insertOrUpdate(pdfDownloadEntity);
            downloadEntities.add(pdfDownloadEntity);
            realm.commitTransaction();
        }
        return downloadEntities;
    }

    /**
     * step4 合并下载文件
     */
    private void mergeDownloadFile(List<DownloadEntity> downloadList, Realm realm) {
        if (Validator.isNotEmpty(downloadList)) {
            for (DownloadEntity downloadEntity : downloadList) {
                String fileId = downloadEntity.getFileId();
                String uuid = downloadEntity.getUuid();
                DownloadEntity queryDownloadEntity = realm.where(DownloadEntity.class).equalTo("fileId", fileId).findFirst();
                if (Validator.isNotEmpty(queryDownloadEntity)) {
                    realm.beginTransaction();
                    queryDownloadEntity.deleteFromRealm();
                    realm.commitTransaction();
                }
                realm.beginTransaction();
                DownloadEntity newDownloadEntity = realm.createObject(DownloadEntity.class);
                newDownloadEntity.setFileId(fileId);
                newDownloadEntity.setUuid(uuid);
                newDownloadEntity.setHasDownloadImage(false);
                newDownloadEntity.setHasMakeSmallImage(false);
                realm.commitTransaction();
                if (needStop) {
                    return;
                }
            }
        }
    }

    private void mergePdfDownloadFile(List<PdfDownloadEntity> downloadList, Realm realm) {
        if (Validator.isNotEmpty(downloadList)) {
            for (PdfDownloadEntity downloadEntity : downloadList) {
                String pdfID = downloadEntity.getPdfID();

                PdfDownloadEntity queryDownloadEntity = realm.where(PdfDownloadEntity.class).equalTo("pdfID", pdfID).findFirst();
                if (Validator.isNotEmpty(queryDownloadEntity)) {
                    realm.beginTransaction();
                    queryDownloadEntity.deleteFromRealm();
                    realm.commitTransaction();
                }
                realm.beginTransaction();
                PdfDownloadEntity newDownloadEntity = realm.createObject(PdfDownloadEntity.class);
                newDownloadEntity.setPdfID(pdfID);
                realm.commitTransaction();
                if (needStop) {
                    return;
                }
            }
        }
    }

    /**
     * step5 需要同步的数据添加至同步表中, 数据表中设置为不需要更新
     */
    private void copyToSyncTable(String serverTime, Realm realm) {
        long firstTime = System.currentTimeMillis();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        String curDate = formatter.format(new Date(firstTime));

        //Books 移动并把数据表的数据isDirty 置为0
        RealmResults<BookEntity> localBookEntities = realm.where(BookEntity.class)
                .equalTo("isDirty", 1)
                .findAll();

        realm.beginTransaction();
        for (BookEntity localBookEntity : localBookEntities) {
            SyncBookEntity syncBookEntity = realm.createObject(SyncBookEntity.class);
            syncBookEntity.setBookId(localBookEntity.getBookId());
            syncBookEntity.setBookName(localBookEntity.getBookName());
            syncBookEntity.setCreateTime(localBookEntity.getCreateTime());
            syncBookEntity.setUpdateTime(localBookEntity.getUpdateTime());
            syncBookEntity.setIsDirty(localBookEntity.getIsDirty());
            syncBookEntity.setIsDelete(localBookEntity.getIsDelete());
            syncBookEntity.setSyncTime(serverTime);
            realm.insertOrUpdate(syncBookEntity);
            localBookEntity.setIsDirty(0);
        }
        realm.commitTransaction();
        RealmResults<SyncBookEntity> syncBooksEntities = realm.where(SyncBookEntity.class).findAll();
        syncBookList = realm.copyFromRealm(syncBooksEntities);

        //Pages 移动并把数据表的数据isDirty 置为0
        RealmResults<PageEntity> localPagesEntities = realm.where(PageEntity.class)
                .equalTo("isTemp", 0)
                .notEqualTo("isDirty", 0)
                .findAll();


        realm.beginTransaction();
        for (PageEntity localPagesEntity : localPagesEntities) {

            SyncPageEntity notUpdatePageEntity = realm.where(SyncPageEntity.class).equalTo("pageId", localPagesEntity.getPageId()).findFirst();
            if (notUpdatePageEntity != null) {
                notUpdatePageEntity.setPageId(localPagesEntity.getPageId());
                notUpdatePageEntity.setBookId(localPagesEntity.getBookId());
                notUpdatePageEntity.setNoteName(localPagesEntity.getNoteName());
                notUpdatePageEntity.setTagId(localPagesEntity.getTagId());
                notUpdatePageEntity.setTagName(localPagesEntity.getTagName());
                notUpdatePageEntity.setIsDirty(localPagesEntity.getIsDirty() == 1 ? 1 : 2);
                notUpdatePageEntity.setPageNum(localPagesEntity.getPageNum());
                if (localPagesEntity.getIsNewAdd() == 1) {
                    notUpdatePageEntity.setIsNewAdd(1);
                } else if (localPagesEntity.getIsNewAdd() == 0 && notUpdatePageEntity.getIsNewAdd() == 1) {
                    notUpdatePageEntity.setIsNewAdd(1);
                } else {
                    notUpdatePageEntity.setIsNewAdd(0);
                }
                notUpdatePageEntity.setIsNewAdd(localPagesEntity.getIsNewAdd());
                notUpdatePageEntity.setBucket(localPagesEntity.getBucket());
                notUpdatePageEntity.setCreateTime(localPagesEntity.getCreateTime());
                notUpdatePageEntity.setIsDelete(localPagesEntity.getIsDelete());
                notUpdatePageEntity.setIsStar(localPagesEntity.getIsStar());
                notUpdatePageEntity.setOcrContent(localPagesEntity.getOcrContent());
                notUpdatePageEntity.setPicUrl(localPagesEntity.getPicUrl());
                notUpdatePageEntity.setUpdateTime(localPagesEntity.getUpdateTime());
                notUpdatePageEntity.setUuid(localPagesEntity.getUuid());
                notUpdatePageEntity.setSyncTime(serverTime);
                notUpdatePageEntity.setFileSize(localPagesEntity.getFileSize());
            } else {
                SyncPageEntity syncPageEntity = realm.createObject(SyncPageEntity.class);
                syncPageEntity.setPageId(localPagesEntity.getPageId());
                syncPageEntity.setBookId(localPagesEntity.getBookId());
                syncPageEntity.setNoteName(localPagesEntity.getNoteName());
                syncPageEntity.setTagId(localPagesEntity.getTagId());
                syncPageEntity.setTagName(localPagesEntity.getTagName());
                syncPageEntity.setIsDirty(localPagesEntity.getIsDirty());
                syncPageEntity.setIsNewAdd(localPagesEntity.getIsNewAdd());
                syncPageEntity.setPageNum(localPagesEntity.getPageNum());
                syncPageEntity.setBucket(localPagesEntity.getBucket());
                syncPageEntity.setCreateTime(localPagesEntity.getCreateTime());
                syncPageEntity.setIsDelete(localPagesEntity.getIsDelete());
                syncPageEntity.setIsStar(localPagesEntity.getIsStar());
                syncPageEntity.setOcrContent(localPagesEntity.getOcrContent());
                syncPageEntity.setPicUrl(localPagesEntity.getPicUrl());
                syncPageEntity.setUpdateTime(localPagesEntity.getUpdateTime());
                syncPageEntity.setUuid(localPagesEntity.getUuid());
                syncPageEntity.setSyncTime(serverTime);
                syncPageEntity.setFileSize(localPagesEntity.getFileSize());
                realm.insertOrUpdate(syncPageEntity);
            }
            localPagesEntity.setIsDirty(0);
            if (localPagesEntity.getIsNewAdd() == 1) {
                localPagesEntity.setIsNewAdd(0);
            }
        }
        realm.commitTransaction();

        RealmResults<SyncPageEntity> syncPageEntities = realm.where(SyncPageEntity.class).findAll();
        syncPageList = realm.copyFromRealm(syncPageEntities);


        //Tags 移动并把数据表的数据isDirty 置为0
        RealmResults<TagEntity> localTagEntities = realm.where(TagEntity.class)
                .equalTo("isDirty", 1)
                .findAll();
        realm.beginTransaction();
        for (TagEntity localTagEntity : localTagEntities) {
            SyncTagEntity syncTagEntity = realm.createObject(SyncTagEntity.class);
            syncTagEntity.setTagId(localTagEntity.getTagId());
            syncTagEntity.setTagName(localTagEntity.getTagName());
            syncTagEntity.setCreateTime(localTagEntity.getCreateTime());
            syncTagEntity.setUpdateTime(localTagEntity.getUpdateTime());
            syncTagEntity.setIsDirty(localTagEntity.getIsDirty());
            syncTagEntity.setIsDelete(localTagEntity.getIsDelete());
            syncTagEntity.setSyncTime(serverTime);
            realm.insertOrUpdate(syncTagEntity);
            localTagEntity.setIsDirty(0);
        }
        realm.commitTransaction();
        RealmResults<SyncTagEntity> syncTagEntities = realm.where(SyncTagEntity.class).findAll();
        syncTagList = realm.copyFromRealm(syncTagEntities);


        //Books 移动并把数据表的数据isDirty 置为0
        RealmResults<BookPdfEntity> localPdfEntities = realm.where(BookPdfEntity.class)
                .equalTo("isDirty", 1)
                .findAll();

        realm.beginTransaction();
        for (BookPdfEntity localPdfEntity : localPdfEntities) {
            SyncPdfEntity syncPdfEntity = realm.createObject(SyncPdfEntity.class);
            syncPdfEntity.setPdfId(localPdfEntity.getPdfId());
            syncPdfEntity.setPdfName(localPdfEntity.getPdfName());
            syncPdfEntity.setCreateTime(localPdfEntity.getCreateTime());
            syncPdfEntity.setUpdateTime(localPdfEntity.getUpdateTime());
            syncPdfEntity.setPdfSize(localPdfEntity.getPdfSize());
            syncPdfEntity.setPdfPath(localPdfEntity.getPdfPath());
            syncPdfEntity.setIsDirty(localPdfEntity.getIsDirty());
            syncPdfEntity.setIsDelete(localPdfEntity.getIsDelete());
            syncPdfEntity.setIsNewAdd(localPdfEntity.getIsNewAdd());
            syncPdfEntity.setSyncTime(serverTime);
            realm.insertOrUpdate(syncPdfEntity);
            localPdfEntity.setIsDirty(0);
        }
        realm.commitTransaction();
        RealmResults<SyncPdfEntity> syncPdfEntities = realm.where(SyncPdfEntity.class).findAll();
        syncPdfList = realm.copyFromRealm(syncPdfEntities);
    }


    /**
     * step6 需要同步的数据拷贝到data/file/sync 文件夹下
     */
    private void copyToSyncDir() {
        if (FileUtils.createOrExistsDir(syncDir)) {
            for (SyncPageEntity syncPageEntity : syncPageList) {
                if (syncPageEntity.getIsDelete() == 1 || syncPageEntity.getIsDirty() == 2) {
                    continue;
                }
                String syncImgPath = syncDir + syncPageEntity.getPageId() + CZURConstants.JPG;
                FileUtils.copy(syncPageEntity.getPicUrl(), syncImgPath, new FileUtils.OnReplaceListener() {
                    @Override
                    public boolean onReplace(File srcFile, File destFile) {
                        return true;
                    }

                });
            }
        }
    }

    private void copyPdfToSyncDir() {
        if (FileUtils.createOrExistsDir(syncDir)) {
            for (SyncPdfEntity syncPdfEntity : syncPdfList) {
                if (syncPdfEntity.getIsDelete() == 1 || syncPdfEntity.getIsDirty() == 2) {
                    continue;
                }
                String syncPdfPath = syncDir + syncPdfEntity.getPdfId() + CZURConstants.PDF;
                FileUtils.copy(syncPdfEntity.getPdfPath(), syncPdfPath, new FileUtils.OnReplaceListener() {
                    @Override
                    public boolean onReplace(File srcFile, File destFile) {
                        return true;
                    }
                });
            }
        }
    }

    /**
     * step8 检查云存储剩余空间是否足够
     */
    private boolean checkCloudIsEnough(Realm realm) {
        float newAddFileSize = 0;
        float deleteFileSize = 0;
        float changeFileSize = 0;
        float totalFileSize;
        //1.本次新增文件大小总和
        RealmResults<SyncPageEntity> addPageEntities = realm.where(SyncPageEntity.class).equalTo("isDirty", 1).equalTo("isNewAdd", 1).findAll();
        if (addPageEntities.size() > 0) {
            for (SyncPageEntity addPageEntity : addPageEntities) {
                String syncImgPath = syncDir + addPageEntity.getPageId() + CZURConstants.JPG;
                if (FileUtils.isFileExists(syncImgPath)) {
                    newAddFileSize += FileUtils.getFileLength(syncImgPath);
                }

            }
        }
        //1.本次新增文件大小总和
        RealmResults<SyncPdfEntity> addPdfEntities = realm.where(SyncPdfEntity.class).equalTo("isDirty", 1).equalTo("isNewAdd", 1).findAll();
        if (addPdfEntities.size() > 0) {
            for (SyncPdfEntity addPdfEntity : addPdfEntities) {
                String syncPdfPath = syncDir + addPdfEntity.getPdfId() + CZURConstants.PDF;
                if (FileUtils.isFileExists(syncPdfPath)) {
                    newAddFileSize += FileUtils.getFileLength(syncPdfPath);
                }

            }
        }


        //2.本次删除文件大小总和

        RealmResults<SyncPageEntity> deletePageEntities = realm.where(SyncPageEntity.class).equalTo("isDirty", 1).equalTo("isDelete", 1).findAll();
        if (deletePageEntities.size() > 0) {
            FileSizeModel.BodyBean bodyBean = new FileSizeModel.BodyBean();
            List<FileSizeModel.BodyBean.KeyListBean> keyList = new ArrayList<>();
            FileSizeModel.BodyBean.KeyListBean keyListBean = new FileSizeModel.BodyBean.KeyListBean();
            for (SyncPageEntity deletePageEntity : deletePageEntities) {
                keyListBean.setFileSize(deletePageEntity.getFileSize() + "");
                keyListBean.setOssKey(deletePageEntity.getPageId());
                keyList.add(keyListBean);
            }
            bodyBean.setKeyList(keyList);
            FileSizeModel.BodyBean deleteBean = getFileSize(bodyBean);
            for (FileSizeModel.BodyBean.KeyListBean deleteKeyBean : deleteBean.getKeyList()) {
                deleteFileSize -= Float.parseFloat(deleteKeyBean.getFileSize());
            }

        }
        RealmResults<SyncPdfEntity> deletePdfEntities = realm.where(SyncPdfEntity.class).equalTo("isDirty", 1).equalTo("isDelete", 1).findAll();
        if (deletePdfEntities.size() > 0) {
            FileSizeModel.BodyBean bodyBean = new FileSizeModel.BodyBean();
            List<FileSizeModel.BodyBean.KeyListBean> keyList = new ArrayList<>();
            FileSizeModel.BodyBean.KeyListBean keyListBean = new FileSizeModel.BodyBean.KeyListBean();
            for (SyncPdfEntity deletePdfEntity : deletePdfEntities) {
                String syncPdfPath = syncDir + deletePdfEntity.getPdfId() + CZURConstants.PDF;
                keyListBean.setFileSize(FileUtils.getFileLength(syncPdfPath) + "");
                keyListBean.setOssKey(deletePdfEntity.getPdfId());
                keyList.add(keyListBean);
            }
            bodyBean.setKeyList(keyList);
            FileSizeModel.BodyBean deleteBean = getFilePdfSize(bodyBean);
            for (FileSizeModel.BodyBean.KeyListBean deleteKeyBean : deleteBean.getKeyList()) {
                deleteFileSize -= Float.parseFloat(deleteKeyBean.getFileSize());
            }

        }


        //3.本次修改文件大小总和

        RealmResults<SyncPageEntity> changePageEntities = realm.where(SyncPageEntity.class).equalTo("isDirty", 1).notEqualTo("isNewAdd", 1).equalTo("isDelete", 0).findAll();
        if (changePageEntities.size() > 0) {
            FileSizeModel.BodyBean bodyBean = new FileSizeModel.BodyBean();
            List<FileSizeModel.BodyBean.KeyListBean> keyList = new ArrayList<>();
            FileSizeModel.BodyBean.KeyListBean keyListBean = new FileSizeModel.BodyBean.KeyListBean();
            for (SyncPageEntity changePageEntity : changePageEntities) {
                keyListBean.setFileSize(changePageEntity.getFileSize() + "");
                keyListBean.setOssKey(changePageEntity.getPageId());
                keyList.add(keyListBean);
            }
            bodyBean.setKeyList(keyList);
            FileSizeModel.BodyBean changeBean = getFileSize(bodyBean);
            for (FileSizeModel.BodyBean.KeyListBean changeKeyBean : changeBean.getKeyList()) {
                for (FileSizeModel.BodyBean.KeyListBean listBean : keyList) {
                    if (listBean.getOssKey().equals(changeKeyBean.getOssKey())) {
                        changeFileSize += (Float.parseFloat(listBean.getFileSize()) - Float.parseFloat(changeKeyBean.getFileSize()));
                    }
                }
            }

        }

        totalFileSize = newAddFileSize + deleteFileSize + changeFileSize;
        return totalFileSize > userPreferences.getUsagesLimit() - userPreferences.getUsages();

    }

    /**
     * step8 下载并且处理文件
     */
    private HashSet<String> downloadAndProcessFile(Realm realm) {
        RealmResults<DownloadEntity> downloadEntities = realm.where(DownloadEntity.class).findAll();
        HashSet<String> fileNoExistSet = new HashSet<>();
        for (DownloadEntity downloadEntity : downloadEntities) {
            // 检查文件是否在OSS上存在
            OSS oss = OSSInstance.Companion.getInstance().oss();
            try {
                if (oss == null) {
//                    needStop = true;
//                    return fileNoExistSet;
                    continue;
                }
                boolean hasFileExist = oss.doesObjectExist(CZURConstants.BUCKET,
                        userPreferences.getUserId() + CZURConstants.OSS_SERVER_PATH + downloadEntity.getFileId() + "_" + downloadEntity.getUuid() + CZURConstants.JPG);
                if (!hasFileExist) {
                    fileNoExistSet.add(downloadEntity.getFileId());
                    realm.beginTransaction();
                    downloadEntity.deleteFromRealm();
                    realm.commitTransaction();
                    continue;
                }
            } catch (ClientException | ServiceException e) {
                continue;
            }

            if (!downloadEntity.isHasDownloadImage()) {
                boolean isDownloadImgSuccess = downloadFile(true, downloadEntity.getFileId(), downloadEntity.getUuid());
                if (isDownloadImgSuccess) {
                    realm.beginTransaction();
                    downloadEntity.setHasDownloadImage(true);
                    realm.commitTransaction();
                }
            }

            if (needStop) {
                return fileNoExistSet;
            }
            if (!downloadEntity.isHasMakeSmallImage() && downloadEntity.isHasDownloadImage()) {
                boolean isMakeSmallImgSuccess = makeSmallImg(downloadEntity.getFileId());
                if (isMakeSmallImgSuccess) {
                    realm.beginTransaction();
                    downloadEntity.setHasMakeSmallImage(true);
                    realm.commitTransaction();
                }
            }
            if (needStop) {
                return fileNoExistSet;
            }
            if (downloadEntity.isHasDownloadImage() && downloadEntity.isHasMakeSmallImage()) {
                realm.beginTransaction();
                downloadEntity.deleteFromRealm();
                realm.commitTransaction();
            }
            if (needStop) {
                return fileNoExistSet;
            }
        }
        return fileNoExistSet;
    }

    /**
     * step8 下载并且处理文件
     */
    private HashSet<String> downloadPdfFile(Realm realm) {
        RealmResults<PdfDownloadEntity> downloadEntities = realm.where(PdfDownloadEntity.class).findAll();
        HashSet<String> fileNoExistSet = new HashSet<>();
        for (PdfDownloadEntity downloadEntity : downloadEntities) {
            // 检查文件是否在OSS上存在
            OSS oss = OSSInstance.Companion.getInstance().oss();
            try {
                if (oss == null) {
//                    needStop = true;
//                    return fileNoExistSet;
                    continue;
                }
                boolean hasFileExist = oss.doesObjectExist(CZURConstants.BUCKET,
                        userPreferences.getUserId() + CZURConstants.OSS_PDF_PATH + downloadEntity.getPdfID() + CZURConstants.PDF);
                if (!hasFileExist) {
                    fileNoExistSet.add(downloadEntity.getPdfID());
                    realm.beginTransaction();
                    downloadEntity.deleteFromRealm();
                    realm.commitTransaction();
                    continue;
                }
            } catch (ClientException | ServiceException e) {
                continue;
            }

            if (!downloadEntity.isHasDownloadPdf()) {
                boolean isDownloadPdfSuccess = downloadFile(false, downloadEntity.getPdfID(), "");
                if (isDownloadPdfSuccess) {
                    realm.beginTransaction();
                    downloadEntity.setHasDownloadPdf(true);
                    realm.commitTransaction();
                }
            }

            if (needStop) {
                return fileNoExistSet;
            }

            if (downloadEntity.isHasDownloadPdf()) {
                realm.beginTransaction();
                downloadEntity.deleteFromRealm();
                realm.commitTransaction();
            }
            if (needStop) {
                return fileNoExistSet;
            }
        }
        return fileNoExistSet;
    }

    /**
     * 下载文件
     */
    private boolean downloadFile(boolean isPage, String fileId, String Uuid) {

        boolean isSuccess = false;
        if (!FileUtils.createOrExistsDir(isPage ? dirPath : dirPdfPath)) {
            return false;
        }
        OSS ossClient = OSSInstance.Companion.getInstance().oss();
        if (ossClient == null) {
            return false;
        }
        String imgKey = isPage ? userPreferences.getUserId() + CZURConstants.OSS_SERVER_PATH + fileId + "_" + Uuid + CZURConstants.JPG : userPreferences.getUserId() + CZURConstants.OSS_PDF_PATH + fileId + CZURConstants.PDF;
//构造下载文件请求
        GetObjectRequest get = new GetObjectRequest(CZURConstants.BUCKET, imgKey);
        try {
            // 同步执行下载请求，返回结果
            GetObjectResult getResult = ossClient.getObject(get);
            // 获取文件输入流
            InputStream inputStream = getResult.getObjectContent();
            byte[] buffer = new byte[2048];
            int len;
            String sourcePath = isPage ? dirPath + fileId + CZURConstants.JPG : dirPdfPath + fileId + CZURConstants.PDF;
            FileOutputStream downloadStream = new FileOutputStream(sourcePath);
            while ((len = inputStream.read(buffer)) != -1) {
                // 处理下载的数据，比如图片展示或者写入文件等
                downloadStream.write(buffer, 0, len);
                downloadStream.flush();
            }
            downloadStream.close();
            inputStream.close();
            logI(isPage ? "download success" : "download pdf success");
            isSuccess = true;

            // 下载后可以查看文件元信息
            ObjectMetadata metadata = getResult.getMetadata();
        } catch (ClientException e) {
            logE("download defeat" + e.toString());
            // 本地异常如网络异常等
            e.printStackTrace();
        } catch (ServiceException e) {
            logE("download defeat" + e.toString());
            // 服务异常
            Log.e("RequestId", e.getRequestId());
            Log.e("ErrorCode", e.getErrorCode());
            Log.e("HostId", e.getHostId());
            Log.e("RawMessage", e.getRawMessage());
        } catch (IOException e) {
            logE("download defeat" + e.toString());
            e.printStackTrace();
        } catch (Exception e) {
            logE("unknown exception" + e.toString());
            return false;
        }
        return isSuccess;
    }


    /**
     * 压缩并且保存小图
     */
    private boolean makeSmallImg(String pageIdUUid) {
        if (FileUtils.createOrExistsDir(dirPath)) {
            String sourcePath = dirPath + pageIdUUid + CZURConstants.JPG;
            String smallPath = dirPath + pageIdUUid + CZURConstants.SMALL_JPG;
            Bitmap bitmap = ImageUtils.getBitmap(sourcePath);
            Bitmap smallBitmap = ImageUtils.compressByScale(bitmap, 300, 300, true);
            boolean isSuccessSave = ImageUtils.save(smallBitmap, smallPath, Bitmap.CompressFormat.JPEG, true);
            return isSuccessSave;

        }
        return false;
    }

    /**
     * 是否所有的图片下载并且压缩小图成功
     */
    private boolean isAllDownloadAndMakeSmallImg(Realm realm) {
        RealmResults<DownloadEntity> downloadEntities = realm.where(DownloadEntity.class).findAll();
        return !Validator.isNotEmpty(downloadEntities);
    }

    private boolean isAllPdfDownload(Realm realm) {
        RealmResults<PdfDownloadEntity> downloadEntities = realm.where(PdfDownloadEntity.class).findAll();
        return !Validator.isNotEmpty(downloadEntities);
    }

    /**
     * step9 检查并更新需要同步表数据
     */
    private void checkAndUpdateSyncData(List<SyncEntity.CzurBooksNotesBean> booksList, List<SyncEntity.CzurBooksPageTagsBean> tagsList, Realm realm) {
        realm.beginTransaction();
        for (SyncPageEntity syncPageEntity : syncPageList) {

            boolean isExist = false;
            for (SyncEntity.CzurBooksNotesBean notesBean : booksList) {
                if (notesBean.getId().equals(syncPageEntity.getBookId())) {
                    if (notesBean.getIsDelete()) {
                        isExist = true;
                    }
                }
            }
            if ((syncPageEntity.getIsDelete() == 0) && isExist) {
                syncPageEntity.setIsDelete(1);
            }

            boolean isTagNoExist = false;
            for (SyncEntity.CzurBooksPageTagsBean tagsBean : tagsList) {
                if (tagsBean.getTagId().equals(syncPageEntity.getTagId())) {
                    if (tagsBean.isDelete()) {
                        isTagNoExist = true;
                        break;
                    }
                }
            }
            if (isTagNoExist) {
                syncPageEntity.setTagName("");
                syncPageEntity.setTagId("");
            }


        }

        realm.commitTransaction();
    }

    private void checkSyncPdf(List<SyncEntity.CzurBooksPdfsBean> pdfsList, Realm realm) {
        realm.beginTransaction();
        pdfIds = new ArrayList<>();
        pdfNames = new ArrayList<>();

        RealmResults<BookPdfEntity> localPdfEntities = realm.where(BookPdfEntity.class).equalTo("isDelete", 0).findAll();
        if (localPdfEntities != null) {
            for (BookPdfEntity localPdfEntity : localPdfEntities) {
                String pdfName = localPdfEntity.getPdfName();
                for (SyncEntity.CzurBooksPdfsBean pdfsBean : pdfsList) {

                    if (pdfName.equals(pdfsBean.getFileName()) && !pdfsBean.getIsDelete()) {
                        int i = 0;
//                            String pdfInitName = pdfsBean.getFileName();
                        String finalName = pdfName;
                        BookPdfEntity sameNamePdf = realm.where(BookPdfEntity.class)
                                .equalTo("pdfName", pdfName)
                                .equalTo("isDelete", 0)
                                .findFirst();

                        while (Validator.isNotEmpty(sameNamePdf) || checkSameNameInSyncPdf(pdfsList, finalName)) {
                            i++;
                            finalName = pdfName + String.format(getString(R.string.repeat_name_format), i + "");
                            sameNamePdf = realm.where(BookPdfEntity.class)
                                    .equalTo("pdfName", finalName)
                                    .equalTo("isDelete", 0)
                                    .findFirst();

                        }
                        pdfIds.add(localPdfEntity.getPdfId());
                        pdfNames.add(finalName);
                        for (SyncPdfEntity syncPdfEntity : syncPdfList) {
                            if (syncPdfEntity.getIsDelete() == 0 && syncPdfEntity.getPdfId().equals(localPdfEntity.getPdfId())) {
                                syncPdfEntity.setPdfName(finalName);
                            }
                        }
                    }
                }

            }
        }
        realm.commitTransaction();
    }

    private boolean checkSameNameInSyncPdf(List<SyncEntity.CzurBooksPdfsBean> pdfsBeanList, String name) {
//        logI(new Gson().toJson(syncPdfList));
        for (SyncEntity.CzurBooksPdfsBean pdfsBean : pdfsBeanList) {
            if (pdfsBean.getFileName().equals(name) && !pdfsBean.getIsDelete()) {
                return true;
            }
        }
        return false;
    }

    /**
     * step10 检查是否上传文件 或者已删除  并且删除同步文件夹的文件
     */
    private void checkIsUploadedAndDeleteFilesInSyncDir(Realm realm) {
        RealmResults<SyncPageEntity> syncPageEntities = realm.where(SyncPageEntity.class).findAll();
        for (SyncPageEntity syncPageEntity : syncPageEntities) {
            if (!syncPageEntity.isHasUploadImage()) {
                if (syncPageEntity.getIsDelete() == 1 || syncPageEntity.getIsDirty() == 2) {
                    setUploadFlagAndDeleteFile(syncPageEntity, false, realm);
                } else {
                    boolean uploadResult = uploadFiles(true, syncPageEntity.getPageId(), syncPageEntity.getUuid());
                    if (uploadResult) {
                        setUploadFlagAndDeleteFile(syncPageEntity, true, realm);
                    }
                }
            }
            if (needStop) {
                return;
            }
        }

        RealmResults<SyncPdfEntity> synPdfEntities = realm.where(SyncPdfEntity.class).findAll();
        for (SyncPdfEntity synPdfEntity : synPdfEntities) {
            if (!synPdfEntity.isHasUploadPdf()) {
                if (synPdfEntity.getIsDelete() == 1 || synPdfEntity.getIsDirty() == 2) {
                    setPdfUploadFlagAndDeleteFile(synPdfEntity, false, realm);
                } else {
                    boolean uploadResult = uploadFiles(false, synPdfEntity.getPdfId(), "");
                    if (uploadResult) {
                        setPdfUploadFlagAndDeleteFile(synPdfEntity, true, realm);
                    }
                }
            }
            if (needStop) {
                return;
            }
        }
    }

    private void setUploadFlagAndDeleteFile(SyncPageEntity syncPageEntity, boolean needDeleteFile, Realm realm) {
        realm.beginTransaction();
        syncPageEntity.setHasUploadImage(true);
        realm.commitTransaction();
        if (needDeleteFile) {
            String syncImgPath = syncDir + syncPageEntity.getPageId() + CZURConstants.JPG;
            FileUtils.delete(syncImgPath);
        }
    }

    private void setPdfUploadFlagAndDeleteFile(SyncPdfEntity syncPdfEntity, boolean needDeleteFile, Realm realm) {
        realm.beginTransaction();
        syncPdfEntity.setHasUploadPdf(true);
        realm.commitTransaction();
        if (needDeleteFile) {
            String syncPdfPath = syncDir + syncPdfEntity.getPdfId() + CZURConstants.PDF;
            FileUtils.delete(syncPdfPath);
        }
    }

    /**
     * 上传文件
     */
    private boolean uploadFiles(boolean isPage, String fileId, String Uuid) {
        boolean isUploadSuccess = false;
        String sourcePath = isPage ? syncDir + fileId + CZURConstants.JPG : syncDir + fileId + CZURConstants.PDF;
        String ossPath = isPage ? userPreferences.getUserId() + CZURConstants.OSS_SERVER_PATH + fileId + "_" + Uuid + CZURConstants.JPG : userPreferences.getUserId() + CZURConstants.OSS_PDF_PATH + fileId + CZURConstants.PDF;
//        logI("upload", sourcePath, ossPath);
        OSS ossClient = OSSInstance.Companion.getInstance().oss();
        if (ossClient == null) {
            return false;
        }
        // 构造上传请求
        PutObjectRequest put = new PutObjectRequest(CZURConstants.BUCKET, ossPath, sourcePath);

        try {
            PutObjectResult putResult = ossClient.putObject(put);
            isUploadSuccess = true;
//            logI("PutObject: UploadSuccess", "ETag :" + putResult.getETag(), "RequestId: " + putResult.getRequestId());
        } catch (ClientException e) {
            // 本地异常如网络异常等
            e.printStackTrace();
        } catch (ServiceException e) {
            // 服务异常
            Log.e("RequestId", e.getRequestId());
            Log.e("ErrorCode", e.getErrorCode());
            Log.e("HostId", e.getHostId());
            Log.e("RawMessage", e.getRawMessage());
        }
        return isUploadSuccess;
    }

    /**
     * 是否所有的图片下载并且压缩小图成功
     */
    private boolean isAllPagesUpload(Realm realm) {
        boolean isAllUpload = true;
        RealmResults<SyncPageEntity> syncPageEntities = realm.where(SyncPageEntity.class).findAll();
        for (SyncPageEntity syncPageEntity : syncPageEntities) {
            if (!syncPageEntity.isHasUploadImage()) {
                isAllUpload = false;
            }
        }
        return isAllUpload;
    }

    private boolean isAllPdfsUpload(Realm realm) {
        boolean isAllUpload = true;
        RealmResults<SyncPdfEntity> syncPdfEntities = realm.where(SyncPdfEntity.class).findAll();
        for (SyncPdfEntity syncPdfEntity : syncPdfEntities) {
            if (!syncPdfEntity.isHasUploadPdf()) {
                isAllUpload = false;
            }
        }
        return isAllUpload;
    }

    /**
     * stepX 上传同步表中的数据
     */
    private boolean syncDataToServer(SyncEntity syncEntity) {
        String syncJson = new Gson().toJson(syncEntity);
        try {
            RequestBody requestBody = RequestBody.create(JSON, syncJson);
            Request request = new Request.Builder()
                    .header("udid", userPreferences.getIMEI())
                    .header("App-Key", CZURConstants.CLOUD_ANDROID)
                    .header("T-ID", userPreferences.getToken())
                    .header("U-ID", userPreferences.getUserId())
                    .header("Content-Type", "application/json")
                    .url(BuildConfig.BASE_URL + CZURConstants.SYNC_URL)
                    .post(requestBody)
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(request).execute();
            String responseString = response.body().string();
            BaseModel baseModel = new Gson().fromJson(responseString, BaseModel.class);
            // 请求成功
            if (response.isSuccessful()) {
                int code = baseModel.getCode();
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
                    logI("syncDataToServer.上传数据库至服务器 成功");
                    return true;
                } else {
                    logE("syncDataToServer.上传数据库至服务器 失败");
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            logE("syncDataToServer.e=" + e.toString());
            return false;
        }
    }

    /**
     * 把同步表的数据转换成上传给服务器的List
     */
    private SyncEntity transFormRealmListToSyncList(List<SyncBookEntity> syncBookList, List<SyncPageEntity> syncPageList, List<SyncTagEntity> syncTagList, List<SyncPdfEntity> syncPdfList) {
        List<SyncEntity.CzurBooksNotesBean> booksBeen = new ArrayList<>();
        List<SyncEntity.CzurBooksPagesBean> pagesBeen = new ArrayList<>();
        List<SyncEntity.CzurBooksPageTagsBean> tagsBeen = new ArrayList<>();
        List<SyncEntity.CzurBooksPdfsBean> pdfBeen = new ArrayList<>();

        for (SyncBookEntity syncBookEntity : syncBookList) {
            SyncEntity.CzurBooksNotesBean bookBean = new SyncEntity.CzurBooksNotesBean();
            bookBean.setId(syncBookEntity.getBookId());
            bookBean.setUserId(userPreferences.getUserId());
            bookBean.setName(syncBookEntity.getBookName());
            bookBean.setIsDelete(syncBookEntity.getIsDelete() == 1);
            bookBean.setCreateOn(syncBookEntity.getCreateTime());
            bookBean.setUpdateOn(syncBookEntity.getUpdateTime());
            bookBean.setSynchronousOn(syncBookEntity.getSyncTime());
            booksBeen.add(bookBean);
        }
        for (SyncPageEntity syncPageEntity : syncPageList) {
            SyncEntity.CzurBooksPagesBean pagesBean = new SyncEntity.CzurBooksPagesBean();
            pagesBean.setId(syncPageEntity.getPageId());
            pagesBean.setNoteName(syncPageEntity.getNoteName());
            pagesBean.setUserId(userPreferences.getUserId());
            pagesBean.setTagId(syncPageEntity.getTagId());
            pagesBean.setTagName(syncPageEntity.getTagName());
            pagesBean.setNoteId(syncPageEntity.getBookId());
            pagesBean.setUuid(syncPageEntity.getUuid());
            pagesBean.setOssBucket(CZURConstants.BUCKET);
            pagesBean.setIsStar(syncPageEntity.getIsStar() == 1);
            pagesBean.setIsDelete(syncPageEntity.getIsDelete() == 1);
            pagesBean.setOcrContent(syncPageEntity.getOcrContent());
            pagesBean.setCreateOn(syncPageEntity.getCreateTime());
            pagesBean.setUpdateOn(syncPageEntity.getUpdateTime());
            pagesBean.setSynchronousOn(syncPageEntity.getSyncTime());
            pagesBean.setPageNum(syncPageEntity.getPageNum());
            pagesBean.setFileSize(syncPageEntity.getFileSize());
            pagesBeen.add(pagesBean);
        }

        for (SyncTagEntity syncTagEntity : syncTagList) {
            SyncEntity.CzurBooksPageTagsBean tagsBean = new SyncEntity.CzurBooksPageTagsBean();
            tagsBean.setTagId(syncTagEntity.getTagId());
            tagsBean.setTagName(syncTagEntity.getTagName());
            tagsBean.setUserId(userPreferences.getUserId());
            tagsBean.setCreateTime(syncTagEntity.getCreateTime());
            tagsBean.setUpdateTime(syncTagEntity.getUpdateTime());
            tagsBean.setDelete(syncTagEntity.getIsDelete() == 1);
            tagsBean.setSynchronousTime(syncTagEntity.getSyncTime());
            tagsBeen.add(tagsBean);
        }

        for (SyncPdfEntity syncPdfEntity : syncPdfList) {
            SyncEntity.CzurBooksPdfsBean pdfsBean = new SyncEntity.CzurBooksPdfsBean();
            pdfsBean.setId(syncPdfEntity.getPdfId());
            pdfsBean.setFileName(syncPdfEntity.getPdfName());
            pdfsBean.setUserId(userPreferences.getUserId());
            pdfsBean.setOssKey(userPreferences.getUserId() + File.separator + syncPdfEntity.getPdfId() + CZURConstants.PDF);
            pdfsBean.setOssBucket(CZURConstants.BUCKET);
            pdfsBean.setIsDelete(syncPdfEntity.getIsDelete() == 1);
            pdfsBean.setCreateOn(syncPdfEntity.getCreateTime());
            pdfsBean.setUpdateOn(syncPdfEntity.getUpdateTime());
            pdfsBean.setSynchronousOn(syncPdfEntity.getSyncTime());
            pdfsBean.setFileSize(syncPdfEntity.getPdfSize());
            pdfBeen.add(pdfsBean);
        }
        SyncEntity syncEntity = new SyncEntity();
        syncEntity.setCzurBooksNotes(booksBeen);
        syncEntity.setCzurBooksPages(pagesBeen);
        syncEntity.setCzurBooksPageTags(tagsBeen);
        syncEntity.setCzurBooksPdfs(pdfBeen);
        return syncEntity;
    }

    private void checkMergeData(List<SyncEntity.CzurBooksNotesBean> booksList, List<SyncEntity.CzurBooksPagesBean> pagesList,
                                List<SyncEntity.CzurBooksPageTagsBean> tagsList, List<SyncEntity.CzurBooksPdfsBean> pdfsList, HashSet<String> fileNoExistSet, HashSet<String> pdfNoExistSet, Realm realm) {
        // 检查页
        ArrayList<SyncEntity.CzurBooksPagesBean> needRemovePageList = new ArrayList<>();
        for (SyncEntity.CzurBooksPagesBean page : pagesList) {
            SyncPageEntity queryResult = realm.where(SyncPageEntity.class).equalTo("pageId", page.getId()).findFirst();
            // 推数据中存在当前页 || 当前页OSS中不存在，从增量数据中删除
            if (Validator.isNotEmpty(queryResult) || fileNoExistSet.contains(page.getId())) {
                needRemovePageList.add(page);
            }
        }
        pagesList.removeAll(needRemovePageList);
        needRemovePageList = new ArrayList<>();

        ArrayList<SyncEntity.CzurBooksPdfsBean> needRemovePdfList = new ArrayList<>();
        for (SyncEntity.CzurBooksPdfsBean pdf : pdfsList) {
            SyncPdfEntity queryResult = realm.where(SyncPdfEntity.class).equalTo("pdfId", pdf.getId()).findFirst();
            // 推数据中存在当前页 || 当前页OSS中不存在，从增量数据中删除
            if (Validator.isNotEmpty(queryResult) || pdfNoExistSet.contains(pdf.getId())) {
                needRemovePdfList.add(pdf);
            }
        }
        pdfsList.removeAll(needRemovePdfList);
        needRemovePdfList = new ArrayList<>();

        // 检查书
        ArrayList<SyncEntity.CzurBooksNotesBean> needRemoveBookList = new ArrayList<>();
        for (SyncEntity.CzurBooksNotesBean book : booksList) {
            SyncBookEntity queryResult = realm.where(SyncBookEntity.class).equalTo("bookId", book.getId()).findFirst();
            // 推数据中存在当前书，从增量数据中删除
            if (Validator.isNotEmpty(queryResult)) {
                needRemoveBookList.add(book);

                // 如果删除的书为“已删除”状态，检查所有增量数据中的页，如果有同BookID的，从增量数据中删除
                if (queryResult.getIsDelete() == 1) {
                    for (SyncEntity.CzurBooksPagesBean page : pagesList) {
                        if (page.getNoteId().equals(queryResult.getBookId())) {
                            needRemovePageList.add(page);
                        }
                    }
                }
            }
        }
        booksList.removeAll(needRemoveBookList);
        pagesList.removeAll(needRemovePageList);

        // 检查标签
        ArrayList<SyncEntity.CzurBooksPageTagsBean> needRemoveTagList = new ArrayList<>();
        for (SyncEntity.CzurBooksPageTagsBean tag : tagsList) {
            SyncTagEntity queryResult = realm.where(SyncTagEntity.class).equalTo("tagId", tag.getTagId()).findFirst();
            // 推数据中存在当前书，从增量数据中删除
            if (Validator.isNotEmpty(queryResult)) {
                needRemoveTagList.add(tag);

                // 如果删除的标签为“已删除”状态，检查所有增量数据中的页，如果TagID相同，修改增量数据页为无标签状态
                if (queryResult.getIsDelete() == 1) {
                    for (SyncEntity.CzurBooksPagesBean page : pagesList) {
                        if (page.getTagId().equals(queryResult.getTagId())) {
                            realm.beginTransaction();
                            page.setTagId("");
                            page.setTagName("");
                            realm.commitTransaction();
                        }
                    }
                }
            }
        }
        tagsList.removeAll(needRemoveTagList);
    }

    /**
     * step12 合并books数据
     */
    private boolean mergeBook(List<SyncEntity.CzurBooksNotesBean> booksList, Realm realm) {
        boolean needNotifyDelete = false;
        if (Validator.isNotEmpty(booksList)) {
            for (SyncEntity.CzurBooksNotesBean booksBean : booksList) {
                BookEntity bookEntity = realm.where(BookEntity.class)
                        .equalTo("bookId", booksBean.getId())
                        .findFirst();
                if (Validator.isNotEmpty(bookEntity)) {
                    if (bookEntity.getIsDirty() == 0) {
                        realm.beginTransaction();
                        bookEntity.setBookName(booksBean.getName());
                        bookEntity.setIsDelete(booksBean.getIsDelete() ? 1 : 0);
                        bookEntity.setUpdateTime(booksBean.getUpdateOn());
                        realm.commitTransaction();
                        if (booksBean.getIsDelete()) {
                            needNotifyDelete = true;
                            realm.beginTransaction();
                            RealmResults<PageEntity> pageEntities = realm.where(PageEntity.class)
                                    .equalTo("bookId", booksBean.getId())
                                    .findAll();
                            for (PageEntity pageEntity : pageEntities) {
                                pageEntity.setIsDirty(0);
                                pageEntity.setIsDelete(1);
                            }
                            realm.commitTransaction();
                        }
                    }
                } else {
                    realm.beginTransaction();
                    BookEntity newBookEntity = realm.createObject(BookEntity.class, booksBean.getId());
                    newBookEntity.setBookName(booksBean.getName());
                    newBookEntity.setIsDirty(0);
                    newBookEntity.setIsDelete(booksBean.getIsDelete() ? 1 : 0);
                    newBookEntity.setUpdateTime(booksBean.getUpdateOn());
                    newBookEntity.setCreateTime(booksBean.getCreateOn());
                    realm.commitTransaction();
                }

            }
        }
        return needNotifyDelete;
    }

    /**
     * step12 合并pages数据
     */
    private boolean mergePages(List<SyncEntity.CzurBooksPagesBean> pagesList, Realm realm) {
        boolean needNotifyDelete = false;

        if (Validator.isNotEmpty(pagesList)) {
            for (SyncEntity.CzurBooksPagesBean pagesBean : pagesList) {
                PageEntity pageEntity = realm.where(PageEntity.class)
                        .equalTo("pageId", pagesBean.getId())
                        .findFirst();

                if (Validator.isNotEmpty(pageEntity)) {
                    if (pageEntity.getIsDirty() == 0) {
                        realm.beginTransaction();
                        createOrUpdateRealmPage(pageEntity, pagesBean);
                        if (pagesBean.getIsDelete()) {
                            needNotifyDelete = true;
                        }
                        realm.commitTransaction();
                    }
                } else {
                    realm.beginTransaction();
                    PageEntity newPageEntity = realm.createObject(PageEntity.class, pagesBean.getId());
                    createOrUpdateRealmPage(newPageEntity, pagesBean);
                    realm.commitTransaction();
                }
            }
        }
        return needNotifyDelete;
    }

    /**
     * step12 合并tags数据
     */
    private boolean mergeTags(List<SyncEntity.CzurBooksPageTagsBean> tagsList, Realm realm) {
        boolean needNotifyDelete = false;
        if (Validator.isNotEmpty(tagsList)) {
            for (SyncEntity.CzurBooksPageTagsBean tagsBean : tagsList) {
                TagEntity tagEntity = realm.where(TagEntity.class)
                        .equalTo("tagId", tagsBean.getTagId())
                        .findFirst();
                if (Validator.isNotEmpty(tagEntity)) {
                    if (tagEntity.getIsDirty() == 0) {
                        realm.beginTransaction();
                        tagEntity.setTagName(tagsBean.getTagName());
                        tagEntity.setUpdateTime(tagsBean.getUpdateTime());
                        tagEntity.setIsDelete(tagsBean.isDelete() ? 1 : 0);

                        realm.commitTransaction();
                        if (tagsBean.isDelete()) {
                            needNotifyDelete = true;
                            realm.beginTransaction();
                            RealmResults<PageEntity> pageEntities = realm.where(PageEntity.class)
                                    .equalTo("tagId", tagsBean.getTagId())
                                    .findAll();
                            for (PageEntity pageEntity : pageEntities) {
                                pageEntity.setTagName("");
                                pageEntity.setTagId("");
                            }
                            realm.commitTransaction();
                        }
                    }
                } else {
                    realm.beginTransaction();
                    TagEntity newTagEntity = realm.createObject(TagEntity.class, tagsBean.getTagId());
                    newTagEntity.setTagName(tagsBean.getTagName());
                    newTagEntity.setIsDirty(0);
                    newTagEntity.setIsDelete(tagsBean.isDelete() ? 1 : 0);
                    newTagEntity.setUpdateTime(tagsBean.getUpdateTime());
                    newTagEntity.setCreateTime(tagsBean.getCreateTime());
                    realm.commitTransaction();
                }

            }
        }
        return needNotifyDelete;
    }

    /**
     * step12 合并pages数据
     */
    private boolean mergePdf(List<SyncEntity.CzurBooksPdfsBean> pdfsList, Realm realm) {
        boolean needNotifyDelete = false;

        if (Validator.isNotEmpty(pdfsList)) {
            for (SyncEntity.CzurBooksPdfsBean pdfsBean : pdfsList) {
                BookPdfEntity pdfEntity = realm.where(BookPdfEntity.class)
                        .equalTo("pdfId", pdfsBean.getId())
                        .findFirst();

                if (Validator.isNotEmpty(pdfEntity)) {
                    if (pdfEntity.getIsDirty() == 0) {
                        realm.beginTransaction();
                        createOrUpdateRealmPdf(pdfEntity, pdfsBean);
                        if (pdfsBean.getIsDelete()) {
                            needNotifyDelete = true;
                        }
                        realm.commitTransaction();
                    }
                } else {
                    realm.beginTransaction();
                    BookPdfEntity newPdfEntity = realm.createObject(BookPdfEntity.class, pdfsBean.getId());
                    createOrUpdateRealmPdf(newPdfEntity, pdfsBean);
                    realm.commitTransaction();
                }
            }
        }
        realm.beginTransaction();
        for (int i = 0; i < pdfIds.size(); i++) {
            BookPdfEntity bookPdfEntity = realm.where(BookPdfEntity.class).equalTo("pdfId", pdfIds.get(i)).findFirst();
            bookPdfEntity.setPdfName(pdfNames.get(i));

        }
        realm.commitTransaction();
        return needNotifyDelete;
    }

    /**
     * step13 转移星标文件
     */
    private void transStarPageToTag(Realm realm) {
        realm.beginTransaction();
        RealmResults<PageEntity> pageEntities = realm.where(PageEntity.class).equalTo("isStar", 1).equalTo("isDelete", 0).findAll();

        if (pageEntities.size() > 0) {
            TagEntity tagEntity = realm.where(TagEntity.class).equalTo("tagName", getString(R.string.is_star)).findFirst();
            if (tagEntity != null && tagEntity.getIsDelete() == 0) {
                for (PageEntity pageEntity : pageEntities) {
                    pageEntity.setIsStar(0);
                    pageEntity.setTagId(tagEntity.getTagId());
                    pageEntity.setTagName(tagEntity.getTagName());
                    if (pageEntity.getIsDirty() != 1) {
                        pageEntity.setIsDirty(2);
                    }
                }

            } else {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                String curDate = formatter.format(new Date(System.currentTimeMillis()));
                TagEntity newTagEntity = new TagEntity();
                newTagEntity.setTagId(UUID.randomUUID().toString());
                newTagEntity.setTagName(getString(R.string.is_star));
                newTagEntity.setIsDirty(1);
                newTagEntity.setCreateTime(curDate);
                newTagEntity.setUpdateTime(curDate);
                realm.copyToRealmOrUpdate(newTagEntity);

                for (PageEntity pageEntity : pageEntities) {
                    pageEntity.setIsStar(0);
                    pageEntity.setTagId(newTagEntity.getTagId());
                    pageEntity.setTagName(newTagEntity.getTagName());
                    if (pageEntity.getIsDirty() != 1) {
                        pageEntity.setIsDirty(2);
                    }
                }
            }

        }
        realm.commitTransaction();
    }

    /**
     * step13 转移星标文件
     */
    private void fillNoteName(List<SyncEntity.CzurBooksNotesBean> booksList, Realm realm) {
        realm.beginTransaction();
        for (SyncEntity.CzurBooksNotesBean czurBooksNotesBean : booksList) {
            if (!czurBooksNotesBean.getIsDelete()) {
                BookEntity bookEntity = realm.where(BookEntity.class).equalTo("bookId", czurBooksNotesBean.getId()).findFirst();
                RealmResults<PageEntity> pageEntities = realm.where(PageEntity.class).equalTo("bookId", czurBooksNotesBean.getId()).findAll();
                for (PageEntity pageEntity : pageEntities) {
                    pageEntity.setNoteName(bookEntity.getBookName());
                }
            }

        }
        realm.commitTransaction();
    }


    /**
     * 新建或者更新数据库书页
     */
    private void createOrUpdateRealmPage(PageEntity pageEntity, SyncEntity.CzurBooksPagesBean pagesBean) {
        pageEntity.setBookId(pagesBean.getNoteId());
        pageEntity.setPageNum(pagesBean.getPageNum());
        pageEntity.setNoteName(pagesBean.getNoteName());
        pageEntity.setBucket(pagesBean.getOssBucket());
        pageEntity.setUuid(pagesBean.getUuid());
        pageEntity.setTagId(pagesBean.getTagId());
        pageEntity.setTagName(pagesBean.getTagName());
        pageEntity.setIsStar(pagesBean.getIsStar() ? 1 : 0);
        pageEntity.setOcrContent(pagesBean.getOcrContent());
        pageEntity.setIsDelete(pagesBean.getIsDelete() ? 1 : 0);
        pageEntity.setIsDirty(0);
        pageEntity.setCreateTime(pagesBean.getCreateOn());
        pageEntity.setUpdateTime(pagesBean.getUpdateOn());
        pageEntity.setIsTemp(0);
        pageEntity.setFileSize(pagesBean.getFileSize());
        pageEntity.setTakePhotoTime(CZURConstants.TAKE_PHOTO_INIT_TIME);
        pageEntity.setPicUrl(dirPath + pagesBean.getId() + CZURConstants.JPG);
        pageEntity.setSmallPicUrl(dirPath + pagesBean.getId() + CZURConstants.SMALL_JPG);
    }

    /**
     * 新建或者更新数据库书页
     */
    private void createOrUpdateRealmPdf(BookPdfEntity pdfEntity, SyncEntity.CzurBooksPdfsBean pdfsBean) {
        pdfEntity.setPdfName(pdfsBean.getFileName());
        pdfEntity.setPdfPath(dirPdfPath + pdfsBean.getId() + CZURConstants.PDF);
        pdfEntity.setIsDelete(pdfsBean.getIsDelete() ? 1 : 0);
        pdfEntity.setIsDirty(0);
        pdfEntity.setCreateTime(pdfsBean.getCreateOn());
        pdfEntity.setUpdateTime(pdfsBean.getUpdateOn());
        pdfEntity.setPdfSize(pdfsBean.getFileSize());

    }


    /**
     * 获取FileSize信息
     */
    private FileSizeModel.BodyBean getFileSize(FileSizeModel.BodyBean fileSizeModel) {
        String request = new Gson().toJson(fileSizeModel);
        try {
            RequestBody requestBody = RequestBody.create(JSON, request);
            Request httpRequest = new Request.Builder()
                    .header("udid", userPreferences.getIMEI())
                    .header("App-Key", CZURConstants.CLOUD_ANDROID)
                    .header("T-ID", userPreferences.getToken())
                    .header("U-ID", userPreferences.getUserId())
                    .header("Content-Type", "application/json")
                    .url(BuildConfig.BASE_URL + CZURConstants.FILE_SIZE_URL)
                    .post(requestBody)
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(httpRequest).execute();
            String responseString = response.body().string();
            FileSizeModel baseModel = new Gson().fromJson(responseString, FileSizeModel.class);
            // 请求成功
            if (response.isSuccessful()) {
                int code = baseModel.getCode();
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
                    return baseModel.getBody();
                } else {
                    return null;
                }
            } else {
                return null;
            }
        } catch (Exception e) {
            logE("getFileSize.e=" + e.toString());
            return null;
        }
    }

    /**
     * 获取FileSize信息
     */
    private FileSizeModel.BodyBean getFilePdfSize(FileSizeModel.BodyBean fileSizeModel) {
        String request = new Gson().toJson(fileSizeModel);
        try {
            RequestBody requestBody = RequestBody.create(JSON, request);
            Request httpRequest = new Request.Builder()
                    .header("udid", userPreferences.getIMEI())
                    .header("App-Key", CZURConstants.CLOUD_ANDROID)
                    .header("T-ID", userPreferences.getToken())
                    .header("U-ID", userPreferences.getUserId())
                    .header("Content-Type", "application/json")
                    .url(BuildConfig.BASE_URL + CZURConstants.FILE_PDF_SIZE_URL)
                    .post(requestBody)
                    .build();

            Response response = MiaoHttpManager.getInstance().getHttpClient().newCall(httpRequest).execute();
            String responseString = response.body().string();
            FileSizeModel baseModel = new Gson().fromJson(responseString, FileSizeModel.class);
            // 请求成功
            if (response.isSuccessful()) {
                int code = baseModel.getCode();
                if (code == MiaoHttpManager.STATUS_SUCCESS) {
//                    logI("获取pdf文件大小 成功", new Gson().toJson(baseModel.getBody()));
                    return baseModel.getBody();
                } else {
//                    logI("获取pdf文件大小 失败");
                    return null;
                }
            } else {
//                logI("获取pdf文件大小 失败");
                return null;
            }
        } catch (Exception e) {
            logE("getFilePdfSize.e=" + e.toString());
            return null;
        }
    }

    /**
     * 获取用户信息
     */
    private void getUserInfo() {
        try {
            MiaoHttpEntity<UserInfoModel> userInfo = httpManager.request().userInfo(
                    userPreferences.getUserId(), UserInfoModel.class);
            if (userInfo.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                userPreferences.setUsages(userInfo.getBody().getUsages());
                userPreferences.setUsagesLimit(userInfo.getBody().getUsagesLimit());
            } else {
                syncStop(beginTime);
            }
        } catch (Exception e) {
            logE("getUserInfo.e=" + e.toString());
            syncStop(beginTime);
            e.printStackTrace();
        }
    }

    /**
     * 结束线程池并且停止service 发送同步结束EventBus
     */
    private void syncFinish(long beginTime) {
        stopSync(beginTime);
        EventBus.getDefault().postSticky(new SyncFinishEvent(EventType.SYNC_IS_FINISH));
        EventBus.getDefault().post(new SyncFinishEvent(EventType.SYNC_ANIM_FINISH));
    }

    /**
     * 结束线程池并且停止service 发送中断Eventbus
     */
    private void syncStop(long beginTime) {
        stopSync(beginTime);
        EventBus.getDefault().postSticky(new NoticeServiceIsStopEvent(EventType.SYNC_IS_STOP));
    }

    /**
     * 停止Service
     */
    private void stopSync(long beginTime) {
        EventBus.getDefault().post(new StopSyncTimeCountEvent(EventType.STOP_SYNC_TIME_COUNT));
        long runTime = System.currentTimeMillis() - beginTime;
        if (runTime <= 2000) {
            try {
                Thread.sleep(2000 - runTime);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        if (ServiceUtils.isServiceRunning(SyncService.class)) {
            logI("service  stopped  by self");
            stopSelf();
            ServiceUtils.stopService(SyncService.class);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            stopForeground(true);
        } else {
            stopSelf();
        }

        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }


    public void showMessage(int resId) {
        ToastUtils.showShort(resId);
    }

    public void showMessage(String text) {
        ToastUtils.showShort(text);
    }
}

