package com.czur.cloud.model;

import java.io.Serializable;

public class PdfModel implements Serializable {

    private String id;
    private String userId;
    private String fileName;
    private long fileSize;
    private String takeOn;
    private String createOn;
    private String url;
    private String ossKey;
    private String percent;
    private String randomKey;

    private boolean isCheckBoxVisible;
    private boolean isChecked;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getTakeOn() {
        return takeOn;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getOssKey() {
        return ossKey;
    }

    public boolean isCheckBoxVisible() {
        return isCheckBoxVisible;
    }

    public void setCheckBoxVisible(boolean checkBoxVisible) {
        isCheckBoxVisible = checkBoxVisible;
    }

    public boolean isChecked() {
        return isChecked;
    }

    public void setChecked(boolean checked) {
        isChecked = checked;
    }

    public String getPercent() {
        return percent;
    }

    public long getFileSize() {
        return fileSize;
    }

    public String getRandomKey() {
        return randomKey;
    }

}
