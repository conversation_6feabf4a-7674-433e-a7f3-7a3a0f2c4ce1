package com.czur.cloud.util;


import static com.czur.czurutils.log.CZURLogUtilsKt.logD;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.LinkProperties;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;

import com.blankj.utilcode.util.ToastUtils;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.UpdateEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * 网络连接断开监听类
 */
public class NetworkStatusCallback extends ConnectivityManager.NetworkCallback {

    private static final String TAG = "NetworkStatusCallback";

    private Context context;
    private ConnectivityManager manager;
    private Network network = null;

    public NetworkStatusCallback(Context context) {
        this.context = context;
        this.manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
    }

    @Override
    public void onAvailable(Network network) {
        super.onAvailable(network);
        //调用判断 网络可用
        logD(TAG+".onAvailable.network="+network);

        isNetworkEnable(network);
    }

    @Override
    public void onLosing(Network network, int maxMsToLive) {
        super.onLosing(network, maxMsToLive);
        logD(TAG+".onLosing");
    }

    @Override
    public void onLost(Network network) {
        super.onLost(network);
        //调用判断 网络掉线
//        isNetworkEnable(network);
        logD(TAG+".onLost");
    }

    @Override
    public void onUnavailable() {
        super.onUnavailable();
    }

    @Override
    public void onCapabilitiesChanged(Network network, NetworkCapabilities networkCapabilities) {
        super.onCapabilitiesChanged(network, networkCapabilities);
//        logD(TAG+".onCapabilitiesChanged.networkCapabilities="+networkCapabilities);
    }

    @Override
    public void onLinkPropertiesChanged(Network network, LinkProperties linkProperties) {
        super.onLinkPropertiesChanged(network, linkProperties);
    }

    /**
     * 为什么每次都要判断，原因是WIFI和蜂窝网络都可用时，如果只是断掉WIFI，也会调用onLoss方法，所以改为只判断当前可用的，无论是哪个
     * @param network
     */
    private void isNetworkEnable(Network network) {
        NetworkInfo active = manager.getActiveNetworkInfo();
        boolean result = null != active && active.getState() == NetworkInfo.State.CONNECTED;
//        KLog.i("调试信息", "网络连接状态: " + result);
        //下面根据结果，发送事件或者做业务操作
        if (null != active && active.getState() == NetworkInfo.State.CONNECTED) {
//            ClusomModule.getInstance().login();
            if (this.network != network) {
                EventBus.getDefault().post(new UpdateEvent(EventType.CHECK_FORCE_NEW_VERSION));//网络切换时,检查是否有强制更新版本
                this.network = network;
            }
        } else {
            ToastUtils.showShort("当前为离线状态,不可用");
        }
    }


}
