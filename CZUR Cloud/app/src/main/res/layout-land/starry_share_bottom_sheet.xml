<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="400dp"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/starryMargin20"
    android:paddingEnd="@dimen/starryMargin20"
    android:layout_gravity="center"
    android:background="@drawable/starry_rec_10_bg_white"
    android:layout_alignParentBottom="true"
    >

    <TextView
        android:id="@+id/share_dialog_title"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginStart="@dimen/starryMargin20"
        android:layout_marginEnd="@dimen/starryMargin20"
        android:gravity="center"
        android:text="@string/starry_share_title"
        android:textColor="@color/black_22"
        android:textSize="18sp"
        android:textStyle="bold" />

    <LinearLayout
        android:id="@+id/bottom_share_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/share_dialog_title"
        android:layout_marginBottom="@dimen/starryMargin20">

        <LinearLayout
            android:id="@+id/weixin_share"
            style="@style/starry_meeting_share_ll">

            <ImageView
                android:layout_width="60dp"
                android:layout_height="80dp"
                android:background="@drawable/btn_rec_5_bg_with_gray_f0"
                android:padding="15dp"
                android:src="@mipmap/starry_share_wchat" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:textAlignment="center"
                android:text="@string/starry_share_wchat"
                android:textColor="@color/black_22" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/copy_share"
            style="@style/starry_meeting_share_ll">

            <ImageView
                android:layout_width="60dp"
                android:layout_height="80dp"
                android:background="@drawable/btn_rec_5_bg_with_gray_f0"
                android:padding="18dp"
                android:src="@mipmap/starry_share_copy" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:textAlignment="center"
                android:text="@string/starry_share_copy"
                android:textColor="@color/black_22" />

        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_below="@+id/bottom_share_ll"
        android:background="@color/starry_list_liner_color"
        />

    <TextView
        android:id="@+id/share_dialog_cancel_btn"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_below="@+id/bottom_share_ll"
        android:layout_marginStart="@dimen/starryMargin20"
        android:layout_marginEnd="@dimen/starryMargin20"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="@color/black_22"
        android:textSize="18sp" />

</RelativeLayout>
