package com.czur.cloud.ui.auramate;

import static com.czur.czurutils.log.CZURLogUtilsKt.logE;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.czur.cloud.R;
import com.czur.cloud.entity.realm.MissedCallEntity;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.RefreshMissedCallEvent;
import com.czur.cloud.model.AuraDeviceModel;
import com.czur.cloud.model.MissedCallModel;
import com.czur.cloud.netty.observer.NettyUtils;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.component.popup.CloudCommonPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.ui.home.IndexActivity;
import com.czur.cloud.ui.user.UserFeedbackActivity;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.reflect.TypeToken;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import io.realm.Realm;
import io.realm.RealmQuery;
import io.realm.RealmResults;

/**
 * Created by Yz on 2018/6/1.
 * Email：<EMAIL>
 */
public class AuraMateMenuActivity extends AuramateBaseActivity implements View.OnClickListener {
    private CloudCommonPopup commonPopup;
    private ImageView userBackBtn;
    private RelativeLayout auraHomeMenuAddAuraHomeRl;
    private RelativeLayout auraHomeMenuDeleteRl;
    private HttpManager httpManager;
    private UserPreferences userPreferences;
    private TextView userTitle;
    private RelativeLayout auraHomeMyPdfRl;
    private RelativeLayout auraHomeMissedVideoRl;
    private ImageView missedRedPoint;
    private RelativeLayout auraMateMenuAdviceRl;
    private RelativeLayout auraMateMenuQuestionRl;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.white);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_aura_menu_home);
        initComponent();
        registerEvent();
    }

    @Override
    protected boolean PCNeedFinish() {
        return false;
    }


    private void initComponent() {
        userPreferences = UserPreferences.getInstance(this);
        httpManager = HttpManager.getInstance();
        userBackBtn = (ImageView) findViewById(R.id.user_back_btn);
        auraHomeMissedVideoRl = (RelativeLayout) findViewById(R.id.aura_home_missed_video_rl);
        missedRedPoint = (ImageView) findViewById(R.id.missed_red_point);
        auraHomeMenuAddAuraHomeRl = (RelativeLayout) findViewById(R.id.aura_home_menu_add_aura_home_rl);
        auraHomeMenuDeleteRl = (RelativeLayout) findViewById(R.id.aura_home_menu_delete_rl);
        userTitle = (TextView) findViewById(R.id.user_title);
        userTitle.setText(R.string.more);
        auraHomeMyPdfRl = (RelativeLayout) findViewById(R.id.aura_home_my_pdf_rl);
        auraMateMenuAdviceRl = (RelativeLayout) findViewById(R.id.aura_mate_menu_advice_rl);
        auraMateMenuQuestionRl = (RelativeLayout) findViewById(R.id.aura_mate_menu_question_rl);
        getCallAndSetup();
    }


    private void registerEvent() {
        auraMateMenuAdviceRl.setOnClickListener(this);
        auraMateMenuQuestionRl.setOnClickListener(this);
        auraHomeMyPdfRl.setOnClickListener(this);
        userBackBtn.setOnClickListener(this);
        auraHomeMenuDeleteRl.setOnClickListener(this);
        auraHomeMenuAddAuraHomeRl.setOnClickListener(this);
        auraHomeMissedVideoRl.setOnClickListener(this);
        setNetListener();
    }

    private void getCallAndSetup() {
        ThreadUtils.executeByIo(new ThreadUtils.SimpleTask<String>() {
            @Override
            public String doInBackground() throws Throwable {
                String time = getServerTimeSync();
                try (Realm realm = Realm.getDefaultInstance()) {
                    List<MissedCallModel> callList = getMissedCallList();
                    if (Validator.isNotEmpty(callList)) {
                        realm.executeTransaction(new Realm.Transaction() {
                            @Override
                            public void execute(Realm realm) {
                                for (MissedCallModel callModel : callList) {
                                    MissedCallEntity sameEntity = realm.where(MissedCallEntity.class).equalTo("id", callModel.getId()).findFirst();
                                    if (sameEntity == null) {
                                        MissedCallEntity object = realm.createObject(MissedCallEntity.class, callModel.getId());
                                        object.setCallId(callModel.getCallId());
                                        object.setCreateTime(callModel.getCreateTime());
                                        object.setDirection(callModel.getDirection());
                                        object.setOwnerType(callModel.getOwnerType());
                                        object.setStatus(callModel.getStatus());
                                        object.setUdid(callModel.getUdid());
                                        object.setEquipmentUuid(callModel.getEquipmentUuid());
                                        object.setDeviceName(callModel.getDeviceName());
                                        object.setUserId(callModel.getUserId());
                                        object.setHaveRead(0);
                                    }

                                }

                                List<AuraDeviceModel> auraMateList = getAuraMateList();
                                if (Validator.isNotEmpty(auraMateList)) {
                                    RealmQuery<MissedCallEntity> realmQuery = realm.where(MissedCallEntity.class);
                                    for (AuraDeviceModel auraDeviceModel : auraMateList) {
                                        realmQuery.notEqualTo("equipmentUuid", auraDeviceModel.getEquipmentUID());
                                    }
                                    realmQuery.findAll().deleteAllFromRealm();
                                }

                            }
                        });

                    }
                }
                return time;
            }

            @Override
            public void onSuccess(String result) {
                Realm realm = Realm.getDefaultInstance();
                RealmResults<MissedCallEntity> entities = realm.where(MissedCallEntity.class).equalTo("haveRead", 0).findAll();
                if (entities.size() > 0) {
                    missedRedPoint.setVisibility(View.VISIBLE);
                } else {
                    missedRedPoint.setVisibility(View.GONE);
                }
                userPreferences.setCallTime(result);
                realm.close();
            }

            @Override
            public void onFail(Throwable t) {
                super.onFail(t);
            }
        });
    }

    private List<MissedCallModel> getMissedCallList() {
        try {
            logI("用" + userPreferences.getCallTime() + "获取未接来电");
            final MiaoHttpEntity<MissedCallModel> reportEntity = httpManager.request().getMissedCallSync(userPreferences.getUserId(), userPreferences.getCallTime(), new TypeToken<List<MissedCallModel>>() {
            }.getType());
            if (reportEntity == null) {
                return null;
            }
            if (reportEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return reportEntity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private List<AuraDeviceModel> getAuraMateList() {
        try {
            final MiaoHttpEntity<AuraDeviceModel> entity = httpManager.request().getAuraDevicesSync(userPreferences.getUserId(), new TypeToken<List<AuraDeviceModel>>() {
            }.getType());
            if (entity == null) {
                return null;
            }
            if (entity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                return entity.getBodyList();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private String getServerTimeSync() {
        try {
            MiaoHttpEntity<String> serverTimeEntity = httpManager.request().getServerTime(
                    userPreferences.getUserId(), String.class);
            if (serverTimeEntity.getCode() == MiaoHttpManager.STATUS_SUCCESS) {
                String serverTime = serverTimeEntity.getBody();
                return serverTime;
            } else {
                return null;
            }

        } catch (Exception e) {
            logE(e.toString());
            e.printStackTrace();
        }

        return null;

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.aura_home_missed_video_rl:
                EventBus.getDefault().post(new RefreshMissedCallEvent(EventType.REFRESH_MISSED_CALL));
                missedRedPoint.setVisibility(View.GONE);
                Intent intent = new Intent(this, AuraMateMissedCallActivity.class);
                ActivityUtils.startActivity(intent);
                break;
            case R.id.user_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.aura_home_menu_delete_rl:
                showConfirmDeleteDialog();
                break;
            case R.id.aura_home_menu_add_aura_home_rl:
                Intent intent3 = new Intent(this, AuraMateWifiHistoryActivity.class);
                intent3.putExtra("noNeedKey", false);
                ActivityUtils.startActivity(intent3);
                break;
            case R.id.aura_home_my_pdf_rl:
                ActivityUtils.startActivity(AuraMatePdfActivity.class);
                break;
            case R.id.aura_mate_menu_advice_rl:
                Intent intent2 = new Intent(AuraMateMenuActivity.this, UserFeedbackActivity.class);
                intent2.putExtra("isQuestion", false);
                intent2.putExtra("type", 3);
                intent2.putExtra("isFromAuraMate", true);
                ActivityUtils.startActivity(intent2);
                break;
            case R.id.aura_mate_menu_question_rl:
                Intent intent1 = new Intent(AuraMateMenuActivity.this, UserFeedbackActivity.class);
                intent1.putExtra("isQuestion", true);
                intent1.putExtra("type", 3);
                intent1.putExtra("isFromAuraMate", true);
                ActivityUtils.startActivity(intent1);
                break;
            default:
                break;
        }
    }

    /**
     * @des:确认是否删除ET
     * @params:
     * @return:
     */

    private void showConfirmDeleteDialog() {
        CloudCommonPopup.Builder builder = new CloudCommonPopup.Builder(AuraMateMenuActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getResources().getString(R.string.prompt));
        builder.setMessage(getResources().getString(R.string.remove_aura_mate_confirm));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (commonPopup != null) {
                    commonPopup.dismiss();
                }
                removeEquipment(getString(R.string.AURA_HOME));
            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        commonPopup = builder.create();
        commonPopup.show();
    }

    /**
     * @des: 移除设备
     * @params:[equipName]
     * @return:void
     */
    private void removeEquipment(String equipName) {
        httpManager.request().removeEquipment(
                userPreferences.getUserId(), equipName, String.class, new MiaoHttpManager.Callback<String>() {
                    @Override
                    public void onStart() {
                        showProgressDialog();
                    }

                    @Override
                    public void onResponse(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        NettyUtils.getInstance().stopNettyService();
//                        ServiceUtils.stopService(NettyService.class);
                        userPreferences.setHasAuraMate(false);
                        showMessage(R.string.remove_success);
                        Intent intent = new Intent(AuraMateMenuActivity.this, IndexActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                        ActivityUtils.startActivity(intent);
//                        ServiceUtils.stopService(NettyService.class);
                        NettyUtils.getInstance().stopNettyService();

                    }

                    @Override
                    public void onFailure(MiaoHttpEntity<String> entity) {
                        hideProgressDialog();
                        if (entity.getCode() == MiaoHttpManager.STATUS_FAIL) {
                            showMessage(R.string.toast_internal_error);
                        } else {
                            showMessage(R.string.request_failed_alert);
                        }

                    }

                    @Override
                    public void onError(Exception e) {
                        hideProgressDialog();
                        showMessage(R.string.request_failed_alert);
                    }
                });
    }

}
