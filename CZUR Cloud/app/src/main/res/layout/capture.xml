<?xml version="1.0" encoding="UTF-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
   >

    <!-- 整体透明画布 -->
    <SurfaceView
        android:id="@+id/preview_view"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent" />

    <!-- 扫描取景框 -->
    <com.czur.cloud.ui.et.zxing.view.ViewfinderView
        android:id="@+id/viewfinder_view"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent" />
    
    <RelativeLayout
    android:layout_width="fill_parent"
    android:layout_height="50dp"
    android:layout_gravity="top"
    android:background="#99000000">
<!--
    <ImageButton
        android:id="@+id/capture_imageview_back"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_centerVertical="true"
        />-->

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:textColor="#ffffffff"
        android:textSize="20sp"
        android:text="扫一扫"/>
        <RelativeLayout
            android:id="@+id/scan_top_bar"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:background="@color/black_2a">

            <ImageView
                android:id="@+id/capture_imageview_back"
                android:layout_width="29.5dp"
                android:layout_height="36dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="9dp"
                android:padding="10dp"
                android:src="@mipmap/white_back_icon" />


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="@string/scan_title"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

        </RelativeLayout>

</RelativeLayout>

</merge>