<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">

    <include
        android:id="@+id/change_password_top_bar"
        layout="@layout/layout_user_top_bar" />

    <LinearLayout
        android:id="@+id/username_edt_ll"
        android:background="@color/gary_ff"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_below="@+id/change_password_top_bar"
        android:orientation="vertical">

        <EditText
            android:id="@+id/user_change_user_name_edt"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:textStyle="bold"
            android:layout_marginLeft="17dp"
            android:background="@null"
            android:maxLength="20"
            android:maxLines="1"
            android:hint="@string/user_user_name"
            android:textColor="@color/normal_blue"
            android:textColorHint="@color/gary_c4"
            android:textSize="15sp" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/gary_ef" />
    </LinearLayout>



    <com.czur.cloud.ui.component.ProgressButton
        android:layout_below="@+id/username_edt_ll"
        android:layout_marginTop="23dp"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="30dp"
        android:id="@+id/user_change_user_name_btn"
        android:layout_width="250dp"
        android:layout_height="40dp"
        android:background="@drawable/selector_register_btn"
        app:progress_btn_color="#ffffff"
        app:progress_btn_tv="@string/confirm_text"/>

</RelativeLayout>