package com.czur.cloud.ui.eshare.adapter;
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.czur.cloud.R
import com.czur.cloud.ui.eshare.myentity.FileBrowserEntity
import com.czur.cloud.ui.starry.utils.singleClick

class FileBrowserTabAdapter : RecyclerView.Adapter<FileBrowserTabAdapter.MyViewHolder>() {
    private val items: MutableList<FileBrowserEntity.FileEntity> = ArrayList()

    private class MyDiffCallback(
        private val oldItems: List<FileBrowserEntity.FileEntity>,
        private val newItems: List<FileBrowserEntity.FileEntity>
    ) : DiffUtil.Callback() {
        override fun getOldListSize(): Int {
            return oldItems.size
        }

        override fun getNewListSize(): Int {
            return newItems.size
        }

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldItems[oldItemPosition]
            val newItem = newItems[newItemPosition]
            // 判断两个项是否代表相同的数据对象
            return oldItem.absPath == newItem.absPath
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldItem = oldItems[oldItemPosition]
            val newItem = newItems[newItemPosition]
            // 判断两个项的内容是否相同（例如，内容是否完全相等）
//            return oldItem.absPath == newItem.absPath
//                    && oldItem.name == newItem.name
            return newItemPosition < newItems.size -2
        }
    }

    fun setData(newItems: MutableList<FileBrowserEntity.FileEntity>) {
        // 使用 DiffUtil 计算差异并更新数据集
        val diffResult = DiffUtil.calculateDiff(MyDiffCallback(items, newItems))
        items.clear()
        items.addAll(newItems)
        diffResult.dispatchUpdatesTo(this)
    }

    private var onItemClickListener:OnItemClickListener? = null
    fun setItemClickListener(onItemClickListener:OnItemClickListener){
        this.onItemClickListener = onItemClickListener
    }
    fun getData(): MutableList<FileBrowserEntity.FileEntity> {
        return items
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyViewHolder {
        // 创建 ViewHolder
        val view: View =
            LayoutInflater.from(parent.context)
                .inflate(R.layout.item_browser_file_tab_layout, parent, false)
        return MyViewHolder(view)
    }

    override fun onBindViewHolder(holder: MyViewHolder, position: Int) {
        // 绑定数据到 ViewHolder
        val item = items[position]
        holder.file_name_tv.text = item.name

        holder.file_name_tv.apply {
            text = item.name
        }

        if (position == 0){
            holder.arrow_iv.visibility = View.GONE
        }else{
            holder.arrow_iv.visibility = View.VISIBLE
        }

        if (position == items.size -1){
            // textview字体加粗
            holder.file_name_tv.paint.isFakeBoldText = true
        }else{
            holder.file_name_tv.paint.isFakeBoldText = false
        }
        holder.file_name_tv.singleClick {
            onItemClickListener?.onItemClick(item)
        }
    }

    override fun getItemCount(): Int {
        return items.size
    }

    class MyViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val file_name_tv: TextView // 文件名称
        val arrow_iv: ImageView // 文件选择框

        init {
            file_name_tv = itemView.findViewById<TextView>(R.id.file_name_tv)
            arrow_iv = itemView.findViewById<ImageView>(R.id.arrow_iv)
        }

    }

    interface OnItemClickListener {
        fun onItemClick(entity: FileBrowserEntity.FileEntity)
    }

}