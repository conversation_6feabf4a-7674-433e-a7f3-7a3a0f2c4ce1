package com.czur.cloud.ui.starry.meeting

import android.Manifest
import android.app.KeyguardManager
import android.app.NotificationManager
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.view.Window
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.*
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.event.*
import com.czur.cloud.netty.bean.StarryRecivedNoticeData
import com.czur.cloud.netty.bean.StarryRecivedReply
import com.czur.cloud.netty.core.CZURTcpClient
import com.czur.cloud.netty.observer.NettyService
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.ui.eshare.ESPermissionUtils
import com.czur.cloud.ui.eshare.EShareEmptyActivity
import com.czur.cloud.ui.home.IndexActivity
import com.czur.cloud.ui.mirror.comm.FastBleConstants
import com.czur.cloud.ui.starry.activity.StarryActivity
import com.czur.cloud.ui.starry.api.StarryRepository
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.event.MeetingRecordingEvent
import com.czur.cloud.ui.starry.event.ServiceJoiningMeetingRoomEvent
import com.czur.cloud.ui.starry.livedatabus.*
import com.czur.cloud.ui.starry.meeting.agora.DisplayManager
import com.czur.cloud.ui.starry.meeting.base.BackPressedListener
import com.czur.cloud.ui.starry.meeting.base.BaseMeetingActivity
import com.czur.cloud.ui.starry.meeting.base.CZURAtyManager
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.bean.vo.InitParam
import com.czur.cloud.ui.starry.meeting.common.*
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopup
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonPopupAlert
import com.czur.cloud.ui.starry.meeting.dialog.StarryCommonToast
import com.czur.cloud.ui.starry.meeting.fragment.MeetingMainFragment
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.meeting.model.ModelManager
import com.czur.cloud.ui.starry.meeting.viewmodel.ControlBarViewModel
import com.czur.cloud.ui.starry.meeting.viewmodel.MeetingViewModel
import com.czur.cloud.ui.starry.model.MeetingRequest
import com.czur.cloud.ui.starry.model.StarryCallInModel
import com.czur.cloud.ui.starry.utils.Tools
import com.czur.cloud.ui.starry.utils.clearContent
import com.czur.cloud.ui.starry.utils.getContent
import com.czur.cloud.ui.starry.utils.isJoinMeetingText
import com.czur.cloud.ui.starry.viewmodel.RecentlyViewModel
import com.czur.cloud.ui.starry.viewmodel.StarryViewModel
import com.czur.cloud.util.AppClearUtils
import com.czur.czurutils.log.logD
import com.czur.czurutils.log.logE
import com.czur.czurutils.log.logI
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import okhttp3.Dispatcher
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*
import kotlin.concurrent.fixedRateTimer
import kotlin.concurrent.schedule


private const val TAG = "MeetingMainActivity"

class MeetingMainActivity : BaseMeetingActivity() {
    override fun getLayout() = LAYOUT_NONE  // 就是管理Fragment的, 不需要布局文件

    private var starryPopupRecording: StarryCommonPopupAlert? = null
    private var starryPopupCam: StarryCommonPopup? = null
    private var starryPopupMic: StarryCommonPopup? = null
    private var customToast: StarryCommonToast? = null

    private var timer: Timer? = null
    private var testTimer: Timer? = null

    private var stopMeetingFirstFlag = false

    private var reTryServerTimes = 0     // 等待长连接联通的次数

    private var isForegroundOpenVideo = false    //在前台时，是否是打开的摄像头的记录

    companion object {
        const val KEY_ROOM = "room"
        const val KEY_BOOT_DATA = "data"

        const val KEY_BOOT_MODEL = "ACTION_KEY_CALLIN_MODEL"
        const val KEY_BOOT_TYPE = "BOOT_TYPE"
        const val BOOT_TYPE_NEW_CALL = "newCALL"
        const val BOOT_TYPE_START = "start"     // 开始新会议
        const val BOOT_TPE_JOIN = "join"// 加入会议

        // 双重校验懒汉式实现单例模式
        val instance: MeetingMainActivity by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            MeetingMainActivity()
        }

        // 标记 MeetingMainActivity 是否 Destory 了
        var isMeetingMainActivityDestory = true

    }

    private val meetingVM: MeetingViewModel by viewModels()
    private var bootType: String = ""
    private var startMeetingJson = ""
    private var roomNo = ""
    private val meetingFragment = MeetingMainFragment()

    private val controlBarVM: ControlBarViewModel by viewModels()

    private var callInModel = StarryCallInModel()   // 来电信息

    private var lastStopForceTime = 0L    //上一次stop_force接收到的时间

    private val viewModel by lazy {
        if (StarryActivity.mainActivity == null) {
            StarryActivity.mainActivity = this
        }
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(StarryViewModel::class.java)
    }

    private val recentlyViewModel by lazy {
        ViewModelProvider(StarryActivity.mainActivity ?: this).get(RecentlyViewModel::class.java)
    }

    // 初始为竖屏画面
    private var mCurrentOrientation = Configuration.ORIENTATION_PORTRAIT

    private var lastMeetingRecordStates = false

    private val dialogQueueUtil: DialogQueueUtils by lazy {
        DialogQueueUtils()
    }

    override fun initWindow(window: Window) {
        super.initWindow(window)

        // 弹出带输入框的对话框，弹起软键盘，背景activity不抬起。
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)

        isMeetingMainActivityDestory = false

        // 设定屏幕常亮
        getWindow().setFlags(
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON,
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
        )

        val km = getSystemService(KEYGUARD_SERVICE) as KeyguardManager
        val kl = km.newKeyguardLock("unLock")
        kl.disableKeyguard()
        window.addFlags(
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON //保持屏幕长亮
                    or WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED //锁屏状态下显示
                    or WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON //打开屏幕
                    or WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD //解锁
        )
        // 解锁
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(true)
        }

        // window层级只能开启 硬件加速
        logD("开启硬件加速")
        getWindow().setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        )

        // 设置页面状态栏显示
        window.statusBarColor = resources.getColor(R.color.black)
        window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE

    }

    override fun skipAndFinish(): Boolean {
        val bootType = intent.getStringExtra(KEY_BOOT_TYPE) ?: ""
        // 华为机型在分享到微信的时候, 会重复情动MeetingMainActivity, 要防止ViewModel被初始化
        // 由于是懒加载, 所以只要不调用ViewModel就没事
        return if (bootType.isBlank()) {
            logI(TAG, "没有必要的启动参数, finish")
            true
        } else {
            super.skipAndFinish()
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (skipAndFinish) return
        val newOrientation = resources.configuration.orientation
        if (newOrientation != mCurrentOrientation) {
            // Oppo A7的问题, 正常只能点击按钮切换横竖屏
            // 在Oppo A7 上, 分享到微信时会强制回归竖屏, 导致fragment显示错误
            // 所以检测到切换了横竖屏, 就销毁对应的2个Fragment
            // 由于切换完竖屏后, 立刻Activity就进入后台了, 所以fragment不能使用动画, 否则会消不去对应的实体, 挡住点击事件
            //  直接从Activity中移除即可
            logI("StarryMeetingActivity.横竖屏切换")
            viewModel.meetingCompanyListFragment?.dismissImmediate()
            viewModel.meetingCompanyListFragment = null
            controlBarVM.memberListFragment?.dismissImmediate()
            controlBarVM.memberListFragment = null

        }
        mCurrentOrientation = newOrientation
        if (mCurrentOrientation == Configuration.ORIENTATION_PORTRAIT) {
            // If current screen is portrait
            // 设置页面状态栏显示
            window.statusBarColor = resources.getColor(R.color.black)
            window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE
            // 设置取消全屏
            window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)

        } else if (mCurrentOrientation == Configuration.ORIENTATION_LANDSCAPE) {
            //If current screen is landscape
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                // 延伸显示区域到刘海
                val lp: WindowManager.LayoutParams = window.attributes
                lp.layoutInDisplayCutoutMode =
                    WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
                window.attributes = lp
                // 设置页面全屏显示
                window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            }
            // 设置全屏
            window.setFlags(
                WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN
            )
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: BaseEvent) {
        logI("StarryMeetingActivity.onEvent=" + event.eventType)
        when (event.eventType) {
            EventType.ESHARE_JOINED_MEETING_SCAN->{
                        val intent1 =
                            Intent(this@MeetingMainActivity, EShareEmptyActivity::class.java)
                        intent1.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_MULTIPLE_TASK
                        intent1.putExtra(StarryConstants.ESHARE_EMPTY_TYPE, StarryConstants.ESHARE_EMPTY_TYPE_SCAN)
                        startActivity(intent1)
                        EventBus.getDefault().removeStickyEvent(event)


            }

//            // 快捷菜单跳转,加入会议
//            EventType.SHORTCUT_JOIN_MEET -> {
//                val defaultIntent =
//                    Intent(this@MeetingMainActivity, BlankActivityForJoinMeeting::class.java)
//                defaultIntent.putExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_TYPE, StarryConstants.STARRY_BLANK_TYPE_SHORTCUT_INMEETING)
//                ActivityUtils.startActivity(defaultIntent)
//            }

            // 会议中,pc端添加了联系人(增删改)
            EventType.STARRY_COMPANY_ADD_CONTACTS -> {
//                val newEvent = event as StarryCommonEvent
//                val data = newEvent.params
//                val model = Gson().fromJson(data, StarryRecivedNoticeData::class.java)
                launch {
                    viewModel.getEnterpriseMembers()
                }

            }

            //只在用户处理完权限申请操作,重新更新用户的音视频开启状态
            EventType.STARRY_CHANGE_CAMARA_MIC_STATUS -> {
                if (PermissionUtils.isGranted(
                        Manifest.permission.RECORD_AUDIO,
                        Manifest.permission.CAMERA
                    )
                ) {
                    //设置视频音频当前状态
                    if (StarryPreferences.getInstance().targetCamStatus == CAM_AUDIO_STATUS_OPEN) {
                        ModelManager.membersModel.selfVideoInUseLive.postValue(true)
                        meetingVM.notMuteLocalAudioOrVideo(StreamType.VIDEO, true)
                        StarryPreferences.getInstance().lastCam = true
                        StarryPreferences.getInstance().targetCamStatus = CAM_AUDIO_STATUS_NO
                    }

                    if (StarryPreferences.getInstance().targetMicStatus == CAM_AUDIO_STATUS_OPEN) {
                        ModelManager.membersModel.selfAudioInUseLive.postValue(true)
                        meetingVM.notMuteLocalAudioOrVideo(StreamType.AUDIO, true)
                        StarryPreferences.getInstance().lastMic = true
                        StarryPreferences.getInstance().targetMicStatus = CAM_AUDIO_STATUS_NO

                    }


                } else {
                    //重新设置目标改变状态都为-1(不处理) 0(关) 1(开)
                }
            }

            // starry读取剪贴板内容
            EventType.STARRY_MEETING_CLIPBOARD -> {
                getClipboardContentDelay()
            }

            // 单点登录， 在其他设备登录
            EventType.STARRY_NOTICE_OTHER_DEVICE_LOGIN -> {
                if (meetingVM.selfShareStatus.value == true) {
                    logD("自己在分享, 停止分享")
                    meetingVM.stopShareScreen(this)
                }
                // 触发一次短连接,会自动解析返回的1111
//                requestUserInfo()
            }

            //准备链接eshare投屏,需要关闭会议投屏
            EventType.ESHARE_READY_TO_CONNECT -> {
                if (meetingVM.selfShareStatus.value == true) {
                    meetingVM.stopShareScreen(this)
                }
            }

            // 用户点击屏幕
            EventType.STARRY_USER_TOUCH_SCREEN -> {
                controlBarVM.onUserOperation()
            }

            // 状态修改的成员信息
            // userUpdate
            EventType.STARRY_ROOM_USER_UPDATE -> {
                val newUserListEvent = event as StarryUserListEvent
                val bean = newUserListEvent.params
                val replyBean = bean.body.reply
                launch {
                    val userJson = Gson().toJson(replyBean.user)
                    logI("STARRY_ROOM_USER_UPDATE.userJson=${userJson}")
                    // 更新用户列表的单个用户信息
                    ModelManager.membersModel.updateUserToList(userJson)
                }
            }

            // 长时间挂机熔断处理消息
            EventType.STARRY_MEETING_CMD_STOP_FORCE -> {
                val cmdEvent = event as StarryMeetingCMDRoomEvent
                val room = cmdEvent.room
                // 判断是否是同一个room
                if (!isOneRoom(room)) {
                    return
                }

                val currentTimeMillis = System.currentTimeMillis()
                if (currentTimeMillis - lastStopForceTime > 1000) {
                    lastStopForceTime = currentTimeMillis
                } else {
                    lastStopForceTime = currentTimeMillis
                    return
                }

                meetingVM.removedStopMeeting()

                // 如果当前为横屏，需要先转成竖屏，然后退出会议！
                // 否则后面的页面会异常
                if (resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
//                    logI("MeetingMainActivity.ORIENTATION_LANDSCAPE")
                    this.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                }
                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.O) {
                    val intent = Intent(this, BlankActivityForAlert::class.java)
                    intent.putExtra(
                        StarryConstants.STARRY_BLANK_TYPE,
                        StarryConstants.STARRY_BLANK_TYPE_STOP_FORCE
                    )
                    ActivityUtils.startActivity(intent)
                }
            }

            // 退出会议，清理资源
            EventType.STARRY_MEETING_CMD_EXIT -> {
                meetingVM.onClearedAgoraManager()
            }

            // 无网络弹窗
            EventType.STARRY_SHOW_NONETWORK_ALERT -> {
                showConfirmAlertDialog()
            }

            EventType.STARRY_DISMISS_NONETWORK_ALERT -> {
                Handler(Looper.getMainLooper()).postDelayed(Runnable {
                    dismissConfirmAlertDialog()
                }, 4 * 1000)
            }

            // Starry设备上线
            EventType.STARRY_DEVICE_ONLINE -> {
                logI("StarryMeetingActivity.Starry设备上线")
                //如果是在会议进行中，需要join一下
                if (meetingVM.showMeeting.value == true) {
//                    // 加入会议，告知CZUR服务器
//                    // 并且需要同步更新一下本地的音视频状态
//                    meetingVM.starryReJoinMeetingAndUpdateStatus()
                    if (MeetingModel.isAgoraInMeeting) {
                        // 加入会议，告知CZUR服务器
                        // 并且需要同步更新一下本地的音视频状态
                        meetingVM.starryReJoinMeetingAndUpdateStatus()
                    } else {
                        logI("StarryMeetingActivity.Starry设备--上线,重新加入声网会议")
                        // 重新加入声网会议（声网被同步断开了）
                        nettySuccessAndRejoinChannel()
                        MeetingModel.isAgoraInMeeting = true
                    }

                    // 会议中，在后台，摄像头开着的，告知关闭摄像头显示
                    if (!AppUtils.isAppForeground()) {
                        isForegroundOpenVideo = ModelManager.membersModel.selfVideoInUse
                        if (isForegroundOpenVideo) {
                            // 1.App切到后台或手机锁屏，关闭摄像头显示头像
                            meetingVM.goToBackend()
                        }
                    }
                }
            }

            // Starry设备掉线
            EventType.STARRY_DEVICE_OFFLINE -> {
                logI("StarryMeetingActivity.Starry设备掉线,需要重连")
                // 长连接断开，等待10s，没有连接成功，则需要同步断开声网
                Timer().schedule(10 * 1000) {
                    // nettyService断开了 or 长连接断开了；
                    if (!ServiceUtils.isServiceRunning(NettyService::class.java)
                        ||
                        !CZURTcpClient.getInstance().isConnected.get()
                    ) {
                        meetingVM.onClearedAgoraManager()
                        logI("StarryMeetingActivity.Starry设备--掉线15s,断开声网会议")
                    }
                }
            }

            //发起会议错误，退出
            EventType.STARRY_MEETING_ERROR_EXIT -> {
                ToastUtils.showLong(R.string.starry_meeting_error_msg)
                meetingVM.removedStopMeeting()
            }

            //加入会议连接超时，退出；
            EventType.STARRY_JOIN_MEETING_FAIL -> {

                launch {
                    ToastUtils.showLong(R.string.starry_join_meeting_fail)

                    // 断开长连接，并重连
                    ServiceUtils.stopService(NettyService::class.java)
                    delay(1000)
                    EventBus.getDefault()
                        .post(StarryCommonEvent(EventType.STARRY_DEVICE_OFFLINE, ""))

                    meetingVM.stepOut()
                    meetingVM.removedStopMeeting()
                }
            }

            EventType.STARRY_ROOM_USER_LIST -> {
                val newUserListEvent = event as StarryUserListEvent
                val bean = newUserListEvent.params
                val replyBean = bean.body.reply
                launch {
                    // 加入会议时,第一次来memberlist
                    val stickyEvent = EventBus.getDefault().getStickyEvent(
                        ServiceJoiningMeetingRoomEvent::class.java
                    )
                    if (stickyEvent != null) {//长连接入会成功 声网入会
                        EventBus.getDefault().removeStickyEvent(stickyEvent)
                        meetingVM.joinChannelAgora(stickyEvent.token, stickyEvent.meetingRoomNo)
                    }
                    val someoneRecording = replyBean.metadata.someoneRecording
//                    someoneRecording = true
                    //是否在录制会议内容
                    if (lastMeetingRecordStates.not() && someoneRecording) {//关->开
                        showRecordingRemindDialog()
                        EventBus.getDefault().postSticky(MeetingRecordingEvent(true))
                        lastMeetingRecordStates = true
                        EventBus.getDefault()
                            .post(StarryCommonEvent(EventType.STARRY_MEETING_RECORDING, "open"))
                    } else if (lastMeetingRecordStates && someoneRecording.not()) {//开->关
                        EventBus.getDefault().postSticky(MeetingRecordingEvent(false))
                        lastMeetingRecordStates = false
                        EventBus.getDefault()
                            .post(StarryCommonEvent(EventType.STARRY_MEETING_RECORDING, "close"))
                    }

//                    logI("EventType.STARRY_ROOM_USER_LIST launch:${replyBean.users}")

                    meetingVM.isJoinMeetingAndMemberList = true
                    val userJson = Gson().toJson(replyBean.users)
                    // 更新用户列表
                    ModelManager.membersModel.updateUserList(userJson)
                    // 更新会议状态
                    val metadata = replyBean.metadata
                    val isLocked = metadata.locked
                    val meetingCode = metadata.meetingCode
                    val meetingPwd = metadata.meetingPassword ?: ""
                    MeetingModel.meetingCode = meetingCode
                    LiveDataBus.get().with(StarryConstants.MEETING_USER_LIST_CHANGE_CODE).value =
                        true

                    if (MeetingModel.meetingPassword != meetingPwd) {
                        MeetingModel.meetingPassword = meetingPwd
                        LiveDataBus.get().with(StarryConstants.MEETING_USER_LIST_CHANGE_PWD).value =
                            true
                    }
                    MeetingModel.updateMeetParams(isLocked)

                }
            }

            // 接收到的会议结束指令-会议结束
            EventType.STARRY_MEETING_CMD_STOP -> {
                val cmdEvent = event as StarryMeetingCMDRoomEvent
                val bean = cmdEvent.params
                val room = cmdEvent.room
                // 判断是否是同一个room
                if (!isOneRoom(room)) {
                    return
                }

                val currentTimeMillis = System.currentTimeMillis()
                if (currentTimeMillis - lastStopForceTime > 1000) {
                    lastStopForceTime = currentTimeMillis
                } else {
                    lastStopForceTime = currentTimeMillis
                    return
                }

                if (bean?.meeting_no == UserHandler.accountNo.toString()) {
                    meetingVM.removedStopMeeting()
                } else {
                    if (!stopMeetingFirstFlag) {
                        stopMeetingFirstFlag = true
                        meetingVM.notMuteLocalAudioOrVideo(StreamType.VIDEO, false)
                        meetingVM.notMuteLocalAudioOrVideo(StreamType.AUDIO, false)

                        // 如果当前为横屏，需要先转成竖屏，然后退出会议！
                        // 否则后面的页面会异常
                        if (resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
//                            logI("MeetingMainActivity.ORIENTATION_LANDSCAPE")
                            this.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                        }
                        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.O) {
                            val intent = Intent(this, BlankActivityForAlert::class.java)
                            intent.putExtra(
                                StarryConstants.STARRY_BLANK_TYPE,
                                StarryConstants.STARRY_BLANK_TYPE_STOP
                            )
                            ActivityUtils.startActivity(intent)
                        }
                        meetingVM.removedStopMeeting()
                    }
                }
            }

            // 接收到的离开会议室指令--被移除
            EventType.STARRY_MEETING_CMD_REMOVE -> {
                val cmdEvent = event as StarryMeetingCMDRoomEvent
                val room = cmdEvent.room
                // 判断是否是同一个room
                if (!isOneRoom(room)) {
                    return
                }

                // 如果当前为横屏，需要先转成竖屏，然后退出会议！
                // 否则后面的页面会异常
                if (resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
//                    logI("MeetingMainActivity.ORIENTATION_LANDSCAPE")
                    this.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                }
                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.O) {
                    val intent = Intent(this, BlankActivityForAlert::class.java)
                    intent.putExtra(
                        StarryConstants.STARRY_BLANK_TYPE,
                        StarryConstants.STARRY_BLANK_TYPE_REMOVED
                    )
                    ActivityUtils.startActivity(intent)
                }
                // 被移除时，需要把弹窗都置空，以免后续无法再弹
                if (starryPopupMic != null && starryPopupMic?.isShowing == true) {
                    dialogQueueUtil.dialogDelete(starryPopupMic)
                    starryPopupMic?.dismiss()
                    starryPopupMic = null
                }
                if (starryPopupCam != null) {
                    dialogQueueUtil.dialogDelete(starryPopupCam)
                    starryPopupCam?.dismiss()
                    starryPopupCam = null
                }
                meetingVM.removedStopMeeting()
            }

            //接收到的音频控制关指令(关闭麦克风)
            EventType.STARRY_MEETING_CMD_MUTE_AUDIO -> {
                launch {
                    delay(400)//快速点击时候,避免处理消失的速度快于显示的速度
                    val cmdEvent = event as StarryMeetingCMDRoomEvent
                    val room = cmdEvent.room
                    // 判断是否是同一个room
                    if (!isOneRoom(room)) {
                        return@launch
                    }

                    // 管理员邀请成员开启麦克风相关逻辑
                    // 只需要自动关闭弹窗即可
//                    if (starryPopupMic != null && starryPopupMic?.isShowing == true) {
                    if (starryPopupMic != null) {
                        dialogQueueUtil.dialogDelete(starryPopupMic)
                        starryPopupMic?.dismiss()
                        starryPopupMic = null
                    }

                    if (!meetingVM.isAdmin() && (meetingVM.selfAudioInUse.value == true)) {
                        ToastUtils.showLong(getString(R.string.starry_meeting_msg_audio_mute))
                        meetingVM.changeLocalVideoAudio(
                            DisplayManager.STREAM_TYPE_AUDIO,
                            ModelManager.membersModel.selfAudioInUse,
                            ModelManager.membersModel.selfVideoInUse,
                            true
                        )
                        ModelManager.membersModel.selfAudioInUseLive.value = false
                    }
                }

            }

            //接收到的音频控制开指令
            EventType.STARRY_MEETING_CMD_OPEN_AUDIO -> {
                val cmdEvent = event as StarryMeetingCMDRoomEvent
                val room = cmdEvent.room
                // 判断是否是同一个room
                if (!isOneRoom(room)) {
                    return
                }

                if (!meetingVM.isAdmin()) {
                    showConfirmAudioChangeOnDialog()
                }
            }

            //接收到的视频控制关指令(关闭视频)
            EventType.STARRY_MEETING_CMD_MUTE_VIDEO -> {
                val cmdEvent = event as StarryMeetingCMDRoomEvent
                val room = cmdEvent.room
                // 判断是否是同一个room
                if (!isOneRoom(room)) {
                    return
                }
                if (!meetingVM.isAdmin()) {
                    ToastUtils.showLong(getString(R.string.starry_meeting_msg_video_mute))
                    meetingVM.changeLocalVideoAudio(
                        DisplayManager.STREAM_TYPE_VIDEO,
                        ModelManager.membersModel.selfAudioInUse,
                        ModelManager.membersModel.selfVideoInUse,
                        true
                    )
                    ModelManager.membersModel.selfVideoInUseLive.value = false
                }
            }
            //接收到的视频控制开指令
            EventType.STARRY_MEETING_CMD_OPEN_VIDEO -> {
                val cmdEvent = event as StarryMeetingCMDRoomEvent
                val room = cmdEvent.room

                // 判断是否是同一个room
                if (!isOneRoom(room)) {
                    return
                }
                if (!meetingVM.isAdmin()) {
                    showConfirmVideoChangeOnDialog()
                }
            }

            //接收到的成为会议管理员指令
            EventType.STARRY_MEETING_CMD_HOST -> {
                LiveDataBus.get().with(StarryConstants.MEETING_BE_ADMIN).value = true
            }

            //接收到的开始屏幕分享指令
            EventType.STARRY_MEETING_CMD_SHARE -> {
                val cmdEvent = event as StarryMeetingCMDRoomEvent
                val replyBean = cmdEvent.params
                val room = cmdEvent.room
                // 判断是否是同一个room
                if (!isOneRoom(room)) {
                    return
                }
                showShareingScreen(replyBean)
            }

            EventType.STARRY_MEETING_CMD_STOPSHARE -> {
                val cmdEvent = event as StarryMeetingCMDRoomEvent
                val replyBean = cmdEvent.params
                val room = cmdEvent.room
                // 判断是否是同一个room
                if (!isOneRoom(room)) {
                    return
                }
                stopShareingScreen(replyBean)

            }

            EventType.STARRY_MEETING_CMD_QUITSHARE -> {
                val cmdEvent = event as StarryMeetingCMDRoomEvent
                val replyBean = cmdEvent.params
                val room = cmdEvent.room
                // 判断是否是同一个room
                if (!isOneRoom(room)) {
                    return
                }
                stopShareingSelfScreen(replyBean)
            }

            //其他端加入会议时收到的消息
            EventType.STARRY_MEETING_CMD_OTHER_JOINED -> {
                val cmdEvent = event as StarryMeetingCMDRoomEvent
                val replyBean = cmdEvent.params
                val room = cmdEvent.room
                // 判断是否是同一个room
                if (!isOneRoom(room)) {
                    return
                }
                otherJoinedMeeting(replyBean)
            }

            //多端只能加入一个会议
            EventType.STARRY_MEETING_CMD_OTHER_MEETING_JOINED -> {
                val cmdEvent = event as StarryMeetingCMDRoomEvent
                val replyBean = cmdEvent.params
                val room = cmdEvent.room
                // 判断是否是同一个room
                if (!isOneRoom(room)) {
                    return
                }
                otherMeetingJoined(replyBean)
            }

            //呼叫中,来电邀请处理
            EventType.STARRY_MESSAGE_CALL_VIDEO_CALLING_MEETING -> {
                // 暂不处理
                logI("StarryMeetingActivity.STARRY_MESSAGE_CALL_VIDEO_CALLING_MEETING.暂不处理")
            }

            //会议中,来电邀请处理
            EventType.STARRY_MESSAGE_CALL_VIDEO_IN_MEETING -> {
                val eventCallIn = event as StarryCommonEvent
                val nicknameFrom = eventCallIn.params
                val t = Timer()
                val builder = StarryCommonToast.Builder(this)
                builder.setMessage(getString(R.string.starry_recall_msg, nicknameFrom))
                    .setOnPositiveListener { dialog, _ ->
                        dialog.dismiss()
                        t.cancel()
                    }
                customToast = builder.create()
                customToast?.show()

                t.schedule(object : TimerTask() {
                    override fun run() {
                        if (customToast?.isShowing == true) {
                            customToast?.dismiss()
                        }
                        t.cancel()
                    }
                }, (10 * 1000).toLong())
            }
            //中兴手机会议中听不到音频
            EventType.STARRY_PLAYOUT_WARN -> {
                meetingVM.needRejoinMeeting = true
                logI("中兴_STARRY_PLAYOUT_WARN")
                zhongxingReJoinMeeting()
            }

            // 意外断网后，声网自动重新入会成功后回调，通知服务器重新入会成功
            EventType.STARRY_REJOIN_SUCCESS -> {
                logI("STARRY_REJOIN_SUCCESS")
                reTryServerTimes = 0
                reTrySendJoinCMD()
            }

            else -> {
            }
        }


    }

    // 等待长连接联通,然后发送join
    private fun reTrySendJoinCMD() {
        logI("${TAG}.reTrySendJoinCMDreTrySendJoinCMD.isConnected=${CZURTcpClient.getInstance().isConnected.get()};reTryServerTimes=${reTryServerTimes}")

        //  如果会议已经结束了，就不要发送了
        if (meetingVM.finish.value == true) {
            return
        }
        // 防止 activity 已经destory了，还绘制UI
        if (isMeetingMainActivityDestory) {
            return
        }

        //如果是在会议进行中，需要join一下
        reTryServerTimes++
        if (reTryServerTimes >= 60) {

            // Bug #19260
            //【视频会议】15方视频会议中，安卓端被管理员移除会议后，仍为在会状态，并且会议中各端能听到说话
            if (NetworkUtils.isConnected()) {
                // 没连上，断开重连
                AppClearUtils.reStartNettyStarryService()
                Thread.sleep(2000)
                // 重新告知 告知CZUR服务器 加入会议
                EventBus.getDefault().post(StarryCommonEvent(EventType.STARRY_REJOIN_SUCCESS, ""))
            }
            return
        }

        if (CZURTcpClient.getInstance().isConnected.get()) {
            if (meetingVM.finish.value == true) {
                return
            }
            if (meetingVM.showMeeting.value == true) {
                // 加入会议，告知CZUR服务器
                // 并且需要同步更新一下本地的音视频状态
                meetingVM.starryReJoinMeetingAndUpdateStatus()
            } else {
                meetingVM.onRejoinChannel()
            }
        } else {
            Handler().postDelayed(kotlinx.coroutines.Runnable {
                reTrySendJoinCMD()
            }, 1000)
        }
    }

    private fun isOneRoom(room: String): Boolean {
        return room == MeetingModel.room
    }

    // 显示 他人的 屏幕分享
    private fun showShareingScreen(replyBean: StarryRecivedReply) {
        meetingVM.settingOtherShare(true, replyBean.share_stream)
        // 跳转首页
        meetingVM.doubleClickUid.postValue(replyBean.share_stream)
    }

    // 关闭 他人的 屏幕分享
    private fun stopShareingScreen(replyBean: StarryRecivedReply) {
        if (meetingVM.displayUidsMainLive.value?.size ?: 0 == 1
            && !MeetingModel.isQuitShare
        ) {
            /**
             * 为了避免双人会议,另一个人正在屏幕分享中,直接退出会议以后,结束分享的推送晚于成员变动的推送
             * ,导致屏幕小窗在下一次长链接推送会出现当前会议人出现在小屏幕,5秒后恢复的情况
             */
            return
        } else {
            meetingVM.settingOtherShare(false)
        }

        // 停止分享后，是否自己需要分享
        if (MeetingModel.isQuitShare) {
            MeetingModel.isQuitShare = false
            // 开始分享
            meetingVM.startShareScreen(this)
        }
    }

    // 关闭 被他人关闭屏幕分享
    private fun stopShareingSelfScreen(replyBean: StarryRecivedReply) {
        // 如果当前为横屏，需要先转成竖屏，然后退出会议！
        // 否则后面的页面会异常
        var intent: Intent? = null
        if (MeetingModel.currentOrientationStatus == Configuration.ORIENTATION_LANDSCAPE) {
            intent = Intent(this, BlankActivityForAlertLand.instance::class.java)
        } else {
            intent = Intent(this, BlankActivityForAlert::class.java)
        }
        intent.putExtra(
            StarryConstants.STARRY_BLANK_TYPE,
            StarryConstants.STARRY_BLANK_TYPE_QUITSHARE
        )
        ActivityUtils.startActivity(intent)
        meetingVM.stopShareScreen(this)
    }

    // 其他端加入会议时收到的消息
    private fun otherJoinedMeeting(replyBean: StarryRecivedReply) {

        // 它端入会，标记一下，方便后续入会的判断
        MeetingModel.isPCEnter = true

        meetingVM.removedStopMeeting()
        // 如果当前为横屏，需要先转成竖屏，然后退出会议！
        // 否则后面的页面会异常
        if (resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            logI("MeetingMainActivity.ORIENTATION_LANDSCAPE")
            this.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
        val intent = Intent(this, BlankActivityForAlert::class.java)
        intent.putExtra(
            StarryConstants.STARRY_BLANK_TYPE,
            StarryConstants.STARRY_BLANK_TYPE_OTHER_JOIN
        )
        ActivityUtils.startActivity(intent)
        logI("${TAG}.otherJoinedMeeting:${getString(R.string.starry_meeting_other_joined)}")
    }

    // 多端只能加入一个会议
    private fun otherMeetingJoined(replyBean: StarryRecivedReply) {
        meetingVM.stepOut()

        // 如果当前为横屏，需要先转成竖屏，然后退出会议！
        // 否则后面的页面会异常
        if (resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            logI("MeetingMainActivity.ORIENTATION_LANDSCAPE")
            this.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
        val intent = Intent(this, BlankActivityForAlert::class.java)
        intent.putExtra(
            StarryConstants.STARRY_BLANK_TYPE,
            StarryConstants.STARRY_BLANK_TYPE_OTHER_JOIN
        )
        ActivityUtils.startActivity(intent)
        logI("${TAG}.otherMeetingJoined:${getString(R.string.starry_meeting_other_joined)}")


    }

    override fun handlePreIntent(preIntent: Intent) {
        super.handlePreIntent(preIntent)
        bootType = preIntent.getStringExtra(KEY_BOOT_TYPE) ?: ""
        when (bootType) {
            BOOT_TYPE_NEW_CALL -> roomNo = preIntent.getStringExtra(KEY_ROOM) ?: ""
            BOOT_TYPE_START -> startMeetingJson = preIntent.getStringExtra(KEY_BOOT_DATA) ?: ""
            BOOT_TPE_JOIN -> roomNo = preIntent.getStringExtra(MEETING_KEY_ROOM) ?: ""
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        logI("正在开会中, 不响应该事件")
    }

    override fun onDestroy() {
        isMeetingMainActivityDestory = true

        if (skipAndFinish) {
            super.onDestroy()
            return
        }

        if (testTimer != null) {
            testTimer?.cancel()
            testTimer = null
        }
        CZURAtyManager.onActivityDestroyed(this)
        MeetingModel.meetingActivityIsRunning = false
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this)
        }

        if (customToast != null) {
            customToast?.dismiss()
            customToast = null
        }

        MeetingModel.isInMeeting.postValue(false)
        if (MeetingModel.shareMode && meetingVM.selfShareStatus.value == true) {//账号被顶掉的情况,需要判断一下进行停止
            meetingVM.stopShareScreen(this)
        }

        Thread.sleep(200)
        super.onDestroy()
    }

    override fun initViews() {
        super.initViews()
        logI("MeetingMainActivity.initViews")

        // 初始化会议中的密码刷新 记录值
        viewModel.inMeetingResetPwd = StarryPreferences.getInstance().lastPwd

        MeetingModel.meetingActivityIsRunning = true
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }

        if (!CZURAtyManager.hasActivity(MeetingMainActivity::class.java.simpleName)) {
            CZURAtyManager.onActivityCreated(this, null)
        }

        // 先判断一下viewmodel的用户信息是否为空，则需要从服务器获取一次数据
        if (viewModel.userInfo.value == null) {
//            logI("MeetingMainActivity.initViews.viewModel.userInfo.value=${viewModel.userInfo.value}")
            viewModel.getDataFromServer()
        }

        meetingVM.finish.observe(this) {
            if (it) {
                logD("会议结束, 关闭页面")

                MeetingModel.currentOrientationStatus = Configuration.ORIENTATION_PORTRAIT

                if (meetingVM.selfShareStatus.value == true) {
                    logD("自己在分享, 停止分享")
                    meetingVM.stopShareScreen(this)
                }

                //*****************/
                //后台在成员分享中被移除后,调用stopshare不好用,分享锁依然被占用,问题后台在查
                //ios好用的原因是因为调用了exit,后台会自动判断,解除掉分享锁
                //现在stopshare也可以自动判断在会议中的状态,后台处理
                //增加此代码块,保持和ios相同

                // Bug #19180 【视频会议】安卓端离开会议，左下角显示两条 离开会议 消息
                // 安卓端退出会议时发送了两条退出指令，需要安卓端处理，新版需求退出和加入全是后端转发消息来定的。
//                val cmd = MeetingCMD.EXIT.cmd
//                val meetingNo = UserHandler.accountNo.toString() ?: "0"
//                MsgProcessor.commonMeetCMD(cmd, meetingNo)
                //*****************/

                meetingVM.onClearedAgoraManager()
                // 需要先关闭成员列表和chat弹出界面
//                controlBarVM.memberListFragment?.dismiss()
//                controlBarVM.chartFragment?.dismiss()

                // 记录自己的音视频按钮状态
                val isOpenCam = ModelManager.membersModel.selfVideoInUseLive.value ?: false
                val isOpenMic = ModelManager.membersModel.selfAudioInUseLive.value ?: false
                StarryPreferences.getInstance()?.lastCam = isOpenCam
                StarryPreferences.getInstance()?.lastMic = isOpenMic

                // 标记呼叫结束，进入会议
                MeetingModel.isCallingOutMeeting = false
                MeetingModel.isCallingInMeeting = false
                MeetingModel.isInMeeting.postValue(false)
                MeetingModel.isCallingInMeetingFlag.postValue(false)
// 离开会议，通知刷新最新列表
                EventBus.getDefault()
                    .post(StarryCommonEvent(EventType.STARRY_MEETING_CMD_EXIT, ""))

                ActivityUtils.finishActivity(this@MeetingMainActivity)
            }
        }

        initNetListener()

        initCheckService()

        // 每次声网网络重新连接后，检查长连接
        LiveDataBus.get()
            .with(StarryConstants.MEETING_LONG_CONNECTION_CHECK, Boolean::class.java)
            .observe(this) {
                meetingVM.checkStarryServiceRunning(this@MeetingMainActivity)
            }

        // 每次声网网络连接失败，离开会议
        LiveDataBus.get()
            .with(StarryConstants.MEETING_AGORA_CONNECT_FAILED, Boolean::class.java)
            .observe(this) {
                ToastUtils.showLong(R.string.starry_join_network_fail)

                launch {
                    // 断开长连接，并重连
                    ServiceUtils.stopService(NettyService::class.java)
                    delay(1000)
                    EventBus.getDefault()
                        .post(StarryCommonEvent(EventType.STARRY_DEVICE_OFFLINE, ""))

                    // 离开会议
                    meetingVM.stepOut()
                }

            }


        // 本地视频启动失败, 重新进入会议
        LiveDataBus.get()
            .with(StarryConstants.STARRY_LOCAL_VIDEO_STREAM_STATE_FAILED, Boolean::class.java)
            .observe(this) {
                logI("${TAG}.STARRY_LOCAL_VIDEO_STREAM_STATE_FAILED")

                // 关闭视频，重新打开视频
                onReOpenVideo()
            }

        LiveDataBus.get()
            .with(StarryConstants.STARRY_LOCAL_AUDIO_STREAM_STATE_FAILED, Boolean::class.java)
            .observe(this) {
                logI("${TAG}.STARRY_LOCAL_AUDIO_STREAM_STATE_FAILED")

                // 关闭视频，重新打开视频
                onReOpenAudio()
            }

        // 清除会议推送通知消息
        val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.cancelAll()

        // 如果有正在进行会议复制信息如何显示
        LiveDataBus.get()
            .with(StarryConstants.MEETING_REJOIN_FROM_COPY, String::class.java)
            .observe(this) {
                if (!IndexActivity.isJumpToApp) {
                    val keyUrl = BuildConfig.SHARE_STARRY_URL
                    val meetUUID = Tools.getMeetingNameFromClipboard(it, keyUrl).trim()
                    checkMeetingCode(meetUUID, it, StarryConstants.STARRY_BLANK_TYPE_COPY)
                }
            }

        // 如果有正在进行会议点击页面跳转如何显示
        LiveDataBus.get()
            .with(StarryConstants.MEETING_REJOIN_FROM_WEB, String::class.java)
            .observe(this) {
                IndexActivity.isJumpToApp = true
                checkMeetingCode(it, "", StarryConstants.STARRY_BLANK_TYPE_WEB)
            }

        // 如果有正在进行会议，断开当前会议
        LiveDataBus.get()
            .with(StarryConstants.MEETING_REJOIN_STOP_FROM_WEB, Boolean::class.java)
            .observe(this) {
                onJoinMeeting()
            }

        viewModel.checkShowMeetingRemindDialog()


        // CONNECTION_CHANGED_INVALID_TOKEN(8): 生成的 Token 无效
        LiveDataBus.get()
            .with(StarryConstants.MEETING_AGORA_CONNECT_INVALID_TOKEN, Boolean::class.java)
            .observe(this) {
                meetingVM.needRejoinMeeting = true
                meetingVM.needRejoinMeetingOnce = false
                logI("MEETING_AGORA_CONNECT_INVALID_TOKEN:全部小头像了")
                zhongxingReJoinMeeting()
            }

    }

    private fun onReOpenAudio() {
        meetingVM.enableLocalAudio(false)
        Thread.sleep(200)
        meetingVM.enableLocalAudio(true)
    }

    private fun onReOpenVideo() {
        meetingVM.enableLocalVideo(false)
        Thread.sleep(200)
        meetingVM.enableLocalVideo(true)
    }


    private fun initCheckService() {
        val checkTask: TimerTask = object : TimerTask() {
            override fun run() {
                try {
                    meetingVM.checkStarryServiceRunning(this@MeetingMainActivity)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        Timer().schedule(
            checkTask,
            0,
            (FastBleConstants.RUN_DELAY_TIMES1000 * 60 * 5).toLong()
        )
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        recentlyViewModel.reloadCounts = 0
        logD("initData:type-${bootType}")
        when (bootType) {

            BOOT_TYPE_NEW_CALL, BOOT_TPE_JOIN -> {
                callInModel = intent.getSerializableExtra(KEY_BOOT_MODEL) as StarryCallInModel
                logI("MeetingMainActivity.initData.BOOT_TYPE_NEW_CALL.callInModel=${callInModel}")
                meetingVM.callInModel = callInModel
                roomNo = callInModel.room ?: ""
                MeetingModel.roomTitle = callInModel.room_name

                meetingVM.initData(
                    InitParam.byRoom(roomNo),
                    initListener = ::bootMeetingMain
                )
            }

            BOOT_TYPE_START -> {

                // 标记开始呼叫
                MeetingModel.isCallingOutMeeting = true

                val isMic = StarryPreferences.getInstance()?.lastMic ?: true
                val isCam = StarryPreferences.getInstance()?.lastCam ?: false
                ModelManager.membersModel.selfVideoInUseLive.value = isCam
                ModelManager.membersModel.selfAudioInUseLive.value = isMic

                callInModel.apply {
                    accountNo = StarryPreferences.getInstance().accountNo
                    nickname_from = ""
                    czurId = StarryPreferences.getInstance().czurId
                    headImage = ""
                    userid_from = UserPreferences.getInstance().userId
                    udid_from = UserPreferences.getInstance().udid
                    room = ""
                    isMicOpen = isMic
                    isCamOpen = isCam
                }
                meetingVM.callInModel = callInModel

                val tmp: MeetingRequest =
                    Gson().fromJson(startMeetingJson, MeetingRequest::class.java)
                MeetingModel.roomTitle = tmp.meetingName

                meetingVM.initData(
                    InitParam.byStartJson(startMeetingJson),
                    initListener = ::bootMeetingMain
                )

            }

            else -> {
                logE("不支持的bootType:${bootType}")
                finish()
                return
            }
        }

        meetingVM.networkErrorStatus.observe(this) {
            if (it && (controlBarVM.userIdle.value == true)) {
                // 开启计时器
                // startTimer()
            } else {
                // 停止计时器
                stopTimer()
            }
        }

        controlBarVM.userIdle.observe(this) {
            if (it && (meetingVM.networkErrorStatus.value == true)) {
                // 开启计时器
                // startTimer()
            } else {
                // 停止计时器
                stopTimer()
            }
        }

        ModelManager.membersModel.selfAudioInUseLive.observe(this) {
            setLastMic(it)
        }

        ModelManager.membersModel.selfVideoInUseLive.observe(this) {
            setLastCam(it)
        }

        MeetingModel.isInMeeting.observe(this) {
            logI("中兴_isInMeeting ${it}")
            zhongxingReJoinMeeting()
        }

        ModelManager.membersModel.addressBookList.observe(this) {
            logI("addressBookList===")
            meetingVM.syncSelfVideoAudio()
        }

    }

    private fun zhongxingReJoinMeeting() {
        if (meetingVM.needRejoinMeetingOnce) {
            return
        }
        if (meetingVM.needRejoinMeeting && MeetingModel.isInMeeting.value == true) {
            val count = ModelManager.membersModel.memberList.count {
                it.isInMeeting
            }
            if (count > 1) {
                meetingVM.needRejoinMeeting = false
                meetingVM.needRejoinMeetingOnce = true
                launch {
                    delay(1000)
                    //会议中至少有2人在线
                    logI("中兴离开会议一下1")
                    meetingVM.pauseExitMeeting()
                    delay(1000)
                    logI("中兴重新加入会议1")
                    meetingVM.preJoin()
                    meetingVM.joinChannelLongService(
                        MeetingModel.getTokenByJoin(callInModel.room),
                        callInModel.room
                    )
//                    meetingVM.joinChannel(
//                            MeetingModel.getTokenByJoin(callInModel.room),
//                            callInModel.room
//                    )
                }
            }
        }
    }

    private fun setLastMic(it: Boolean) {
        StarryPreferences.getInstance()?.lastMic = it
    }

    private fun setLastCam(it: Boolean) {
        StarryPreferences.getInstance()?.lastCam = it
    }

    private fun stopTimer() {
        if (timer != null) {
            timer?.cancel()
            timer = null
        }
    }

    private fun startTimer() {
        if (timer == null) {
            timer = fixedRateTimer("", false, 0, 10 * 1000L) {
                timer?.cancel()
                timer = null
                EventBus.getDefault()
                    .post(StarryCommonEvent(EventType.STARRY_SHOW_NONETWORK_ALERT, ""))
            }
        }
    }

    private fun dismissConfirmAlertDialog() {
        if (networkErrorDialog != null && networkErrorDialog?.isShowing == true) {
            networkErrorDialog?.dismiss()
            networkErrorDialog = null
        }
    }

    private var networkErrorDialog: StarryCommonPopup? = null

    // 无网络，提示框
    private fun showConfirmAlertDialog() {
        if (networkErrorDialog != null && networkErrorDialog?.isShowing == true) {
            return
        }
        networkErrorDialog = StarryCommonPopup.Builder(this)
            .setTitle(getString(R.string.starry_popupwindow_title))
            .setMessage(getString(R.string.starry_network_error_dialog_msg))
            .setPositiveTitle(getString(R.string.starry_network_error_dialog_rejoin))
            .setNegativeTitle(getString(R.string.starry_network_error_dialog_exit))
            .setOnPositiveListener { dialog, _ ->
                if (meetingVM.clickNoNetwork()) {
                    // 开启计时器
                    startTimer()
                } else {
                    stopTimer()
                    Thread.sleep(500)

//                    meetingVM.stepOut()
                    if (MeetingModel.shareMode && meetingVM.selfShareStatus.value == true) {
                        meetingVM.stopShareScreen(this@MeetingMainActivity)
                    }
                    if (meetingVM.showMeeting.value == true) {
                        // 重新加入会议，告知CZUR服务器
                        meetingVM.starryReJoinMeetingAndUpdateStatus()
                    }

                    Thread.sleep(1000)
                }
                dialog?.dismiss()

            }
            .setOnNegativeListener { dialog, _ ->
                meetingVM.stepOut()
                if (MeetingModel.shareMode && meetingVM.selfShareStatus.value == true) {
                    meetingVM.stopShareScreen(this@MeetingMainActivity)
                }
                dialog.dismiss()
            }
            .create()

        networkErrorDialog?.show()
    }

    /**
     * 启动主画面
     */
    private fun bootMeetingMain() {
        // 显示会议主画面
        supportFragmentManager.beginTransaction()
            .replace(android.R.id.content, meetingFragment)
            .commitAllowingStateLoss()
//            .commit()
    }

    // 开会时,禁用返回键,防止退出
    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {

            interceptBackPressed()

            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    /**
     * 拦截事件
     */
    private fun interceptBackPressed(): Boolean {
        val count = supportFragmentManager.fragments.count()
        val fragmentList = arrayListOf<Fragment>()
        supportFragmentManager.fragments.forEach {
            if (it is BackPressedListener) {
                fragmentList.add(0, it)
            }
        }

        supportFragmentManager.fragments.forEachIndexed { index, fragment ->
            if (fragment is BackPressedListener) {
                if (count <= 3) {
                    if (fragment.handleBackPressed()) {
                        return true
                    }
                } else {
                    if ((index == count - 1) && fragment.handleBackPressed()) {
                        return true
                    }
                }
            }
        }
        return false
    }

    //接收到的音频控制开指令
    private fun showConfirmAudioChangeOnDialog() {

//        if ((starryPopupMic != null && starryPopupMic?.isShowing == true) || ModelManager.membersModel.selfAudioInUseLive.value == true) {
        if ((starryPopupMic != null) || ModelManager.membersModel.selfAudioInUseLive.value == true) {
            return
        }

        val builder = StarryCommonPopup.Builder(this)
            .setTitle(getString(R.string.starry_popupwindow_title))
            .setMessage(getString(R.string.starry_popupwindow_msg_audio_change_on))
            .setPositiveTitle(getString(R.string.starry_popupwindow_msg_audio_ok))
            .setNegativeTitle(getString(R.string.starry_popupwindow_msg_audio_cancel))
            .setOnPositiveListener { dialog, _ ->
                dialog?.dismiss()
//                if (ESPermissionUtils.checkVideoAndAudioPermission()) {
//                    meetingVM.changeLocalVideoAudio(
//                        DisplayManager.STREAM_TYPE_AUDIO,
//                        ModelManager.membersModel.selfAudioInUse,
//                        ModelManager.membersModel.selfVideoInUse,
//                        false
//                    )
//
//                } else {
//                    meetingVM.notMuteLocalAudioOrVideo(StreamType.AUDIO, true)
//                }
                if (ESPermissionUtils.checkVideoAndAudioPermission()) {
                    val flag = !ModelManager.membersModel.selfAudioInUse
                    meetingVM.switchSelfAudio(flag)
                    meetingVM.notMuteLocalAudioOrVideo(StreamType.AUDIO, flag)
                    ModelManager.membersModel.selfAudioInUseLive.value = flag

                    // 同步需要更新列表的音视频开关
                    ModelManager.membersModel.updateSelfMemberListStatus()
                } else {
                    meetingVM.switchSelfAudio(false)
                    meetingVM.notMuteLocalAudioOrVideo(StreamType.AUDIO, false)
                }
                starryPopupMic = null
            }
            .setOnNegativeListener { dialog, _ ->
                dialog.dismiss()
                starryPopupMic = null
            }.setOnDismissListener {
                starryPopupMic = null
            }
        starryPopupMic = builder.create()
        dialogQueueUtil.addDialog(starryPopupMic)
        dialogQueueUtil.show()
    }

    // 接收到的视频控制开指令
    private fun showConfirmVideoChangeOnDialog() {

        if (starryPopupCam != null) {
            return
        }

        val builder = StarryCommonPopup.Builder(this)
            .setTitle(getString(R.string.starry_popupwindow_title))
            .setMessage(getString(R.string.starry_popupwindow_msg_video_change_on))
            .setPositiveTitle(getString(R.string.starry_popupwindow_msg_video_ok))
            .setNegativeTitle(getString(R.string.starry_popupwindow_msg_video_cancel))
            .setOnPositiveListener { dialog, _ ->
                dialog.dismiss()
                if (ESPermissionUtils.checkVideoAndAudioPermission()) {
                    meetingVM.changeLocalVideoAudio(
                        DisplayManager.STREAM_TYPE_VIDEO,
                        ModelManager.membersModel.selfAudioInUse,
                        ModelManager.membersModel.selfVideoInUse,
                        false
                    )
                    ModelManager.membersModel.selfVideoInUseLive.value = true
                    dialog?.dismiss()
                    starryPopupCam = null
                } else {
                    meetingVM.notMuteLocalAudioOrVideo(StreamType.VIDEO, true)
                }

            }
            .setOnNegativeListener { dialog, _ ->
                dialog.dismiss()
                starryPopupCam = null
            }
            .setOnDismissListener {
                starryPopupCam = null
            }
        starryPopupCam = builder.create()
        dialogQueueUtil.addDialog(starryPopupCam)
        dialogQueueUtil.show()
    }

    private fun initNetListener() {
        NetworkUtils.registerNetworkStatusChangedListener(object :
            NetworkUtils.OnNetworkStatusChangedListener {
            override fun onDisconnected() {
                meetingVM.networkErrorStatus.postValue(true)
                EventBus.getDefault()
                    .post(StarryCommonEvent(EventType.STARRY_SHOW_NONETWORK_ALERT, ""))

                // 当自己正在分享时，需要断开分享
                if (meetingVM.selfShareStatus.value == true) {
                    meetingVM.stopShareScreen(this@MeetingMainActivity)
                }
            }

            override fun onConnected(networkType: NetworkUtils.NetworkType?) {
//                if (reTryServerTimes < 60) {
//                    reTryServerTimes = 59
//                }
                meetingVM.networkErrorStatus.postValue(false)
                EventBus.getDefault()
                    .post(StarryCommonEvent(EventType.STARRY_DISMISS_NONETWORK_ALERT, ""))

                // 判断一下是否在后台，是，需要告知隐藏视频流
                if (!AppUtils.isAppForeground()) {
                    isForegroundOpenVideo = ModelManager.membersModel.selfVideoInUse
                    if (isForegroundOpenVideo) {
                        // 1.App切到后台或手机锁屏，关闭摄像头显示头像
                        meetingVM.goToBackend()
                    }
                }
            }

        })
    }

    override fun onResume() {
        super.onResume()
        if (skipAndFinish) return
        if (MeetingModel.isInMeeting.value == true && !meetingVM.isStartingShare) {
            if (isForegroundOpenVideo) {
                // 1.回到会议页面，恢复视频流
                meetingVM.backToTheFrontend()
            } else {
                meetingVM.syncSelfVideoAudio()
            }
        }

        //如果麦克风和摄像头权限发生变化,进行开启处理
        if (!StarryPreferences.getInstance().lastCam && !StarryPreferences.getInstance().lastMic) {
            if (ESPermissionUtils.checkVideoAndAudioPermission()) {
                if (StarryPreferences.getInstance().targetCamStatus == CAM_AUDIO_STATUS_OPEN
                    || StarryPreferences.getInstance().targetMicStatus == CAM_AUDIO_STATUS_OPEN
                ) {
                    EventBus.getDefault()
                        .post(StarryCommonEvent(EventType.STARRY_CHANGE_CAMARA_MIC_STATUS, ""))
                }
            }
        }
    }


    // 有正在进行会议点击页面跳转或复制信息如何显示
    private fun checkMeetingCode(
        uuid: String,
        strContent: String = "",
        type: String = StarryConstants.STARRY_BLANK_TYPE_COPY
    ) {
        launch {
            val starryRepository = StarryRepository()
            val retData = starryRepository.shareMeetingInfo(uuid)
            logI("${TAG}.checkMeetingCode=${uuid}")
            logI("${TAG}.checkMeetingCode.retData=${retData}")
            val meetingCode = retData.meetingCode
            val meetingPwd = retData.meetingPassword
            // 会议中
            // 不同会议，弹窗提示：点击加入则直接加入会议离开（暂离）当前正在进行的会议。
            //已进行的相同会议：无需处理，没任何反应。
            if (meetingCode != MeetingModel.meetingCode) {
                val ret = starryRepository.joinMeeting(meetingCode, meetingPwd) ?: 0
                logI("${TAG}.joinMeeting.ret=${ret}")

                // 先断开当前会议，再开始新的会议
                val defaultIntent =
                    Intent(this@MeetingMainActivity, BlankActivityForJoinMeeting::class.java)
                defaultIntent.putExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_TYPE, type)
                defaultIntent.putExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_UUID, uuid)
                defaultIntent.putExtra(StarryConstants.STARRY_BLANK_CLIPBOARD_CONTENT, strContent)
                ActivityUtils.startActivity(defaultIntent)
            } else {
                IndexActivity.isJumpToApp = false
            }
        }
    }

    // 加入会议
    private fun onJoinMeeting() {
        logI("${TAG}.onJoinMeeting.exitMeeting")
        if (meetingVM.joinedMemberCount > 1) {
            meetingVM.pauseExitMeeting()
        } else {
            meetingVM.pauseStopMeeting()
        }
        meetingVM.finish.postValue(true)

        // StarryActivity
//        LiveDataBus.get().with(StarryConstants.MEETING_REJOIN_MEETING_FROM_WEB).value = true
        // IndexActivity
//        LiveDataBus.get().with(StarryConstants.MEETING_REJOIN_MEETING_FROM_WEB_INDEX).value = true
        EventBus.getDefault()
            .post(StarryClipboardEvent(EventType.STARRY_MEETING_REJOIN_FROM_WEB, ""))
    }

    /**
     * 获取剪切板内容
     */
    private fun getClipboardContentDelay() {
        Handler().postDelayed(
            { getClipboardContentThread() },
            1000
        )
    }

    private fun getClipboardContentThread() {
        try {
            runOnUiThread { getClipboardContent() }
        } catch (e: java.lang.Exception) {
            logE("${TAG}.getClipboardContentThread.error=${e.toString()}")
            e.printStackTrace()
        }
    }

    private fun getClipboardContent() {
        logI("${TAG}.getClipboardContent")
        // 返回数据
        val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clipData = clipboard.primaryClip
        val ret = clipData.getContent().takeIf {
            it.isJoinMeetingText()
        } ?: return

        clipboard.clearContent()

//                val keyUrl = BuildConfig.SHARE_STARRY_URL
//                val meetUUID = Tools.getMeetingNameFromClipboard(ret, keyUrl).trim()
        LiveDataBus.get().with(StarryConstants.MEETING_REJOIN_FROM_COPY).value = ret
    }

    override fun onStop() {
        super.onStop()

        if (MeetingModel.isInMeeting.value == true) {
            if (meetingVM.isStartingShare) {
                return
            }
            isForegroundOpenVideo = ModelManager.membersModel.selfVideoInUse
            if (isForegroundOpenVideo) {
                // 1.App切到后台或手机锁屏，关闭摄像头显示头像
                meetingVM.goToBackend()
            }
        }
    }


    private fun showRecordingRemindDialog() {
        if (starryPopupRecording != null && starryPopupRecording?.isShowing == true) {
            return
        }

        starryPopupRecording = StarryCommonPopupAlert.Builder(this)
            .setTitle(getString(R.string.starry_popupwindow_title))
            .setMessage(getString(R.string.starry_recording_remind))
            .setPositiveTitle(getString(R.string.starry_i_got_it))
            .setBoldMsgStyle(false)
            .setOnPositiveListener { dialog, _ ->
                dialog.dismiss()
            }
            .create()
        starryPopupRecording?.show()

    }

    // 长连接重连成功后，需要重新连接声网
    private fun nettySuccessAndRejoinChannel() {
        launch {
            delay(300)
            logI("长连接重连成功，重新加入会议.nettySuccessAndRejoinChannel")
            meetingVM.preJoin()
            meetingVM.joinChannelLongService(
                MeetingModel.getTokenByJoin(callInModel.room),
                callInModel.room
            )
        }
    }
}