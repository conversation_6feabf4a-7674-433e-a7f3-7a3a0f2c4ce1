<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="io.agora.rtc.ss">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <application>
        <activity
            android:name=".impl.ScreenCapture$ScreenCaptureAssistantActivity"
            android:process=":screensharingsvc"
            android:screenOrientation="fullUser"
            android:theme="@android:style/Theme.Translucent" />
        <service
            android:name=".impl.ScreenSharingService"
            android:foregroundServiceType="mediaProjection"
            android:process=":screensharingsvc"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.screenshare" />
            </intent-filter>
        </service>
    </application>

</manifest>
