package com.czur.cloud.netty.core;

import static com.czur.czurutils.log.CZURLogUtilsKt.logD;
import static com.czur.czurutils.log.CZURLogUtilsKt.logI;

import android.app.Activity;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Log;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.JsonUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ServiceUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.blankj.utilcode.util.Utils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.common.CZURConstants;
import com.czur.cloud.entity.realm.MessageEntity;
import com.czur.cloud.event.AuraMateCheckVideoEvent;
import com.czur.cloud.event.AuraMateOfflineEvent;
import com.czur.cloud.event.AuraMateOnlineEvent;
import com.czur.cloud.event.AuraMateOperateEvent;
import com.czur.cloud.event.AuraMateReadyUpdateEvent;
import com.czur.cloud.event.AuraMateUpdateEvent;
import com.czur.cloud.event.EventType;
import com.czur.cloud.event.GetRoomChannelEvent;
import com.czur.cloud.event.HdVideoSwitchEvent;
import com.czur.cloud.event.HdViewEvent;
import com.czur.cloud.event.HdViewSaveEvent;
import com.czur.cloud.event.HdViewSaveV2Event;
import com.czur.cloud.event.LogoutEvent;
import com.czur.cloud.event.NoIncommingCallEvent;
import com.czur.cloud.event.StarryCheckMeetingListEvent;
import com.czur.cloud.event.StarryCommonEvent;
import com.czur.cloud.event.StarryMeetingCMDEvent;
import com.czur.cloud.event.StarryMeetingCMDRoomEvent;
import com.czur.cloud.event.StarryUserListEvent;
import com.czur.cloud.event.VideoCameraEvent;
import com.czur.cloud.event.aurahome.ATBindSuccessEvent;
import com.czur.cloud.event.aurahome.ATCheckDeviceIsOnlineEvent;
import com.czur.cloud.event.aurahome.CalibrateEvent;
import com.czur.cloud.event.aurahome.VideoEvent;
import com.czur.cloud.netty.CZURMessageConstants;
import com.czur.cloud.netty.bean.PushBodyBean;
import com.czur.cloud.netty.bean.ReceiveMsgBean;
import com.czur.cloud.netty.bean.ReceivedMsgBodyBean;
import com.czur.cloud.netty.bean.RecivedObjectBean;
import com.czur.cloud.netty.bean.RecivedObjectBody;
import com.czur.cloud.netty.bean.StarryRecivedBody;
import com.czur.cloud.netty.bean.StarryRecivedMessageBean;
import com.czur.cloud.netty.bean.StarryRecivedNoticeBean;
import com.czur.cloud.netty.bean.StarryRecivedNoticeBody;
import com.czur.cloud.netty.bean.StarryRecivedReply;
import com.czur.cloud.netty.observer.CheckTimeOutService;
import com.czur.cloud.netty.observer.NettyUtils;
import com.czur.cloud.preferences.StarryPreferences;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.auramate.AuraMateActivity;
import com.czur.cloud.ui.auramate.AuraMatePreRemoteVideoActivity;
import com.czur.cloud.ui.auramate.AuraMateRecordActivity;
import com.czur.cloud.ui.auramate.AuraMateRemoteVideoActivity;
import com.czur.cloud.ui.home.IndexActivity;
import com.czur.cloud.ui.starry.activity.StarryCallInActivity;
import com.czur.cloud.ui.starry.meeting.model.MeetingModel;
import com.czur.cloud.ui.starry.model.StarryDoingMeetingModel;
import com.czur.cloud.ui.starry.model.StarryUserInfoModel;
import com.czur.cloud.ui.starry.utils.RomUtil;
import com.czur.cloud.util.NotifyUtil;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.Gson;

import org.apache.commons.lang3.StringEscapeUtils;
import org.greenrobot.eventbus.EventBus;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;

import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.realm.Realm;
import io.realm.RealmResults;
import io.realm.Sort;

/**
 * Created by shaojun
 */
@ChannelHandler.Sharable
public class ClientMessageHandler extends SimpleChannelInboundHandler<String> {
    private String TAG = getClass().getName();
    private Handler handler = new Handler(Looper.getMainLooper());
    private AtomicBoolean inActive = new AtomicBoolean(true);

    private static class ClientMessageHandlerInstance {
        private static final ClientMessageHandler INSTANCE = new ClientMessageHandler();
    }


    public static ClientMessageHandler getInstance() {
        return ClientMessageHandler.ClientMessageHandlerInstance.INSTANCE;
    }

    //客户端收到消息
    @Override
    protected void channelRead0(ChannelHandlerContext channelHandlerContext, String s) throws Exception {
        String receviceFromServerStr = s;
        logI("channelRead0.ReceiveMsgFromServer=" + s + " from {" + channelHandlerContext.channel() + "}.");

        ReceiveMsgBean receiveMsgBean = new Gson().fromJson(s, ReceiveMsgBean.class);
        Context context = Utils.getApp().getApplicationContext();
        String receiveType = receiveMsgBean.getType();
        switch (receiveType) {

            case CZURConstants.RECEIVED_HEARTBEAT:
                MessageProcess.sendHeartbeat(channelHandlerContext);
                break;

            case CZURConstants.RECEIVED_DONE:
//                String requestid = receiveMsgBean.getRequestid();
//                Log.d(TAG, "收到回复 -DONE-" + requestid + "///" + receiveMsgBean.getBody());
                break;
            case CZURConstants.RECEIVED_CONNECTED:
                Log.d(TAG, "上线成功 -CONNECTED-");
                CZURTcpClient.getInstance().registerAppOnline(context);
//                CZURTcpClientStarry.getInstance().registerAppOnline(context);
                EventBus.getDefault().post(new AuraMateOnlineEvent(EventType.AURA_MATE_ONLINE));
                EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_DEVICE_ONLINE, ""));
                break;
            case CZURConstants.PUSH:
                if (AppUtils.isAppForeground()) {
                    return;
                }
                PushBodyBean pushBodyBean = new Gson().fromJson(s, PushBodyBean.class);
                Intent intent0 = new Intent(context, IndexActivity.class);
                intent0.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                String extra = StringEscapeUtils.unescapeJava(pushBodyBean.getBody().getExtra());
                intent0.putExtra("msgType", JsonUtils.getString(extra, "msgType"));
                intent0.putExtra("device", JsonUtils.getString(extra, "device"));
                // Jason for DayReport
                intent0.putExtra("reportId", JsonUtils.getString(extra, "reportId"));
                intent0.putExtra("type", JsonUtils.getString(extra, "type"));
                intent0.putExtra("relationId", JsonUtils.getString(extra, "relationId"));
                int id0 = (int) SystemClock.uptimeMillis();
//                PendingIntent pIntent0 = PendingIntent.getActivity(context, id0, intent0, PendingIntent.FLAG_UPDATE_CURRENT);
                PendingIntent pIntent0;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    pIntent0 = PendingIntent.getActivity(context, id0, intent0
                            , PendingIntent.FLAG_IMMUTABLE | PendingIntent.FLAG_UPDATE_CURRENT);
                } else {
                    pIntent0 = PendingIntent.getActivity(context, id0, intent0, PendingIntent.FLAG_UPDATE_CURRENT);
                }
                int smallIcon0 = R.mipmap.small_icon;
                String ticker0 = context.getResources().getString(R.string.auramate_new_message);
                String title0 = pushBodyBean.getBody().getTitle();
                String content0 = pushBodyBean.getBody().getMsg_content();
                AppUtils.relaunchApp();
                NotifyUtil notify0 = new NotifyUtil(context, id0);
                notify0.notifyNormalSingline(pIntent0, smallIcon0, ticker0, title0, content0, true, true, false);
                break;
            case CZURConstants.BUSSINESS:
                // 先进行泛型获取，不具体获取data类型，如果是starry的
                // 接收到正在进行的会议 MESSAGE_STARRY_CHECK_MEETING_LIST("CHECK_MEETING_LIST"),
                // 先拦截，进行处理，否则，给后面流程进行正常处理（原有的处理逻辑）
                RecivedObjectBean recivedObjectBean = new Gson().fromJson(s, RecivedObjectBean.class);
                RecivedObjectBody recivedObjectBody = recivedObjectBean.getBody();
                String actionStarry = recivedObjectBody.getAction();
                if (actionStarry == null) {
                    return;
                }

                // SYNC消息，忽略,不知干啥的
                if (actionStarry.equals(CZURMessageConstants.MessageName.MESSAGE_COMMON_SYNC.getMsg())) {
                    return;
                }

                //24.接收到正在进行的会议
                if (actionStarry.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_CHECK_MEETING_LIST.getMsg())) {
                    StarryRecivedMessageBean starryReceivedBean = new Gson().fromJson(receviceFromServerStr, StarryRecivedMessageBean.class);
                    StarryRecivedBody starryBody = starryReceivedBean.getBody();
                    List<StarryDoingMeetingModel> starryData = starryBody.getData();

                    EventBus.getDefault().post(new StarryCheckMeetingListEvent(EventType.STARRY_MEETING_CMD_CHECK_MEETING_LIST, starryData));

                    return;
                }

                // action是NEW_NOTICE，
                if (actionStarry.equals(CZURMessageConstants.MessageName.MESSAGE_ENTERPRISE_NOTICE.getMsg())) {
                    String moduleStarry = recivedObjectBody.getModule();

                    // action是NEW_NOTICE，module是other_device_login
                    // 在其他设备登录
                    if (moduleStarry.equals(CZURMessageConstants.MessageName.MESSAGE_NOTICE_MODULE_OTHER_DEVICE_LOGIN.getMsg())) {
                        StarryRecivedNoticeBean starryReceivedBean = new Gson().fromJson(receviceFromServerStr, StarryRecivedNoticeBean.class);
                        StarryRecivedNoticeBody starryBody = starryReceivedBean.getBody();
                        String starryData = starryBody.getData();
                        EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_NOTICE_OTHER_DEVICE_LOGIN, starryData));

                        return;
                    }


                    // action是NEW_NOTICE，module是enterprise
                    if (moduleStarry.equals(CZURMessageConstants.MessageName.MESSAGE_NOTICE_MODULE_ENTERPRISE.getMsg())) {
                        StarryRecivedNoticeBean starryReceivedBean = new Gson().fromJson(receviceFromServerStr, StarryRecivedNoticeBean.class);
                        StarryRecivedNoticeBody starryBody = starryReceivedBean.getBody();
                        String starryData = starryBody.getData();
                        EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_COMPANY_NOTICE, starryData));

                        return;
                    }

                    // action是NEW_NOTICE，module是contacts    添加的联系人(增删改)
                    if (moduleStarry.equals(CZURMessageConstants.MessageName.MESSAGE_NOTICE_MODULE_CONTACTS.getMsg())) {
                        StarryRecivedNoticeBean starryReceivedBean = new Gson().fromJson(receviceFromServerStr, StarryRecivedNoticeBean.class);
                        StarryRecivedNoticeBody starryBody = starryReceivedBean.getBody();
                        String starryData = starryBody.getData();
                        EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_COMPANY_ADD_CONTACTS, starryData));

                        return;
                    }

                    // 管理员暂离后,会议人员全部退出,触发
                    if (moduleStarry.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_CLOSE_MEETING.getMsg())) {
                        // {"module":"CLOSE_MEETING","action":"NEW_NOTICE","room":"36180"}
                        StarryRecivedNoticeBean starryReceivedBean = new Gson().fromJson(receviceFromServerStr, StarryRecivedNoticeBean.class);
                        StarryRecivedNoticeBody starryBody = starryReceivedBean.getBody();
                        String starryData = starryBody.getData();
                        String starryRoom = starryBody.getRoom();
                        EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_CLOSE_MEETING, starryRoom));

                        return;
                    }
                }

                ReceivedMsgBodyBean receivedBean = new Gson().fromJson(s, ReceivedMsgBodyBean.class);
                ReceivedMsgBodyBean.BodyBean body = receivedBean.getBody();
                String action = body.getAction();
                String module = body.getModule();

                ReceivedMsgBodyBean.BodyBean.DataBean dataBean = body.getData();

                // MEETING会议相关
                if (action.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING.getMsg())) {
                    StarryRecivedMessageBean starryReceivedBean = new Gson().fromJson(receviceFromServerStr, StarryRecivedMessageBean.class);
                    StarryRecivedBody starryBody = starryReceivedBean.getBody();
                    String method = starryBody.getMethod();

                    if (method.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_UPDATE_PHONE_CMD.getMsg())) {
                        StarryRecivedReply replyBean = starryBody.getReply();
                        String cmd = replyBean.getCmd();
                        // 接收到更改绑定机号的指令
                        if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD_UPDATE_USER_PHONE.getMsg())) {

                            String mobile = replyBean.getMeeting_no();
                            if (!mobile.isEmpty() && (mobile.length() == 11)) {
                                StarryUserInfoModel starryUserInfoModel = StarryPreferences.getInstance().getStarryUserinfoModel();
                                starryUserInfoModel.setAccountNo(mobile);
                                starryUserInfoModel.setMobile(mobile);
                                StarryPreferences.getInstance().setStarryUserinfoModel(starryUserInfoModel);
                                UserPreferences.getInstance().setUserMobile(mobile);
                                EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_CHANGE_PHONE, mobile));
                            }
                        }

                        // 极简扫描注销发出的指令
                        if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_CMD_DE_REGISTER.getMsg())) {
                            EventBus.getDefault().post(new LogoutEvent(EventType.DE_REGISTER));
//                            String starryData = starryBody.getModule();
//                            EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_NOTICE_OTHER_DEVICE_LOGIN, starryData));
                        }

                    }

                    // 接收到 服务器的 指令 cmd
                    if (method.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD.getMsg())) {
                        StarryRecivedReply replyBean = starryBody.getReply();

                        String cmd = replyBean.getCmd();
                        String room = starryBody.getRoom();

                        // 接收到的会议结束指令
                        if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD_STOP.getMsg())) {
                            EventBus.getDefault().post(new StarryMeetingCMDRoomEvent(EventType.STARRY_MEETING_CMD_STOP, replyBean, room));
                        }
                        //接收到的离开会议室指令
                        else if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD_REMOVE.getMsg())) {
                            EventBus.getDefault().post(new StarryMeetingCMDRoomEvent(EventType.STARRY_MEETING_CMD_REMOVE, replyBean, room));
                        }
                        // 接收到的音频控制关指令
                        else if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD_MUTE_AUDIO.getMsg())) {
                            EventBus.getDefault().post(new StarryMeetingCMDRoomEvent(EventType.STARRY_MEETING_CMD_MUTE_AUDIO, replyBean, room));
                        }
                        //接收到的音频控制开指令
                        else if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD_OPEN_AUDIO.getMsg())) {
                            EventBus.getDefault().post(new StarryMeetingCMDRoomEvent(EventType.STARRY_MEETING_CMD_OPEN_AUDIO, replyBean, room));
                        }
                        //    //接收到的视频控制关指令
                        else if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD_MUTE_VIDEO.getMsg())) {
                            EventBus.getDefault().post(new StarryMeetingCMDRoomEvent(EventType.STARRY_MEETING_CMD_MUTE_VIDEO, replyBean, room));
                        }
                        //    //接收到的视频控制开指令
                        else if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD_OPEN_VIDEO.getMsg())) {
                            EventBus.getDefault().post(new StarryMeetingCMDRoomEvent(EventType.STARRY_MEETING_CMD_OPEN_VIDEO, replyBean, room));
                        }
                        //    //接收到的成为会议管理员指令
                        else if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD_HOST.getMsg())) {
                            EventBus.getDefault().post(new StarryMeetingCMDEvent(EventType.STARRY_MEETING_CMD_HOST, replyBean));
                        }
                        //    //接收到的开始屏幕分享指令
                        else if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD_SHARE.getMsg())) {
                            EventBus.getDefault().post(new StarryMeetingCMDRoomEvent(EventType.STARRY_MEETING_CMD_SHARE, replyBean, room));
                        }
                        //    //接收到的stop屏幕分享指令
                        else if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD_STOPSHARE.getMsg())) {
                            EventBus.getDefault().post(new StarryMeetingCMDRoomEvent(EventType.STARRY_MEETING_CMD_STOPSHARE, replyBean, room));
                        }
                        //    //接收到的stop屏幕分享指令
                        else if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD_QUITSHARE.getMsg())) {
                            EventBus.getDefault().post(new StarryMeetingCMDRoomEvent(EventType.STARRY_MEETING_CMD_QUITSHARE, replyBean, room));
                        }
                        //    // 其他端加入会议时收到的消息
                        else if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD_OTHER_JOINED.getMsg())) {
                            EventBus.getDefault().post(new StarryMeetingCMDRoomEvent(EventType.STARRY_MEETING_CMD_OTHER_JOINED, replyBean, room));
                        }
                        // 长时间挂机熔断处理消息
                        else if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD_STOP_FORCE.getMsg())) {
                            EventBus.getDefault().post(new StarryMeetingCMDRoomEvent(EventType.STARRY_MEETING_CMD_STOP_FORCE, replyBean, room));
                        }
                        // 多端只能加入一个会议
                        else if (cmd.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_MEETING_CMD_OTHER_MEETING_JIONED.getMsg())) {
                            EventBus.getDefault().post(new StarryMeetingCMDRoomEvent(EventType.STARRY_MEETING_CMD_OTHER_MEETING_JOINED, replyBean, room));
                        } else {

                        }

                    }
                    //roomUserList 会议成员列表
                    else if (method.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_ROOM_USER_LIST.getMsg())) {
                        StarryRecivedReply replyBean = starryBody.getReply();
                        String room = starryReceivedBean.getRoom();
                        if (room == null)
                            room = "";
                        replyBean.setRoom(room);
                        EventBus.getDefault().post(new StarryUserListEvent(EventType.STARRY_ROOM_USER_LIST, starryReceivedBean));
                    }
                    //userUpdate 状态修改的成员信息
                    else if (method.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_ROOM_USER_UPDATE.getMsg())) {
//                        StarryRecivedReply replyBean = starryBody.getReply();
//                        String room = starryReceivedBean.getRoom();
                        EventBus.getDefault().post(new StarryUserListEvent(EventType.STARRY_ROOM_USER_UPDATE, starryReceivedBean));
                    }
                } else if (action.equals(CZURMessageConstants.MessageName.MESSAGE_NO_INCOMING_CALL.getMsg())) {
                    Log.d(TAG, "没有视频请求");
                    EventBus.getDefault().post(new NoIncommingCallEvent(EventType.STARRY_NO_INCOMING_CALL));
                } else if (action.equals(CZURMessageConstants.MessageName.MESSAGE_CALL_VIDEO.getMsg())) {
                    Log.d(TAG, "收到设备视频请求");
                    UserPreferences userPreferences = UserPreferences.getInstance(context);

                    // Starry的视频会议
                    if (module != null && module.equals(CZURConstants.STARRY_MODULE)) {
                        String userid_from = body.getUserid_from();
                        String nickname_from = body.getNickname_from();
                        String room = body.getRoom();
                        String room_name = body.getRoom_name();
                        String headImage = "";
                        Boolean otherMeeting = false;
                        if (dataBean != null) {
                            headImage = dataBean.getHeadImage();
                            otherMeeting = dataBean.getOtherMeeting();
                        }
                        String udid_from = body.getUdid_from();
                        openCameraStarry(context,
                                receivedBean.getRequestid(),
                                userid_from,
                                udid_from,
                                nickname_from,
                                room,
                                room_name,
                                headImage,
                                otherMeeting
                        );
                        return;
                    }

                    if (Validator.isNotEmpty(userPreferences.getUserId()) && userPreferences.isHasAuraMate()) {
                        Activity activity = ActivityUtils.getTopActivity();
                        //占线
                        if (activity instanceof AuraMatePreRemoteVideoActivity || activity instanceof AuraMateRemoteVideoActivity || activity instanceof AuraMateRecordActivity) {
                            CZURTcpClient.getInstance().videoRequestSecond(context, body.getUdid_from(), 4, UserPreferences.getInstance(context).getUserId(), body.getCall_id());
                            return;
                        }
                        openCameraAuraMate(context, receivedBean.getRequestid(), body.getUdid_from(), false);
                    }
                } else if (action.equals(CZURMessageConstants.MessageName.MESSAGE_CALL_VIDEO_TRANSFER.getMsg())) {
                    Log.d(TAG, "收到设备视频请求(转发)");
                    UserPreferences userPreferences = UserPreferences.getInstance(context);
                    //占线
                    if (Validator.isNotEmpty(userPreferences.getUserId()) && userPreferences.isHasAuraMate()) {
                        Activity activity = ActivityUtils.getTopActivity();
                        if (activity instanceof AuraMatePreRemoteVideoActivity || activity instanceof AuraMateRemoteVideoActivity || activity instanceof AuraMateRecordActivity) {
                            CZURTcpClient.getInstance().videoRequestSecond(context, body.getUdid_from(), 4, UserPreferences.getInstance(context).getUserId(), body.getCall_id());
                            return;
                        }
                        openCameraAuraMate(context, receivedBean.getRequestid(), body.getUdid_from(), true);
                    }
                } else if (action.equals(CZURMessageConstants.MessageName.MESSAGE_AURA_MATE.getMsg())) {
                    Log.d(TAG, "发送视频请求后，收到回执");
                    if (body.getMethod().equals(CZURMessageConstants.Video.METHOD.getValue())) {
                        ReceivedMsgBodyBean.BodyBean.ReplyBean reply = body.getReply();
                        String status = reply.getStatus();
                        if (status.equals("1")) {
                            String channel = UUID.randomUUID().toString();
                            EventBus.getDefault().post(new GetRoomChannelEvent(EventType.GET_VIDEO_ROOM_CHANNEL, channel, body.getUdid_from()));
                        } else if (status.equals("4")) {
                            ToastUtils.showShort(R.string.msg_busy);
                            //如果是占线等，先在该界面提示，再关掉回到主画面
                            backToMain(2000);
                        }
                    }
                } else if (action.equals(CZURMessageConstants.MessageName.MESSAGE_STARRY_STOP_CALL.getMsg())) {
                    // 这个消息 后台没有给 room信息, 有可能导致bug
                    EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_MEETING_CMD_STOP_CALL, ""));
                } else {
                    String device_uuid = body.getDevice_uuid();
                    String message_name = "";
                    ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean device_status = null;
                    boolean switchResult = false;
                    if (dataBean != null) {
                        message_name = dataBean.getMessage_name();
                        device_status = dataBean.getDevice_status();
                    }

                    if (checkMessage(receivedBean)) {
                        switch (message_name) {
                            case CZURConstants.NO_TIME: //NoTime
                                EventBus.getDefault().post(new HdVideoSwitchEvent(EventType.SWITCH_HD_RESULT, CZURConstants.AURA_HD_VIDEO_NO_TIME));
                                break;
                            case CZURConstants.VIDEO_CAMERA_SWITCH_HD:
                                switchResult = dataBean.getSwitchResult();
                                int result = switchResult ? CZURConstants.AURA_HD_VIDEO_SWITCH_SUCCESS : CZURConstants.AURA_HD_VIDEO_SWITCH_FAULT;
                                EventBus.getDefault().post(new HdVideoSwitchEvent(EventType.SWITCH_HD_RESULT, result));
                                break;
                            case CZURConstants.BIND_SUCCESS:
                                EventBus.getDefault().post(new ATBindSuccessEvent(EventType.AURA_BIND_SUCCESS,
                                        device_uuid, device_status));
                                break;
                            case CZURConstants.CONNECT_WIFI_SUCCESS:
                                Log.d(TAG, "设备联网成功");
                                EventBus.getDefault().post(new ATBindSuccessEvent(EventType.AURA_BIND_SUCCESS,
                                        device_uuid, device_status));
                                break;
                            case CZURConstants.CHECK_DEVICE_IS_ONLINE:
                                Log.d(TAG, "APP检查设备后，设备上线");
                                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.CHECK_DEVICE_IS_ONLINE,
                                        device_uuid, device_status));
                                break;
                            case CZURConstants.DEVICE_ONLINE:
                                Log.d(TAG, "设备自己上线");
                                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.CHECK_DEVICE_IS_ONLINE,
                                        device_uuid, device_status));
                                break;
                            case CZURConstants.MODE_CHANGED:
                                Log.d(TAG, "设备PC模式和普通模式切换");
                                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.MODE_CHANGE, body.getDevice_uuid(), device_status));
                                break;
                            case CZURConstants.LIGHT_MODE:
                                Log.d(TAG, "亮度模式__超时");
                                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.LIGHT_MODE,
                                        body.getDevice_uuid(), device_status));
                                break;
                            case CZURConstants.LIGHT_SWITCH:
                                Log.d(TAG, "开关灯BIZ" + "//" + device_status.getLight_switch());
                                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.LIGHT_SWITCH,
                                        device_uuid, device_status));
                                break;
                            case CZURConstants.LIGHT_LEVEL:
                                Log.d(TAG, "亮度等级变化");
                                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.LIGHT_LEVEL,
                                        device_uuid, device_status));
                                break;
                            case CZURConstants.SP_SWITCH:
                                Log.d(TAG, "坐姿开关");
                                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SP_SWITCH,
                                        device_uuid, device_status));
                                break;
                            case CZURConstants.SP_LEVEL:
                                Log.d(TAG, "坐姿检测等级");
                                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SP_LEVEL,
                                        device_uuid, device_status));
                                break;
                            case CZURConstants.SP_VOLUME:
                                Log.d(TAG, "坐姿音量");
                                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SP_VOLUME,
                                        device_uuid, device_status));
                                break;
                            case CZURConstants.CHANGE_LANGUAGE:
                                Log.d(TAG, "切换系统语言");
                                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SYSTEM_LANGUAGE,
                                        device_uuid, device_status));
                                break;
                            case CZURConstants.APP_READY_FOR_VIDEO:
                                Log.d(TAG, "设备端检查手机端是否准备好");
                                EventBus.getDefault().post(new VideoEvent(EventType.APP_IS_READY_FOR_VIDEO,
                                        device_uuid, dataBean.getVideo_chat_room_no()));
                                break;
                            case CZURConstants.DEVICE_READY_FOR_VIDEO:
                                Log.d(TAG, "APP主动视频，设备回复房间号，APP进房间");
//                                if (dataBean.getIs_ready_for_video().equals(CZURMessageConstants.CallIn.YES.getValue())) {
                                EventBus.getDefault().post(new VideoEvent(EventType.DEVICE_IS_READY_FOR_VIDEO,
                                        device_uuid, dataBean.getVideo_chat_room_no(), dataBean.getIs_ready_for_video()));
//                                }
                                break;
                            case CZURConstants.CALIBRATE_VIDEO:
                                Log.d(TAG, "坐姿校准，设备回复房间号，APP进房间");
                                EventBus.getDefault().post(new VideoEvent(EventType.SITTING_POSITION_VIDEO,
                                        device_uuid, "", dataBean.getIs_ready_for_calibrate()));
                                break;
                            case CZURConstants.DEVICE_CANCEL_VIDEO:
                                Log.d(TAG, "设备取消视频");
                                EventBus.getDefault().post(new VideoEvent(EventType.DEVICE_CANCEL_VIDEO,
                                        device_uuid, dataBean.getVideo_chat_room_no()));
                                break;
                            case CZURConstants.VIDEO_CANCEL:
                                Log.d(TAG, "设备退出房间");
                                EventBus.getDefault().post(new VideoEvent(EventType.VIDEO_CANCEL,
                                        device_uuid, ""));
                                break;
                            case CZURConstants.SWITCH_CAMERA:
                                Log.d(TAG, "切换摄像头");
                                EventBus.getDefault().post(new VideoCameraEvent(EventType.VIDEO_CAMERA_SWITCH,
                                        device_uuid, dataBean.getVideo_camera()));
                                break;
                            case CZURConstants.CALIBRATE_PHOTO:
                                Log.d(TAG, "坐姿校准，返回校准照片");
                                EventBus.getDefault().post(new CalibrateEvent(EventType.SITTING_POSITION_CALIBRATE
                                        , dataBean.getSp_calibrate_result(), dataBean.getSp_calibrate_image_oss_key()));
                                break;
                            case CZURConstants.DEVICE_CHANGED_RELATIONSHIP:
                                Log.d(TAG, "收到 被动解绑通知");
                                EventBus.getDefault().post(new AuraMateOperateEvent(EventType.AURA_MATE_CHANGED, ""));
                                backToMain(0);
                                break;
                            case CZURConstants.UPDATE_FW:
                                Log.d(TAG, "设备回复升级");
                                EventBus.getDefault().post(new AuraMateUpdateEvent(EventType.AURA_MATE_UPDATE, device_uuid, dataBean.getUpdate_fw_result()));
                                break;
                            case CZURConstants.READY_FOR_OTA_UPDATE:
                                Log.d(TAG, "设备端已准备好升级");
                                EventBus.getDefault().post(new AuraMateReadyUpdateEvent(EventType.AURA_MATE_READY_UPDATE, device_uuid, device_status));
                                break;
                            case CZURConstants.SMART_POWER_SAVING:
                                Log.d(TAG, "智能省电开关");
                                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SMART_POWER, device_uuid, device_status));
                                break;
                            case CZURConstants.SEDENTARY_REMINDER_SWITCH:
                                Log.d(TAG, "久坐提醒开关");
                                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SEDENTARY_REMINDER_SWITCH, device_uuid, device_status));
                                break;
                            case CZURConstants.SEDENTARY_REMINDER_DURATION:
                                Log.d(TAG, "久坐提醒间隔设置");
                                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SEDENTARY_REMINDER_DURATION, device_uuid, device_status));
                                break;
                            case CZURConstants.HD_VIEW:
                                Log.d(TAG, "高清查看状态" + dataBean.getProcessing_status());
                                String ossKey = "";
                                String ossBucket = "";
                                if (dataBean.getProcessing_status().equals(CZURMessageConstants.HdView.UPLOAD_COMPLETED.getStatus())) {
                                    ossKey = dataBean.getOss_key();
                                    ossBucket = dataBean.getOss_bucket();
                                }
                                EventBus.getDefault().post(new HdViewEvent(EventType.HD_VIEW, device_uuid, dataBean.getProcessing_status(), ossKey, ossBucket));
                                break;
                            case CZURConstants.HD_VIEW_SAVE:
                                Log.d(TAG, "高清查保存看状态" + dataBean.getSave_status());
                                EventBus.getDefault().post(new HdViewSaveEvent(EventType.HD_SAVE_VIEW, device_uuid, dataBean.getSave_status()));
                                break;
                            case CZURConstants.HD_VIEW_SAVE_V2:
                                Log.d(TAG, "高清相册收藏状态" + dataBean.getSave_status());
                                EventBus.getDefault().post(new HdViewSaveV2Event(EventType.HD_SAVE_VIEW_V2, device_uuid, dataBean.getSave_status()));
                                break;
                            case CZURConstants.CHECK_VIDEO_REQUEST_ACTIVE:
                                Log.d(TAG, "视频通话有效状态" + dataBean.getSave_status());
                                EventBus.getDefault().post(new AuraMateCheckVideoEvent(EventType.CHECK_VIDEO_REQUEST_ACTIVE, device_uuid, dataBean.getIs_request_active()));
                                break;
                        }
                    }
                }
                break;
            default:
                Log.d(TAG, receiveType);
                break;
        }
    }

    //消息有效性校验
    private boolean checkMessage(ReceivedMsgBodyBean receivedBean) {
        try (Realm realm = Realm.getDefaultInstance()) {
            String requestId = receivedBean.getRequestid();
            String deviceUdid = receivedBean.getBody().getUdid_from();
            ReceivedMsgBodyBean.BodyBean body = receivedBean.getBody();
            ReceivedMsgBodyBean.BodyBean.DataBean dataBean = body.getData();
            String name = dataBean.getMessage_name();
            logD("开始处理消息：" + name);
            String uuid = dataBean.getUuid();
            long msgTime = receivedBean.getTimestamp();
            //记录收到消息的时间
            long localTime = System.currentTimeMillis();
            //服务端消息去重
            MessageEntity sameMsg = realm.where(MessageEntity.class).equalTo("requestId", requestId).equalTo("type", 1).findFirst();
            if (sameMsg == null) {
                //不是重复消息 将收到消息插入DB，type = 1：收到消息
                realm.executeTransaction(new Realm.Transaction() {
                    @Override
                    public void execute(Realm realm) {
                        MessageEntity messageEntity = new MessageEntity();
                        messageEntity.setRequestId(requestId);
                        messageEntity.setUuid(uuid);
                        messageEntity.setDeviceUDID(deviceUdid);
                        messageEntity.setServerTimestamp(msgTime);
                        messageEntity.setType(1);
                        messageEntity.setName(name);
                        realm.copyToRealm(messageEntity);
                    }
                });

                //根据 #收到消息 的uuid查询DB #发送消息查询结果
                MessageEntity searchEntity = realm.where(MessageEntity.class)
                        .equalTo("uuid", uuid)
                        .equalTo("type", 0)
                        .findFirst();
                boolean isFromApp;
                //是发送后回复的消息
                //或者是直接收到的消息
                isFromApp = searchEntity != null;
                //检查消息是否有效
                if (isFromApp) {
                    if (searchEntity.getStatus() == 4) {
                        //已经有后发的消息做过UI处理，此条消息废弃，不做之后的逻辑
                        return false;
                    } else {
                        RealmResults<MessageEntity> sameMessages = realm.where(MessageEntity.class)
                                .equalTo("name", name)
                                .equalTo("deviceUDID", deviceUdid)
                                .equalTo("type", 0)
                                .notEqualTo("status", 4)
                                .lessThanOrEqualTo("createTime", localTime)
                                .sort("createTime", Sort.ASCENDING)
                                .findAll();

                        if (sameMessages != null && sameMessages.size() > 0 && !searchEntity.getUuid().equals(sameMessages.get(0).getUuid())) {
                            realm.executeTransaction(new Realm.Transaction() {
                                @Override
                                public void execute(Realm realm) {
                                    searchEntity.setDataBegin(sameMessages.get(0).getDataBegin());
                                }
                            });
                        }
                        for (MessageEntity message : sameMessages) {
                            realm.executeTransaction(new Realm.Transaction() {
                                @Override
                                public void execute(Realm realm) {
                                    message.setStatus(4);
                                }
                            });
                        }
                    }
                    if (localTime - searchEntity.getCreateTime() <= 0) {
                        //异常消息
                        return false;
//                    } else if (localTime - searchEntity.getCreateTime() <= 8000) {
                    } else if (localTime - searchEntity.getCreateTime() <= BuildConfig.CLIENT_TIMEOUT) {
                        return true;
                    } else {
                        return timeOutToReset(name, searchEntity.getDataBegin(), body);
                    }
                } else {
                    //服务端或设备直接发送的消息
                    RealmResults<MessageEntity> messageEntitys = realm.where(MessageEntity.class)
                            .equalTo("name", name)
                            .greaterThan("serverTimestamp", msgTime)
                            .equalTo("type", 1)
                            .equalTo("deviceUDID", deviceUdid)
                            .findAll();
                    //消息有效,向UI层发正常消息，修改UI
                    //消息无效，因为收到并处理过服务端更晚转发的消息
                    return messageEntitys == null || messageEntitys.size() == 0;
                }
            } else {
                return false;
            }
        }

    }

    //被动接收超时
    private boolean timeOutToReset(String message_name, String dataBegin, ReceivedMsgBodyBean.BodyBean body) {
        boolean isValid;
        ReceivedMsgBodyBean.BodyBean.DataBean dataBean = body.getData();
        ReceivedMsgBodyBean.BodyBean.DataBean.DeviceStatusBean device_status = dataBean.getDevice_status();
        switch (message_name) {
            case CZURConstants.LIGHT_SWITCH:
                Log.d(TAG, "开关灯__超时");
                device_status.setLight_switch(dataBegin);
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.LIGHT_SWITCH,
                        body.getDevice_uuid(), device_status));
                isValid = false;
                break;
            case CZURConstants.LIGHT_LEVEL:
                Log.d(TAG, "亮度等级变化__超时");
                device_status.setLight_level(dataBegin);
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.LIGHT_LEVEL,
                        body.getDevice_uuid(), device_status));
                isValid = false;
                break;
            case CZURConstants.LIGHT_MODE:
                Log.d(TAG, "亮度模式__超时");
                device_status.setLight_level(dataBegin);
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.LIGHT_MODE,
                        body.getDevice_uuid(), device_status));
                isValid = false;
                break;
            case CZURConstants.SP_SWITCH:
                Log.d(TAG, "坐姿开关__超时");
                device_status.setSp_reminder_switch(dataBegin);
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SP_SWITCH,
                        body.getDevice_uuid(), device_status));
                isValid = false;
                break;
            case CZURConstants.SP_LEVEL:
                Log.d(TAG, "坐姿检测等级__超时");
                device_status.setSp_reminder_sensitivity_level(dataBegin);
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SP_LEVEL,
                        body.getDevice_uuid(), device_status));
                isValid = false;
                break;
            case CZURConstants.SP_VOLUME:
                Log.d(TAG, "坐姿音量__超时");
                device_status.setSp_reminder_sensitivity_volume(Integer.parseInt(dataBegin));
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SP_VOLUME,
                        body.getDevice_uuid(), device_status));
                isValid = false;
                break;
            case CZURConstants.SWITCH_CAMERA:
                Log.d(TAG, "切换摄像头__超时");
                EventBus.getDefault().post(new VideoCameraEvent(EventType.VIDEO_CAMERA_SWITCH,
                        body.getDevice_uuid(), dataBean.getVideo_camera()));
                isValid = false;
                break;
            case CZURConstants.CHANGE_LANGUAGE:
                Log.d(TAG, "设置语言__超时");
                device_status.setSystem_language(dataBegin);
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SYSTEM_LANGUAGE,
                        body.getDevice_uuid(), device_status));
                isValid = false;
                break;
            case CZURConstants.SMART_POWER_SAVING:
                Log.d(TAG, "设置智能省电__超时");
                device_status.setSmart_power_saving_switch(dataBegin);
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SMART_POWER,
                        body.getDevice_uuid(), device_status));
                isValid = false;
                break;
            case CZURConstants.SEDENTARY_REMINDER_SWITCH:
                Log.d(TAG, "设置久坐提醒开关__超时");
                device_status.setSedentary_reminder_switch(dataBegin);
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SEDENTARY_REMINDER_SWITCH,
                        body.getDevice_uuid(), device_status));
                isValid = false;
                break;
            case CZURConstants.SEDENTARY_REMINDER_DURATION:
                Log.d(TAG, "设置久坐提醒间隔__超时");
                device_status.setSedentary_reminder_duration(Integer.parseInt(dataBegin));
                EventBus.getDefault().post(new ATCheckDeviceIsOnlineEvent(EventType.SEDENTARY_REMINDER_DURATION,
                        body.getDevice_uuid(), device_status));
                isValid = false;
                break;
            default:
                isValid = true;
                break;
        }
        return isValid;
    }

    //连接成功触发channelActive
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        super.channelActive(ctx);
        inActive.set(false);
        logI("channelActive.socket连接成功.inActive.get()=" + inActive.get());

    }

    //断开连接触发channelInactive
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        super.channelInactive(ctx);
        ServiceUtils.stopService(CheckTimeOutService.class);
        if (!inActive.get()) {
            inActive.set(true);
            logI("channelInactive.socket断线了.inActive.get()=" + inActive.get());
            /**
             * 相对于ctx.close 会从下往上每一条都close,
             * 对于不关注关闭结果的,可以直接用close,但目前存在问题,怀疑是未关闭完成,就开启了新的netty
             * 尝试修复网络不断重连的问题
             */
            ctx.channel().close();

            Context context = Utils.getApp().getApplicationContext();
            UserPreferences instance = UserPreferences.getInstance(context);
            CZURTcpClient.getInstance().closeChannel();

            EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_DEVICE_OFFLINE, ""));
            // 稍等一下再连接
            Thread.sleep(1000);
            if (instance.isHasAuraMate() && instance.isUserLogin()) {
                NettyUtils.getInstance().restartNettyService();
//                reStartNettyService(context);
                EventBus.getDefault().post(new AuraMateOfflineEvent(EventType.AURA_MATE_OFFLINE));
            }
        }
    }

    private void backToMain(long timeDelay) {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (ActivityUtils.isActivityExistsInStack(AuraMateActivity.class)) {
                    ActivityUtils.finishToActivity(AuraMateActivity.class, false);
//                } else {
//                    if (ActivityUtils.getTopActivity() != null) {
//                        ActivityUtils.finishActivity(ActivityUtils.getTopActivity());
//                    }
                }
            }
        }, timeDelay);
    }

    private void openCameraAuraMate(Context context, String callId, String udidFrom, boolean isTransfer) {
        logI("ClientMessageHandler.openCamera.callId=" + callId + ",udidFrom=" + udidFrom + ",isTransfer=" + isTransfer);
        // 针对 华为android11以上，
        // 放置后台
        // 后台弹窗未开启
        if ((Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q)
                && (!AppUtils.isAppForeground())
//                && (Build.MANUFACTURER.equals("HUAWEI"))
//                && (!RomUtil.onCheckHwOp(context))
        ) {
            logI("ClientMessageHandler.AuraMate.单独处理华为机型，放置后台的情况！");

            EventBus.getDefault().post(new StarryCommonEvent(EventType.AURAMATE_MESSAGE_CALL_VIDEO_IN_BACKGROUND, ""));
            return;
        }

        PermissionUtils.permission(PermissionConstants.CAMERA, PermissionConstants.MICROPHONE)
                .rationale(new PermissionUtils.OnRationaleListener() {
                    @Override
                    public void rationale(UtilsTransActivity activity, ShouldRequest shouldRequest) {
                        ToastUtils.showShort(R.string.denied_camera);
                        shouldRequest.again(true);
                    }
                })
                .callback(new PermissionUtils.FullCallback() {
                    @Override
                    public void onGranted(List<String> permissionsGranted) {
                        Intent intent = new Intent(context, AuraMatePreRemoteVideoActivity.class);
                        intent.putExtra("isCallIn", true);
                        intent.putExtra("isTransfer", isTransfer);
                        intent.putExtra("callId", callId);
                        intent.putExtra("udidFrom", udidFrom);
                        intent.putExtra("equipmentId", udidFrom);
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        ActivityUtils.startActivity(intent);
                    }

                    @Override
                    public void onDenied(List<String> permissionsDeniedForever,
                                         List<String> permissionsDenied) {
                        ToastUtils.showShort(R.string.denied_camera);
                    }
                })
                .theme(new PermissionUtils.ThemeCallback() {
                    @Override
                    public void onActivityCreate(Activity activity) {
                        ScreenUtils.setFullScreen(activity);
                    }
                })
                .request();
    }

    // 唤起 Starry呼入页面
    private void openCameraStarry(Context context,
                                  String callId,
                                  String userid_from,
                                  String udid_from,
                                  String nickname_from,
                                  String room,
                                  String room_name,
                                  String headImage,
                                  Boolean otherMeeting) {
        logI("ClientMessageHandler.openCameraStarry.callId=" + callId +
                ",userid_from=" + userid_from +
                ",udid_from=" + udid_from +
                ",nickname_from=" + nickname_from +
                ",room=" + room +
                ",room_name=" + room_name +
                ",headImage=" + headImage +
                ",otherMeeting=" + otherMeeting);


        // 会议中，来电邀请处理
        if (Boolean.TRUE.equals(MeetingModel.isInMeeting.getValue())) {
            EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_MESSAGE_CALL_VIDEO_IN_MEETING, nickname_from));
            return;
        }

        // 呼叫中，来电邀请处理
        if (MeetingModel.isCallingOutMeeting) {
            EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_MESSAGE_CALL_VIDEO_CALLING_MEETING, nickname_from));
            return;
        }

        // 针对 华为android11以上，
        // 放置后台
        // 后台弹窗未开启
        if ((Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q)
                && (!AppUtils.isAppForeground())
//                && (Build.MANUFACTURER.equals("HUAWEI"))
//                && (!RomUtil.onCheckHwOp(context))
        ) {
            logI("ClientMessageHandler.StarryMeeting.单独处理华为机型，放置后台的情况！");

            EventBus.getDefault().post(new StarryCommonEvent(EventType.STARRY_MESSAGE_CALL_VIDEO_IN_BACKGROUND, nickname_from));
            return;
        }

        // 弹出呼入邀请页面
        Intent intent = new Intent(context, StarryCallInActivity.class);
        intent.putExtra("isCallIn", true);
        intent.putExtra("callId", callId);
        intent.putExtra("useridFrom", userid_from);
        intent.putExtra("udidFrom", udid_from);
        intent.putExtra("nicknameFrom", nickname_from);
        intent.putExtra("Room", room);
        intent.putExtra("roomName", room_name);
        intent.putExtra("headImage", headImage);
        intent.putExtra("otherMeeting", otherMeeting);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        ActivityUtils.startActivity(intent);

    }

    //异常回调,默认的exceptionCaught只会打出日志，不会关掉channel
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        super.exceptionCaught(ctx, cause);
        cause.printStackTrace();
        ctx.close();
    }

}
