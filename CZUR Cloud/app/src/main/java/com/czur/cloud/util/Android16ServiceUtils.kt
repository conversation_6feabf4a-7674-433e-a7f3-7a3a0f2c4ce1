package com.czur.cloud.util

import android.app.Service
import android.app.job.JobInfo
import android.app.job.JobScheduler
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.annotation.RequiresApi
import com.czur.czurutils.log.logI
import com.czur.czurutils.log.logE

/**
 * Android 16 服务适配工具类
 * 处理前台服务、JobScheduler等服务相关的适配
 */
object Android16ServiceUtils {
    private const val TAG = "Android16ServiceUtils"

    /**
     * 安全启动前台服务（适配Android 16）
     */
    fun startForegroundServiceSafely(
        context: Context,
        serviceClass: Class<out Service>,
        serviceType: Int = android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE
    ): Boolean {
        return try {
            val intent = Intent(context, serviceClass)
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0+需要使用startForegroundService
                context.startForegroundService(intent)
                logI("$TAG.startForegroundServiceSafely: Started foreground service ${serviceClass.simpleName}")
            } else {
                context.startService(intent)
                logI("$TAG.startForegroundServiceSafely: Started service ${serviceClass.simpleName}")
            }
            true
        } catch (e: Exception) {
            logE("$TAG.startForegroundServiceSafely error: ${e.message}")
            false
        }
    }

    /**
     * 检查JobScheduler任务状态（Android 16增强）
     */
    @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
    fun checkJobSchedulerStatus(context: Context, jobId: Int): JobSchedulerStatus {
        return try {
            val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
            
            val pendingJob = jobScheduler.getPendingJob(jobId)
            if (pendingJob == null) {
                return JobSchedulerStatus(
                    isScheduled = false,
                    reasons = intArrayOf(),
                    description = "Job not found"
                )
            }
            
            // Android 16新增：获取所有失败原因
            val reasons = if (Build.VERSION.SDK_INT >= 36) {
                Android16Utils.getPendingJobReasons(context, jobId)
            } else {
                // Android 14+的单一原因API
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    try {
                        val getPendingJobReasonMethod = jobScheduler.javaClass.getMethod("getPendingJobReason", Int::class.java)
                        val reason = getPendingJobReasonMethod.invoke(jobScheduler, jobId) as Int
                        intArrayOf(reason)
                    } catch (e: Exception) {
                        intArrayOf()
                    }
                } else {
                    intArrayOf()
                }
            }
            
            val description = if (reasons.isNotEmpty()) {
                "Job pending due to: ${reasons.joinToString(", ") { getJobPendingReasonDescription(it) }}"
            } else {
                "Job is scheduled and ready to run"
            }
            
            JobSchedulerStatus(
                isScheduled = true,
                reasons = reasons,
                description = description
            )
            
        } catch (e: Exception) {
            logE("$TAG.checkJobSchedulerStatus error: ${e.message}")
            JobSchedulerStatus(
                isScheduled = false,
                reasons = intArrayOf(),
                description = "Error checking job status: ${e.message}"
            )
        }
    }

    /**
     * 获取JobScheduler失败原因的描述
     */
    private fun getJobPendingReasonDescription(reason: Int): String {
        return when (reason) {
            0 -> "UNDEFINED"
            1 -> "APP"
            2 -> "APP_STANDBY"
            3 -> "BACKGROUND_RESTRICTION"
            4 -> "CONSTRAINT_BATTERY_NOT_LOW"
            5 -> "CONSTRAINT_CHARGING"
            6 -> "CONSTRAINT_CONNECTIVITY"
            7 -> "CONSTRAINT_CONTENT_TRIGGER"
            8 -> "CONSTRAINT_DEVICE_IDLE"
            9 -> "CONSTRAINT_MINIMUM_LATENCY"
            10 -> "CONSTRAINT_PREFETCH"
            11 -> "CONSTRAINT_STORAGE_NOT_LOW"
            12 -> "DEVICE_STATE"
            13 -> "EXECUTING"
            14 -> "INVALID_JOB_ID"
            15 -> "JOB_SCHEDULER_OPTIMIZATION"
            16 -> "QUOTA"
            17 -> "USER"
            18 -> "CONSTRAINT_DEADLINE"
            else -> "UNKNOWN($reason)"
        }
    }

    /**
     * 创建适配Android 16的JobInfo
     */
    @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
    fun createJobInfo(
        context: Context,
        jobId: Int,
        serviceClass: Class<*>,
        intervalMillis: Long = 15 * 60 * 1000L, // 15分钟
        requiresCharging: Boolean = false,
        requiresDeviceIdle: Boolean = false,
        requiredNetworkType: Int = JobInfo.NETWORK_TYPE_NONE
    ): JobInfo {
        val componentName = ComponentName(context, serviceClass)
        
        val builder = JobInfo.Builder(jobId, componentName)
            .setRequiredNetworkType(requiredNetworkType)
            .setRequiresCharging(requiresCharging)
            .setRequiresDeviceIdle(requiresDeviceIdle)
            .setPersisted(true)
        
        // 设置执行间隔
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            builder.setPeriodic(intervalMillis, JobInfo.getMinFlexMillis())
        } else {
            builder.setPeriodic(intervalMillis)
        }
        
        // Android 16特定优化
        if (Build.VERSION.SDK_INT >= 36) {
            // 可以在这里添加Android 16特定的JobInfo配置
            logI("$TAG.createJobInfo: Creating JobInfo with Android 16 optimizations")
        }
        
        return builder.build()
    }

    /**
     * 调度JobScheduler任务
     */
    @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
    fun scheduleJob(context: Context, jobInfo: JobInfo): Boolean {
        return try {
            val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
            val result = jobScheduler.schedule(jobInfo)
            
            if (result == JobScheduler.RESULT_SUCCESS) {
                logI("$TAG.scheduleJob: Job ${jobInfo.id} scheduled successfully")
                true
            } else {
                logE("$TAG.scheduleJob: Failed to schedule job ${jobInfo.id}, result: $result")
                false
            }
        } catch (e: Exception) {
            logE("$TAG.scheduleJob error: ${e.message}")
            false
        }
    }

    /**
     * 取消JobScheduler任务
     */
    @RequiresApi(Build.VERSION_CODES.LOLLIPOP)
    fun cancelJob(context: Context, jobId: Int): Boolean {
        return try {
            val jobScheduler = context.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
            jobScheduler.cancel(jobId)
            logI("$TAG.cancelJob: Job $jobId cancelled")
            true
        } catch (e: Exception) {
            logE("$TAG.cancelJob error: ${e.message}")
            false
        }
    }

    /**
     * 检查服务是否正在运行
     */
    fun isServiceRunning(context: Context, serviceClass: Class<*>): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val services = activityManager.getRunningServices(Integer.MAX_VALUE)
            
            services.any { serviceInfo ->
                serviceInfo.service.className == serviceClass.name
            }
        } catch (e: Exception) {
            logE("$TAG.isServiceRunning error: ${e.message}")
            false
        }
    }

    /**
     * 获取前台服务类型的描述
     */
    fun getForegroundServiceTypeDescription(serviceType: Int): String {
        return when (serviceType) {
            android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE -> "CONNECTED_DEVICE"
            android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC -> "DATA_SYNC"
            android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK -> "MEDIA_PLAYBACK"
            android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_PHONE_CALL -> "PHONE_CALL"
            android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_LOCATION -> "LOCATION"
            android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_CAMERA -> "CAMERA"
            android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE -> "MICROPHONE"
            else -> "UNKNOWN($serviceType)"
        }
    }

    /**
     * JobScheduler状态数据类
     */
    data class JobSchedulerStatus(
        val isScheduled: Boolean,
        val reasons: IntArray,
        val description: String
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as JobSchedulerStatus

            if (isScheduled != other.isScheduled) return false
            if (!reasons.contentEquals(other.reasons)) return false
            if (description != other.description) return false

            return true
        }

        override fun hashCode(): Int {
            var result = isScheduled.hashCode()
            result = 31 * result + reasons.contentHashCode()
            result = 31 * result + description.hashCode()
            return result
        }
    }
}
