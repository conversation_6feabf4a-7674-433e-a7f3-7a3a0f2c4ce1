package com.czur.cloud.model;

public class EtEquipmentModel {


    /**
     * id : 2819
     * alias : ET16_002816
     * type : ET16
     * bindUserId : 87
     * isBinded : true
     * inUsingUserId : 0
     * inUsingUserInfo : null
     * isInUsing : false
     * isShared : false
     * usingOn : 1525850077000
     * offUsingOn : 1525850981000
     * connectNetworkTime : null
     * isConnectedTutk : 0
     * isPublic : false
     */

    private int id;
    private String alias;
    private String type;
    private String sn;
    private int bindUserId;
    private boolean isBinded;
    private boolean needUpdateFw;
    private int inUsingUserId;
    private Object inUsingUserInfo;
    private boolean isInUsing;
    private boolean isShared;
    private long usingOn;
    private long offUsingOn;
    private Object connectNetworkTime;
    private int isConnectedTutk;
    private boolean isPublic;




    public boolean isNeedUpdateFw() {
        return needUpdateFw;
    }

    public void setNeedUpdateFw(boolean needUpdateFw) {
        this.needUpdateFw = needUpdateFw;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getBindUserId() {
        return bindUserId;
    }

    public void setBindUserId(int bindUserId) {
        this.bindUserId = bindUserId;
    }

    public boolean isIsBinded() {
        return isBinded;
    }

    public void setIsBinded(boolean isBinded) {
        this.isBinded = isBinded;
    }

    public int getInUsingUserId() {
        return inUsingUserId;
    }

    public void setInUsingUserId(int inUsingUserId) {
        this.inUsingUserId = inUsingUserId;
    }

    public Object getInUsingUserInfo() {
        return inUsingUserInfo;
    }

    public void setInUsingUserInfo(Object inUsingUserInfo) {
        this.inUsingUserInfo = inUsingUserInfo;
    }

    public boolean isIsInUsing() {
        return isInUsing;
    }

    public void setIsInUsing(boolean isInUsing) {
        this.isInUsing = isInUsing;
    }

    public boolean isIsShared() {
        return isShared;
    }

    public void setIsShared(boolean isShared) {
        this.isShared = isShared;
    }

    public long getUsingOn() {
        return usingOn;
    }

    public void setUsingOn(long usingOn) {
        this.usingOn = usingOn;
    }

    public long getOffUsingOn() {
        return offUsingOn;
    }

    public void setOffUsingOn(long offUsingOn) {
        this.offUsingOn = offUsingOn;
    }

    public Object getConnectNetworkTime() {
        return connectNetworkTime;
    }

    public void setConnectNetworkTime(Object connectNetworkTime) {
        this.connectNetworkTime = connectNetworkTime;
    }

    public int getIsConnectedTutk() {
        return isConnectedTutk;
    }

    public void setIsConnectedTutk(int isConnectedTutk) {
        this.isConnectedTutk = isConnectedTutk;
    }

    public boolean isIsPublic() {
        return isPublic;
    }

    public void setIsPublic(boolean isPublic) {
        this.isPublic = isPublic;
    }
}
