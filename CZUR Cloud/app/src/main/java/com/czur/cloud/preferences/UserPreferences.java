package com.czur.cloud.preferences;

import android.app.Application;
import android.content.Context;

import com.czur.cloud.BuildConfig;
import com.czur.cloud.R;
import com.czur.cloud.model.AuraDeviceModel;
import com.czur.cloud.model.RegisterModel;
import com.czur.cloud.ui.mirror.model.SittingDeviceModel;
import com.czur.cloud.ui.mirror.model.SittingUpdateModel;
import com.czur.cloud.util.validator.StringUtils;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.List;


public class UserPreferences extends BasePreferences {

    private static final String PREF = UserPreferences.class.getSimpleName();
    //基础变量
    private static final String USER_INFO_PREF = "czur_cloud_user_info_pref";
    private static final String IS_USER_LOGIN = "czur_cloud_is_user_login";
    private static final String IS_AUTO_SYNC = "czur_cloud_is_auto_sync";
    private static final String IS_SYNC_ONLY_WIFI = "czur_cloud_sync_wifi";
    private static final String SYNC_TIME = "czur_cloud_sync_time";
    private static final String HANDWRITING_COUNT = "czur_cloud_handwriting_count";
    private static final String IMEI = "czur_cloud_imei";
    private static final String CHANNEL = "czur_cloud_channel";
    private static final String ENDPOINT = "czur_cloud_endpoint";

    private static final String IS_THIRD_PARTY = "czur_cloud_is_third_party";
    private static final String THIRD_PARTY_TOKEN = "czur_cloud_third_token";
    private static final String THIRD_PARTY_REFRESH_TOKEN = "czur_cloud_third_refresh_token";
    private static final String THIRD_PARTY_OPENID = "czur_cloud_third_openId";
    private static final String THIRD_PARTY_PLAT_NAME = "czur_cloud_third_plat_name";
    private static final String SERVICE_PLAT_NAME = "czur_cloud_service_plat_name";

    private static final String LOGIN_USER_NAME = "czur_cloud_login_user_name";
    private static final String LOGIN_PASSWORD = "czur_cloud_login_password";
    private static final String USAGES = "czur_cloud_usages";
    private static final String USAGES_LIMIT = "czur_cloud_usages_limit";
    private static final String PHOTO_OSS_KEY = "czur_cloud_photo_oss_key";
    private static final String U_KEY = "czur_cloud_u_key";


    private static final String APK_ID = "czur_cloud_apk_id";
    private static final String PDF_PATH = "czur_cloud_pdf_path";
    private static final String TEMP_PATH = "czur_cloud_temp_path";
    private static final String SD_PATH = "czur_cloud_sd_path";

    private static final String OCR_LANGUAGE = "czur_cloud_ocr_language";
    private static final String OCR_NEW_LANGUAGE = "czur_cloud_ocr_new_language";
    private static final String HANDWRITING_LANGUAGE = "czur_global_ocr_language";
    private static final String USER_CLEAR_COUNT = "czur_cloud_clear_count";

    private static final String PDF_TYPE = "czur_cloud_pdf_size";
    private static final String PDF_IS_HORIZONTAL = "czur_cloud_pdf_is_horizontal";
    private static final String PDF_QUALITY = "czur_cloud_pdf_quality";

    private static final String AURA_OCR_LANGUAGE = "czur_cloud_aura_ocr_language";
    private static final String SITTING_POSITION_LEVEL = "czur_cloud_sitting_position_level";
    private static final String NOTIFY_ALL = "czur_cloud_is_notify_all";
    private static final String NOTIFY_USE = "czur_cloud_is_notify_use";
    private static final String NOTIFY_FILE = "czur_cloud_is_notify_file";
    private static final String NOTIFY_OFFLINE = "czur_cloud_is_notify_offline";
    private static final String ERRORSIT_ALL = "czur_cloud_is_errorsit_all";

    private static final String HEART_BEAT_TIME = "czur_cloud_heart_beat_time";
    private static final String IS_HAS_AURA_HOME_DEVICES = "czur_cloud_is_has_aura_home";
    private static final String UDID = "czur_cloud_udid";
    private static final String REGID = "czur_cloud_regid";
    private static final String HAS_AURA_MATE = "czur_cloud_has_aura_mate";
    private static final String LAST_REPORT_TIME = "czur_cloud_last_report_time";
    private static final String LAST_CALL_TIME = "czur_cloud__last_call_time";
    private static final String AURAMATE_IS_SLIDE = "czur_cloud_is_slide";
    private static final String AURAMATE_IS_NOTICED_MISSED_CALL = "czur_cloud_is_first_missed_call";
    private static final String AURA_MATE_SHOW_DIALOG = "czur_cloud_aura_mate_show_dialog";
    private static final String AURA_MATE_DEVICES_LIST = "czur_cloud_aura_mate_devices_list";

    private static final String MIRROR_EXPER_COUNT = "czur_cloud_mirror_exper_count";
    private static final String HAS_MIRROR = "czur_cloud_has_mirror";

    private static final String PINCODE = "czur_eshare_pincode";
    private static final String PINCODEMAP = "czur_eshare_pincode_map";

    private static final String COUNTRY_CODE = "czur_country_code";
    private static final String AURA_MOVIES_RED_POINT = "czur_aura_movies_red_point";

    private static final String FIRST_IN_VERSION_408 = "FIRST_IN_VERSION_408";// 第一次升级到408版本需要退出登录清除数据

    private static UserPreferences instance;

    public static void init(Application application) {
        instance = new UserPreferences(application, PREF);
    }

    public static UserPreferences getInstance() {
        return instance;
    }

    public static UserPreferences getInstance(Context context) {
        if (instance == null) {
            instance = new UserPreferences(context, PREF);
        }
        return instance;
    }

    public UserPreferences(Context context, String prefsName) {
        super(context, prefsName);
    }


    // EShare save pinCode
    public void setESharePinCode(String pincode) {
        put(PINCODE, pincode);
    }

    public String getESharePinCode() {
        Object obj = get(PINCODE);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    //绑定ip
    public void setESharePinCode(String ip,String pincode) {
        put(PINCODE+ip, pincode);
    }
    public String getESharePinCode(String ip) {
        Object obj = get(PINCODE+ip);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    //Jason 2020-12-30 begin
    private static final String SITTING_DEVICES_LIST = "czur_cloud_sitting_devices_list";
    private static final String SITTING_DEVICE_MODEL = "czur_cloud_sitting_device_model";
    private static final String SITTING_UPDATE_MODEL = "czur_cloud_sitting_update_model";
    private static final String SITTING_IS_UPDATING = "czur_cloud_sitting_is_updating";

    public void setSittingDevices(List<SittingDeviceModel> datas) {
//        if (Validator.isNotEmpty(datas)) {
            String json = new Gson().toJson(datas);
            put(SITTING_DEVICES_LIST, json);
//        }
    }

    public List<SittingDeviceModel> getSittingDevices() {
        String json = (String) get(SITTING_DEVICES_LIST);
        if (StringUtils.isBlank(json)) {
            return null;
        }

        return new Gson().fromJson(json, new TypeToken<List<SittingDeviceModel>>() {
        }.getType());
    }

    public void setSittingDeviceModel(SittingDeviceModel data) {
        if (Validator.isNotEmpty(data)) {
            String json = new Gson().toJson(data);
            put(SITTING_DEVICE_MODEL, json);
        }
    }

    public SittingDeviceModel getSittingDeviceModel() {
        String json = (String) get(SITTING_DEVICE_MODEL);
        if (StringUtils.isBlank(json)) {
            return null;
        }

        return new Gson().fromJson(json, new TypeToken<SittingDeviceModel>() {
        }.getType());
    }

    public void setSittingUpdateModel(SittingUpdateModel data) {
        if (Validator.isNotEmpty(data)) {
            String json = new Gson().toJson(data);
            put(SITTING_UPDATE_MODEL, json);
        }
    }

    public SittingUpdateModel getSittingUpdateModel() {
        String json = (String) get(SITTING_UPDATE_MODEL);
        if (StringUtils.isBlank(json)) {
            return null;
        }

        return new Gson().fromJson(json, new TypeToken<SittingUpdateModel>() {
        }.getType());
    }

    public void setExperienceCount(int experienceCount) {        put(MIRROR_EXPER_COUNT, experienceCount);    }

    public int getExperienceCount() {
        Object obj = get(MIRROR_EXPER_COUNT);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }

    public void setIsSittingUpdating(boolean flag) {
        put(SITTING_IS_UPDATING, flag);
    }

    public boolean isSittingUpdating() {
        Object obj = get(SITTING_IS_UPDATING);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }

    //Jason 2020-12-30 end

    public void setUser(RegisterModel userData) {
        if (Validator.isNotEmpty(userData)) {
            String json = new Gson().toJson(userData);
            put(USER_INFO_PREF, json);
        }
    }

    public RegisterModel getUser() {
        String json = (String) get(USER_INFO_PREF);
        if (StringUtils.isBlank(json)) {
            return null;
        }

        return new Gson().fromJson(json, RegisterModel.class);
    }

    public void setAuraMateDevices(List<AuraDeviceModel> datas) {
        if (Validator.isNotEmpty(datas)) {
            String json = new Gson().toJson(datas);
            put(AURA_MATE_DEVICES_LIST, json);
        }
    }

    public List<AuraDeviceModel> getAuraMateDevices() {

        String json = (String) get(AURA_MATE_DEVICES_LIST);
        if (StringUtils.isBlank(json)) {
            return null;
        }

        return new Gson().fromJson(json, new TypeToken<List<AuraDeviceModel>>() {
        }.getType());
    }

    public void setHasMirror(boolean isLogin) {
        put(HAS_MIRROR, isLogin);
    }

    public boolean isHasMirror() {
        Object obj = get(HAS_MIRROR);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }



    public void setIsUserLogin(boolean isLogin) {
        put(IS_USER_LOGIN, isLogin);
    }

    public boolean isUserLogin() {
        Object obj = get(IS_USER_LOGIN);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }

    public void setHasAuraMate(boolean isLogin) {
        put(HAS_AURA_MATE, isLogin);
    }

    public boolean isHasAuraMate() {
//        Object obj = get(HAS_AURA_MATE);
//        if (Validator.isEmpty(obj)) {
//            return false;
//        } else {
//            return (boolean) obj;
//        }
        return true;
    }

    public boolean isValidUser() {
        RegisterModel userData = getUser();
        if (userData != null && Validator.isNotEmpty(userData.getId())) {
            return true;
        }
        return false;
    }

    public boolean isInValidUser() {
        return !isValidUser();
    }

    public void resetUser() {
        put(USER_INFO_PREF, StringUtils.EMPTY);
        put(AURA_MATE_DEVICES_LIST, StringUtils.EMPTY);
        setIsThirdParty(false);
        setThirdPartyToken(StringUtils.EMPTY);
        setThirdPartyOpenid(StringUtils.EMPTY);
        setThirdPartyPlatName(StringUtils.EMPTY);
        setServicePlatName(StringUtils.EMPTY);

        setLoginPassword(StringUtils.EMPTY);
        setLoginUserName(StringUtils.EMPTY);
        setPhotoOssKey(StringUtils.EMPTY);
        setUkey(StringUtils.EMPTY);
        setHandwritingCount("0");
        setHasAuraMate(false);
        setIsSyncOnlyWifi(true);
        setUserClearCount(0);
        setIsAutoSync(true);
        setReportTime("");
        setUsages(0);
        setUsagesLimit(0);
        setSyncTime(0);
        setIsAutoSync(true);
        setIsSyncOnlyWifi(true);

    }

    public String getUserId() {
        if (isValidUser()) {
            return getUser().getId();
        }
        return "";
    }

    public long getLongUserId() {
        if (isValidUser()) {
            return Long.parseLong(getUser().getId());
        }
        return 0;
    }

    public void setUserId(String id) {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setId(id);
            setUser(userData);
        }
    }

    public void setOcrLanguageId(int viewId) {
        put(HANDWRITING_LANGUAGE, viewId);
    }

    public int getOcrLanguageId() {
        Object obj = get(HANDWRITING_LANGUAGE);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }

    public void setOcrLanguageString(String language) {
        put(OCR_NEW_LANGUAGE, language);
    }

    public String getOcrLanguageString() {
        Object obj = get(OCR_NEW_LANGUAGE);
        if (Validator.isEmpty(obj)) {
            return "简体中文";
        } else {
            return (String) obj;
        }
    }


    public String getUserName() {
        if (isValidUser()) {
            return getUser().getName();
        }
        return "";
    }

    public void setUserName(String name) {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setName(name);
            setUser(userData);
        }
    }

    public String getUserMobile() {
        if (isValidUser()) {
            return getUser().getMobile();
        }
        return "";
    }

    public void setUserMobile(String mobile) {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setMobile(mobile);
            setUser(userData);
        }
    }

    public String getUserEmail() {
        if (isValidUser()) {
            return getUser().getEmail();
        }
        return "";
    }

    public void setUserEmail(String email) {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setEmail(email);
            setUser(userData);
        }
    }

    public String getUserPhoto() {
        if (isValidUser()) {
            return getUser().getPhoto();
        }
        return "";
    }

    public void setUserPhoto(String photo) {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setPhoto(photo);
            setUser(userData);
        }
    }

    public boolean isUserActive() {
        if (isValidUser()) {
            return getUser().isActive();
        }
        return false;
    }

    public void setUserActive(boolean active) {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setActive(active);
            setUser(userData);
        }
    }


    public void setToken(String token) {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setToken(token);
            setUser(userData);
        }
    }

    public String getToken() {
        if (isValidUser()) {
            return getUser().getToken();
        }
        return "";
    }

    public void resetToken() {
        if (isValidUser()) {
            RegisterModel userData = getUser();
            userData.setToken(StringUtils.EMPTY);
            setUser(userData);
        }
    }

    public void setIMEI(String imei) {
        put(IMEI, imei);
    }

    public String getIMEI() {
        Object obj = get(IMEI);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setEndpoint(String endpoint) {
        put(ENDPOINT, endpoint);
    }

    public String getEndpoint() {
        Object obj = get(ENDPOINT);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setUsages(long usages) {
        put(USAGES, usages);
    }

    public long getUsages() {
        Object obj = get(USAGES);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (long) obj;
        }
    }

    public void setUsagesLimit(long usagesLimit) {
        put(USAGES_LIMIT, usagesLimit);
    }

    public long getUsagesLimit() {
        Object obj = get(USAGES_LIMIT);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (long) obj;
        }
    }

    public void setSyncTime(long syncTime) {

        put(SYNC_TIME, syncTime);
    }

    public long getSyncTime() {

        Object obj = get(SYNC_TIME);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (long) obj;
        }
    }

    public void setPhotoOssKey(String photoOssKey) {
        put(PHOTO_OSS_KEY, photoOssKey);
    }

    public String getPhotoOssKey() {
        Object obj = get(PHOTO_OSS_KEY);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setApkId(long apkId) {
        put(APK_ID, apkId);
    }

    public long getApkId() {
        Object obj = get(APK_ID);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (long) obj;
        }
    }

    public void setUserClearCount(int clearCount) {
        put(USER_CLEAR_COUNT, clearCount);
    }

    public int getUserClearCount() {
        Object obj = get(USER_CLEAR_COUNT);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }

    public void setHandwritingCount(String handwritingCount) {
        put(HANDWRITING_COUNT, handwritingCount);
    }

    public String getHandwritingCount() {
        Object obj = get(HANDWRITING_COUNT);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setIsThirdParty(boolean isThirdParty) {
        put(IS_THIRD_PARTY, isThirdParty);
    }

    public boolean isThirdParty() {
        Object obj = get(IS_THIRD_PARTY);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }

    public void setThirdPartyToken(String thirdPartyToken) {
        put(THIRD_PARTY_TOKEN, thirdPartyToken);
    }

    public String getThirdPartyToken() {
        Object obj = get(THIRD_PARTY_TOKEN);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setThirdPartyRefreshToken(String thirdPartyRefreshToken) {
        put(THIRD_PARTY_REFRESH_TOKEN, thirdPartyRefreshToken);
    }

    public String getThirdPartyRefreshToken() {
        Object obj = get(THIRD_PARTY_REFRESH_TOKEN);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setThirdPartyOpenid(String thirdPartyOpenid) {
        put(THIRD_PARTY_OPENID, thirdPartyOpenid);
    }

    public String getThirdPartyOpenid() {
        Object obj = get(THIRD_PARTY_OPENID);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setThirdPartyPlatName(String thirdPartyPlatName) {
        put(THIRD_PARTY_PLAT_NAME, thirdPartyPlatName);
    }

    public String getThirdPartyPlatName() {
        Object obj = get(THIRD_PARTY_PLAT_NAME);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setServicePlatName(String thirdPartyPlatName) {
        put(SERVICE_PLAT_NAME, thirdPartyPlatName);
    }

    public String getServicePlatName() {
        Object obj = get(SERVICE_PLAT_NAME);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setChannel(String channel) {
        put(CHANNEL, channel);
    }

    public String getChannel() {
        Object obj = get(CHANNEL);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setTempPath(String tempPath) {
        put(TEMP_PATH, tempPath);
    }

    public String getTempPath() {
        Object obj = get(TEMP_PATH);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setPdfPath(String pdfPath) {
        put(PDF_PATH, pdfPath);
    }

    public String getPdfPath() {
        Object obj = get(PDF_PATH);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setSdPath(String phoneRootPath) {
        put(SD_PATH, phoneRootPath);
    }

    public String getSdPath() {
        Object obj = get(SD_PATH);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setLoginUserName(String loginUserName) {
        put(LOGIN_USER_NAME, loginUserName);
    }

    public String getLoginUserName() {
        Object obj = get(LOGIN_USER_NAME);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setLoginPassword(String loginPassword) {
        put(LOGIN_PASSWORD, loginPassword);
    }

    public String getLoginPassword() {
        Object obj = get(LOGIN_PASSWORD);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setUkey(String uKey) {
        put(U_KEY, uKey);
    }

    public String getUkey() {
        Object obj = get(U_KEY);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setIsAutoSync(boolean isAutoSync) {
        put(IS_AUTO_SYNC, isAutoSync);
    }

    public boolean getIsAutoSync() {
        Object obj = get(IS_AUTO_SYNC);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setIsSyncOnlyWifi(boolean isSyncOnlyWifi) {
        put(IS_SYNC_ONLY_WIFI, isSyncOnlyWifi);
    }

    public boolean getIsSyncOnlyWifi() {
        Object obj = get(IS_SYNC_ONLY_WIFI);
        if (Validator.isNotEmpty(obj)) {
            return (boolean) obj;
        }
        return true;
    }

    public void setLastOcrViewId(int viewId) {
        put(OCR_LANGUAGE, viewId);
    }

    public int getLastOcrViewId() {
        Object obj = get(OCR_LANGUAGE);
        if (Validator.isEmpty(obj)) {
            return R.id.ocr_chinese_btn;
        } else {
            return (int) obj;
        }
    }

    public void setPdfType(int pdfType) {
        put(PDF_TYPE, pdfType);
    }

    public int getPdfType() {
        Object obj = get(PDF_TYPE);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }

    public void setPdfIsHorizontal(int isHorizontal) {
        put(PDF_IS_HORIZONTAL, isHorizontal);
    }

    public int getPdfIsHorizontal() {
        Object obj = get(PDF_IS_HORIZONTAL);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (int) obj;
        }
    }

    public void setPdfQuality(int pdfQuality) {
        put(PDF_QUALITY, pdfQuality);
    }

    public int getPdfQuality() {
        Object obj = get(PDF_QUALITY);
        if (Validator.isEmpty(obj)) {
            return 1;
        } else {
            return (int) obj;
        }
    }


    //1 低，2中，3高
    public void setSittingPositionLevel(int level) {
        put(SITTING_POSITION_LEVEL, level);
    }

    public int getSittingPositionLevel() {
        Object obj = get(SITTING_POSITION_LEVEL);
        if (Validator.isEmpty(obj)) {
            return 2;
        } else {
            return (int) obj;
        }
    }


    public void setNotifyAll(boolean isNotifyALL) {
        put(NOTIFY_ALL, isNotifyALL);
    }
    public boolean isNotifyAll() {
        Object obj = get(NOTIFY_ALL);
        if (Validator.isEmpty(obj)) {
            return true;
        } else {
            return (boolean) obj;
        }
    }

    public void setNotifyUse(boolean isNotify) {
        put(NOTIFY_USE, isNotify);
    }
    public boolean isNotifyUse() {
        Object obj = get(NOTIFY_USE);
        if (Validator.isEmpty(obj)) {
            return true;
        } else {
            return (boolean) obj;
        }
    }

    public void setNotifyFile(boolean isNotify) {
        put(NOTIFY_FILE, isNotify);
    }
    public boolean isNotifyFile() {
        Object obj = get(NOTIFY_FILE);
        if (Validator.isEmpty(obj)) {
            return true;
        } else {
            return (boolean) obj;
        }
    }

    public void setNotifyOffline(boolean isNotify) {
        put(NOTIFY_OFFLINE, isNotify);
    }
    public boolean isNotifyOffline() {
        Object obj = get(NOTIFY_OFFLINE);
        if (Validator.isEmpty(obj)) {
            return true;
        } else {
            return (boolean) obj;
        }
    }

    public void setErrorsitAll(boolean isNotifyALL) {
        put(ERRORSIT_ALL, isNotifyALL);
    }

    public boolean isErrorsitAll() {
        Object obj = get(ERRORSIT_ALL);
        if (Validator.isEmpty(obj)) {

            if (BuildConfig.IS_OVERSEAS){
                //海外默认关闭 false
                return true;
            }else {
                //国内默认开启 true
                return true;
            }
        } else {
            return (boolean) obj;
        }
    }

    public boolean isErrorsitAllOversea() {
        Object obj = get(ERRORSIT_ALL);
        if (Validator.isEmpty(obj)) {
            //国内默认开启 true
//            return true;
            //海外默认关闭 false
            return false;
        } else {
            return (boolean) obj;
        }
    }

    public void setIsHasAuraHomeDevices(boolean isLogin) {
        put(IS_HAS_AURA_HOME_DEVICES, isLogin);
    }

    public boolean isHasAuraHomeDevices() {
        Object obj = get(IS_HAS_AURA_HOME_DEVICES);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }

    public void setHeartBeatTime(long heartBeatTime) {
        put(HEART_BEAT_TIME, heartBeatTime);
    }

    public long getHeartBeatTime() {
        Object obj = get(HEART_BEAT_TIME);
        if (Validator.isEmpty(obj)) {
            return 0;
        } else {
            return (long) obj;
        }
    }

    public void setUdid(String udid) {
        put(UDID, udid);
    }

    public String getUdid() {
        Object obj = get(UDID);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setRegid(String regid) {
        put(REGID, regid);
    }

    public String getRegid() {
        Object obj = get(REGID);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }


    public void setReportTime(String reportTime) {
        put(LAST_REPORT_TIME, reportTime);
    }

    public String getReportTime() {
        Object obj = get(LAST_REPORT_TIME);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setCallTime(String reportTime) {
        put(LAST_CALL_TIME, reportTime);
    }

    public String getCallTime() {
        Object obj = get(LAST_CALL_TIME);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setAuraMateIsSlide(boolean isSlide) {
        put(AURAMATE_IS_SLIDE, isSlide);
    }

    public boolean isAuraMateSlide() {
        Object obj = get(AURAMATE_IS_SLIDE);
        if (Validator.isEmpty(obj)) {
            return true;
        } else {
            return (boolean) obj;
        }
    }

    public void setAuramateIsNoitcedMissedCall(boolean isSlide) {
        put(AURAMATE_IS_NOTICED_MISSED_CALL, isSlide);
    }

    public boolean isNoticedMissedCall() {
        Object obj = get(AURAMATE_IS_NOTICED_MISSED_CALL);
        if (Validator.isEmpty(obj)) {
            return true;
        } else {
            return (boolean) obj;
        }
    }

    public void setAuraMateDialog(boolean isSlide) {
        put(AURA_MATE_SHOW_DIALOG, isSlide);
    }

    public boolean isAuraMateDialog() {
        Object obj = get(AURA_MATE_SHOW_DIALOG);
        if (Validator.isEmpty(obj)) {
            return true;
        } else {
            return (boolean) obj;
        }
    }

    public void setCountryCode(String countryCode) {
        put(COUNTRY_CODE, countryCode);
    }

    public String getCountryCode() {
        Object obj = get(COUNTRY_CODE);
        if (Validator.isEmpty(obj)) {
            return "";
        } else {
            return (String) obj;
        }
    }

    public void setAuraMoviesRedPoint(boolean moviesRedPoint) {
        put(AURA_MOVIES_RED_POINT, moviesRedPoint);
    }

    public boolean getAuraMoviesRedPoint() {
        Object obj = get(AURA_MOVIES_RED_POINT);
        if (Validator.isEmpty(obj)) {
            return false;
        } else {
            return (boolean) obj;
        }
    }
    public void setFirstInVersion408(boolean firstInVersion408) {
        put(FIRST_IN_VERSION_408, firstInVersion408);
    }

    public boolean isFirstInVersion408() {
        Object obj = get(FIRST_IN_VERSION_408);
        if (Validator.isEmpty(obj)) {
            return true;
        } else {
            return (boolean) obj;
        }
    }
}
