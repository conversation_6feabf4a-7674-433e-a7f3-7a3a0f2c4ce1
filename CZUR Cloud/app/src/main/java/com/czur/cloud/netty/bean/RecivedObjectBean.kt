package com.czur.cloud.netty.bean


data class RecivedObjectBean(
    val body: RecivedObjectBody,
    val nodeId: Int?,
    val requestid: String?,
    val timestamp: Long?,
    val type: String?
)

data class RecivedObjectBody(
    val action: String?,
    val group_push: Int?,
    val method: String?,
    val reply: Any?,
    val data: Any?,
    val module: String?,
    val room: String?,
    val udid_from: String?,
    val userid_from: String?
)