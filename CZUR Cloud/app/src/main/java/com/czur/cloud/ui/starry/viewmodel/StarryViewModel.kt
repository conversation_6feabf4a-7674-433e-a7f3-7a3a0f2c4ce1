package com.czur.cloud.ui.starry.viewmodel

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.ToastUtils
import com.czur.cloud.BuildConfig
import com.czur.cloud.R
import com.czur.cloud.preferences.StarryPreferences
import com.czur.cloud.preferences.UserPreferences
import com.czur.cloud.ui.starry.api.StarryRepository
import com.czur.cloud.ui.starry.common.StarryConstants
import com.czur.cloud.ui.starry.common.StarryConstants.STARRY_USERTYPE_PERSION
import com.czur.cloud.ui.starry.meeting.base.FloatFragment
import com.czur.cloud.ui.starry.meeting.baselib.utils.getString
import com.czur.cloud.ui.starry.meeting.baselib.utils.launch
import com.czur.cloud.ui.starry.meeting.bean.InviteBean
import com.czur.cloud.ui.starry.meeting.common.UserHandler
import com.czur.cloud.ui.starry.meeting.model.MeetingModel
import com.czur.cloud.ui.starry.meeting.model.ModelManager
import com.czur.cloud.ui.starry.meeting.utils.PinyinUtils
import com.czur.cloud.ui.starry.model.JoinMeetData
import com.czur.cloud.ui.starry.model.JoinMeetModel
import com.czur.cloud.ui.starry.model.MeetingMember
import com.czur.cloud.ui.starry.model.MeetingRequest
import com.czur.cloud.ui.starry.model.Participant
import com.czur.cloud.ui.starry.model.StarryAddressBookModel
import com.czur.cloud.ui.starry.model.StarryCompanyInviterModel
import com.czur.cloud.ui.starry.model.StarryEnterpriseMemberModel
import com.czur.cloud.ui.starry.model.StarryEnterpriseModel
import com.czur.cloud.ui.starry.model.StarryUserInfoModel
import com.czur.cloud.ui.starry.utils.Tools
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.withContext
import java.util.Date
import java.util.Timer
import java.util.TimerTask

class StarryViewModel:ViewModel() {

    private val starryRepository by lazy {
        StarryRepository()
    }

    private val starryPreferences by lazy {
        StarryPreferences.getInstance()
    }

    // 用户信息
    var userInfo = MutableLiveData<StarryUserInfoModel>()
    // 返回的企业、个人数据
    var enterpriseMembers = MutableLiveData<StarryEnterpriseMemberModel>()
    // 企业列表
    var enterpriseList = MutableStateFlow<List<StarryEnterpriseModel>>(listOf())
    var enterpriseListJoined = MutableLiveData<List<StarryEnterpriseModel>>()

    // 个人联系人列表
    var addressBookList = MutableLiveData<List<StarryAddressBookModel>>()
    // 是否有未读消息
    var isUnReadNoticesFlag = MutableLiveData<Boolean>(false)
    // 最近是否显示红点
    var isMissedCallRecordsFlag = MutableLiveData<Boolean>(false)
    // 是否显示未处理消息的弹窗
    var isShowMeetingRemindDialog = MutableLiveData<Boolean>(false)

    // 当前选择的企业
    var currentCompanyModel = MutableLiveData<StarryEnterpriseModel>()
    // 当前的人员列表(根据类型区分)
    var currentContactsList = MutableLiveData<List<StarryAddressBookModel>>()
    var currentContactsSortList = MutableLiveData<List<StarryAddressBookModel>>()
    // 当前选中的用户类型：个人联系人，企业联系人
    private var currentUserType = StarryConstants.STARRY_USER_TYPE_CONTACT

    // 选择的类型：发起会议, 会议中添加人员
    private var selectListType = StarryConstants.STARRY_SELECT_TYPE_START

    private var meetingMembers = MutableLiveData(mutableListOf<MeetingMember>())

    var currentIndexDescription = MutableLiveData("")

    // 记录一下是否存在上一级列表页面（公司列表）
    var starryPrePageName = ""

    // 记录本次会议中的密码变化,初始时=上次的密码
    var inMeetingResetPwd = ""

    // 动态生成密码
    var starryMeetingPwdAuto = ""
    var starryMeetingPwdSet = ""
    // 是否显示密码
    var starryMeetingIsShowPwd = false

    var meetingCompanyListFragment: FloatFragment? = null


    // 动态生成密码
    fun onRandMeetingPwd(){
        starryMeetingPwdAuto = Tools.onRandMeetingPassword()
        StarryPreferences.getInstance().lastPwd = starryMeetingPwdAuto

        Log.i("StarryViewModel","onRandMeetingPwd.starryCompanyListPwd=${starryMeetingPwdAuto}")
    }

    fun onRandMeetingPwdInMeeting(){
        starryMeetingPwdAuto = Tools.onRandMeetingPassword()
        // 同步记录一下刷新的密码
        inMeetingResetPwd = starryMeetingPwdAuto
        Log.i("StarryViewModel","onRandMeetingPwdInMeeting.starryCompanyListPwd=${starryMeetingPwdAuto}")
    }

    fun getDataFromServer(){

        launch {

            // 用户信息
            getUserAccount()

            // 是否有未读消息
            hasUnReadNotices()

            // 查询企业通讯录和个人联系人
            getEnterpriseMembers()
        }
    }

    // 成者云账号获取用户信息
    fun getUserAccount() {
        launch{
            val user = starryRepository.getStarryUserInfo() ?: StarryUserInfoModel("0")
            userInfo.postValue(user)
            starryPreferences.setNewUserInfo(user)

        }
    }
    fun checkShowMeetingRemindDialog(){
        launch{
            val flag = starryRepository.showMeetingRemindDialog() ?: false
        }

    }

    fun showMeetingRemindDialog(){
        launch{
            val flag = starryRepository.showMeetingRemindDialog() ?: false
            isShowMeetingRemindDialog.postValue(flag)
        }

    }

    // 最近是否显示红点
    fun missedCallRecords(){
        launch{
            val flag = starryRepository.missedCallRecords() ?: false
            isMissedCallRecordsFlag.value=flag
        }
    }

    // 最近小红点已读
    fun redpointRead(){
        launch{
            starryRepository.redpointRead()
            isMissedCallRecordsFlag.value=false
        }
    }

    // 是否有未读消息
    fun hasUnReadNotices(){
        launch{
            val accountNo = starryPreferences.accountNo
            val flag = starryRepository.hasUnReadNotices(accountNo) ?: false
            isUnReadNoticesFlag.value=flag
        }
    }

    // 查询企业通讯录和个人联系人
    fun getEnterpriseMembers(dataLoaded: (() -> Unit)? = null){
        launch{
            delay(100)
            val accountId = starryPreferences?.accountNo ?: ""
            val meetingNo = ""
            val name = starryPreferences?.name ?: ""
            val model = starryRepository.getEnterpriseMembers(accountId, meetingNo, name)

            val address = model.addressBook
            val enterpriseList1 = model.enterPriseMembers

            enterpriseMembers.value = model
            addressBookList.value = address
            // 同步一下address数据
            ModelManager.membersModel.addressBookList.value = address

//            enterpriseList.postValue(enterprise)
            // 多个企业时按字母A-Z顺序排列，但在有邀请加入时，且未处理消息时，该企业显示在企业列表最顶部
            val tmpList = ArrayList<StarryEnterpriseModel>()
            enterpriseList1.forEach {
                val tmp = it
                tmp.pinyin = PinyinUtils.ccs2Pinyin(it.enterpriseName).lowercase()

                tmpList.add(tmp)
            }
            val enterpriseList2 = tmpList.sortedWith(compareBy(StarryEnterpriseModel::joinStatus, StarryEnterpriseModel::pinyin))

            enterpriseList.value = enterpriseList2
            enterpriseListJoined.value = enterpriseList2.filter {
                it.joinStatus == StarryConstants.STARRY_COMPANY_STATUS_JOINED
            }

            // 无企业，视为个人普通用户
            if (enterpriseList2.isEmpty()){
                val user = starryPreferences.starryUserinfoModel ?: StarryUserInfoModel("0")
                user.userType = STARRY_USERTYPE_PERSION
//                starryPreferences.starryUserinfoModel = user
                starryPreferences.setNewUserInfo(user)
            }

            if (currentUserType == StarryConstants.STARRY_USER_TYPE_CONTACT){
                setCurrentUserTypeContact()
            }

            dataLoaded?.invoke()
        }
    }

    // 查询自己是否在企业中
    suspend fun isSelfInEnterprise(accountId: String, name: String): Boolean {
        return starryRepository.isInEnterprise(accountId, "", name)
    }

    // 发起新的会议
    fun startNewMeeting(members1: List<MeetingMember>, title: String = ""): String {
        meetingMembers.value = members1.toMutableList()

        val name = UserPreferences.getInstance().user.name
        val subject = getString(R.string.starry_main_meeting_start_title, name)
        val members = meetingMembers.value ?: mutableListOf()
//        val meetingPwd: String = starryMeetingPwdAuto ?: ""
        val meetingPwd =  if (starryMeetingIsShowPwd)
            StarryPreferences.getInstance().lastPwd else ""

        var jsonParam = ""

        launch {
            jsonParam = startMeeting(members, subject, meetingPwd)
        }
        meetingMembers = MutableLiveData(mutableListOf<MeetingMember>())

//        logI("StarryViewModel.startNewMeeting.开会参数:jsonParam=${jsonParam}")
//        "开会参数:jsonParam=${jsonParam}".prt()
//        "开会参数:jsonParam=${jsonParam}".log()

        return jsonParam
    }

    /**
     * 开始会议
     * @param members: 要开始会议的人员
     * @param subject: 开会的主题
     */
    private fun startMeeting(members: List<MeetingMember>, subject: String, meetPwd: String = ""): String {

        val isRecord = false

        val meetingRequest =
            MeetingRequest(isRecord, subject, mutableListOf(), meetPwd)

        // 自己是管理员
        meetingRequest.participants.add(Participant(true, starryPreferences?.accountNo ?: ""))

        members.forEach {
            val member = Participant(false, it.accountNo.toString())
            meetingRequest.participants.add(member)
        }

        return Gson().toJson(meetingRequest)

    }

    // 会议中邀请新成员-pc端
    fun inviteMemberPC(willInvitedSet: List<String>){
        launch {
            val inviteBean =
                InviteBean(MeetingModel.room.toInt(), willInvitedSet)
            val synJson = Gson().toJson(inviteBean)
            val ret = MeetingModel.inviteMember(synJson)

            if (!ret){
                // inviteMemberPC:{"code":2031,"msg":"超过最大方数限制","data":null}.失败!
                if (MeetingModel.inviteErrorCode == 2031){
                    ToastUtils.showLong(R.string.starry_invite_msg_error_over)
                }else {
                    ToastUtils.showLong(R.string.starry_invite_msg_error)
                }
            }
//            logI("StarryViewModel.inviteMember.ret = ${ret}")
        }
    }

    // 设置当前的用户类型--个人联系人
    fun setCurrentUserTypeContact() {
        currentUserType = StarryConstants.STARRY_USER_TYPE_CONTACT

        val companyModel = StarryEnterpriseModel()
        currentCompanyModel.value = companyModel
        val contactList = addressBookList.value ?: listOf()
        currentContactsList.postValue(contactList)
    }

    // 设置当前的用户类型--企业联系人
    fun setCurrentUserTypeCompany(position: Int) {
        currentUserType = StarryConstants.STARRY_USER_TYPE_COMPANY
//        Log.i("Jason", "enterpriseList=${enterpriseList}")
        var companyModel = StarryEnterpriseModel()

        if ((enterpriseList.value?.size ?: 0) > 0)
            companyModel = enterpriseList.value?.get(position) ?: StarryEnterpriseModel()

        currentCompanyModel.value = companyModel
        val contactList = companyModel.membersList
        currentContactsList.value = contactList

    }

    fun setCurrentUserTypeCompany(enterprise: StarryEnterpriseModel) {
        currentUserType = StarryConstants.STARRY_USER_TYPE_COMPANY

        currentCompanyModel.value = enterprise
        val contactList = enterprise.membersList
        currentContactsList.value = contactList

    }

    fun getCurrentUserType(): String {
        return currentUserType
    }

    fun setSelectType(selectType: String) {
        selectListType = selectType
    }
    fun getSelectType(): String {
        return selectListType
    }

    // 无网络下点击按钮,提示
    fun clickNoNetwork(): Boolean{
        return if (NetworkUtils.isConnected()) {
            false
        }else{
            ToastUtils.showLong(R.string.starry_network_error_msg)
            true
        }
    }

    // 测试volume效果3
    val volume = MutableLiveData<Int>(0)
    fun startTimer(){
        val task = MyTimerTask()
        Timer().schedule(task, Date(), 1000)
    }


    inner class MyTimerTask : TimerTask() {
        override fun run() {
            volume.postValue((0..225).random())
        }
    }

    //
    suspend fun updateJoinStatus(enterpriseId: String, status: Int): StarryCompanyInviterModel {
        return starryRepository.updateJoinStatus(enterpriseId, status)
    }

    //
    suspend fun readByEnterpriseId(enterpriseId: String, type: String): Boolean {
        return starryRepository.readByEnterpriseId(enterpriseId, type)
    }


    fun clearedData(){
        onCleared()
    }

    override fun onCleared() {
        super.onCleared()

        userInfo.value = StarryUserInfoModel()
        isUnReadNoticesFlag.value = false

    }

    // 加入会议-APP、PC
    suspend fun joinMeeting(meetingCode: String, meetingPassword: String): Int {
        val flag = withContext(Dispatchers.IO) {
            try {
                JoinMeetModel.joinMeeting(meetingCode, meetingPassword)
            }catch (e: Exception){
                JoinMeetModel.ERROR_CODE_OTHER
            }
        }
        return flag
    }

    suspend fun checkPassword(meetingCode: String, meetingPassword: String): Int {
        val flag = withContext(Dispatchers.IO) {
            try {
                JoinMeetModel.checkPassword(meetingCode, meetingPassword)
            }catch (e: Exception){
                JoinMeetModel.ERROR_CODE_OTHER
            }
        }
        return flag
    }

    // 修改会议密码-APP、PC
    fun updateMeetingPassword(roomId: String, password: String){
        launch{
            val flag = starryRepository.updateMeetingPassword(roomId, password) ?: false
            if (flag){
                MeetingModel.meetingPassword = password
            }
        }
    }

    suspend fun getMeetingCodeString(): String {
        val roomName = MeetingModel.roomTitle ?: ""
        val meetingCode = MeetingModel.meetingCode ?: ""
        val meetingPwd = MeetingModel.meetingPassword ?: ""
        val account = UserHandler.accountNo ?: StarryPreferences.getInstance().accountNo

        val shareUuid = withContext(Dispatchers.IO) {
            shareMeeting(roomName, meetingCode, meetingPwd, account) ?: ""
        }
        if (shareUuid.isNotBlank()) {
            val shareUrl = "${BuildConfig.SHARE_STARRY_URL}${shareUuid}"
            val codeMeet = Tools.formateMeetCode(MeetingModel.meetingCode, "-")
            return if (MeetingModel.meetingPassword == "") {
                String.format(
                    getString(R.string.starry_meeting_paste_title_no_pwd),
                    StarryPreferences.getInstance().name,
                    MeetingModel.roomTitle,
                    shareUrl,
                    codeMeet
                )
            } else {
                String.format(
                    getString(R.string.starry_meeting_paste_title),
                    StarryPreferences.getInstance().name,
                    MeetingModel.roomTitle,
                    shareUrl,
                    codeMeet,
                    MeetingModel.meetingPassword
                )
            }
        }
        return ""
    }

    // 分享会议
    suspend fun shareMeeting(roomName: String, meetingCode: String, meetingPassword: String, account: String): String {
        return starryRepository.shareMeeting(roomName, meetingCode, meetingPassword, account) ?: ""
    }

    // 查看分享的会议信息
    suspend fun shareMeetingInfo(meetingUUID: String): JoinMeetData {
        return starryRepository.shareMeetingInfo(meetingUUID) ?: JoinMeetData()
    }


}