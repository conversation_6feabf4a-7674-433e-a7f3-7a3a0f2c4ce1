package com.czur.cloud.ui.starry.network;

import com.czur.cloud.model.NettyModelStarry;
import com.czur.cloud.ui.starry.model.Notice;
import com.czur.cloud.ui.starry.model.StarryAddressBookModel;
import com.czur.cloud.ui.starry.model.StarryCallRecordDataModel;
import com.czur.cloud.ui.starry.model.StarryCallRecordDetailModel;
import com.czur.cloud.ui.starry.model.StarryEnterpriseMemberModel;
import com.czur.cloud.ui.starry.model.StarryNoticeData;
import com.czur.cloud.ui.starry.model.StarryTokenModel;
import com.czur.cloud.ui.starry.model.StarryUserInfoModel;

import java.lang.reflect.Type;

public interface StarryHttpServices {

    ///////// 投影仪  //////////
    //获取netty地址,获取长连接地址,请求长连接地址
    @MiaoHttpGet("netty/node")
    void getNettyUrl(@MiaoHttpParam("u_id") String uId,
                     Class<NettyModelStarry> clazz,
                     StarryHttpManager.Callback<NettyModelStarry> callback);


}
