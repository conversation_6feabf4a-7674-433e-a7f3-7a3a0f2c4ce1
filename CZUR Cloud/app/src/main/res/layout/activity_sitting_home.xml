<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/gary_f9">

    <FrameLayout
        android:id="@+id/sitting_home_frameLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/jingMargin10"
        android:layout_below="@+id/sitting_home_top_bar">

    </FrameLayout>

    <RelativeLayout
        android:id="@+id/sitting_home_top_bar"
        android:layout_width="match_parent"
        android:layout_height="44dp">

        <ImageView
            android:id="@+id/sitting_home_back_btn"
            android:layout_width="29.5dp"
            android:layout_height="36dp"
            android:layout_centerVertical="true"
            android:layout_marginLeft="9dp"
            android:padding="10dp"
            android:src="@mipmap/login_back_icon" />

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:id="@+id/tv_ip_port_config"
            android:layout_width="70dp"
            android:layout_height="match_parent"
            android:layout_toEndOf="@+id/sitting_home_back_btn"
            android:gravity="center" />

        <RelativeLayout
            android:id="@+id/sitting_home_more_btn"
            android:layout_width="44dp"
            android:layout_height="match_parent"
            android:layout_alignParentRight="true">

            <ImageView
                android:layout_width="18.5dp"
                android:layout_height="17.5dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_marginRight="18.5dp"
                android:background="@mipmap/book_more_icon" />

            <View
                android:layout_width="5dp"
                android:layout_height="5dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentRight="true"
                android:layout_marginTop="8dp"
                android:layout_marginRight="14dp"
                android:background="@drawable/circle_red"
                android:visibility="gone" />

        </RelativeLayout>

        <com.czur.cloud.ui.component.MediumBoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/smart_sitting"
            android:textColor="@color/account_title"
            android:textSize="18sp" />

    </RelativeLayout>

    <include
        layout="@layout/layout_no_network"
        android:visibility="gone" />


</RelativeLayout>
