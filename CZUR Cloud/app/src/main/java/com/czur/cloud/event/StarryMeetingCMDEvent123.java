package com.czur.cloud.event;

import com.czur.cloud.netty.bean.StarryRecivedReply;

public class StarryMeetingCMDEvent123 extends BaseEvent {
	private StarryRecivedReply params;
	private String params2;

	public StarryMeetingCMDEvent123(EventType eventType, StarryRecivedReply params) {
		super(eventType);
		this.params=params;
	}
	public StarryRecivedReply getParams() {
		return params;
	}

	@Override
	public boolean match(Object obj) {
		return true;
	}
}
