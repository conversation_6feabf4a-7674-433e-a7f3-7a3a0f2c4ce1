package com.czur.cloud.ui.market

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.util.Log
import android.view.View
import android.webkit.*
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.BarUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.NetworkUtils
import com.czur.cloud.R
import com.czur.cloud.ui.base.BaseActivity
import kotlinx.android.synthetic.main.activity_webview.*
import kotlinx.android.synthetic.main.layout_reload_webview.*
import kotlinx.android.synthetic.main.layout_reload_webview.reload_webview_rl

/**
 * Created by Jason 20221101
 */
class WebViewActivityKt : BaseActivity() {
    private val webView: WebView by lazy { WebView(this) }

    private var title: String? = null
    private var url: String? = null
    private var handler: Handler? = null
    private var lastTime: Long = 0L

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        BarUtils.setStatusBarColor(this, 0, true)
        BarUtils.setStatusBarLightMode(window, true)
        setContentView(R.layout.activity_webview)
        showProgressDialog()
        initComponent()
        registerEvent()
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initComponent() {
        title = intent.getStringExtra("title")
        url = intent.getStringExtra("url")
        handler = Handler()
        webview_title_tv?.text = title
        val titleSize = intent.getIntExtra("titleSize", 18)
        webview_title_tv?.textSize = titleSize.toFloat()
        LogUtils.i("WebViewActivity.title=$title", "url=$url")

        webView.setLayerType(View.LAYER_TYPE_SOFTWARE, null)
        val settings = webView.settings.apply{
            domStorageEnabled = true
            //解决一些图片加载问题
            javaScriptEnabled = true
            blockNetworkImage = false
        }
        webView.webViewClient = webClient
        webView.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView, progress: Int) {
                //当进度走到100的时候做自己的操作，我这边是弹出dialog
                if (progress == 100) {
                    hideProgressDialog()
                }
            }
        }
        webView.addJavascriptInterface(JSCallAndroidObject(), "jsCallAndroidObject")
        url?.let {
            lastTime = System.currentTimeMillis()
            LogUtils.i("WebViewActivityNew.loadUrl.Start:$lastTime", webView)
            webView.loadUrl(it)
        }
    }

    private fun registerEvent() {
        if (NetworkUtils.isConnected()) {
            reload_webview_rl?.visibility = View.GONE
        } else {
            reload_webview_rl?.visibility = View.VISIBLE
        }
        webview_back_btn?.setOnClickListener {
            if (webView.canGoBack() == true) {
                webView.goBack() //goBack()表示返回WebView的上一页面
            } else {
                ActivityUtils.finishActivity(this@WebViewActivityKt)
            }
        }
        reload_btn?.setOnClickListener {
            showProgressDialog()
            webView.reload()
        }
    }

    /***
     * 设置Web视图的方法
     */
    private val webClient: WebViewClient = object : WebViewClient() {
        //处理网页加载失败时
        override fun onReceivedError(
            view: WebView,
            request: WebResourceRequest,
            error: WebResourceError
        ) {
            super.onReceivedError(view, request, error)
            reloadProgress()
            reload_webview_rl?.visibility = View.VISIBLE
            webView.visibility = View.GONE
            LogUtils.e("WebViewActivityNew.onReceivedError");
        }

        override fun onPageFinished(view: WebView, url: String) {
//            LogUtils.i("WebView load finish");
            val timestamp = System.currentTimeMillis()
            LogUtils.i("WebViewActivityNew.onPageFinished.End:${timestamp},耗时：${timestamp-lastTime}")
            reloadProgress()
            reload_webview_rl?.visibility = View.GONE
            webView.visibility = View.VISIBLE
        }

        @Deprecated("Deprecated in Java")
        override fun shouldOverrideUrlLoading(view: WebView, url1: String): Boolean {
            LogUtils.i("WebViewActivityNew.shouldOverrideUrlLoading.url1=${url1}")
            var url = url1
            Log.d("webview", "url: $url")
            if (url.startsWith("mailto:")) {
                url = url.replaceFirst("mailto:".toRegex(), "")
                url = url.trim { it <= ' ' }
                val i = Intent(Intent.ACTION_SEND)
                i.setType("plain/text").putExtra(Intent.EXTRA_EMAIL, arrayOf(url))
                startActivity(i)
                return true
            }
            view.loadUrl(url)
            return true
        }
    }

    private fun reloadProgress() {
        handler?.postDelayed({ hideProgressDialog() }, 100)
    }

    /**
     * @des: js交互
     * @params:
     * @return:
     */
    inner class JSCallAndroidObject {
        private val TAG = MallFragment.JSCallAndroidObject::class.java.simpleName
        @JavascriptInterface
        fun jsCallAndroid(msg: String?): String {
            val uri = Uri.parse(msg)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            startActivity(intent)
            LogUtils.i(msg)
            return "from Android"
        }
    }

    override fun onDestroy() {
        webView.loadDataWithBaseURL(null, "", "text/html", "uft-8", null)
        webView.clearHistory()
        webView.clearCache(true)
        super.onDestroy()
    }

}
