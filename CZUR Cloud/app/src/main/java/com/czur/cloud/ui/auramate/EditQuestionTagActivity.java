package com.czur.cloud.ui.auramate;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;
import com.czur.cloud.adapter.EditQuestionTagAdapter;
import com.czur.cloud.entity.AuraMateWrongTagModel;
import com.czur.cloud.model.AuraFileTotalModel;
import com.czur.cloud.network.HttpManager;
import com.czur.cloud.network.core.MiaoHttpEntity;
import com.czur.cloud.network.core.MiaoHttpManager;
import com.czur.cloud.preferences.UserPreferences;
import com.czur.cloud.ui.component.popup.AddTagPopup;
import com.czur.cloud.ui.component.popup.CloudCommonPopupConstants;
import com.czur.cloud.util.EtUtils;
import com.czur.cloud.util.validator.Validator;
import com.google.gson.reflect.TypeToken;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by Yz on 2018/2/26.
 * Email：<EMAIL>
 */

public class EditQuestionTagActivity extends AuramateBaseActivity implements View.OnClickListener {


    private ImageView normalBackBtn;
    private TextView normalTitle;
    private RecyclerView tagRecyclerView;
    private TextView finishBtn;
    private EditQuestionTagAdapter tagAdapter;
    private EditText dialogEdt;
    private boolean isPreview;

    private UserPreferences userPreferences;
    private List<AuraMateWrongTagModel> auraMateWrongTagModels;



    private int position;
    private boolean isFolder;
    private boolean hasBig;
    private AuraFileTotalModel.FileListBean entity;
    private String ownerId;
    private String tagId;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStatusBarColor(R.color.gary_f9);
        BarUtils.setStatusBarLightMode(this, true);
        setContentView(R.layout.activity_edit_wrong_tag);
        initComponent();
        registerEvent();
    }

    @Override
    public boolean PCNeedFinish() {
        return false;
    }


    private void initComponent() {
        userPreferences = UserPreferences.getInstance(this);
        auraMateWrongTagModels = new ArrayList<>();
        normalBackBtn = (ImageView) findViewById(R.id.normal_back_btn);
        normalTitle = (TextView) findViewById(R.id.normal_title);
        tagRecyclerView = (RecyclerView) findViewById(R.id.tag_recyclerView);
        entity = (AuraFileTotalModel.FileListBean) getIntent().getSerializableExtra("image");
        isFolder = getIntent().getBooleanExtra("isFolder", false);
        position = getIntent().getIntExtra("position", 0);
        hasBig = getIntent().getBooleanExtra("hasBig", false);
        ownerId = getIntent().getStringExtra("ownerId");

        finishBtn = (TextView) findViewById(R.id.finish_btn);

        normalTitle.setText(getString(R.string.choose_object));

        tagAdapter = new EditQuestionTagAdapter(this, auraMateWrongTagModels);
        tagAdapter.setAddTagListener(addTagListener);
        tagAdapter.setOnTagItemClickListener(onTagItemClickListener);
        tagRecyclerView.setAdapter(tagAdapter);
        tagRecyclerView.setHasFixedSize(true);
        tagRecyclerView.setLayoutManager(new GridLayoutManager(this, 2));

        getTag();

    }

    private void getTag() {
        HttpManager.getInstance().request().getWrongQuestionTag(userPreferences.getUserId(), equipmentId, ownerId,new TypeToken<List<AuraMateWrongTagModel>>() {
        }.getType(), new MiaoHttpManager.Callback<AuraMateWrongTagModel>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<AuraMateWrongTagModel> entity) {
                hideProgressDialog();
                tagAdapter.refreshData(entity.getBodyList());

            }

            @Override
            public void onFailure(MiaoHttpEntity<AuraMateWrongTagModel> entity) {
                hideProgressDialog();

            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();

            }
        });
    }

    private void registerEvent() {
        normalBackBtn.setOnClickListener(this);
        finishBtn.setOnClickListener(this);
        setNetListener();
    }

    private EditQuestionTagAdapter.AddTagListener addTagListener = new EditQuestionTagAdapter.AddTagListener() {
        @Override
        public void onAddTagClickListener(int position) {
            showAddTagDialog(true);
        }
    };


    private EditQuestionTagAdapter.OnTagItemClickListener onTagItemClickListener = new EditQuestionTagAdapter.OnTagItemClickListener() {
        @Override
        public void onAuraWrongTagEntityClick(AuraMateWrongTagModel auraMateWrongTagModel, int position, boolean hasSelect) {
            if (hasSelect){
                finishBtn.setBackground(getResources().getDrawable(R.drawable.btn_rec_5_bg_with_blue_aura_home));
                finishBtn.setEnabled(true);
                finishBtn.setClickable(true);
                tagId= auraMateWrongTagModel.getId()+"";
            }else {
                finishBtn.setBackground(getResources().getDrawable(R.drawable.btn_rect_6_bg_gray_ec));
                finishBtn.setEnabled(false);
                finishBtn.setClickable(false);
            }

        }
    };

    /**
     * @des: 显示添加Dialog
     * @params:
     * @return:
     */

    private void showAddTagDialog(boolean isAdd) {
        AddTagPopup.Builder builder = new AddTagPopup.Builder(EditQuestionTagActivity.this, CloudCommonPopupConstants.COMMON_TWO_BUTTON);
        builder.setTitle(getString(R.string.add_object));
        builder.setOnPositiveListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (!EtUtils.containsEmoji(dialogEdt.getText().toString())) {
                    if (Validator.isNotEmpty(dialogEdt.getText().toString())) {
                        addWrongQuestionTag();
                    } else {
                        showMessage(R.string.object_should_not_be_empty);
                    }

                } else {
                    showMessage(R.string.nickname_toast_symbol);
                }

                dialog.dismiss();

            }
        });
        builder.setOnNegativeListener(new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });

        AddTagPopup commonPopup = builder.create();
        dialogEdt = (EditText) commonPopup.getWindow().findViewById(R.id.edt);
        commonPopup.show();
    }

    private void addWrongQuestionTag() {
        HttpManager.getInstance().request().addWrongQuestionTag(userPreferences.getUserId(), equipmentId,ownerId, dialogEdt.getText().toString(), String.class, new MiaoHttpManager.Callback<String>() {
            @Override
            public void onStart() {
                showProgressDialog();
            }

            @Override
            public void onResponse(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                getTag();

            }

            @Override
            public void onFailure(MiaoHttpEntity<String> entity) {
                hideProgressDialog();
                if (entity.getCode()==MiaoHttpManager.STATUS_NAME_IS_SAMED){
                    showMessage(R.string.had_same_name_object);
                }

            }

            @Override
            public void onError(Exception e) {
                hideProgressDialog();

            }
        });
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.normal_back_btn:
                ActivityUtils.finishActivity(this);
                break;
            case R.id.finish_btn:
                Intent intent= new Intent(EditQuestionTagActivity.this,AuraMateSelectWrongQuestionActivity.class);

                intent.putExtra("tagId", tagId);
                intent.putExtra("isFolder", isFolder);
                intent.putExtra("position", position);
                intent.putExtra("image", entity);
                intent.putExtra("hasBig", hasBig);
                intent.putExtra("equipmentId",equipmentId);
                intent.putExtra("ownerId",ownerId);
                ActivityUtils.startActivity(intent);

                break;
            default:
                break;
        }
    }


}
