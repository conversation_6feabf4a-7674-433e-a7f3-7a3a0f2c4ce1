package com.czur.cloud.entity.realm;

import io.realm.RealmObject;

/**
 * Created by <PERSON>z on 2018/4/27.
 * Email：<EMAIL>
 */
public class MessageEntity extends RealmObject {

    private String requestId;
    private String deviceUDID;
    private long createTime;
    private int  type ;
    private int  status ;
    private String uuid;

    private String  dataBegin;
    private boolean needReceipt;
    private String  name ;

    public long getServerTimestamp() {
        return serverTimestamp;
    }

    public void setServerTimestamp(long serverTimestamp) {
        this.serverTimestamp = serverTimestamp;
    }

    private long serverTimestamp;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }



    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public boolean isNeedReceipt() {
        return needReceipt;
    }

    public void setNeedReceipt(boolean needReceipt) {
        this.needReceipt = needReceipt;
    }
    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getDataBegin() {
        return dataBegin;
    }

    public void setDataBegin(String dataBegin) {
        this.dataBegin = dataBegin;
    }

    public String getDeviceUDID() {
        return deviceUDID;
    }

    public void setDeviceUDID(String deviceUDID) {
        this.deviceUDID = deviceUDID;
    }

}
