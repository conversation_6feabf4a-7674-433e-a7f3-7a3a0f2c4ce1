package com.czur.cloud.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.RecyclerView.ViewHolder;

import com.czur.cloud.R;
import com.czur.cloud.model.EtFileModel;

import java.util.List;

/**
 * Created by Yz on 2018/3/16
 * Email：<EMAIL>
 */


public class MoveAdapter extends RecyclerView.Adapter<ViewHolder> {


    private static final int ITEM_TYPE_NORMA = 0;
    private Activity mActivity;
    //当前需要显示的所有的图片数据
    private List<EtFileModel.FoldersBean> datas;

    private LayoutInflater mInflater;

    /**
     * 构造方法
     */
    public MoveAdapter(Activity activity, List<EtFileModel.FoldersBean> datas) {

        this.mActivity = activity;
        this.datas = datas;
        mInflater = LayoutInflater.from(activity);
    }

    public void refreshData(List<EtFileModel.FoldersBean> datas) {
        this.datas = datas;
        notifyDataSetChanged();

    }



    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        return new NormalViewHolder(mInflater.inflate(R.layout.item_move_folder, parent, false));
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, final int position) {

        if (holder instanceof NormalViewHolder) {

            final NormalViewHolder mHolder = (NormalViewHolder) holder;
            mHolder.mItem = datas.get(position);

            if (mHolder.mItem.isAutoCreate()){
                mHolder.itemEtMoveFolderNameTv.setText(mHolder.mItem.getName().replaceAll("New Doc",mActivity.getString(R.string.et_doc)));
            }else {
                mHolder.itemEtMoveFolderNameTv.setText(mHolder.mItem.getName());
            }
            mHolder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemClickListener != null) {
                        onItemClickListener.onItemClick(position,mHolder.mItem);
                    }
                }
            });

        }
    }


    @Override
    public int getItemViewType(int position) {

        return ITEM_TYPE_NORMA;

    }


    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }


    private class NormalViewHolder extends ViewHolder {
        public final View mView;
        EtFileModel.FoldersBean mItem;
        TextView itemEtMoveFolderNameTv;


        NormalViewHolder(View itemView) {
            super(itemView);
            mView = itemView;
            itemEtMoveFolderNameTv = (TextView) itemView.findViewById(R.id.item_et_move_folder_name_tv);


        }


    }


    private onItemClickListener onItemClickListener;

    public void setOnItemClickListener(onItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface onItemClickListener {
        void onItemClick(int position, EtFileModel.FoldersBean foldersBean);
    }


}
