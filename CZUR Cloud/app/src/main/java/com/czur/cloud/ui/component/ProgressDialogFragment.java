package com.czur.cloud.ui.component;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.DialogInterface.OnKeyListener;
import android.graphics.Color;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.fragment.app.DialogFragment;

import com.blankj.utilcode.util.BarUtils;
import com.czur.cloud.R;


/**
 * Created by Yz on 2018/3/9
 * Email：<EMAIL>
 * 全局加载框
 */

public class ProgressDialogFragment extends DialogFragment {

    public static final String TAG = "ProgressDialogFragment";
    private static final String KEY_TAG = "key_tag";

    private Dialog dialog = null;

    private boolean touchable;
    private boolean isDark;
    private boolean cancelable;

    private OnCancelListener cancelListener;
    private String tag;
    private boolean isMomentDialog;//

    /**
     * @return
     */
    public static ProgressDialogFragment newInstance(String tag) {
        ProgressDialogFragment dialog = new ProgressDialogFragment();
        Bundle args = new Bundle();
        args.putString(KEY_TAG, tag);
        dialog.setArguments(args);

        return dialog;
    }

    /**
     * @param savedInstanceState
     * @return
     * @see DialogFragment#onCreateDialog(Bundle)
     */
    @Override
    public Dialog onCreateDialog(Bundle savedInstanceState) {
        if (getArguments() != null) {
            tag = getArguments().getString(KEY_TAG);
        }

        if (touchable) {
            dialog = new Dialog(getActivity(), R.style.TransparentProgressDialog);
            dialog.getWindow().setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL, WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL);
        } else {
            dialog = createLoadingDialog(getActivity(), isDark);
        }
        BarUtils.setStatusBarColor(dialog.getWindow(), Color.TRANSPARENT);
        if (!isDark) {
            BarUtils.setStatusBarLightMode(dialog.getWindow(), true);
            BarUtils.setNavBarColor(dialog.getWindow(), getActivity().getColor(R.color.gary_f9));
        } else {
            BarUtils.setStatusBarLightMode(dialog.getWindow(), false);
            BarUtils.setNavBarColor(dialog.getWindow(), getActivity().getColor(R.color.black_2a));
        }
        dialog.setCancelable(cancelable);
        dialog.setCanceledOnTouchOutside(false);
        dialog.setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    if (cancelListener != null) {
                        cancelListener.onCancel(dialog, tag);
                    }
                    return true;
                }

                return false;
            }
        });

        return dialog;
    }

    /**
     * @param isDark
     */
    public void setIsDark(boolean isDark) {
        this.isDark = isDark;
    }

    /**
     * @param touchable
     */
    public void setTouchable(boolean touchable) {
        this.touchable = touchable;
    }

    public void setMomentDialog(boolean isMomentDialog){
        this.isMomentDialog = isMomentDialog;
    }
    /**
     * @param cancelable
     * @see DialogFragment#setCancelable(boolean)
     */
    @Override
    public void setCancelable(boolean cancelable) {
        super.setCancelable(cancelable);

        this.cancelable = cancelable;
    }

    /**
     * @param cancelListener
     */
    public void setOnCancelListener(OnCancelListener cancelListener) {
        this.cancelListener = cancelListener;
    }

    /**
     * @see DialogFragment#dismiss()
     */
    @Override
    public void dismiss() {
        super.dismiss();

        if (dialog != null) {
            dialog.dismiss();
        }
    }

    public interface OnCancelListener {
        void onCancel(DialogInterface dialog, String tag);
    }

    private Dialog createLoadingDialog(Context context, boolean isDark) {
        LayoutInflater inflater = LayoutInflater.from(context);
        View view = inflater.inflate(R.layout.dialog_loading, null);
        final RelativeLayout layout = (RelativeLayout) view.findViewById(R.id.dialog_view);
        // main.xml中的ImageView
        if (isDark) {
            layout.setBackgroundColor(context.getResources().getColor(R.color.blackOpaque60));
        }else
        if (isMomentDialog){
            layout.setBackgroundColor(context.getResources().getColor(R.color.white));
        }

        ImageView circleImg = (ImageView) view.findViewById(R.id.img);
        // 加载动画
        Animation imgAnim = AnimationUtils.loadAnimation(
                context, R.anim.dialog_anim);
        // 使用ImageView显示动画
        circleImg.startAnimation(imgAnim);

        Animation bgAnim = AnimationUtils.loadAnimation(
                context, R.anim.dialog_fade_in);
        layout.startAnimation(bgAnim);

        // 创建自定义样式dialog
        Dialog loadingDialog = new Dialog(context, R.style.TransparentProgressDialog);
        loadingDialog.setContentView(view);
        Window window = loadingDialog.getWindow();
        //获取对话框当前的参数值
        android.view.WindowManager.LayoutParams params = loadingDialog.getWindow().getAttributes();
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        params.height = WindowManager.LayoutParams.MATCH_PARENT;
        //参数为0到1之间。0表示完全透明，1就是不透明。按需求调整参数
        params.alpha = 1f;
        return loadingDialog;
    }
}
