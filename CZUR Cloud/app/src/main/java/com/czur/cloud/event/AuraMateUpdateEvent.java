package com.czur.cloud.event;

public class AuraMateUpdateEvent extends BaseEvent {


	private String deviceId;
	private int isUpdate;
	public AuraMateUpdateEvent(EventType eventType, String deviceId, int isUpdate) {
		super(eventType);
		this.deviceId=deviceId;
		this.isUpdate=isUpdate;
	}
	public String getDeviceId() {
		return deviceId;
	}

	public int getIsUpdate() {
		return isUpdate;
	}

	@Override
	public boolean match(Object obj) {
		return true;
	}
}
